package com.chevron.promote.service.impl;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.ServletContext;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.context.ContextLoader;

import com.chevron.pms.dao.PartnerAddressMapper;
import com.chevron.point.business.PointAccountBizService;
import com.chevron.point.business.PointBizService;
import com.chevron.point.business.PointBusinessBizService;
import com.chevron.point.dic.BusinessStatus;
import com.chevron.point.dic.BusinessType;
import com.chevron.point.dic.PointAccountType;
import com.chevron.point.dto.PointType;
import com.chevron.point.model.WXTPointAccountVo;
import com.chevron.point.model.WXTPointAccountVoExample;
import com.chevron.point.model.WXTPointBusinessVo;
import com.chevron.point.model.WXTPointValueDetailVo;
import com.chevron.promote.business.ApproveHistoryService;
import com.chevron.promote.business.PromoteBizService;
import com.chevron.promote.dao.PromoteActivityGifMapper;
import com.chevron.promote.dao.PromoteActivityMapper;
import com.chevron.promote.dao.PromoteActivitySpecialGiftMapper;
import com.chevron.promote.dao.PromoteApplicationBatchMapper;
import com.chevron.promote.dao.PromotePromotionalDetailMapper;
import com.chevron.promote.dao.PromoteRegionalPointsLogMapper;
import com.chevron.promote.dao.PromoteSeminarActivityDetailMapper;
import com.chevron.promote.model.BasePromote;
import com.chevron.promote.model.PromoteActivity;
import com.chevron.promote.model.PromoteActivityGif;
import com.chevron.promote.model.PromoteActivitySpecialGift;
import com.chevron.promote.model.PromoteApplicationBatch;
import com.chevron.promote.model.PromoteOrder;
import com.chevron.promote.model.PromoteOrderDetail;
import com.chevron.promote.model.PromotePacksEnum;
import com.chevron.promote.model.PromotePromotionalDetail;
import com.chevron.promote.model.PromoteRegionalPointsLog;
import com.chevron.promote.model.PromoteStatusEnum;
import com.chevron.promote.model.ReqPromoteActivityApprove;
import com.chevron.promote.model.ResponseActivityPacksDetail;
import com.chevron.promote.model.ResponsePacksDetail;
import com.chevron.promote.model.response.ResponsePromoteAdvertisementDetail;
import com.chevron.promote.model.response.ResponsePromoteAgricultureDetail;
import com.chevron.promote.model.response.ResponsePromoteRoadShowDetail;
import com.chevron.promote.model.response.ResponsePromoteSeminarDetail;
import com.chevron.promote.model.response.ResponsePromoteShopSignsDetail;
import com.chevron.promote.model.response.ResponsePromoteTryOilDetail;
import com.chevron.promote.model.response.ResponsePromoteXDOpenDetail;
import com.chevron.promote.service.IPromoteApplicationService;
import com.chevron.promote.service.IPromoteApplicationWorkFlowService;
import com.common.config.MyPropertyConfigurer;
import com.common.constants.Constants;
import com.common.exception.WxPltException;
import com.common.util.CommonUtil;
import com.common.util.ContextUtil;
import com.common.util.CustomUUIDGenerator;
import com.common.util.EmptyChecker;
import com.sys.auth.model.WxTUser;
import com.sys.email.service.EmailSenderService;
import com.sys.organization.model.OrganizationVo;
import com.sys.organization.service.OrganizationService;
import com.sys.properties.service.SequenceTypes;
import com.sys.properties.service.WxTPropertiesService;
@Service
public class PromoteApplicationWorkFlowServiceImpl  extends BasePromote implements IPromoteApplicationWorkFlowService{
	private static Logger log = LoggerFactory.getLogger(PromoteDistributionWorkFlowServiceImpl.class);
	@Resource
	ApproveHistoryService approveHisService;
	@Resource
	PromoteApplicationBatchMapper promoteApplyBatchMapper;
	@Resource
	PromoteBizService promoteBizService;
	@Resource
	PromoteActivityMapper promoteActivityMapper;
	@Resource
	WxTPropertiesService propertiesServiceImpl;
	@Resource
	PartnerAddressMapper partnerAddressMapper;
	@Resource
	PromotePromotionalDetailMapper promotePromotionalDetailMapper ;
	@Resource
	PromoteActivityGifMapper promoteGifMapper;
	@Resource
	IPromoteApplicationService promoteApplicationService;
	/*@Resource
	PromoteRegionalDetailMapper pointMapper;*/
	@Resource
	PromoteSeminarActivityDetailMapper promoteSeminarActivityDetailMapper;
	@Resource
	PointBusinessBizService pointBusinessBizService;
	@Resource
	private PointBizService pointBizService;
	@Resource
	private PointAccountBizService pointAccountBizService;
	@Resource
	private OrganizationService organizationService;
	@Resource
	PromoteRegionalPointsLogMapper pointsLogMapper;
	@Resource 
	EmailSenderService emailSenderService;
	@Resource
	PromoteActivitySpecialGiftMapper specialGiftMapper;
	private static final String PACKSUNIT = "个";
	@Override
	public Map<String, Object> queryWorkFlowTasks(String loginName,
			String pKey, String batchId) throws Exception {
		// TODO 
		return null;
	}
	
	
	@Override
	public Map<String, Object> approvePromoteApplication(String applyBatchId) {
		// TODO V2
		return null;
	}
	
	@Override
	@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
	public Map<String, Object> approvePromoteApplication(ReqPromoteActivityApprove reqApproveInstance) {
		log.info("approvePromoteApplication reqApproveInstance:{}",reqApproveInstance.toString());
		Map<String,Object> resultMap = new HashMap<String,Object>();
		try
		{
			Long applyBatchId = reqApproveInstance.getApplyBatchId();
			String version = reqApproveInstance.getVersion();
			String status = reqApproveInstance.getStatus();
			String approveRemark = reqApproveInstance.getApproveRemark();
			String queryUserType = reqApproveInstance.getQueryUserType();
			
			
			//1.根据批次号，版本号，定义类型判断是否有审批过
			Map<String,Object> reqMap = new HashMap<String,Object>();
			reqMap.put("bachId", applyBatchId);
			reqMap.put("processKey", ApproveHistoryService.APPLY_PRO_KEY_PROMOTE_APPLICATION);
			reqMap.put("versionFlag", version);
			List<PromoteApplicationBatch> lst = promoteApplyBatchMapper.checkPromoteApplyHasApprovedByMap(reqMap);
			if(null!=lst && !lst.isEmpty())
			{
				throw new Exception("已经被审核，不能进行重复审核");
			}
			
			//2.在录入审批历史
			String stepDesc = "";
			if(PROMOTE_SUPERVISOR.equals(queryUserType))
			{
				stepDesc = ".大区经理审批";
			}else if(PROMOTE_CHANNELMANAGER.equals(queryUserType))
			{
				stepDesc = ".渠道经理审批";
			}else if(PROMOTE_MARKETING.equals(queryUserType))
			{
				stepDesc = ".Marketing审批";
			}
					
			approveHisService.insertApproveHis(ApproveHistoryService.APPLY_PRO_KEY_PROMOTE_APPLICATION, applyBatchId, status, approveRemark,version+stepDesc);
			PromoteApplicationBatch promoteApply = new PromoteApplicationBatch();
			promoteApply.setBatchid(applyBatchId);
			promoteApply.setVersionFlag(""+(Integer.parseInt(version)+1));
			
			//3.如果是不通过，需要更新批次状态为驳回
			if(ApproveHistoryService.APPLY_STATUS_UNAPPROVED.equals(status))
			{
				promoteApply.setApproveStatus(status);
				promoteApply.setCurrentStep(PROMOTE_SALES);//回到销售，让销售更改后重新提交
				//获取创建此活动的用户id
				PromoteApplicationBatch tmpPromoteApply = promoteApplyBatchMapper.selectByPrimaryKey(applyBatchId);
				promoteBizService.doSndMessageForApprove(PROMOTE_SALES,tmpPromoteApply.getCreatorId(),ApproveHistoryService.APPLY_PRO_KEY_PROMOTE_APPLICATION, PROMOTE_SALES_TIP+PROMOTE_ACTIVITY_UNAPPROVED);
				promoteBizService.doSndMailForApprove(PROMOTE_SALES,tmpPromoteApply.getCreatorId(),ApproveHistoryService.APPLY_PRO_KEY_PROMOTE_APPLICATION, PROMOTE_SALES_TIP+PROMOTE_ACTIVITY_UNAPPROVED);
			}else
			{
				//处理审核通过的情况
				doHandleApproved(reqApproveInstance,promoteApply);
				
			}
			
			
			//最后都需要更新版本号
			promoteApplyBatchMapper.updateByPrimaryKeySelective(promoteApply);
			resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
		}catch (Exception e) {
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();  
			e.printStackTrace();
			log.error("approvePromoteApplication"+e.getLocalizedMessage());
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, RESULT_ERROR_MSG+e.getMessage());
		}
		return resultMap;
	}
	
	
	private void doHandleApproved(ReqPromoteActivityApprove reqApproveInstance,PromoteApplicationBatch promoteApply)throws Exception {
		Double activityTotalAmount = reqApproveInstance.getActivityAmount()==null?0:reqApproveInstance.getActivityAmount();
		String queryUserType = reqApproveInstance.getQueryUserType();
		String status = reqApproveInstance.getStatus();
		// 判断是否存在下一级审批
		boolean isNextApprove = true;
		if(PROMOTE_MARKETING.equals(queryUserType))
		{
			isNextApprove = false;
		}
		if(!isNextApprove)
		{
			//如果不存在下一级审批
			promoteApply.setApproveStatus(status);
			doProcessApproved(reqApproveInstance);
			String activityUUId =  CustomUUIDGenerator.generateUUID();
			//如果不是地促积分，，
			if(!PROMOTE_ACTIVITY_DCJF.equals(reqApproveInstance.getActivityType()))
			{
				doSndMessageToDealer(promoteApply,activityUUId);
			}
			
			//更新活动的状态
			doUpdateActivityStatusByBatchId(promoteApply.getBatchid(),status,activityUUId);
			
		}else
		{
			//存在下一级审批
			//首先判断总金额 //小区经理
			if(queryUserType.equals(PROMOTE_SUPERVISOR))
			{
//				if(activityTotalAmount<promoteBizService.getActivityAmountLimit())//没有达到限定值，直接给marketing审批
//				{
					promoteBizService.doSndMessageForApprove(PROMOTE_MARKETING,null,ApproveHistoryService.APPLY_PRO_KEY_PROMOTE_APPLICATION, PROMOTE_MARKETING_TIP+PROMOTE_ACTIVITY_APPROVE);
					promoteBizService.doSndMailForApprove(PROMOTE_MARKETING,null,ApproveHistoryService.APPLY_PRO_KEY_PROMOTE_APPLICATION, PROMOTE_MARKETING_TIP+PROMOTE_ACTIVITY_APPROVE);
					promoteApply.setCurrentStep(PROMOTE_MARKETING);
//				}else
//				{
//					promoteBizService.doSndMessageForApprove(PROMOTE_CHANNELMANAGER,null,ApproveHistoryService.APPLY_PRO_KEY_PROMOTE_APPLICATION, PROMOTE_CHANNELMANAGER_TIP+PROMOTE_ACTIVITY_APPROVE);
//					promoteApply.setCurrentStep(PROMOTE_CHANNELMANAGER);//下一步的当前步
//				}
			}else if(queryUserType.equals(PROMOTE_CHANNELMANAGER))//大区经理
			{
				promoteBizService.doSndMessageForApprove(PROMOTE_MARKETING,null,ApproveHistoryService.APPLY_PRO_KEY_PROMOTE_APPLICATION, PROMOTE_MARKETING_TIP+PROMOTE_ACTIVITY_APPROVE);
				promoteBizService.doSndMailForApprove(PROMOTE_MARKETING,null,ApproveHistoryService.APPLY_PRO_KEY_PROMOTE_APPLICATION, PROMOTE_MARKETING_TIP+PROMOTE_ACTIVITY_APPROVE);
				promoteApply.setCurrentStep(PROMOTE_MARKETING);//下一步
			}
			//TODO 发送审核邮件....
			
		}
	}


	private void doUpdateActivityStatusByBatchId(Long batchid, String status,String activityUUId)throws Exception {
		Map<String,Object> reqMap = new HashMap<String,Object>();
		reqMap.put("status", PromoteStatusEnum.STATUS_UNFEEDBACK.getStatusCode());//待反馈
		reqMap.put("activityUUId", activityUUId);//uuid
		reqMap.put("applyBatchId", batchid);
		promoteActivityMapper.updateActivityByMap(reqMap);
		
	}


	private void doSndMessageToDealer(PromoteApplicationBatch promoteApply,String activityUUId) {
		//0 获取活动的经销商
		List<PromoteActivity> lst = promoteActivityMapper.getActivityLstByBatchId(promoteApply.getBatchid());
		if(null==lst || lst.isEmpty())
		{	log.info("doSndMessageToDealer lst is null");
			return ;
		}
		PromoteActivity promoteActivity = lst.get(0);
		/*//1 根据经销商获取  经销商下面的超级管理员信息
		List<WxTUser> lstUsers = promoteBizService.getChevronSuperManagerInfo(promoteActivity.getActivityOrganizersId());
		if(null==lstUsers || lstUsers.isEmpty())
		{
			log.info("doSndMessageToDealer lstUsers is null");
			return ;
		}*/
		//1 获取对应活动销售用户信息
		Long userId = promoteActivity.getCreatorId();
		WxTUser tuser = promoteBizService.getCurrentSalesInfo(userId);
		List<WxTUser> lstUsers = new ArrayList<WxTUser>();
		lstUsers.add(tuser);
		
		//2 发送邮件
		String activityTypeName = promoteActivity.getActivityMainTypeName();
		/*String subject = "地区加速计划活动审核通过通知";
		String msgContent = "您有新的"+activityTypeName+"活动已被审批,待活动完成后,请登录PP后台系统进行反馈";
		doSndEmails(lstUsers,subject,msgContent,promoteActivity.getActivityOrganizers());*/
		//3 发送短信
		String basePath = (String) Constants
				.getSystemPropertyByCodeType(Constants.APP_HOST) + "/";
		String mobileMessageContent = "您有新的"+activityTypeName+"活动已被审批,待活动完成后,请点击后面链接进行反馈:"+basePath+BasePromote.ACTIVITY_FEEDBACK_MOBILEPAGE+activityUUId;
		promoteBizService.doSndMobile(lstUsers,mobileMessageContent);
		
		
	}

	private void doSndEmails(List<WxTUser> lstUsers,String subject,String content,String parnterName) {
		log.info("doSndEmails lstUsers:{}",lstUsers.toString());
		List<String> emailLst = new ArrayList<String>();
		for(WxTUser tuser:lstUsers)
		{
			String emailaddr = tuser.getEmail();
			if(null!=emailaddr && !emailaddr.isEmpty())
			{
				emailLst.add(emailaddr);
			}
		}
		if(null==emailLst||emailLst.isEmpty())
		{
			log.info("doSndEmails emailLst:{}",emailLst.toString());
			return;
		}
		String[] acceptEmails = new String[emailLst.size()];
		int i=0;
		for(String email: emailLst)
		{
			acceptEmails[i]=email;
			i++;
		}
		
		//发送邮件
		Map<String,Object> emailInfoMap = new HashMap<String,Object>();
		emailInfoMap.put("acceptUserName", parnterName+"经销商");
		emailInfoMap.put("msgcontent", content);
		//发送邮件信息
		ServletContext context = ContextLoader.getCurrentWebApplicationContext().getServletContext();
		
		boolean isSuccess = emailSenderService.sendEmailForCommon(context,
				acceptEmails, null, subject,emailInfoMap, null, MyPropertyConfigurer.getVal("PROMOTE_DEALER_EMAIL"));
	    log.info("doSndEmails end isSuccess:{}",isSuccess);
		
	}


	private void doProcessApproved(ReqPromoteActivityApprove reqApproveInstance)throws Exception {
		String activityType = reqApproveInstance.getActivityType();
		Long activityId = reqApproveInstance.getActivityId();
		//如果是 新店开业，或者路演活动  需要生成订单
		//if(PROMOTE_ACTIVITY_DLXDKY.equals(activityType) || PROMOTE_ACTIVITY_DLLY.equals(activityType))
		if(!PROMOTE_ACTIVITY_DCJF.equals(activityType))
		{
			//根据activityId查询  活动
			List<PromoteActivity> lst = promoteActivityMapper.getActivityLstByBatchId(reqApproveInstance.getApplyBatchId());
			if(null!=lst && !lst.isEmpty())
			{
				PromoteActivity tmpPromoteActivity = lst.get(0);
				//根据经销商id获取发货的信息
				//delete by bo.liu 180704 start
				//List<PartnerAddress> lst1 = partnerAddressMapper.selAddressByPartnerId(tmpPromoteActivity.getActivityOrganizersId());
				//PartnerAddress partnerAddre = lst1.get(0);
				//delete by bo.liu 180704 end 
				PromoteOrder promoteOrder = new PromoteOrder();
				List<PromoteOrderDetail> lstOrderDetail = new ArrayList<PromoteOrderDetail>();
				String sequenceNo =  propertiesServiceImpl.getSequenceByType(SequenceTypes.PROMOTE_ORDER_NO,6,1);
				String orderNo = CommonUtil.generateOrderCode(20)+sequenceNo;
				promoteOrder.setOrderNo(orderNo);
				promoteOrder.setActivityId(reqApproveInstance.getActivityId());
				promoteOrder.setActivityType(tmpPromoteActivity.getActivityType());
				promoteOrder.setAddress(tmpPromoteActivity.getContactAddress());
				promoteOrder.setPartnerId(tmpPromoteActivity.getActivityOrganizersId());
				promoteOrder.setContactTel(tmpPromoteActivity.getContactTel());
				promoteOrder.setContactPerson(tmpPromoteActivity.getContactPerson());
				promoteOrder.setCreateTime(new Date());
				//promoteOrder.setStatus(status)
				if(PROMOTE_ACTIVITY_DLXDKY.equals(activityType) || PROMOTE_ACTIVITY_DLLY.equals(activityType))
				{
					List<PromotePromotionalDetail> lst3 =  promotePromotionalDetailMapper.selectByActivityId(activityId);
					for(PromotePromotionalDetail tmpPromotionalDetail:lst3)
					{
						PromoteOrderDetail promoteOrderDetail = new PromoteOrderDetail();
						promoteOrderDetail.setMaterialAmount(tmpPromotionalDetail.getProductTotal());
						promoteOrderDetail.setMaterialCode(tmpPromotionalDetail.getProductCode());
						promoteOrderDetail.setMaterialName(tmpPromotionalDetail.getProductName());
						promoteOrderDetail.setMaterialCount(tmpPromotionalDetail.getProductCount());
						promoteOrderDetail.setPromoteOrderNo(orderNo);
						if(PROMOTE_ACTIVITY_DLXDKY.equals(activityType))
						{
							promoteOrderDetail.setRemark("卡车司机促销品");
						}else
						{
							promoteOrderDetail.setRemark("消费者活动礼品");
						}
						lstOrderDetail.add(promoteOrderDetail);
					}
				}
				if(PROMOTE_ACTIVITY_DLXDKY.equals(activityType))
				{
					//攻店的小礼品
					List<PromoteActivityGif> lstGif = promoteGifMapper.getGifLstByActivityId(activityId);
					for(PromoteActivityGif tmpGif:lstGif)
					{
						PromoteOrderDetail promoteOrderDetail = new PromoteOrderDetail();
						promoteOrderDetail.setMaterialAmount(tmpGif.getGifAmount());
						promoteOrderDetail.setMaterialCode(tmpGif.getGifCode());
						promoteOrderDetail.setMaterialName(tmpGif.getGifCode());
						promoteOrderDetail.setMaterialCount(tmpGif.getGifCount());
						promoteOrderDetail.setMaterialId(tmpGif.getGifId());
						promoteOrderDetail.setPromoteOrderNo(orderNo);
						promoteOrderDetail.setRemark("攻店礼品（自选门店设备）");
						lstOrderDetail.add(promoteOrderDetail);
					}
				}
				
				//工作服：
				doHandleWorkClothes(lstOrderDetail,activityId,orderNo);
				
				promoteOrder.setLstDetails(lstOrderDetail);
				//公共的礼包
				ResponseActivityPacksDetail responsePacksDetail =  promoteBizService.getPacksDetailInfo("true",tmpPromoteActivity.getSourcePlanId());
				doHandleCommonPacks(promoteOrder,responsePacksDetail,activityType,activityId);
				if(promoteOrder.getLstDetails().size() > 0) {
					promoteApplicationService.generatePromoteOrder(promoteOrder);
				}
			}
			
			
		}
		
//		if(PROMOTE_ACTIVITY_YTH.equals(activityType)) {
		
			//获取对应的积分数量
//			List<ResponsePromoteSeminarDetail> lst = promoteSeminarActivityDetailMapper.getSeminarActivityDetail(activityId);
//			ResponsePromoteSeminarDetail seminarDetail = lst.get(0);
//			if(null==seminarDetail)
//			{
//				return;
//			}
			//创建业务类型，用于描述进货积分的类型
//			WXTPointBusinessVo careteBusinessVo = doCreateBusiness(""+activityId,seminarDetail.getApplyPoints());
			//创建待插入的积分list
//			List<WXTPointValueDetailVo> detailList= doCreatePointValueDetails(careteBusinessVo,lst);
//			pointBizService.importCaltexPointCustom(careteBusinessVo, detailList);
			
			//记录到地促积分流向日志表中
//			PromoteRegionalPointsLog regionalPointsLog = new PromoteRegionalPointsLog();
//			regionalPointsLog.setActivityId(activityId);
//			regionalPointsLog.setBusinessId(careteBusinessVo.getId());
//			regionalPointsLog.setCreateTime(new Date());
//			regionalPointsLog.setPartnerId(seminarDetail.getActivityOrganizersId());
//			regionalPointsLog.setPoints(seminarDetail.getApplyPoints());
//			regionalPointsLog.setRemark("审批者:"+ContextUtil.getCurUser().getChName()+BusinessType.CALTEX_POINT_FROM_PROMOTE.name()+",促销积分业务id:"+careteBusinessVo.getId());
//			pointsLogMapper.insertSelective(regionalPointsLog);
			
//		}
		
		
		/*//如果是地促积分，需要有积分流向
		if(PROMOTE_ACTIVITY_DCJF.equals(activityType))
		{
		
			//获取对应的积分数量
			List<ResponsePromoteRegionalDetail> lst = pointMapper.getRegionalActivityDetail(activityId);
			ResponsePromoteRegionalDetail regionalDetail = lst.get(0);
			if(null==regionalDetail)
			{
				return;
			}
			//创建业务类型，用于描述进货积分的类型
			WXTPointBusinessVo careteBusinessVo = doCreateBusiness(""+activityId,regionalDetail.getApplyPromotionSupportPoints());
			//创建待插入的积分list
			List<WXTPointValueDetailVo> detailList= doCreatePointValueDetails(careteBusinessVo,lst);
			pointBizService.importCaltexPointCustom(careteBusinessVo, detailList);
			
			//记录到地促积分流向日志表中
			PromoteRegionalPointsLog regionalPointsLog = new PromoteRegionalPointsLog();
			regionalPointsLog.setActivityId(activityId);
			regionalPointsLog.setBusinessId(careteBusinessVo.getId());
			regionalPointsLog.setCreateTime(new Date());
			regionalPointsLog.setPartnerId(regionalDetail.getActivityOrganizersId());
			regionalPointsLog.setPoints(regionalDetail.getApplyPromotionSupportPoints());
			regionalPointsLog.setRemark("审批者:"+ContextUtil.getCurUser().getChName()+BusinessType.CALTEX_POINT_FROM_PROMOTE.name()+",进货积分业务id:"+careteBusinessVo.getId());
			pointsLogMapper.insertSelective(regionalPointsLog);
			
		}*/
	}


	private List<PromoteOrderDetail> doHandleWorkClothes(List<PromoteOrderDetail> lstOrderDetail,
			Long activityId,String orderNo)throws Exception {
		//获取工作服
		List<PromoteActivitySpecialGift> lst1 = specialGiftMapper.getSpecialGifLstByActivityId(activityId);
		if(null==lst1 || lst1.isEmpty())
		{
			return lstOrderDetail;
		}
		String lastMaterialCode = "";
		String currentMaterialCode = "";
		Double totalAmount = 0.0;
		int totalCount = 0;
		String remark = "";
		PromoteOrderDetail promoteOrderDetail = null;
		for(PromoteActivitySpecialGift tmpSpGif:lst1)
		{
			currentMaterialCode = tmpSpGif.getMaterialCode();
			if(!lastMaterialCode.equals(currentMaterialCode))
			{
				promoteOrderDetail = new PromoteOrderDetail();
				promoteOrderDetail.setMaterialCode(currentMaterialCode);
				promoteOrderDetail.setMaterialName(tmpSpGif.getMaterialName());
				promoteOrderDetail.setMaterialId(tmpSpGif.getMaterialId());
				promoteOrderDetail.setPromoteOrderNo(orderNo);
				lstOrderDetail.add(promoteOrderDetail);
			}
			totalAmount+=tmpSpGif.getMaterialTotalAmount();
			totalCount+=tmpSpGif.getMaterialCount();
			remark+=tmpSpGif.getSpecialGifRemark()+":"+tmpSpGif.getMaterialCount()+"件,";
			promoteOrderDetail.setMaterialAmount(totalAmount);
			promoteOrderDetail.setMaterialCount(totalCount);
			promoteOrderDetail.setRemark(remark);
			lastMaterialCode = currentMaterialCode;
		}
		return lstOrderDetail;
	}


	private PromoteOrder doHandleCommonPacks(PromoteOrder promoteOrder,
			ResponseActivityPacksDetail responsePacksDetail,String activityType,Long activityId)throws Exception {
		    List<PromoteOrderDetail> lstOrderDetail  =  new ArrayList<PromoteOrderDetail>();
		    lstOrderDetail.addAll(promoteOrder.getLstDetails());
		    //TODO 后续需要页面展现add by bo.liu 180813
		    String remark = "";
			if(PROMOTE_ACTIVITY_DLXDKY.equals(activityType))
			{
				ResponsePromoteXDOpenDetail responseOpenDetail = promoteBizService.getPromoteXDOpenDetail(activityId);
				if(null==responseOpenDetail)
				{
					return promoteOrder;
				}
				
				int rdPacksCount = responseOpenDetail.getPacksNumber();
				int gdPacksCount = responseOpenDetail.getGdPacksNumber();
				remark = PromotePacksEnum.PACKS_RDLB.getPacksDesc()+rdPacksCount+PACKSUNIT+","+PromotePacksEnum.PACKS_GDLB.getPacksDesc()+gdPacksCount+PACKSUNIT;
				List<ResponsePacksDetail> gdlst1 =  responsePacksDetail.getGdPacksLst();
				List<ResponsePacksDetail> rdlst2 = responsePacksDetail.getRdPacksLst();
				if(null!=gdlst1 && !gdlst1.isEmpty())
				{
					for(ResponsePacksDetail gdtmp:gdlst1)
					{
						PromoteOrderDetail promoteOrderDetail = new PromoteOrderDetail();
						promoteOrderDetail.setMaterialAmount(gdtmp.getMaterialsTotalAmount()*gdPacksCount);
						promoteOrderDetail.setMaterialCode(gdtmp.getMaterialsName());
						promoteOrderDetail.setMaterialName(gdtmp.getMaterialsName());
						promoteOrderDetail.setMaterialCount(gdtmp.getMaterialsCount()*gdPacksCount);
						promoteOrderDetail.setMaterialId(gdtmp.getMaterialsId());
						promoteOrderDetail.setPromoteOrderNo(promoteOrder.getOrderNo());
						if(!EmptyChecker.isEmpty(gdtmp.getMaterialsSpecification())) {
							if(gdtmp.getMaterialsId() == 1007) {
								promoteOrderDetail.setRemark("(" + gdtmp.getMaterialsSpecification() + ")*" + gdPacksCount);
							} else {
								promoteOrderDetail.setRemark(gdtmp.getMaterialsSpecification());
							}
						}
						lstOrderDetail.add(promoteOrderDetail);
					}
				}
				if(null!=rdlst2 && !rdlst2.isEmpty())
				{
					for(ResponsePacksDetail rdtmp:rdlst2)
					{
						PromoteOrderDetail promoteOrderDetail = new PromoteOrderDetail();
						promoteOrderDetail.setMaterialAmount(rdtmp.getMaterialsTotalAmount()*rdPacksCount);
						promoteOrderDetail.setMaterialCode(rdtmp.getMaterialsName());
						promoteOrderDetail.setMaterialName(rdtmp.getMaterialsName());
						promoteOrderDetail.setMaterialCount(rdtmp.getMaterialsCount()*rdPacksCount);
						promoteOrderDetail.setMaterialId(rdtmp.getMaterialsId());
						promoteOrderDetail.setPromoteOrderNo(promoteOrder.getOrderNo());
						if(!EmptyChecker.isEmpty(rdtmp.getMaterialsSpecification())) {
							if(rdtmp.getMaterialsId() == 1002) {
								promoteOrderDetail.setRemark("(" + rdtmp.getMaterialsSpecification() + ")*" + rdPacksCount);
							} else {
								promoteOrderDetail.setRemark(rdtmp.getMaterialsSpecification());
							}
						}
						lstOrderDetail.add(promoteOrderDetail);
					}
				}
			}else if(PROMOTE_ACTIVITY_DLLY.equals(activityType))
			{
				ResponsePromoteRoadShowDetail responseRoadShowDetail = promoteBizService.getRoadShowDetail(activityId);
				if(null==responseRoadShowDetail)
				{
					return promoteOrder;
				}
				int roadShowGifPacksCount = responseRoadShowDetail.getRoadShowGifPackageCount();
				int roadShowConsumerPacksCount = responseRoadShowDetail.getRoadShowConsumerPacksCount();
				remark = PromotePacksEnum.PACKS_LYWLB.getPacksDesc()+roadShowGifPacksCount+PACKSUNIT+","+PromotePacksEnum.PACKS_XFZHDBCB.getPacksDesc()+roadShowConsumerPacksCount+PACKSUNIT;
				List<ResponsePacksDetail> giflst1 =  responsePacksDetail.getRoadshowPacksLst();
				List<ResponsePacksDetail> consumerlst2 = responsePacksDetail.getConsumerInteractionPacksLst();
				if(null!=giflst1 && !giflst1.isEmpty())
				{
					for(ResponsePacksDetail giftmp:giflst1)
					{
						PromoteOrderDetail promoteOrderDetail = new PromoteOrderDetail();
						promoteOrderDetail.setMaterialAmount(giftmp.getMaterialsTotalAmount()*roadShowGifPacksCount);
						promoteOrderDetail.setMaterialCode(giftmp.getMaterialsName());
						promoteOrderDetail.setMaterialName(giftmp.getMaterialsName());
						promoteOrderDetail.setMaterialCount(giftmp.getMaterialsCount()*roadShowGifPacksCount);
						promoteOrderDetail.setMaterialId(giftmp.getMaterialsId());
						promoteOrderDetail.setPromoteOrderNo(promoteOrder.getOrderNo());
						lstOrderDetail.add(promoteOrderDetail);
					}
				}
				if(null!=consumerlst2 && !consumerlst2.isEmpty())
				{
					for(ResponsePacksDetail consumertmp:consumerlst2)
					{
						PromoteOrderDetail promoteOrderDetail = new PromoteOrderDetail();
						promoteOrderDetail.setMaterialAmount(consumertmp.getMaterialsTotalAmount()*roadShowConsumerPacksCount);
						promoteOrderDetail.setMaterialCode(consumertmp.getMaterialsName());
						promoteOrderDetail.setMaterialName(consumertmp.getMaterialsName());
						promoteOrderDetail.setMaterialCount(consumertmp.getMaterialsCount()*roadShowConsumerPacksCount);
						promoteOrderDetail.setMaterialId(consumertmp.getMaterialsId());
						promoteOrderDetail.setPromoteOrderNo(promoteOrder.getOrderNo());
						lstOrderDetail.add(promoteOrderDetail);
					}
				}
				
			} else if(PROMOTE_ACTIVITY_YTH.equals(activityType)) {
				ResponsePromoteSeminarDetail seminarDetail = promoteBizService.getSeminarDetail(activityId);
				if(null==seminarDetail)
				{
					return promoteOrder;
				}
				int packsCount = seminarDetail.getApplyPacksCount();
				List<ResponsePacksDetail> seminarlst1 =  responsePacksDetail.getYthPacksLst();
				if(null!=seminarlst1 && !seminarlst1.isEmpty() && packsCount > 0)
				{
					for(ResponsePacksDetail seminartmp:seminarlst1)
					{
						PromoteOrderDetail promoteOrderDetail = new PromoteOrderDetail();
						promoteOrderDetail.setMaterialAmount(seminartmp.getMaterialsTotalAmount()*packsCount);
						promoteOrderDetail.setMaterialCode(seminartmp.getMaterialsName());
						promoteOrderDetail.setMaterialName(seminartmp.getMaterialsName());
						promoteOrderDetail.setMaterialCount(seminartmp.getMaterialsCount()*packsCount);
						promoteOrderDetail.setMaterialId(seminartmp.getMaterialsId());
						promoteOrderDetail.setPromoteOrderNo(promoteOrder.getOrderNo());
						if(!EmptyChecker.isEmpty(seminartmp.getMaterialsSpecification())) {
							if(seminartmp.getMaterialsId() == 1011) {
								promoteOrderDetail.setRemark("(" + seminartmp.getMaterialsSpecification() + ")*" + packsCount);
							} else {
								promoteOrderDetail.setRemark(seminartmp.getMaterialsSpecification());
							}
						}
						lstOrderDetail.add(promoteOrderDetail);
					}
				}
				
				int highPacksCount = seminarDetail.getApplyHighPacks();
				remark = PromotePacksEnum.PACKS_PTYTHLB.getPacksDesc()+packsCount+PACKSUNIT + "," 
				+ PromotePacksEnum.PACKS_YZYTHLB.getPacksDesc() + highPacksCount + PACKSUNIT;
				List<ResponsePacksDetail> yzythlbLst =  responsePacksDetail.getYzythPacksLst();
				if(!EmptyChecker.isEmpty(yzythlbLst) && highPacksCount > 0) {
					for(ResponsePacksDetail yzythlb : yzythlbLst) {
						PromoteOrderDetail yzythOrderDetail = new PromoteOrderDetail();
						yzythOrderDetail.setMaterialAmount(yzythlb.getMaterialsTotalAmount()*highPacksCount);
						yzythOrderDetail.setMaterialCode(yzythlb.getMaterialsName());
						yzythOrderDetail.setMaterialName(yzythlb.getMaterialsName());
						yzythOrderDetail.setMaterialCount(yzythlb.getMaterialsCount()*highPacksCount);
						yzythOrderDetail.setMaterialId(yzythlb.getMaterialsId());
						yzythOrderDetail.setPromoteOrderNo(promoteOrder.getOrderNo());
						if(!EmptyChecker.isEmpty(yzythlb.getMaterialsSpecification())) {
							if(yzythlb.getMaterialsId() == 1041) {
								yzythOrderDetail.setRemark("(" + yzythlb.getMaterialsSpecification() + ")*" + highPacksCount);
							} else {
								yzythOrderDetail.setRemark(yzythlb.getMaterialsSpecification());
							}
						}
						lstOrderDetail.add(yzythOrderDetail);
					}
				}
			} else if(PROMOTE_ACTIVITY_DZHD.equals(activityType)) {
				ResponsePromoteShopSignsDetail shopSignsDetail = promoteBizService.getShopSignsDetail(activityId);
				if(EmptyChecker.isEmpty(shopSignsDetail)) {
					return promoteOrder;
				}
				
				int packsCount = shopSignsDetail.getPacksNumber();
				remark = PromotePacksEnum.PACKS_DZLB.getPacksDesc() + packsCount + PACKSUNIT;
				
				List<ResponsePacksDetail> dzlbList =  responsePacksDetail.getDzPacksLst();
				if(!EmptyChecker.isEmpty(dzlbList) && packsCount > 0) {
					for(ResponsePacksDetail dzlb : dzlbList)
					{
						PromoteOrderDetail dzOrderDetail = new PromoteOrderDetail();
						dzOrderDetail.setMaterialAmount(dzlb.getMaterialsTotalAmount() * packsCount);
						dzOrderDetail.setMaterialCode(dzlb.getMaterialsName());
						dzOrderDetail.setMaterialName(dzlb.getMaterialsName());
						dzOrderDetail.setMaterialCount(dzlb.getMaterialsCount() * packsCount);
						dzOrderDetail.setMaterialId(dzlb.getMaterialsId());
						dzOrderDetail.setPromoteOrderNo(promoteOrder.getOrderNo());
						lstOrderDetail.add(dzOrderDetail);
					}
				}
			} else if(PROMOTE_ACTIVITY_HWGG.equals(activityType)) {
				ResponsePromoteAdvertisementDetail advertisement = promoteBizService.getAdvertisementDetail(activityId);
				if(EmptyChecker.isEmpty(advertisement)) {
					return promoteOrder;
				}
				
				int packsCount = advertisement.getPacksNumber();
				remark = PromotePacksEnum.PACKS_HWGGLB.getPacksDesc() + packsCount + PACKSUNIT;
				
				List<ResponsePacksDetail> hwgglbList =  responsePacksDetail.getHwggPacksLst();
				if(!EmptyChecker.isEmpty(hwgglbList) && packsCount > 0) {
					for(ResponsePacksDetail hwgg : hwgglbList)
					{
						PromoteOrderDetail hwggOrderDetail = new PromoteOrderDetail();
						hwggOrderDetail.setMaterialAmount(hwgg.getMaterialsTotalAmount() * packsCount);
						hwggOrderDetail.setMaterialCode(hwgg.getMaterialsName());
						hwggOrderDetail.setMaterialName(hwgg.getMaterialsName());
						hwggOrderDetail.setMaterialCount(hwgg.getMaterialsCount() * packsCount);
						hwggOrderDetail.setMaterialId(hwgg.getMaterialsId());
						hwggOrderDetail.setPromoteOrderNo(promoteOrder.getOrderNo());
						lstOrderDetail.add(hwggOrderDetail);
					}
				}
			} else if(PROMOTE_ACTIVITY_NKKSGCJX.equals(activityType)) {
				ResponsePromoteAgricultureDetail agricultureDetail = promoteBizService.getAgricultureDetail(activityId);
				if(EmptyChecker.isEmpty(agricultureDetail)) {
					return promoteOrder;
				}
				
				int packsCount = agricultureDetail.getPacksNumber();
				remark = PromotePacksEnum.PACKS_NKLB.getPacksDesc() + packsCount + PACKSUNIT;
				
				List<ResponsePacksDetail> nklbList =  responsePacksDetail.getNkPacksLst();
				if(!EmptyChecker.isEmpty(nklbList) && packsCount > 0) {
					for(ResponsePacksDetail nklb : nklbList)
					{
						PromoteOrderDetail nkOrderDetail = new PromoteOrderDetail();
						nkOrderDetail.setMaterialAmount(nklb.getMaterialsTotalAmount() * packsCount);
						nkOrderDetail.setMaterialCode(nklb.getMaterialsName());
						nkOrderDetail.setMaterialName(nklb.getMaterialsName());
						nkOrderDetail.setMaterialCount(nklb.getMaterialsCount() * packsCount);
						nkOrderDetail.setMaterialId(nklb.getMaterialsId());
						nkOrderDetail.setPromoteOrderNo(promoteOrder.getOrderNo());
						if(!EmptyChecker.isEmpty(nklb.getMaterialsSpecification())) {
							if(nklb.getMaterialsId() == 1049) {
								nkOrderDetail.setRemark("(" + nklb.getMaterialsSpecification() + ")*" + packsCount);
							} else {
								nkOrderDetail.setRemark(nklb.getMaterialsSpecification());
							}
						}
						lstOrderDetail.add(nkOrderDetail);
					}
				}
			} else if(PROMOTE_ACTIVITY_SYYP.equals(activityType)) {
				ResponsePromoteTryOilDetail tryOilDetail = promoteBizService.getTryOilDetail(activityId);
				if(EmptyChecker.isEmpty(tryOilDetail)) {
					return promoteOrder;
				}
				
				int packsCount = tryOilDetail.getPacksNumber();
				remark = PromotePacksEnum.PACKS_SYLB.getPacksDesc() + packsCount + PACKSUNIT;
				
				List<ResponsePacksDetail> sylbList =  responsePacksDetail.getSyPacksLst();
				if(!EmptyChecker.isEmpty(sylbList) && packsCount > 0) {
					for(ResponsePacksDetail sylb : sylbList)
					{
						PromoteOrderDetail syOrderDetail = new PromoteOrderDetail();
						syOrderDetail.setMaterialAmount(sylb.getMaterialsTotalAmount() * packsCount);
						syOrderDetail.setMaterialCode(sylb.getMaterialsName());
						syOrderDetail.setMaterialName(sylb.getMaterialsName());
						syOrderDetail.setMaterialCount(sylb.getMaterialsCount() * packsCount);
						syOrderDetail.setMaterialId(sylb.getMaterialsId());
						syOrderDetail.setPromoteOrderNo(promoteOrder.getOrderNo());
						lstOrderDetail.add(syOrderDetail);
					}
				}
			}
			promoteOrder.setLstDetails(lstOrderDetail);
			promoteOrder.setRemark(remark);//add by bo.liu 180813
			return promoteOrder;
		
	}


	public List<WXTPointValueDetailVo> doCreatePointValueDetails(WXTPointBusinessVo businessVo,List<ResponsePromoteSeminarDetail> lst
			)throws Exception {
		List<WXTPointValueDetailVo> detailList=new ArrayList<WXTPointValueDetailVo>();
		for(ResponsePromoteSeminarDetail su:lst) {
			WXTPointAccountVoExample acExample = new WXTPointAccountVoExample();
			acExample.createCriteria().andPointAccountOwnerIdEqualTo(su.getActivityOrganizersId())
			.andPointAccountTypeEqualTo(PointAccountType.SP.name());
			List<WXTPointAccountVo> accountVos = pointAccountBizService.queryByExample(acExample);
			WXTPointAccountVo pointAccount=null;
			if(accountVos.isEmpty()) {
				OrganizationVo orgVo = organizationService.selectByPrimaryKey(su.getActivityOrganizersId());
				if(orgVo==null) {
					throw new WxPltException("partner不存在，请检查partnerId");
				}
				//插入账户
				pointAccount = new WXTPointAccountVo();
				pointAccount.setIsEnabled(true);
				pointAccount.setPointAccountOwnerId(su.getActivityOrganizersId());
				pointAccount.setPointAccountOwnerCode(orgVo.getOrganizationCode());
				pointAccount.setPointAccountOwnerName(orgVo.getOrganizationName());
				pointAccount.setPointAccountType(PointAccountType.SP.name());
				pointAccount.setCreatedBy(ContextUtil.getCurUserId());
				pointAccountBizService.insert(pointAccount);
			} else {
				pointAccount=accountVos.get(0);
			}
			SimpleDateFormat sdf1=new SimpleDateFormat("MM");
			sdf1.format(new Date());
			//加入待插入的积分list
			WXTPointValueDetailVo detailVo = new WXTPointValueDetailVo();
			detailVo.setPointValue(Double.valueOf(""+su.getApplyPoints()));
			detailVo.setPointType(PointType.PROMOTION_POINT.name());//mod by bo.liu 180904  地促积分流向到进货积分中去
			detailVo.setSubType(BusinessType.CALTEX_POINT_FROM_PROMOTE.name()); //yq增加地促积分的subType的设置
			detailVo.setPointPayed(0D);
			detailVo.setComments("地区计划加速积分");
			detailVo.setBusinessId(businessVo.getId());
			detailVo.setPointAccountId(pointAccount.getId());
			detailVo.setTransTime(new Date());
			detailList.add(detailVo);
		};
		return detailList;
	}


	public WXTPointBusinessVo doCreateBusiness(String applyTitle,int amount)throws Exception {
		WXTPointBusinessVo businessVo = new WXTPointBusinessVo();
		businessVo.setBusinessTypeCode(BusinessType.CALTEX_POINT_FROM_PROMOTE.name());
		String tradeNo = WXTPointValueDetailVo.TRADE_NO();
		businessVo.setRelatedCode(tradeNo);
		// 业务描述
		StringBuilder sb = new StringBuilder();
		sb.append("【").append(ContextUtil.getCurUser().getChName()).append("】在");
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
		sb.append(sdf.format(new Date())).append("确认了地促加速计划[地促积分活动id]:").append(applyTitle).append(",积分总共").append(amount)
				.append("分");

		businessVo.setBusinessDesc(sb.toString());
		businessVo.setBusinessStatus(BusinessStatus.READY.name());
		businessVo.setLastUpdatedBy(ContextUtil.getCurUserId());
		businessVo.setLastUpdateTime(new Date());
		
		//插入业务流水
		pointBusinessBizService.insert(businessVo);
		return businessVo;
	}


	@Override
	public Map<String, Object> queryPromoteApplicationByBatchId(Long batchId) {
		// TODO 
		return null;
	}
	
	@Override
	public Map<String, Object> queryPromoteActivityApprovalHistory(Long batchId) {
		log.info("queryPromoteActivityApprovalHistory batchId:{}",batchId);
		Map<String,Object> returnMap = approveHisService.getApproveHisByBatchId(ApproveHistoryService.APPLY_PRO_KEY_PROMOTE_APPLICATION, batchId);
		return returnMap;
	}
	
	

}
