package com.sys.auth.controller;

import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.common.constants.Constants;
import com.common.exception.WxPltException;
import com.common.util.ContextUtil;
import com.common.util.MessageResourceUtil;
import com.common.util.ResponseMap;
import com.common.util.ResponseStatus;
import com.common.util.SMSUtil;
import com.common.util.SpringUtils;
import com.sys.auth.business.UserBizService;
import com.sys.auth.dao.WxTUserMapper;
import com.sys.auth.model.WxTUser;
import com.sys.auth.service.UserServiceI;
import com.sys.log.util.LogUtils;

@Controller
public class FindPswController {
	
	@Resource
	private WxTUserMapper userMapper;
	
	@Resource
	private UserServiceI userService;
	
	@Autowired
	private UserBizService userBizService;

	@Autowired
	RedisTemplate<Serializable, Serializable> redisTemplate;
	
	private static long CAPTCHA_TIMEOUT = 1000*60*10; //十分钟

	@ResponseBody
	@RequestMapping("/setNewPassword.do")
	public Map<String,Object> setNewPassword(
			@RequestParam("username")String username,
			@RequestParam("newPassword")String newPassword,
			@RequestParam("captcha")String captcha,
			HttpSession session){
		ResponseMap resultMap = new ResponseMap();
		try {
		
			Map<String,Object> savedCaptcha = (Map<String,Object>)session.getAttribute("phoneCaptcha");
			if ((savedCaptcha != null) && (savedCaptcha.get("username").equals(username))
					&& (savedCaptcha.get("captcha").equals(captcha))
					&& ((new Date()).getTime() - ((Date) savedCaptcha.get("startTime")).getTime() < CAPTCHA_TIMEOUT)) {
				try {
					// 验证码验证通过，修改新密码
					WxTUser user = userService.findUserByLoginName(username);
						userBizService.updateUserPassword(user.getUserId(), newPassword, resultMap);
				} catch (WxPltException e) {
					resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
					resultMap.put(Constants.RESULT_ERROR_MSG_KEY, e.getMessage());
				} 
			} else {
				resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
				resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "验证码不匹配，请重新发送短信验证码");
				return resultMap;
			}
			
			//清除session中的验证码
			session.removeAttribute("phoneCaptcha");
	//		resultMap.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (Exception e) {
			resultMap.setReponseStatus(ResponseStatus.ERROR_EXCEPTION);
			resultMap.handleException(e, this, ContextUtil.getCurUserId(), 
					"com.sys.auth.controller.FindPswController.setNewPassword", username);
		}
		return resultMap;
	}

	@ResponseBody
	@RequestMapping("/sendPhoneCaptcha.do")
	public Map<String,Object> sendPhoneCaptcha(
			@RequestParam("username")String username,
			@RequestParam("validatecode")String validatecode,
			HttpServletRequest request, HttpServletResponse response){
		Map<String,Object> resultMap = new HashMap<String,Object>();
		//验证验证码
		RandomValidateCode randomValidateCode = SpringUtils.getBean(RandomValidateCode.class);
		try {
			if(1 != (Integer)randomValidateCode.checkRandCode(validatecode, request, response)){
				resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
				resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "验证码错误，请重新输入");
				return resultMap;
			}
		} catch (Exception e1) {
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			LogUtils.addErrorLog(1l, "com.sys.auth.controller.FindPswController.sendPhoneCaptcha", "验证码认证失败。" + e1.getMessage(), username + "," + validatecode);
			return resultMap;
		}
		HttpSession session = request.getSession();
		WxTUser user = userMapper.findUserByLoginName(username);
		if(user==null) {
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "请检查用户名是否正确");
			return resultMap;
		}
		if(user.isSystemIntegrationUser()){
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "系统集成账号不可重置密码");
			return resultMap;
		}
		if((user.getMobileTel()==null)||(user.getMobileTel().equals(""))){
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "该用户没有绑定手机号，请联系客服");
			return resultMap;
		}
		//发送验证码到手机
		//发送验证码到手机后删除redis中的图片验证码
		redisTemplate.delete(session.getId());
		Random random = new Random();
		int capInt;
		do {
			capInt=Math.abs(random.nextInt())%9999;
		}while(capInt<1000);
		String captcha=String.valueOf(capInt);
		System.out.println("验证码："+captcha);
		try {
			SMSUtil.sendSms(user.getMobileTel(), "您的验证码是："+captcha+"，十分钟内有效", SMSUtil.APP_ID_PP);
		} catch (WxPltException e) {
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "短信发送失败，手机号码不存在或无效，请联系客服");
			LogUtils.addErrorLog(1l, "com.sys.auth.controller.FindPswController.sendPhoneCaptcha", "发送手机验证码失败。" + e.getMessage(), user.getMobileTel());
			return resultMap;
		}catch(Exception e) {
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, "短信发送失败，手机号码不存在或无效，请联系客服");
			e.printStackTrace();
			LogUtils.addErrorLog(1l, "com.sys.auth.controller.FindPswController.sendPhoneCaptcha", "发送手机验证码失败。" + e.getMessage(), user.getMobileTel());
			return resultMap;
		}
		//存入session
		Map<String,Object> username_captcha=new HashMap<String,Object>();
		username_captcha.put("username", username);
		username_captcha.put("captcha", captcha);
		username_captcha.put("startTime",new Date());
		session.setAttribute("phoneCaptcha", username_captcha);
		resultMap.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		resultMap.put(Constants.RESULT_CODE_MSG_KEY, "已通过短信发送验证码到手机");
		StringBuilder sb = new StringBuilder(user.getMobileTel());
		sb.replace(3, 7, "****");
		resultMap.put("phone", sb.toString());
		return resultMap;
	}
}
