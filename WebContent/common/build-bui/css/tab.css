.clearfix {
  *zoom: 1;
}
.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}
.clearfix:after {
  clear: both;
}
.hide-text {
  overflow: hidden;
  text-indent: 100%;
  white-space: nowrap;
}
.input-block-level {
  display: block;
  width: 100%;
  min-height: 28px;
  /* Make inputs at least the height of their button counterpart */

  /* Makes inputs behave like true block-level elements */

  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -ms-box-sizing: border-box;
  box-sizing: border-box;
}
/* inline block */
.bui-inline-block {
  display: inline-block;
  *display: inline;
  *zoom: 1;
}
.button-tabs .bui-tab-item-selected {
  color: #ffffff;
  font-weight: bold;
  background-color: #6c8ffc;
  background-image: -moz-linear-gradient(top, #6c9dfc, #6c79fc);
  background-image: -ms-linear-gradient(top, #6c9dfc, #6c79fc);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#6c9dfc), to(#6c79fc));
  background-image: -webkit-linear-gradient(top, #6c9dfc, #6c79fc);
  background-image: -o-linear-gradient(top, #6c9dfc, #6c79fc);
  background-image: linear-gradient(top, #6c9dfc, #6c79fc);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#6c9dfc', endColorstr='#6c79fc', GradientType=0);
  border-color: #6c79fc #6c79fc #2135fa;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
  border: 1px solid #5984de;
}
.button-tabs .bui-tab-item-selected:hover,
.button-tabs .bui-tab-item-selected:active,
.button-tabs .bui-tab-item-selected.active,
.button-tabs .bui-tab-item-selected.disabled {
  background-color: #6c79fc;
}
.button-tabs .bui-tab-item-selected[disabled] {
  background-color: #6c79fc;
}
.button-tabs .bui-tab-item-selected:active,
.button-tabs .bui-tab-item-selected.active {
  background-color: #3a4bfb \9;
}
.nav-tabs .bui-tab-item {
  border: 1px solid #c3c3d6;
  border-bottom-color: transparent;
  margin-bottom: -1px;
  margin-right: 3px;
}
.nav-tabs .bui-tab-item-text,
.nav-tabs a {
  padding: 5px 15px;
  background-color: #eeeeee;
  position: relative;
  display: inline-block;
  *display: inline;
  /* IE7 inline-block hack */

  *zoom: 1;
}
.nav-tabs .bui-tab-item-selected {
  background-color: #ffffff;
  -webkit-border-radius: 2px 2px 0 0;
  -moz-border-radius: 2px 2px 0 0;
  border-radius: 2px 2px 0 0;
}
.nav-tabs .bui-tab-item-selected .bui-tab-item-text,
.nav-tabs .bui-tab-item-selected a {
  background-color: #ffffff;
  position: relative;
  border-bottom: -1px;
  z-index: 10;
}
.link-tabs .bui-tab-item-selected a {
  color: #3366cc;
}
.tab-nav-bar,
.tab-nav-arrow,
.tab-nav-wrapper,
.tab-nav-inner,
.bui-nav-tab-item,
.tab-item-close,
.bui-nav-tab-item .tab-item-inner,
.bui-nav-tab-item .l,
.bui-nav-tab-item .r {
  background: url("../img/tab-140-120.gif") -9999px -9999px no-repeat;
}
.tab-nav-bar {
  position: relative;
  width: 100%;
  z-index: 1;
  overflow: hidden;
  height: 21px;
  background-position: 0 20px;
  background-repeat: repeat-x;
}
.tab-content-container {
  width: 100%;
}
.tab-content-container iframe {
  border: none;
}
.tab-content {
  height: 100%;
}
.tab-nav-arrow {
  display: block;
  position: absolute;
  text-decoration: none;
  overflow: hidden;
  width: 13px;
  height: 13px;
  text-indent: -99px;
  top: 4px;
}
.bui-tab-force .tab-nav-arrow {
  display: none;
}
.bui-tab-force .tab-nav-wrapper {
  margin: 0;
}
.bui-nav-tab .tab-nav-list {
  padding-left: 15px;
}
.arrow-left {
  background-position: 0 -60px;
  left: 5px;
}
.arrow-left-active .arrow-left {
  background-position: -40px -60px;
  cursor: pointer;
}
.arrow-right {
  background-position: -20px -60px;
  right: 5px;
}
.arrow-right-active .arrow-right {
  background-position: -100px -60px;
  cursor: pointer;
}
.tab-nav-wrapper {
  margin: 0 23px;
  background-position: 0 -100px;
  background-repeat: repeat-x;
}
.tab-nav-inner {
  background-position: 0 20px;
  background-repeat: repeat-x;
  background-color: #e2eaf4;
  margin: 0 2px;
  overflow: hidden;
  position: relative;
}
.tab-nav-list {
  position: relative;
  left: 0;
  padding: 0;
  margin: 0;
  *zoom: 1;
}
.tab-nav-list:before,
.tab-nav-list:after {
  display: table;
  content: "";
}
.tab-nav-list:after {
  clear: both;
}
.bui-nav-tab-item {
  color: #263e74;
  float: left;
  width: 140px;
  height: 21px;
  position: relative;
  cursor: pointer;
  padding: 0;
  z-index: 1;
}
.bui-nav-tab-item .tab-item-inner {
  background-position: -15px -4px;
  margin: 0 20px 0 5px;
  background-color: transparent;
}
.bui-nav-tab-item .l,
.bui-nav-tab-item .r {
  position: absolute;
  display: block;
  height: 21px;
  top: 0;
}
.bui-nav-tab-item .l {
  background-position: 0 -4px;
  width: 20px;
  left: -15px;
}
.bui-nav-tab-item .r {
  background-position: right -4px;
  width: 33px;
  right: 0;
}
.tab-item-title {
  cursor: pointer;
}
.tab-nav-actived {
  z-index: 2;
}
.tab-nav-actived .tab-item-inner {
  background-position: -15px -29px;
}
.tab-nav-actived .l {
  background-position: 0 -29px;
  z-index: 2;
}
.tab-nav-actived .r {
  background-position: right -29px;
  z-index: 2;
}
.tab-item-close {
  display: block;
  position: absolute;
  text-decoration: none;
  overflow: hidden;
  cursor: pointer;
  width: 13px;
  height: 13px;
  text-indent: -99px;
  z-index: 3;
  top: 4px;
  right: 17px;
  background-position: 0 -80px;
}
.tab-item-close:hover {
  background-position: -20px -80px;
}
ul.bui-tab,
.bui-nav-tab ul {
  font-family: tahoma, arial, "SimSun", "Helvetica Neue", Helvetica, sans-serif;
  font-size: 12px;
  list-style: none;
  margin: 0;
  padding: 0;
}
ul.bui-tab li,
.bui-nav-tab ul li {
  line-height: 22px;
}
.button-tabs li,
.nav-tabs li,
.link-tabs li {
  display: inline-block;
  font-size: 12px;
  cursor: pointer;
  line-height: 18px;
  color: #333;
}
