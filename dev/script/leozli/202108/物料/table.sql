/*==============================================================*/
/* Module: MATERIAL2021                                         */
/*==============================================================*/

/*==============================================================*/
/* Table: wx_t_material2021                                     */
/*==============================================================*/
create table wx_t_material2021 (
	id                               bigint                            not null identity,
	dictionary_id                    bigint                                     not null,
	supplier_id                      bigint                                     not null,
	material_code                    nvarchar(64)                                   null,
	material_desc                    nvarchar(256)                                  null,
	material_status                  int                                            null,
	attribute1                       nvarchar(512)                                  null,
	attribute2                       nvarchar(512)                                  null,
	attribute3                       nvarchar(512)                                  null,
	attribute4                       nvarchar(512)                                  null,
	attribute5                       nvarchar(512)                                  null,
	safe_stock_qty                   bigint                                         null,
	version_no                       int                                        not null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	del_flag                         bigint                                         null,
	primary key (id)
);
ALTER TABLE wx_t_material2021 ADD CONSTRAINT uk_material2021 UNIQUE (dictionary_id,supplier_id,del_flag);

/*==============================================================*/
/* Table: wx_t_material2021_dictionary                          */
/*==============================================================*/
create table wx_t_material2021_dictionary (
	id                               bigint                            not null identity,
	material_category                nvarchar(64)                                   null,
	material_name                    nvarchar(256)                                  null,
	material_name_en                 nvarchar(64)                                   null,
	material_price                   decimal(20,6)                                  null,
	material_unit                    nvarchar(64)                                   null,
	material_tag                     nvarchar(64)                                   null,
	material_color                   nvarchar(64)                                   null,
	material_size                    nvarchar(64)                                   null,
	material_quality                 nvarchar(64)                                   null,
	material_tableau                 nvarchar(64)                                   null,
	material_status                  int                                            null,
	attribute1                       nvarchar(512)                                  null,
	attribute2                       nvarchar(512)                                  null,
	attribute3                       nvarchar(512)                                  null,
	attribute4                       nvarchar(512)                                  null,
	attribute5                       nvarchar(512)                                  null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	del_flag                         tinyint                                        null,
	spu                              nvarchar(30)                                   null,
	material_desc                    nvarchar(256)                                  null,
	primary key (id)
);
CREATE INDEX ix_mat_dic_spu ON wx_t_material2021_dictionary (spu);

/*==============================================================*/
/* Table: wx_t_material2021_inventory_log                       */
/*==============================================================*/
create table wx_t_material2021_inventory_log (
	id                               bigint                            not null identity,
	material_id                      bigint                                     not null,
	material_code                    nvarchar(64)                                   null,
	change_qty                       bigint                                         null,
	stock_type                       nvarchar(64)                                   null,
	attribute1                       nvarchar(512)                                  null,
	attribute2                       nvarchar(512)                                  null,
	attribute3                       nvarchar(512)                                  null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	del_flag                         tinyint                                        null,
	original_qty                     bigint                                         null,
	primary key (id)
);

/*==============================================================*/
/* Table: wx_t_material2021_price_log                           */
/*==============================================================*/
create table wx_t_material2021_price_log (
	id                               bigint                            not null identity,
	material_id                      bigint                                     not null,
	change_price                     decimal(20,6)                                  null,
	attribute1                       nvarchar(512)                                  null,
	attribute2                       nvarchar(512)                                  null,
	attribute3                       nvarchar(512)                                  null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	del_flag                         tinyint                                        null,
	original_price                   decimal(20,6)                                  null,
	primary key (id)
);

/*==============================================================*/
/* Table: wx_t_point_material                                   */
/*==============================================================*/
create table wx_t_point_material (
	id                               bigint                            not null identity,
	material_id                      bigint                                     not null,
	point_type_id                    bigint                                     not null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	delete_flag                      bigint                                     not null,
	available_partners               nvarchar(max)                                  null,
	point_value                      decimal(15,2)                                  null,
	primary key (id)
);
ALTER TABLE wx_t_point_material ADD CONSTRAINT uk_point_material UNIQUE (material_id,point_type_id,delete_flag);

/*==============================================================*/
/* Foreign Key(only index): ix_mat_mat_dic                      */
/*==============================================================*/
CREATE INDEX ix_mat_mat_dic ON wx_t_material2021 (dictionary_id);

/*==============================================================*/
/* Foreign Key(only index): ix_material2021_supplier            */
/*==============================================================*/
CREATE INDEX ix_material2021_supplier ON wx_t_material2021 (supplier_id);

/*==============================================================*/
/* Foreign Key(only index): ix_mat_inv_log_mat                  */
/*==============================================================*/
CREATE INDEX ix_mat_inv_log_mat ON wx_t_material2021_inventory_log (material_id);

/*==============================================================*/
/* Foreign Key(only index): ix_mat_price_log_mat_dic            */
/*==============================================================*/
CREATE INDEX ix_mat_price_log_mat_dic ON wx_t_material2021_price_log (material_id);

/*==============================================================*/
/* Foreign Key(only index): ix_point_mat_mat                    */
/*==============================================================*/
CREATE INDEX ix_point_mat_mat ON wx_t_point_material (material_id);

/*==============================================================*/
/* Foreign Key(only index): ix_point_mat_pro_point              */
/*==============================================================*/
CREATE INDEX ix_point_mat_pro_point ON wx_t_point_material (point_type_id);
