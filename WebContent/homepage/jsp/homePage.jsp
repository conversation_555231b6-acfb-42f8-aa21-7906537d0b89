<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ page import="com.common.util.ContextUtil"%>

<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<title>首页</title>
<%@ include file="/common/jsp/hpluspublib.jsp"%>
<link href="${ctx}homepage/css/homePage.css?v=20180529" rel="stylesheet">
</head>
<body class="gray-bg" style="overflow-x: auto;">
	<div id="userType" data="${userType}" style="display:none;"></div>
	<div id="permission" data="${permission}" style="display:none;"></div>
	<div id="partnerId" data="${partnerId}" style="display:none;"></div>
	<div id="spKpi" data="${spKpi}" style="display:none;"></div>
	<div class="wrapper wrapper-content">
	<c:if test="${permission eq 'chevronManager'}">
		<!-- 雪佛龙: 渠道销售/雪佛龙管理员 -->
		<div class="permission-wrapper" permission="chevronManager">
			<div class="row">
				<div id="saleTargetDiv" class="col-sm-p20 top-box">
					<div class="ibox float-e-margins">
						<div class="ibox-title">
							<span class="pull-right"><i class="fa fa-tasks fa-2x blue-text"></i></span>
							<h5><!-- <i class="fa fa-group fa-3x"></i> -->Sales Target</h5>
						</div>
						<div class="ibox-content clickable-number" data-toggle="tooltip" data-placement="bottom" title="点击查看销售目标详情"
						onclick="dashboard_widget.goDetail('business/report/salesTarget.jsp')">
							<h1 class="no-margins " id="actualVolumn">0</h1>
							<!-- <div class="stat-percent font-bold text-success">98% <i class="fa fa-bolt"></i></div> -->
							<small id="saleTargetTip">销售完成比</small>
						</div>
					</div>
				</div>
				<div id="partnerDiv" class="col-sm-p20 top-box">
					<div class="ibox float-e-margins">
						<div class="ibox-title">
							<span class="pull-right"><i class="fa fa-users fa-2x blue-text"></i></span>
							<h5><!-- <i class="fa fa-group fa-3x"></i> -->合伙人</h5>
						</div>
						<div class="ibox-content clickable-number" data-toggle="tooltip" data-placement="bottom" title="点击查看合伙人详情"
						onclick="dashboard_widget.goDetail('business/partner/partnerPage1.jsp')">
							<h1 class="no-margins " id="partnerTotalCount">0</h1>
							<!-- <div class="stat-percent font-bold text-success">98% <i class="fa fa-bolt"></i></div> -->
							<small id="partnerTotalCountTip">合伙人总数量</small>
						</div>
					</div>
				</div>
				<div class="col-sm-p20 top-box hide">
					<div class="ibox float-e-margins" >
						<div class="ibox-title">
							<span class="label label-success pull-right">年</span>
							<h5>目标完成率（%）</h5>
						</div>
						<div class="ibox-content clickable-number" data-toggle="tooltip" data-placement="bottom" title="点击查看详情"
						onclick="dashboard_widget.goDetail('business/workshop/workshopPage.jsp')">
							<h1 class="no-margins" id="sellinPercentComplete">0</h1>
							<small>Sell in 目标完成百分比</small>
						</div>
					</div>
				</div>
				<div class="col-sm-p20 top-box">
					<div class="ibox float-e-margins" >
						<div class="ibox-title">
							<span class="pull-right"><i class="fa fa-tint fa-2x blue-text"></i></span>
							<h5>SELL IN （L）</h5>
						</div>
						<div class="ibox-content clickable-number"  data-toggle="tooltip" data-placement="bottom" title="点击查看采购订单详情"
						onclick="dashboard_widget.goDetail('business/partnerorder/partnerOrderManage.jsp')">
							<h1 class="no-margins" id="sellinVolume">0</h1>
							<div class="stat-percent font-bold text-success hide">~% <i class="fa fa-bolt"></i> </div>
							<small id="sellinVolumeTip">2018年 Sell in 总升数</small>
						</div>
					</div>
				</div>
				<div class="col-sm-p20 top-box">
					<div class="ibox float-e-margins" >
						<div class="ibox-title">
							<span class="pull-right"><i class="fa fa-tint fa-2x blue-text"></i></span>
							<h5>SELL THROUGH （L）</h5>
						</div>
						<div class="ibox-content clickable-number"  data-toggle="tooltip" data-placement="bottom" title="点击查看销售订单详情"
						onclick="dashboard_widget.goDetail('business/order/orderPurchaseManage1.jsp', 'business/partnerorder/partnerOrderManage.jsp')">
							<h1 class="no-margins" id="sellthroughVolume">0</h1>
							<div class="stat-percent font-bold text-success hide">~% <i class="fa fa-bolt"></i></div>
							<small id="sellthroughVolumeTip">2018年 Sell through 总升数</small>
						</div>
					</div>
				</div>
				<div class="col-sm-p20 top-box">
					<div class="ibox float-e-margins" >
						<div class="ibox-title">
							<span class="pull-right"><i class="fa fa-car fa-2x blue-text"></i></span>
							<h5>SELL OUT （L）</h5>
						</div>
						<div class="ibox-content clickable-number"  data-toggle="tooltip" data-placement="bottom" title="点击查看当月核销详情"
						onclick="dashboard_widget.goDetail('business/workshopverification/spVerificationPage.jsp')">
							<h1 class="no-margins" id="selloutVolume">0</h1>
							<div class="stat-percent font-bold text-success hide">~% <i class="fa fa-bolt"></i></div>
							<small id="selloutVolumeTip">2018年 Sell out 总升数</small>
						</div>
					</div>
				</div>
				<div class="col-sm-p20 top-box hide">
					<div class="ibox float-e-margins" >
						<div class="ibox-title">
							<span class="label label-success pull-right">今天</span>
							<h5>ROI （%）</h5>
						</div>
						<div class="ibox-content clickable-number"  data-toggle="tooltip" data-placement="bottom" title="点击查看合伙人费用详情"
						onclick="dashboard_widget.goDetail('business/expense/expenseStatistics.jsp')">
							<h1 class="no-margins" id="percentROI">0</h1>
							<div class="stat-percent font-bold text-success">~% <i class="fa fa-bolt"></i></div>
							<small>投资回报率</small>
						</div>
					</div>
				</div>
				<div class="col-sm-p20 top-box hide">
					<div class="ibox float-e-margins" >
						<div class="ibox-title">
							<span class="label label-success pull-right">今天</span>
							<h5>合作门店</h5>
						</div>
						<div class="ibox-content clickable-number"  data-toggle="tooltip" data-placement="bottom" title="点击查看激活门店详情"
						onclick="dashboard_widget.goDetail('business/workshop/workshopPage.jsp')">
							<h1 class="no-margins" id="workshopTotalCount">0</h1>
							<div class="stat-percent font-bold text-success">98% <i class="fa fa-bolt"></i></div>
							<small>激活门店总量</small>
						</div>
					</div>
				</div>

				<div class="col-sm-p20 top-box hide">
					<div class="ibox float-e-margins">
						<div class="ibox-title">
							<span class="label label-success pull-right">今天</span>
							<h5>技师/店主</h5>
						</div>
						<div class="ibox-content clickable-number" data-toggle="tooltip" data-placement="bottom" title="点击查看技师详情"
						onclick="dashboard_widget.goDetail('business/workshopemp/workshopEmp1.jsp')">
							<h1 class="no-margins" id="mechanicsCount">0</h1>
							<div class="stat-percent font-bold text-success">98% <i class="fa fa-bolt"></i>
							</div>
							<small>技师总量</small>
						</div>
					</div>
				</div>

				<div class="col-sm-p20 top-box hide">
					<div class="ibox float-e-margins" >
						<div class="ibox-title">
							<span class="label label-success pull-right">今天</span>
							<h5>核销升数</h5>
						</div>
						<div class="ibox-content clickable-number" data-toggle="tooltip" data-placement="bottom" title="点击查看核销明细"
						onclick="dashboard_widget.goDetail('business/workshopverification/spVerificationPage.jsp')">
							<h1 class="no-margins" id="validVerifyQuantity">0</h1>
							<div class="stat-percent font-bold text-success">98% <i class="fa fa-bolt"></i>
							</div>
							<small>2018年 核销总升数</small>
						</div>
					</div>
				</div>

				<div class="col-sm-p20 top-box hide">
					<div class="ibox float-e-margins">
						<div class="ibox-title">
							<span class="label label-success pull-right">今天</span>
							<h5>订单</h5>
						</div>
						<div class="ibox-content clickable-number" data-toggle="tooltip" data-placement="bottom" title="点击查看订单详情"
						onclick="dashboard_widget.goDetail('business/order/orderPurchaseManage1.jsp')">
							<h1 class="no-margins" id="orderCount">0</h1>
							<div class="stat-percent font-bold text-success">98% <i class="fa fa-bolt"></i>
							</div>
							<small>2018年订单总量</small>
						</div>
					</div>
				</div>
			</div>

			<div class="subject-indexes-wrapper">
				<ul>
					<li onclick="window.location.hash='#subject-index-1'"><i class="fa fa-area-chart fa-lg fa-fw"></i>销售数据对比图</li>
					<li onclick="window.location.hash='#subject-index-2'"><i class="fa fa-bar-chart fa-lg fa-fw"></i>扫录巡店与核销</li>
					<li onclick="window.location.hash='#subject-index-3'"><i class="fa fa-bars fa-lg fa-fw"></i>门店状况分析</li>
					<li onclick="window.location.hash='#subject-index-4'"><i class="fa fa-pie-chart fa-lg fa-fw"></i>机油类型销售分析</li>
					<li onclick="window.location.hash='#subject-index-5'"><i class="fa fa-bar-chart fa-rotate-90 fa-lg fa-fw"></i>功能模块使用分析</li>
                    <li onclick="window.location.hash='#subject-index-6'"><i class="fa fa-user-md fa-lg fa-fw"></i>APP用户分析</li>
                    <li onclick="window.location.hash='#subject-index-7'" style="display: none"><i class="fa fa-compass fa-lg fa-fw"></i>活跃门店分析</li>
				</ul>
			</div>

			<div class="row">
				<div class="col-sm-12" id="subject-index-1">
					<div class="ibox float-e-margins">
						<div class="ibox-title">
							<h5 style="width:40%">销售数据对比图</h5>
							<div class="pull-right sdc-btn-wrapper">
								<div class="btn-group" id="spRangeBtnGroup" >
									<button type="button" data-spRange="newSp" 
									class="sales-data-comp-btn btn btn-xs btn-white " >新SP</button>
									<button type="button" data-spRange="oldSp"
									class="sales-data-comp-btn btn btn-xs btn-white ">旧SP</button>
									<button type="button" data-spRange="allSp"
									 class="sales-data-comp-btn btn btn-xs btn-white active">全部SP</button>
								</div>
								<div class="btn-group" id="salesDataComparisonbtnGroup">
									<button id="lastYear" type="button" data-type="year" data-year="2016" class="sales-data-comp-btn btn btn-xs btn-white">去年</button>
									<button id="thisYear" type="button" data-type="year" data-year="2017" class="sales-data-comp-btn btn btn-xs btn-white">全年</button>
								</div>
								<div class="btn-group">
									<button id="more" type="button" class="btn btn-xs btn-success"
									onclick="dashboard_widget.goDetail('business/report/spKpiReport.jsp')">合伙人详情</button>
								</div>
						</div>
						</div>
						<div class="ibox-content">
							<div class="row">
								<div class="col-sm-9" id="salesDataComparison">
									<div class="echarts" id="salesDataComparisonChart" style="height: 300px;"></div>
								</div>
								<div class="col-sm-3" id="chainIndex">
									<ul class="stat-list" style="height: 300px;">
										<li style="height:33%" id="focusedSellin">
											<h2 class="no-margins"><span class="volume-value">0</span></h2>
											<small><span class="date-text"></span>Sell-In升数</small>
											<!-- <div class="stat-percent">
												<small><span class="date-text"></span>Sell-In升数</small>
											</div> -->
											<div class="progress progress-striped active">
												<div style="width: 0%;" class="progress-bar">
													<span class="progress-text">环比<span class="percent-prefix">增长</span> <span class="percent-value">~</span>%</span>
												</div>
											</div>
										</li>
										<li style="height:33%" id="focusedSellthrough">
											<h2 class="no-margins volume-value">0</h2>
											<small><span class="date-text"></span>Sell-Through升数</small>
											<!-- <div class="stat-percent"><small>环比  <span class="percent-value">0</span>%<i class="fa fa-level-down text-navy"></i></small> -->
											<!-- </div> -->
											<div class="progress progress-striped active">
												<div style="width: 0%;" class="progress-bar">
													<span class="progress-text">环比<span class="percent-prefix">增长</span> <span class="percent-value ">~</span>%</span>
												</div>
											</div>
										</li>
										<li style="height:33%" id="focusedSellout">
											<h2 class="no-margins volume-value">0</h2>
											<small><span class="date-text"></span>Sell-Out升数</small>
											<!-- <div class="stat-percent" ><small>环比 <span class="percent-value" >0</span>%<i class="fa fa-bolt text-navy"></i></small> -->
											<!-- </div> -->
											<div class="progress progress-striped active">
												<div style="width: 0%;" class="progress-bar">
													<span class="progress-text">环比<span class="percent-prefix">增长</span> <span class="percent-value ">~</span>%</span>
												</div>
											</div>
										</li>
									</ul>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="col-sm-12" id="subject-index-2">
					<div class="ibox float-e-margins">
						<div class="ibox-title">
							<h5>扫店、录店、巡店与核销分析</h5>
							<div class="pull-right">
								<div class="btn-group">
									<button id="more" type="button" class="btn btn-xs btn-success"
									onclick="dashboard_widget.goDetail('business/report/spKpiReport.jsp')">合伙人详情</button>
								</div>
							</div>
						</div>
						<div class="ibox-content">
							<div class="row">
								<div class="col-sm-12">
									<div class="echarts" id="patrolNSelloutEchart" style="height: 300px;"></div>
								</div>
								<!-- <div class="col-sm-6">
									<div class="echarts" id="patrolNSelloutEchart" style="height: 300px;"></div>
								</div> -->
							</div>
						</div>
					</div>
				</div>
				<div class="col-sm-12" id="subject-index-3">
					<div class="ibox float-e-margins">
						<div class="ibox-title">
							<h5 style="width:40%">门店状况分析</h5>
							<div class="btn-group hide">
								<button button="text" id="workshopDataEchartDatePicker" name="workshopDataEchartDatePicker" 
									value="" class="sales-data-comp-btn btn btn-xs btn-white active" 
								onclick="WdatePicker({el:'workshopStatisticsDate', maxDate:'%y-%M-%d', lang:'zh-tw',onpicking:function(dp){dashboard_widget.loadWorkshopData(dp);}});">统计日期：<span id="workshopStatisticsDate">2018-01-01</span></button>
							</div>

							<div class="pull-right wdc-btn-wrapper hide">
								<div class="btn-group" id="active-workshop-btn-group1">
									<button id="lastYear" type="button" data-type="newSp" onclick="dashboard_widget.refreshWorkshop($(this));"
									class="sales-data-comp-btn btn btn-xs btn-white ">新SP</button>
									<button id="lastYear" type="button" data-type="oldSp" onclick="dashboard_widget.refreshWorkshop($(this));"
									class="sales-data-comp-btn btn btn-xs btn-white ">旧SP</button>
									<button id="lastYear" type="button" data-type="allSp" onclick="dashboard_widget.refreshWorkshop($(this));"
									class="sales-data-comp-btn btn btn-xs btn-white active ">全部SP</button>
								</div>
								<div class="btn-group awtc-btn-wrapper" id="active-workshop-btn-group2">
										<button id="lastYear2" type="button" data-type="year" data-year="2017" class="sales-data-comp-btn btn btn-xs btn-white">去年</button>
										<button id="thisYear2" type="button" data-type="year" data-year="2018" class="sales-data-comp-btn btn btn-xs btn-white .active">全年</button>
									</div>
							</div>
						</div>
						<div class="ibox-content">
							<div class="row">
								<div class="what-is-active-workshop">
									<span>活跃门店： 三个月内核销大于</span>
									<input type="text" id="workshopConfigInput1" name="workshopConfigInput1" class="control-text" value="0" 
									onblur="this.value=this.value.replace(/\\D|/g,'')" 
									onkeyup="this.value=this.value.replace(/\D|/g,'')" 
									onafterpaste="this.value=this.value.replace(/\D|/g,'')"/>
									<span>升</span>
									<select class="form-control" id="workshopConfigSelect" name="workshopConfigSelect">
										<option value="or">或者</option>
										<option value="and">并且</option>
									</select>
									<span>三个月内巡店次数大于</span>
									<input type="text" id="workshopConfigInput2" name="workshopConfigInput2"
									class="control-text"  value="0" 
									onblur="this.value=this.value.replace(/\\D|/g,'')" 
									onkeyup="this.value=this.value.replace(/\D|/g,'')" 
									onafterpaste="this.value=this.value.replace(/\D|/g,'')"/>
									<span>次。</span>
									<button id="refreshWorkshopConfig" type="button" onclick="dashboard_widget.refreshWorkshop();"
									class="sales-data-comp-btn btn btn-xs btn-white pull-right">重新计算</button>
								</div>
							</div>
							<div class="row">
								<div class="col-sm-6">
									<div class="echarts" id="workshopDataEchart" style="height: 300px;"></div>
								</div>
								<div class="col-sm-4 pull-right">
									<div class="echarts" id="workshopDataProcess">
										<ul class="stat-list" style="height: 300px;">
											<li style="height:33%;" id="totalMoM" class="tooltip-div" data-toggle="tooltip" data-placement="left" title="激活门店: 即完成扫店录店任务并且已激活的门店。">
												<h2 class="no-margins"><span class=" percent-value">0</span>%</h2>
												<small>激活门店数量环比增长率</small>
												<div class="stat-percent">上月数量 <span class="volume-value">0</span>家<i class="fa fa-level-up text-navy"></i>
												</div>
												<div class="progress progress-small">
													<div style="width: 0%;" class="progress-bar"></div>
												</div>
											</li>
											<li style="height:33%;" id="sunsetTotalMoM"  class="tooltip-div" data-toggle="tooltip" data-placement="left" title="非活跃门店: 即三个月内核销升数为0并且没有任何巡店记录的已激活门店。">
												<h2 class="no-margins"><span class=" percent-value">0</span>%</h2>
												<small>非活跃门店数量环比增长率</small>
												<div class="stat-percent">上月数量  <span class="volume-value">0</span>家<i class="fa fa-level-down text-navy"></i>
												</div>
												<div class="progress progress-small">
													<div style="width: 0%;" class="progress-bar"></div>
												</div>
											</li>
											<li style="height:33%;" id="activeTotalMoM"  class="tooltip-div" data-toggle="tooltip" data-placement="left" title="活跃门店: 即三个月内有核销记录或者有巡店记录的已激活门店。">
												<h2 class="no-margins"><span class=" percent-value">0</span>%</h2>
												<small>活跃门店数量环比增长率</small>
												<div class="stat-percent" >上月数量 <span class="volume-value" >0</span>家<i class="fa fa-bolt text-navy"></i>
												</div>
												<div class="progress progress-small">
													<div style="width: 0%;" class="progress-bar"></div>
												</div>
											</li>
										</ul>
									</div>
								</div>
								<!-- <div class="col-sm-2">
									<ul class="stat-list" style="height: 300px;">
											<li style="height:30%" class="well" >
												<div class="text-left">激活门店: 即完成扫店录店任务并且已激活的门店。</div>
											</li>
											<li style="height:30%" class="well" >
												<div class="text-left">非活跃门店: 即三个月内核销升数为0并且没有任何巡店记录的已激活门店。</div>
											</li>
											<li style="height:30%" class="well" >
												<div class="text-left">活跃门店: 即三个月内有核销记录或者有巡店记录的已激活门店。</div>
											</li>
										</ul>
								</div> -->
							</div>
							<div class="row">
									<div class="col-sm-12">
											<div class="echarts" id="activeWorkshopTrendChart" style="height: 300px;"></div>
										</div>

							</div>
						</div>
					</div>
				</div>

				<div class="col-sm-12" id="subject-index-4">
					<div class="ibox float-e-margins">
						<div class="ibox-title">
							<span class="label label-success pull-right"></span>
							<h5>2018年机油类型销售分析</h5>
						</div>
						<div class="ibox-content">
							<div class="row">
								<div class="col-sm-4">
									<div class="echarts" id="oilTypeScaleEchart" style="height: 300px;"></div>
								</div>
								<div class="col-sm-4">
									<div class="echarts" id="skuScaleEchart" style="height: 300px;"></div>
								</div>
								<div class="col-sm-4">
									<div class="echarts" id="areaDistributionEchart" style="height: 300px;"></div>
								</div>
							</div>
						</div>
					</div>
				</div>

				<div class="col-sm-6 hide" id="workshopMap">
					<div class="ibox float-e-margins">
						<div class="ibox-title">
							<span class="label label-success pull-right"></span>
							<h5>门店分布</h5>
						</div>
						<div class="ibox-content">
							<div class="row">
								<div class="col-sm-12">
									<div class="echarts" id="echarts-map-chart" style="height: 300px;"></div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="col-sm-6 hide">
					<div class="ibox float-e-margins">
						<div class="ibox-title">
							<span class="label label-success pull-right"></span>
							<h5>门店状态</h5>
						</div>
						<div class="ibox-content">
							<div class="row">
								<div class="col-sm-12">
									<div class="echarts" id="echarts-funnel-chart" style="height: 300px;"></div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="col-sm-6 hide">
					<div class="ibox float-e-margins">
						<div class="ibox-title">
							<span class="label label-success pull-right"></span>
							<h5>任务执行情况</h5>
						</div>
						<div class="ibox-content">
							<div class="row">
								<div class="col-sm-12">
									<div class="echarts" id="canvus" style="height: 300px;"></div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="col-sm-6 wk-verif-trend hide">
					<div class="ibox float-e-margins">
						<div class="ibox-title">
							<h5>门店核销走势</h5>
							<div class="pull-right">
								<div class="btn-group">
									<button id="weekButton" type="button" data-type="2" class="btn btn-xs btn-white active">本周</button>
									<button id="monthButton" type="button" data-type="3" class="btn btn-xs btn-white">本月</button>
									<button id="yearButton" type="button" data-type="4" class="btn btn-xs btn-white">本年</button>
								</div>
							</div>
						</div>
						<div class="ibox-content">
							<div class="row">
								<div class="col-sm-12">
									<div class="echarts" id="canvusOilfilter" style="height: 300px;"></div>
								</div>
							</div>
						</div>
					</div>
				</div>

				<div class="col-sm-6 hide">
					<div class="ibox float-e-margins">
						<div class="ibox-title">
							<h5>门店核销统计</h5>
						</div>
						<div class="ibox-content">
							<div class="row">
								<div class="col-sm-12">
									<table id="table_list_1" style="height: 300px;"></table>
								</div>
							</div>
						</div>
					</div>
				</div>
				<!-- 功能模块使用情况 -->
				<div class="col-sm-12" id="subject-index-5">
					<div class="ibox float-e-margins">
						<div class="ibox-title">
							<h5>功能模块使用分析</h5>
							<div class="pull-right">
								<div class="btn-group">
									<button id="more" type="button" class="btn btn-xs btn-success"
                                    onclick="dashboard_widget.goDetail('homepage/jsp/modelUsedDetail.jsp')">功能模块使用详情</button>
								</div>
							</div>
						</div>
						<div class="ibox-content">
							<div class="row">
								<div class="col-sm-12">
									<div class="echarts" id="modelUsedDetailEchart" style="height: 300px;"></div>
								</div>
							</div>
						</div>
					</div>
				</div>
                
                <!-- app用戶及活跃用户 -->
                <div class="col-sm-12" id="subject-index-6">
                    <div class="ibox float-e-margins">
                        <div class="ibox-title">
                            <h5 style="width:40%">APP用户分析</h5>
                            <div class="pull-right sdc-btn-wrapper">
                                <div class="btn-group" id="spUserAndActiveUserDataBtn">
                                    <button id="lastYear1" type="button" data-type="year" data-year="2017" class="sales-data-comp-btn btn btn-xs btn-white">去年</button>
                                    <button id="thisYear1" type="button" data-type="year" data-year="2018" class="sales-data-comp-btn btn btn-xs btn-white .active">全年</button>
                                </div>
                            </div>
                        </div>
                        <div class="ibox-content">
                            <div class="row">
                                <div class="col-sm-12" id="spUserAndActiveUserData">
                                    <div class="echarts" id="spUserAndActiveUserDataChart" style="width:100%; height: 300px;"></div>
                                </div>
                            </div>
                        </div>
                     </div>
		        </div>
                
                <!-- 活跃门店扫码分析 -->
                <div class="col-sm-12 hide" id="subject-index-7">
                    <div class="ibox float-e-margins">
                        <div class="ibox-title">
                            <h5 style="width:40%">活跃门店分析</h5>
                            <div class="pull-right sdc-btn-wrapper">
                                <div class="btn-group" id="spTypeBtnGroup" >
                                    <button type="button" data-spRange="newSp" 
                                    class="sales-data-comp-btn btn btn-xs btn-white " >新SP</button>
                                    <button type="button" data-spRange="oldSp"
                                    class="sales-data-comp-btn btn btn-xs btn-white ">旧SP</button>
                                    <button type="button" data-spRange="allSp"
                                     class="sales-data-comp-btn btn btn-xs btn-white active">全部SP</button>
                                </div>
                                <div class="btn-group" id="activeWorkshopAndMechanicUserDataBtn">
                                    <button id="lastYear2" type="button" data-type="year" data-year="2017" class="sales-data-comp-btn btn btn-xs btn-white">去年</button>
                                    <button id="thisYear2" type="button" data-type="year" data-year="2018" class="sales-data-comp-btn btn btn-xs btn-white .active">全年</button>
                                </div>
                            </div>
                        </div>
                        <div class="ibox-content">
                            <div class="row">
                                <div class="col-sm-12" id="activeWorkshopAndMechanicUserData">
                                    <div class="echarts" id="activeWorkshopAndMechanicUserDataChart" style="width:100%; height: 300px;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

				 <div class="col-sm-12" style="height:470px;">
				 </div>
             </div>
        </div>
	</c:if>

	<c:if test="${permission eq 'partnerManager'}">
		<!-- SP 管理员 -->
		<div class="permission-wrapper" permission="partnerManager">
			<div class="row">
				<div class="col-sm-p20 top-box">
					<div class="ibox float-e-margins" >
						<div class="ibox-title">
							<span class="pull-right"><i class="fa fa-gift fa-2x blue-text"></i></span>
							<h5>返利金额(￥)</h5>
						</div>
						<div class="ibox-content clickable-number"  data-toggle="tooltip" data-placement="bottom" title="点击查看返利明细"
						onclick="dashboard_widget.goDetail('business/partnerorder/partnerbillpage.jsp')">
							<h1 class="no-margins" id="partnerBillTotalAmount">0</h1>
							<div class="stat-percent font-bold text-success hide">~% <i class="fa fa-bolt"></i></div>
							<small>可用金额</small>
						</div>
					</div>
				</div>
				<div class="col-sm-p20 top-box">
					<div class="ibox float-e-margins" >
						<div class="ibox-title">
							<span class="pull-right"><i class="fa fa-gears fa-2x blue-text"></i></span>
							<h5>合作门店</h5>
						</div>
						<div class="ibox-content clickable-number"  data-toggle="tooltip" data-placement="bottom" title="点击查看激活门店详情"
						onclick="dashboard_widget.goDetail('business/workshop/workshopPage.jsp')">
							<h1 class="no-margins" id="partnerWorkshopTotalCount">0</h1>
							<div class="stat-percent font-bold text-success hide">~<i class="fa fa-bolt"></i></div>
							<small>激活门店总量</small>
						</div>
					</div>
				</div>
				<div class="col-sm-p20 top-box">
					<div class="ibox float-e-margins" >
						<div class="ibox-title">
							<span class="pull-right"><i class="fa fa-tint fa-2x blue-text"></i></span>
							<h5>进货总升数 （L）</h5>
						</div>
						<div class="ibox-content clickable-number"  data-toggle="tooltip" data-placement="bottom" title="点击去下单"
						onclick="dashboard_widget.goDetail('business/partnerorder/partnerOrderManageNew.jsp')">
							<h1 class="no-margins" id="sellinVolume">0</h1>
							<div class="stat-percent font-bold text-success hide">~% <i class="fa fa-bolt"></i> </div>
							<small>2018年进货总升数</small>
						</div>
					</div>
				</div>
				<div class="col-sm-p20 top-box">
					<div class="ibox float-e-margins" >
						<div class="ibox-title">
							<span class="pull-right"><i class="fa fa-tint fa-2x blue-text"></i></span>
							<h5>销售到门店升数（L）</h5>
						</div>
						<div class="ibox-content clickable-number"  data-toggle="tooltip" data-placement="bottom" title="点击去下单"
						onclick="dashboard_widget.goDetail('business/order/orderPurchaseManage1.jsp', 'business/partnerorder/partnerOrderManageNew.jsp')">
							<h1 class="no-margins" id="sellthroughVolume">0</h1>
							<div class="stat-percent font-bold text-success hide">~% <i class="fa fa-bolt"></i></div>
							<small>2018年销售到门店总升数</small>
						</div>
					</div>
				</div>
				<div class="col-sm-p20 top-box">
					<div class="ibox float-e-margins" >
						<div class="ibox-title">
							<span class="pull-right"><i class="fa fa-car fa-2x blue-text"></i></span>
							<h5>机油核销升数 （L）</h5>
						</div>
						<div class="ibox-content clickable-number"  data-toggle="tooltip" data-placement="bottom" title="点击查看核销明细"
						onclick="dashboard_widget.goDetail('business/workshopverification/spVerificationPage.jsp')">
							<h1 class="no-margins" id="selloutVolume">0</h1>
							<div class="stat-percent font-bold text-success hide">~% <i class="fa fa-bolt"></i></div>
							<small>2018年机油核销总升数</small>
						</div>
					</div>
				</div>
				<div class="col-sm-p20 top-box hide">
					<div class="ibox float-e-margins" >
						<div class="ibox-title">
							<span class="label label-success pull-right">今天</span>
							<h5>发生费用/已结费用</h5>
						</div>
						<div class="ibox-content clickable-number"  data-toggle="tooltip" data-placement="bottom" title="点击查看详情"
						onclick="dashboard_widget.goDetail('business/workshop/workshopPage.jsp')">
							<h1 class="no-margins" id="percentROI">0</h1>
							<div class="stat-percent font-bold text-success hide">~% <i class="fa fa-bolt"></i></div>
							<small>发生费用/已经结算费用</small>
						</div>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-sm-12" id="">
					<div class="ibox float-e-margins">
						<div class="ibox-title">
							<h5>合伙人业绩分析</h5>
							<div class="pull-right sp-perf-btn-wrapper">
								<div class="btn-group">
									<button id="lastYear" type="button" data-type="year" data-year="2016" class="sp-perf-btn btn btn-xs btn-white ">去年</button>
								</div>
								<div class="btn-group" id="spPerfBtnGroup">
									<button id="thisYear" type="button" data-type="year" data-year="2017" class="sp-perf-btn btn btn-xs btn-white">全年</button>
								</div>
								<!-- <div class="btn-group">
									<button id="more" type="button" class="btn btn-xs btn-white "
									onclick="dashboard_widget.goDetail('business/report/spKpiReport.jsp')">更多详情</button>
								</div> -->
						</div>
						</div>
						<div class="ibox-content">
							<div class="row">
								<div class="col-sm-8">
									<div class="echarts" id="partnerTaskEchart" style="height: 300px;"></div>
								</div>
								<div class="col-sm-4">
									<div class="" id="partnerProgressDetail" style="height: 300px;">
										<ul class="stat-list" style="height: 300px;">
											<li style="height:30%" id="progressSD">
												<h2 class="no-margins"><span class="volume-value">0</span></h2>
												<small><span class="date-text"></span>扫店数量和增长率</small>
												<div class="stat-percent"><small>击败了  <span class="percent-value">~</span>% 的合伙人</small></div>
												<div class="progress progress-striped active">
													<div style="width: 0%;" class="progress-bar progress-bar-warning">
														<span class="progress-text"><span class="percent-prefix">增长</span> <span class="percent-value ">~</span>%</span>
													</div>
												</div>
											</li>
											<li style="height:30%" id="progressLD">
												<h2 class="no-margins"><span class="volume-value">0</span></h2>
												<small><span class="date-text"></span>录店数量和增长率</small>
												<div class="stat-percent"><small>击败了  <span class="percent-value">~</span>% 的合伙人</small></div>
												<div class="progress progress-striped active">
													<div style="width: 0%;" class="progress-bar progress-bar-danger">
														<span class="progress-text"><span class="percent-prefix">增长</span><span class="percent-value ">~</span>%</span>
													</div>
												</div>
											</li>
											<li style="height:30%" id="progressXD">
												<h2 class="no-margins"><span class="volume-value">0</span></h2>
												<small><span class="date-text"></span>巡店数量和增长率</small>
												<div class="stat-percent"><small>击败了  <span class="percent-value">~</span>% 的合伙人</small></div>
												<div class="progress progress-striped active">
													<div style="width: 0%;" class="progress-bar progress-bar-success">
														<span class="progress-text"><span class="percent-prefix">增长</span><span class="percent-value ">~</span>%</span>
													</div>
												</div>
											</li>
										</ul>

									</div>
								</div>

							</div>

							<div class="row ">
								<div class="col-sm-4">
									<div class="echarts" id="SPBDSDTaskEchart" style="min-height: 400px;"></div>
								</div>
								<div class="col-sm-4">
									<div class="echarts" id="SPBDXDTaskEchart" style="min-height: 400px;"></div>
								</div>
								<div class="col-sm-4">
									<div class="echarts" id="SPBDLDTaskEchart" style="min-height: 400px;"></div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</c:if>
	<c:if test="${permission eq 'dadi'}">
		<!-- 保险用户 -->
		<div class="permission-wrapper hide" permission="dadi">
		</div>
	</c:if>
	<c:if test="${permission eq 'o2o'}">
		<!-- O2O用户-->
		<div class="permission-wrapper hide" permission="o2o">
		</div>
	</c:if>


	</div>
	<%@include file="/common/jsp/hplusCommonJs.jsp"%>
	<script src="${ctx }common/hplus/js/plugins/echarts/echarts.min.js"></script>
	<script src="${ctx }common/date/WdatePicker.js"></script>
	<!-- <script src="http://gallerybox.echartsjs.com/dep/echarts/latest/echarts.min.js"></script> -->
    <script src="${ctx }homepage/js/homePage.js?v=20180705"></script>
</body>
</html>
