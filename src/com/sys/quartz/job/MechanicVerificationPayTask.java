package com.sys.quartz.job;

import java.util.Date;

import org.springframework.beans.factory.annotation.Autowired;

import com.chevron.pms.business.MechanicVerificationPayBizService;
import com.common.exception.WxPltException;
import com.common.util.DateUtil;
import com.sys.log.business.LogBizService;
import com.sys.log.model.Log;

public class MechanicVerificationPayTask {

	@Autowired
	private MechanicVerificationPayBizService mechanicVerificationPayBizService;
    
    @Autowired
    private LogBizService logBizService;
	
	public synchronized void execute(){
		final Log log = new Log();
		final Date now = DateUtil.getCurrentDate();
		try {
			mechanicVerificationPayBizService.batchPay();
		} catch (Exception e) {
			log.setExtProperty2("ERROR");
			log.setExtProperty3(e.getMessage());
		}
		log.setOperator(1l);
		log.setLogType("RedPacketsJobLog");
		log.setCreateTime(DateUtil.getCurrentDate());
		log.setExtProperty1((DateUtil.getCurrentDate().getTime() - now.getTime()) + "");
		try {
			logBizService.insert(log);
		} catch (WxPltException e) {
			e.printStackTrace();
		}
	}
}
