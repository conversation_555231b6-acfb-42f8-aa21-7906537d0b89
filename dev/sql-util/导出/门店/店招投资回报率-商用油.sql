select w.id,w.shop_recruitment,(select top 1 vcr.region_name from view_customer_region_sales_channel vcr where vcr.distributor_id=pe.distributor_id order by case when vcr.bu='Indirect' then 0 else 1 end) "Region",
o.organization_name "SP/Dist.", w.work_shop_name "门店名", 
case when (w.from_source=2 and w.shop_recruitment=1) or vma1.store_id is not null then '是' else '否' end "是否店招（是/否）",
isnull(case when vma1.close_time is not null and vma1.close_time<w.shop_recruitment_update_time then vma1.close_time else w.shop_recruitment_update_time end,'2019-01-01') "店招申请时间",
case when datediff(month, isnull((case when vma1.close_time is not null and vma1.close_time<w.shop_recruitment_update_time then vma1.close_time else w.shop_recruitment_update_time end), '2019-01-01'), getdate())=0 then 1 else datediff(month, isnull((case when vma1.close_time is not null and vma1.close_time<w.shop_recruitment_update_time then vma1.close_time else w.shop_recruitment_update_time end), '2019-01-01'), getdate()) end "店招完成至今*个月",
vma1.sign_area "面积", isnull(vma1.settlement_amount,0) "店招费用",
case when st.v1>0 then '是' else '否' end "1月是否订货（是/否）",
case when st.v2>0 then '是' else '否' end "2月是否订货（是/否）",
case when st.v3>0 then '是' else '否' end "3月是否订货（是/否）",
case when st.v4>0 then '是' else '否' end "4月是否订货（是/否）",
case when st.v5>0 then '是' else '否' end "5月是否订货（是/否）",
case when st.v6>0 then '是' else '否' end "6月是否订货（是/否）",
case when st.v7>0 then '是' else '否' end "7月是否订货（是/否）",
case when st.v9>0 then '是' else '否' end "9月是否订货（是/否）",
case when st.v10>0 then '是' else '否' end "10月是否订货（是/否）",
case when st.v11>0 then '是' else '否' end "11月是否订货（是/否）",
dst1.Quantity "DMS数据（2020年总销量）",st.v "商城数据（2020年总销量）",
cst.v "SP/经销商确认的数字（2020年总销量）",
dst1.snaQuantity "DMS数据（2020年SN销量）",
st.snav "商城数据（2020年SN销量）",
cst.snav "SP/经销商确认的数字（2020年SN销量）",
case when w.shop_recruitment_update_time<'2020-01-01' then st.v/11 else st.v/(case when datediff(month, w.shop_recruitment_update_time, getdate())=0 then 1 else datediff(month, w.shop_recruitment_update_time, getdate()) end) end "平均月销量",
case when w.shop_recruitment_update_time<'2020-01-01' then st.snav/11 else st.snav/(case when datediff(month, w.shop_recruitment_update_time, getdate())=0 then 1 else datediff(month, w.shop_recruitment_update_time, getdate()) end) end "平均月SN销量",
case when st.v8_11>0 then '活跃客户' when st.v>0 then '不活跃客户' else '流失客户' end "门店状态"
	--into temp20201109
from wx_t_work_shop w 
--left join view_mkt_apply v on w.id=v.workshop_id and v.mkt_type like 'STORE_%'
--left join wx_t_mkt_cdm_apply a on a.store_id=w.id and a.sign_type like 'STORE_%'
left join wx_t_workshop_partner wp on wp.workshop_id=w.id
left join wx_t_partner_o2o_enterprise pe on pe.partner_id=wp.partner_id
left join wx_t_organization o on o.id=pe.partner_id
left join (select sum(vma.settlement_amount) settlement_amount, min(vma.close_time) close_time, vma.store_id,
sum(case when vma.rn=1 then vma.sign_area else 0 end) sign_area from (
select *,ROW_NUMBER() OVER (PARTITION BY x.store_id ORDER BY x.close_time DESC) rn from (
select wi.close_time, a.store_id, a.settlement_amount,
(Select CASE when StringValue is null or StringValue='' or ISNUMERIC(StringValue)=0 then 0 else convert(float, StringValue) end from parseJSON(a.store_sign_info) where name='signboardHeight')
*(Select CASE when StringValue is null or StringValue='' or ISNUMERIC(StringValue)=0 then 0 else convert(float, StringValue) end from parseJSON(a.store_sign_info) where name='signboardWidth') sign_area
from wx_t_mkt_ci_apply a left join wx_t_work_shop w on w.id=a.store_id
left join wx_t_workflow_instance wi on wi.workflow_code='CI_APPLY' and wi.form_key=a.id
where a.form_status>=60
union all 
select v.audit_time3, v.workshop_id, v.price,
(SELECT field_value FROM dbo.wx_t_mkt_field mf WHERE v.id = mkt_apply_id AND field_name = 'signArea') sign_area 
from view_mkt_apply v 
where v.mkt_type  in ('STORE_IN_STORE', 'STORE_FRONT') and v.price>0 and v.workshop_id is not null
and v.audit_time3 is not null and v.channel='C&I') x) vma
group by vma.store_id) vma1 on vma1.store_id=w.id
left join (SELECT SUM(wol.amount * CONVERT(FLOAT, tp.capacity)) AS v
	, wto.work_shop_id,
	sum(case when month(wto.create_time)=1 then wol.amount * CONVERT(FLOAT, tp.capacity) else 0 end) v1,
	sum(case when month(wto.create_time)=2 then wol.amount * CONVERT(FLOAT, tp.capacity) else 0 end) v2,
	sum(case when month(wto.create_time)=3 then wol.amount * CONVERT(FLOAT, tp.capacity) else 0 end) v3,
	sum(case when month(wto.create_time)=4 then wol.amount * CONVERT(FLOAT, tp.capacity) else 0 end) v4,
	sum(case when month(wto.create_time)=5 then wol.amount * CONVERT(FLOAT, tp.capacity) else 0 end) v5,
	sum(case when month(wto.create_time)=6 then wol.amount * CONVERT(FLOAT, tp.capacity) else 0 end) v6,
	sum(case when month(wto.create_time)=7 then wol.amount * CONVERT(FLOAT, tp.capacity) else 0 end) v7,
	sum(case when month(wto.create_time)=8 then wol.amount * CONVERT(FLOAT, tp.capacity) else 0 end) v8,
	sum(case when month(wto.create_time)=9 then wol.amount * CONVERT(FLOAT, tp.capacity) else 0 end) v9,
	sum(case when month(wto.create_time)=10 then wol.amount * CONVERT(FLOAT, tp.capacity) else 0 end) v10,
	sum(case when month(wto.create_time)=11 then wol.amount * CONVERT(FLOAT, tp.capacity) else 0 end) v11,
	sum(case when month(wto.create_time)>=8 then wol.amount * CONVERT(FLOAT, tp.capacity) else 0 end) v8_11,
	sum(case when tp.product_property='SN Above' then wol.amount * CONVERT(FLOAT, tp.capacity) else 0 end) snav
FROM wx_t_order wto
	LEFT JOIN wx_t_order_line wol ON wto.id = wol.order_id
	LEFT JOIN wx_t_product tp ON wol.sku = tp.sku
	LEFT JOIN wx_t_work_shop ws ON ws.id = wto.work_shop_id
	LEFT JOIN wx_T_workshop_partner wp ON ws.id = wp.workshop_id
WHERE wto.status IN (10, 11)
	AND ws.delete_flag = 0
	AND wol.id IS NOT NULL
	AND wto.create_time >= '2020-01-01'
GROUP BY wto.work_shop_id) st on st.work_shop_id=w.id
left join (SELECT
	tt_order.work_shop_id,
	SUM (
		wol.amount * CONVERT (FLOAT, tp.capacity)
	) v,
	sum(case when tp.product_property='SN Above' then wol.amount * CONVERT(FLOAT, tp.capacity) else 0 end) snav
FROM
	wx_t_order tt_order
LEFT JOIN wx_t_order_line wol ON tt_order.id = wol.order_id
LEFT JOIN wx_t_product tp ON wol.sku = tp.sku
left join wx_t_work_shop tt_workshop on tt_workshop.id=tt_order.work_shop_id
WHERE
	1 = 1
AND tt_order.create_time >= '2020-04-01 00:00:00.000'
AND tt_order.status = '11'
AND tt_workshop.from_source = '1' --	AND tt_order.id = 196191
GROUP BY
	tt_order.work_shop_id) cst on cst.work_shop_id=w.id
left join (select dst.distributorCode,dst.customerCode, sum(dst.Quantity) Quantity,
sum(case when p.product_property='SN Above' then dst.Quantity else 0 end) snaQuantity 
from dw_dms_Data_Interface_Sell_Through dst
left join wx_t_product p on dst.Standardgoodscode=p.sku collate Chinese_PRC_CI_AS
where dst.saleDate>='2020-01-01'
group by dst.distributorCode,dst.customerCode) dst1 on dst1.distributorCode+'/'+dst1.customerCode=w.dms_key collate Chinese_PRC_CI_AI
where (w.from_source|w.business_weight)&2>0 and w.status='3' and w.delete_flag=0 and o.id!=9 --and w.id=167030
order by (select top 1 vcr.region_name from view_customer_region_sales_channel vcr where vcr.distributor_id=pe.distributor_id order by case when vcr.bu='Indirect' then 0 else 1 end),
o.id