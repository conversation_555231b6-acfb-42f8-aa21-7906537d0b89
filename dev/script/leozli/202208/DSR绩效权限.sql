insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_Customer_Service'), 'Dsr.kpi', -1, 1, '', 1, getdate());
insert into wx_t_subject_res_permission (subject_type, subject_id, resource_id,permission_type_id,creation_time,created_by) values ('ROLE',(select r.role_id from wx_t_role r where r.ch_role_name='Chevron_Customer_Service'),(select resource_id from wx_t_resource r left join wx_t_module m on r.module_id=m.module_id where r.resource_name='DSR绩效' and m.module_name='绩效管理'),(select permission_type_id from wx_t_res_permission_type where permission_type_desc='所有合伙人' and resource_type_code='SP'),getdate(),1)
