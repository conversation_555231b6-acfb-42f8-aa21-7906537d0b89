{"version": 3, "sources": ["webpack:///./src/components/comment/_components/list.vue?85fd", "webpack:///./src/components/comment/_components/delete.vue?2c59", "webpack:///src/components/comment/_components/delete.vue", "webpack:///./src/components/comment/_components/delete.vue?53ab", "webpack:///./src/components/comment/_components/delete.vue?c0dd", "webpack:///src/components/comment/_components/list.vue", "webpack:///./src/components/comment/_components/list.vue?8915", "webpack:///./src/components/comment/_components/list.vue", "webpack:///./node_modules/core-js/modules/es6.array.find-index.js", "webpack:///./src/views/qbr/text/promotion/index.vue?a4f5", "webpack:///./src/views/qbr/text/promotion/_pieces/doorhead/index.vue?cfeb", "webpack:///./src/views/qbr/text/promotion/_pieces/doorhead/_resources/structure.js", "webpack:///src/views/qbr/text/promotion/_pieces/doorhead/index.vue", "webpack:///./src/views/qbr/text/promotion/_pieces/doorhead/index.vue?2998", "webpack:///./src/views/qbr/text/promotion/_pieces/doorhead/index.vue", "webpack:///./src/views/qbr/text/promotion/_pieces/activity/index.vue?3ec1", "webpack:///./src/views/qbr/text/promotion/_pieces/activity/_resources/structure.js", "webpack:///src/views/qbr/text/promotion/_pieces/activity/index.vue", "webpack:///./src/views/qbr/text/promotion/_pieces/activity/index.vue?0674", "webpack:///./src/views/qbr/text/promotion/_pieces/activity/index.vue", "webpack:///src/views/qbr/text/promotion/index.vue", "webpack:///./src/views/qbr/text/promotion/index.vue?9da4", "webpack:///./src/views/qbr/text/promotion/index.vue", "webpack:///./src/components/comment/_components/delete.vue?d72d", "webpack:///./src/components/comment/text/index.vue?04f7", "webpack:///src/components/comment/text/index.vue", "webpack:///./src/components/comment/text/index.vue?a1b7", "webpack:///./src/components/comment/text/index.vue", "webpack:///./src/components/comment/_components/list.vue?6702", "webpack:///./src/components/comment/_components/form.vue?2397", "webpack:///src/components/comment/_components/form.vue", "webpack:///./src/components/comment/_components/form.vue?a3d7", "webpack:///./src/components/comment/_components/form.vue", "webpack:///./src/resources/service/comment.js", "webpack:///./src/components/upload/file.vue?e996", "webpack:///src/components/upload/file.vue", "webpack:///./src/components/upload/file.vue?e80a", "webpack:///./src/components/upload/file.vue", "webpack:///./src/resources/plugin/bus.js"], "names": ["render", "_vm", "this", "_h", "$createElement", "_c", "_self", "staticClass", "_l", "item", "key", "id", "_v", "_s", "commentByPersonName", "_f", "Date", "createDate", "commentBy", "userInfo", "userId", "attrs", "on", "delete<PERSON><PERSON><PERSON>", "_e", "content", "path", "$event", "previewImage", "paths", "list", "length", "loadMore", "staticRenderFns", "directives", "name", "rawName", "value", "show", "expression", "confirm", "submit", "cancel", "props", "data", "loading", "methods", "comment", "status", "component", "components", "DeleteComment", "start", "end", "busId", "computed", "watch", "dealerParams", "getComments", "onCommentUpdate", "created", "indexOf", "push", "bus", "commentId", "dealerId", "limit", "comments", "map", "$store", "commit", "filesPath", "file", "splice", "index", "$export", "$find", "KEY", "forced", "Array", "P", "F", "findIndex", "callbackfn", "arguments", "undefined", "structure", "updateData", "type", "inputType", "label", "table", "scope", "column", "quarterArray", "Number", "keyContent", "options", "min", "max", "SingleEditTable", "getTableData", "year", "dealerYear", "quarter", "category", "params", "Comment", "Doorhead", "Activity", "title", "listSpan", "formSpan", "formOffset", "hasFile", "FormView", "ListView", "String", "Boolean", "default", "env", "mobile", "ref", "ruleForm", "rules", "staticStyle", "model", "callback", "$$v", "$set", "fileList", "UploadFile", "required", "$refs", "validate", "valid", "CommentService", "xhr", "method", "jsonrpc", "targetId", "partnerId", "attIds", "BASEURL", "TOKENPARAMS", "handleRemove", "handleSuccess", "userToken", "$emit", "<PERSON><PERSON>"], "mappings": "kHAAA,IAAIA,EAAS,WAAa,IAAIC,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,MAAM,CAACE,YAAY,WAAW,CAACN,EAAIO,GAAIP,EAAQ,MAAE,SAASQ,GAAM,OAAOJ,EAAG,MAAM,CAACK,IAAID,EAAKE,IAAI,CAACN,EAAG,MAAM,CAACA,EAAG,OAAO,CAACE,YAAY,WAAW,CAACN,EAAIW,GAAG,aAAaX,EAAIY,GAAGJ,EAAKK,qBAAqB,cAAcT,EAAG,OAAO,CAACE,YAAY,UAAU,CAACN,EAAIW,GAAG,aAAaX,EAAIY,GAAGZ,EAAIc,GAAG,cAAPd,CAAsB,IAAIe,KAAKP,EAAKQ,YAAY,wBAAyB,cAAeR,EAAKS,YAAcjB,EAAIkB,SAASC,OAAQf,EAAG,iBAAiB,CAACgB,MAAM,CAAC,GAAKZ,EAAKE,IAAIW,GAAG,CAAC,OAASrB,EAAIsB,iBAAiBtB,EAAIuB,MAAM,GAAGnB,EAAG,MAAM,CAACE,YAAY,aAAa,CAACN,EAAIW,GAAGX,EAAIY,GAAGJ,EAAKgB,YAAahB,EAAU,MAAEJ,EAAG,MAAM,CAACE,YAAY,iBAAiBN,EAAIO,GAAIC,EAAU,OAAE,SAASiB,GAAM,OAAOrB,EAAG,MAAM,CAACK,IAAIgB,EAAKnB,YAAY,UAAUc,MAAM,CAAC,IAAMK,EAAK,IAAM,IAAIJ,GAAG,CAAC,MAAQ,SAASK,GAAQ,OAAO1B,EAAI2B,aAAanB,EAAKoB,MAAOH,UAAY,GAAGzB,EAAIuB,UAAUvB,EAAO,IAAEI,EAAG,MAAM,CAAsB,IAApBJ,EAAI6B,KAAKC,OAAc1B,EAAG,MAAM,CAACE,YAAY,SAAS,CAACN,EAAIW,GAAG,0BAA2BX,EAAI6B,KAAKC,OAAS,GAAI1B,EAAG,MAAM,CAACE,YAAY,SAAS,CAACN,EAAIW,GAAG,2BAA2BX,EAAIuB,OAAQvB,EAAW,QAAEI,EAAG,MAAM,CAACA,EAAG,MAAM,CAACE,YAAY,aAAa,CAACN,EAAIW,GAAG,YAAaX,EAAI6B,KAAKC,OAAS,EAAG1B,EAAG,MAAM,CAACA,EAAG,MAAM,CAACE,YAAY,SAASe,GAAG,CAAC,MAAQrB,EAAI+B,WAAW,CAAC/B,EAAIW,GAAG,YAAYX,EAAIuB,MAAM,IAC/xCS,EAAkB,G,oCCDlB,EAAS,WAAa,IAAIhC,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,OAAO,CAAEJ,EAAW,QAAEI,EAAG,OAAO,CAACE,YAAY,WAAW,CAACN,EAAIW,GAAG,UAAU,CAACP,EAAG,OAAO,CAAC6B,WAAW,CAAC,CAACC,KAAK,OAAOC,QAAQ,SAASC,OAAQpC,EAAIqC,KAAMC,WAAW,UAAUhC,YAAY,SAASe,GAAG,CAAC,MAAQrB,EAAIuC,UAAU,CAACvC,EAAIW,GAAG,QAAQP,EAAG,OAAO,CAAC6B,WAAW,CAAC,CAACC,KAAK,OAAOC,QAAQ,SAASC,MAAOpC,EAAQ,KAAEsC,WAAW,SAAShC,YAAY,SAASe,GAAG,CAAC,MAAQrB,EAAIwC,SAAS,CAACxC,EAAIW,GAAG,UAAUP,EAAG,OAAO,CAAC6B,WAAW,CAAC,CAACC,KAAK,OAAOC,QAAQ,SAASC,MAAOpC,EAAQ,KAAEsC,WAAW,SAAShC,YAAY,gBAAgBe,GAAG,CAAC,MAAQrB,EAAIyC,SAAS,CAACzC,EAAIW,GAAG,UAAU,IAChoB,EAAkB,G,YCatB,GACEuB,KAAM,iBACNQ,MAAO,CAAC,MACRC,KAHF,WAII,MAAO,CACLN,MAAM,EACNO,SAAS,IAGbC,QAAS,CACPN,QADJ,WAEMtC,KAAKoC,MAAO,GAEdI,OAJJ,WAKMxC,KAAKoC,MAAO,GAEdG,OAPJ,WAOA,WACMvC,KAAK2C,SAAU,EACfE,EAAN,oFAGQ,GAFA,EAAR,YAEaC,EAAQ,OAAO,EAEpB,EAAR,2BCrC8W,I,wBCQ1WC,EAAY,eACd,EACA,EACA,GACA,EACA,KACA,WACA,MAIa,EAAAA,E,gCC2Cf,GACEd,KAAM,eACNe,WAAY,CACVC,cAAJ,GAEER,MAAO,CAAC,MACRC,KANF,WAOI,MAAO,CACLd,KAAN,GACMsB,MAAN,EACMC,KAAN,EACMR,SAAN,EACMS,MAAN,KAGEC,SAAU,OAAZ,OAAY,CAAZ,GACA,sEAEEC,MAAO,CACLC,aADJ,WAEMvD,KAAKwD,eAEP/C,GAJJ,SAIA,GACMT,KAAK4B,KAAO,GACZ5B,KAAKkD,MAAQ,EACblD,KAAKmD,KAAM,EACXnD,KAAKwD,cACLxD,KAAKyD,gBAAgBtB,KAGzBuB,QA9BF,WA+BI1D,KAAKwD,cACLxD,KAAKyD,gBAAgBzD,KAAKS,KAE5BmC,QAAS,CACPa,gBADJ,SACA,eACyC,IAA/BzD,KAAKoD,MAAMO,QAAQxB,KACrBnC,KAAKoD,MAAMQ,KAAKzB,GAEhB0B,EAAR,kDACU,EAAV,QACU,EAAV,QACU,EAAV,OAEU,EAAV,mBAIIL,YAdJ,WAcA,WACM,GAAIxD,KAAK2C,QAAS,OAAO,EACzB3C,KAAK2C,SAAU,EAEI,IAAf3C,KAAKkD,QACPlD,KAAK4B,KAAO,IAEdiB,EAAN,kBACQiB,UAAR,QACQC,SAAR,cACQb,MAAR,WACQc,MAAR,KACA,0DAGQ,GAFA,EAAR,YAEalB,EAAQ,OAAO,EAEpB,IAAR,qBACYmB,EAASpC,OAAS,KACpB,EAAV,QAEgC,IAApBoC,EAASpC,QACXoC,EAASC,KAAI,SAAvB,GACgB3D,EAAKoB,QACPpB,EAAKoB,MAAQpB,EAAKoB,MAAMuC,KAAI,SAA1C,GACgB,OAAO,EAAvB,mBAGY,EAAZ,oBAKIpC,SA/CJ,WAgDM9B,KAAKkD,MAAQlD,KAAK4B,KAAK5B,KAAK4B,KAAKC,OAAvC,MACM7B,KAAKwD,eAEP9B,aAnDJ,SAmDA,KACM1B,KAAKmE,OAAOC,OAAO,aAAc,CAC/BC,UAAWA,EACXC,KAAMA,KAGVjD,cAzDJ,SAyDA,GACM,IAAN,sDACMrB,KAAK4B,KAAK2C,OAAOC,EAAO,MC3J8U,ICQxW,G,UAAY,eACd,EACA1E,EACAiC,GACA,EACA,KACA,WACA,OAIa,S,6CCjBf,IAAI0C,EAAU,EAAQ,QAClBC,EAAQ,EAAQ,OAAR,CAA4B,GACpCC,EAAM,YACNC,GAAS,EAETD,IAAO,IAAIE,MAAM,GAAGF,IAAK,WAAcC,GAAS,KACpDH,EAAQA,EAAQK,EAAIL,EAAQM,EAAIH,EAAQ,QAAS,CAC/CI,UAAW,SAAmBC,GAC5B,OAAOP,EAAM1E,KAAMiF,EAAYC,UAAUrD,OAAS,EAAIqD,UAAU,QAAKC,MAGzE,EAAQ,OAAR,CAAiCR,I,2CCbjC,IAAI7E,EAAS,WAAa,IAAIC,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,MAAM,CAACA,EAAG,KAAK,CAACJ,EAAIW,GAAG,UAAUP,EAAG,UAAU,CAACgB,MAAM,CAAC,GAAK,4BAA4B,MAAQ,gBAAgB,YAAW,KAAQhB,EAAG,UAAU,CAACgB,MAAM,CAAC,GAAK,4BAA4B,MAAQ,gBAAgB,YAAW,KAAQhB,EAAG,UAAU,CAACgB,MAAM,CAAC,GAAK,8BAA8B,MAAQ,mBAAmB,YAAW,KAAQhB,EAAG,KAAK,CAACJ,EAAIW,GAAG,WAAWP,EAAG,YAAYA,EAAG,KAAK,CAACJ,EAAIW,GAAG,aAAaP,EAAG,aAAa,IAC/f4B,EAAkB,G,YCDlB,EAAS,WAAa,IAAIhC,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,oBAAoB,CAACgB,MAAM,CAAC,GAAK,sBAAsB,UAAYpB,EAAIqF,UAAU,QAAUrF,EAAI4C,SAASvB,GAAG,CAAC,cAAcrB,EAAIsF,eAC7N,EAAkB,G,gDCDP,G,UAAA,CAAC,CACdC,KAAM,SACNC,UAAW,SACX/E,IAAK,YACLqC,SAAS,EACT2C,MAAO,yBACN,CACDF,KAAM,SACNC,UAAW,SACX/E,IAAK,WACLqC,SAAS,EACT2C,MAAO,0BACN,CACDF,KAAM,SACNC,UAAW,SACX/E,IAAK,eACLqC,SAAS,EACT2C,MAAO,yBACN,CACDF,KAAM,SACNC,UAAW,SACX/E,IAAK,cACLqC,SAAS,EACT2C,MAAO,0BACL,CACFF,KAAM,YACN9E,IAAK,QACL2B,MAAO,SAAUsD,EAAOC,GACtB,IAAMF,EAAQE,EAAMC,OAAOH,MACrBI,EAAe,CAAC,OAAQ,OAAQ,OAAQ,QACxCpB,EAAQoB,EAAajC,QAAQ6B,GAAS,EAE5C,OAAQK,OAAOJ,EAAM,GAAG,QAAQjB,KAAW,IACrCqB,OAAOJ,EAAM,GAAG,QAAQjB,KAAW,IACnCqB,OAAOJ,EAAM,GAAG,QAAQjB,KAAW,IACnCqB,OAAOJ,EAAM,GAAG,QAAQjB,KAAW,IAE3CgB,MAAO,SACN,CACDF,KAAM,SACN9E,IAAK,eACLsF,WAAY,oBACZN,MAAO,OACPO,QAAS,CAAC,CACN5D,MAAO,EACPqD,MAAO,SACN,CACDrD,MAAO,EACPqD,MAAO,aACN,CACDrD,MAAO,EACPqD,MAAO,mBACN,CACDrD,MAAO,EACPqD,MAAO,eAEV,CACDF,KAAM,SACNU,IAAK,EACLC,IAAK,GACLzF,IAAK,QACLgF,MAAO,c,wBC1CT,GACEvD,KAAM,0BACNe,WAAY,CACVkD,gBAAJ,QAEExD,KALF,WAMI,MAAO,CACLC,SAAS,EACTyC,UAAN,IAGE/B,SAAU,OAAZ,OAAY,CAAZ,GACA,0EAEEC,MAAO,CACLC,aADJ,WAEMvD,KAAKmG,iBAGTzC,QAnBF,WAoBI1D,KAAKmG,gBAEPvD,QAAS,CACPuD,aADJ,WACA,WACMnG,KAAK2C,SAAU,EACf,OAAN,OAAM,CAAN,gBACM,EAAN,kBACQoB,SAAU/D,KAAK+D,SACfqC,KAAMpG,KAAKqG,WACXC,QAAS,EACTC,SAAU,QAClB,0DAGQ,GAFA,EAAR,YAEazD,EAAQ,OAAO,EAEpB,IAAR,qBACQ,OAAR,OAAQ,CAAR,mBAGIuC,WAlBJ,SAkBA,cACA,GACQtB,SAAU/D,KAAK+D,SACfqC,KAAMpG,KAAKqG,WACXC,QAASA,EACTC,SAAU,OAEZC,EAAS,OAAf,OAAe,CAAf,kBACM,EAAN,0EACQ,IAAK1D,EAAQ,OAAO,EAA5B,eACU,MAAV,OACU,SAAV,IACU,QAAV,0BCvE0Z,I,YCOtZC,EAAY,eACd,EACA,EACA,GACA,EACA,KACA,KACA,MAIa,EAAAA,E,QClBX,EAAS,WAAa,IAAIhD,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,oBAAoB,CAACgB,MAAM,CAAC,UAAYpB,EAAIqF,UAAU,QAAUrF,EAAI4C,SAASvB,GAAG,CAAC,cAAcrB,EAAIsF,eAClM,EAAkB,GCDP,GAAC,CACdC,KAAM,SACN9E,IAAK,cACLgF,MAAO,0BACN,CACDF,KAAM,SACN9E,IAAK,iBACLgF,MAAO,iBACN,CACDF,KAAM,SACN9E,IAAK,QACLgF,MAAO,WACN,CACDF,KAAM,SACN9E,IAAK,eACLsF,WAAY,oBACZN,MAAO,OACPO,QAAS,CAAC,CACN5D,MAAO,EACPqD,MAAO,SACN,CACDrD,MAAO,EACPqD,MAAO,aACN,CACDrD,MAAO,EACPqD,MAAO,mBACN,CACDrD,MAAO,EACPqD,MAAO,eAEV,CACDF,KAAM,SACNU,IAAK,EACLC,IAAK,GACLzF,IAAK,QACLgF,MAAO,aCjBT,GACEvD,KAAM,0BACNe,WAAY,CACVkD,gBAAJ,QAEExD,KALF,WAMI,MAAO,CACLC,SAAS,EACTyC,UAAN,IAGE/B,SAAU,OAAZ,OAAY,CAAZ,GACA,0EAEEC,MAAO,CACLC,aADJ,WAEMvD,KAAKmG,iBAGTzC,QAnBF,WAoBI1D,KAAKmG,gBAEPvD,QAAS,CACPuD,aADJ,WACA,WACMnG,KAAK2C,SAAU,EACf,OAAN,OAAM,CAAN,gBACM,EAAN,kBACQoB,SAAU/D,KAAK+D,SACfqC,KAAMpG,KAAKqG,WACXC,QAAS,EACTC,SAAU,QAClB,0DAGQ,GAFA,EAAR,YAEazD,EAAQ,OAAO,EAEpB,IAAR,qBACQ,OAAR,OAAQ,CAAR,mBAGIuC,WAlBJ,SAkBA,cACA,GACQtB,SAAU/D,KAAK+D,SACfqC,KAAMpG,KAAKqG,WACXC,QAASA,EACTC,SAAU,OAEZC,EAAS,OAAf,OAAe,CAAf,kBACM,EAAN,0EACQ,IAAK1D,EAAQ,OAAO,EAA5B,eACU,MAAV,OACU,SAAV,IACU,QAAV,0BCtE0Z,ICOtZ,EAAY,eACd,EACA,EACA,GACA,EACA,KACA,KACA,MAIa,I,QCWf,GACEb,KAAM,iBACNe,WAAY,CACVyD,QAAJ,OACIC,SAAJ,EACIC,SAAJ,IClC4X,ICOxX,EAAY,eACd,EACA7G,EACAiC,GACA,EACA,KACA,KACA,MAIa,e,sEClBf,yBAAsgB,EAAG,G,oCCAzgB,IAAIjC,EAAS,WAAa,IAAIC,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,MAAM,CAAEJ,EAAS,MAAEI,EAAG,KAAK,CAACJ,EAAIW,GAAGX,EAAIY,GAAGZ,EAAI6G,UAAU7G,EAAIuB,KAAKnB,EAAG,SAAS,CAACA,EAAG,SAAS,CAACgB,MAAM,CAAC,KAAOpB,EAAI8G,WAAW,CAAC1G,EAAG,YAAY,CAACgB,MAAM,CAAC,GAAKpB,EAAIU,OAAO,GAAGN,EAAG,SAAS,CAACgB,MAAM,CAAC,KAAOpB,EAAI+G,SAAS,OAAS/G,EAAIgH,aAAa,CAAC5G,EAAG,YAAY,CAACgB,MAAM,CAAC,GAAKpB,EAAIU,GAAG,WAAWV,EAAIiH,YAAY,IAAI,IAAI,IAC3YjF,EAAkB,G,gDCoBtB,GACEE,KAAM,eACNe,WAAY,CACViE,SAAJ,OACIC,SAAJ,QAEEzE,MAAO,CACLhC,GAAI0G,OACJP,MAAOO,OACPH,QAAS,CACP1B,KAAM8B,QACNC,SAAS,IAGbhE,SAAU,OAAZ,OAAY,CAAZ,GACA,wBADA,CAEIwD,SAFJ,WAGM,OAAO7G,KAAKsH,IAAIC,OAAS,GAAK,IAEhCT,SALJ,WAMM,OAAO9G,KAAKsH,IAAIC,OAAS,GAAK,IAEhCR,WARJ,WASM,OAAO/G,KAAKsH,IAAIC,OAAS,EAAI,MC5C0U,I,YCOzWxE,EAAY,eACd,EACAjD,EACAiC,GACA,EACA,KACA,KACA,MAIa,OAAAgB,E,6CClBf,yBAA0kB,EAAG,G,yDCA7kB,IAAIjD,EAAS,WAAa,IAAIC,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,UAAU,CAACqH,IAAI,WAAWrG,MAAM,CAAC,MAAQpB,EAAI0H,SAAS,MAAQ1H,EAAI2H,QAAQ,CAACvH,EAAG,eAAe,CAACwH,YAAY,CAAC,gBAAgB,QAAQxG,MAAM,CAAC,MAAQpB,EAAI6G,MAAM,KAAO,YAAY,CAACzG,EAAG,WAAW,CAACgB,MAAM,CAAC,KAAO,WAAW,YAAc,OAAOyG,MAAM,CAACzF,MAAOpC,EAAI0H,SAAgB,QAAEI,SAAS,SAAUC,GAAM/H,EAAIgI,KAAKhI,EAAI0H,SAAU,UAAWK,IAAMzF,WAAW,uBAAuB,GAAItC,EAAW,QAAEI,EAAG,eAAe,CAACA,EAAG,cAAc,CAACyH,MAAM,CAACzF,MAAOpC,EAAY,SAAE8H,SAAS,SAAUC,GAAM/H,EAAIiI,SAASF,GAAKzF,WAAW,eAAe,GAAGtC,EAAIuB,KAAKnB,EAAG,eAAe,CAACwH,YAAY,CAAC,aAAa,QAAQ,gBAAgB,mBAAmB,CAACxH,EAAG,YAAY,CAACgB,MAAM,CAAC,KAAO,UAAU,KAAO,QAAQ,QAAUpB,EAAI4C,SAASvB,GAAG,CAAC,MAAQrB,EAAIwC,SAAS,CAACxC,EAAIW,GAAG,WAAWX,EAAIY,GAAGZ,EAAI4C,QAAU,MAAQ,MAAM,aAAa,IAAI,IAC73BZ,EAAkB,G,gDCuBtB,GACEE,KAAM,eACNQ,MAAO,CAAC,KAAM,QAAS,WACvBO,WAAY,CACViF,WAAJ,QAEEvF,KANF,WAOI,MAAO,CACL+E,SAAU,CACRlG,QAAS,IAEXyG,SAAU,GACVrF,SAAS,EACT+E,MAAO,CACLnG,QAAS,CAAC,CACR2G,UAAU,EAAV,QAAV,QAAU,QAAV,YAKEtF,QAAS,CACPL,OADJ,WACA,WACMvC,KAAKmI,MAAM,YAAYC,UAAS,SAAtC,GACQ,IAAIC,EAoBF,OAAO,GAnBH,EAAd,8BAGU,EAAV,WACUxF,EAAV,kBACYiB,UAAW,EAAvB,GACYC,SAAU,EAAtB,wBACYxC,QAAS,EAArB,iBACYyG,SAAU,EAAtB,WACA,mDAGY,GAFA,EAAZ,YAEiBlF,EAAQ,OAAO,EAEpBe,EAAZ,sCACY,EAAZ,oBACY,EAAZ,sBChE4W,I,YCOxWd,EAAY,eACd,EACAjD,EACAiC,GACA,EACA,KACA,KACA,MAIa,OAAAgB,E,mFChBTuF,E,0GACS5F,GACX,OAAO6F,eAAI,CACTC,OAAQ,OACRhH,KAAM,iBACNkB,KAAM,CACJ+F,QAAS,MACTD,OAAQ,gCACRhC,OAAQ,CAAC,CACPkC,SAAUhG,EAAKoB,UACf6E,UAAWjG,EAAKqB,SAChBxC,QAASmB,EAAKnB,QACdqH,OAAQlG,EAAKsF,WAEfvH,GAAI,O,kCAKGiC,GACX,OAAO6F,eAAI,CACTC,OAAQ,OACRhH,KAAM,iBACNkB,KAAM,CACJ+F,QAAS,MACTD,OAAQ,gCACRhC,OAAQ,CAAC,CACPkC,SAAUhG,EAAKoB,UACf6E,UAAWjG,EAAKqB,SAChBb,MAAOR,EAAKQ,MACZc,MAAOtB,EAAKsB,QAEdvD,GAAI,O,oCAKKiC,GACb,OAAO6F,eAAI,CACTC,OAAQ,OACRhH,KAAM,iBACNkB,KAAM,CACJ+F,QAAS,MACTD,OAAQ,6BACRhC,OAAQ,CAAC,CACP/F,GAAIiC,EAAKjC,KAEXA,GAAI,S,KAMG,WAAI6H,G,kCCvDnB,IAAIxI,EAAS,WAAa,IAAIC,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,YAAY,CAACE,YAAY,cAAcc,MAAM,CAAC,OAAUpB,EAAI8I,QAAU,sCAAwC9I,EAAI+I,YAAa,YAAY/I,EAAIgJ,aAAa,aAAahJ,EAAIiJ,cAAc,YAAYjJ,EAAIiI,SAAS,KAAOjI,EAAI2C,KAAK,KAAO,SAAS,SAAW,KAAK,CAACvC,EAAG,YAAY,CAACgB,MAAM,CAAC,KAAO,QAAQ,KAAO,SAAS,CAACpB,EAAIW,GAAG,aAAa,IAC/aqB,EAAkB,G,wBCgBtB,GACEE,KAAM,cACNQ,MAAO,CAAC,QAAS,QACjBC,KAHF,WAII,MAAO,CACLsF,SAAU,GACVa,QAAS,KAGbxF,SAAU,OAAZ,OAAY,CAAZ,GACA,8BADA,CAEIyF,YAFJ,WAGM,OAAO9I,KAAKiJ,UAAY,YAA9B,6BAGE3F,MAAO,CACLnB,MADJ,SACA,GACA,IAAU,EAAV,SACQnC,KAAKgI,SAAW,MAItBpF,QAAS,CACPoG,cADJ,SACA,OACM,IAAN,qDACMhJ,KAAKkJ,MAAM,QAAStH,IAEtBmH,aALJ,SAKA,KACM,IAAN,qDACM/I,KAAKkJ,MAAM,QAAStH,MC9CmU,I,YCOzVmB,EAAY,eACd,EACAjD,EACAiC,GACA,EACA,KACA,KACA,MAIa,OAAAgB,E,2CClBf,gBACe,WAAIoG", "file": "js/chunk-9ba4d316.37b1c6a7.js", "sourcesContent": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"l-panel\"},[_vm._l((_vm.list),function(item){return _c('div',{key:item.id},[_c('div',[_c('span',{staticClass:\"l-title\"},[_vm._v(\"\\n        \"+_vm._s(item.commentByPersonName)+\"\\n      \")]),_c('span',{staticClass:\"l-time\"},[_vm._v(\"\\n        \"+_vm._s(_vm._f(\"format-date\")(new Date(item.createDate),'YYYY-MM-DD HH:mm:ss' ))+\"\\n      \")]),(item.commentBy === _vm.userInfo.userId)?_c('delete-comment',{attrs:{\"id\":item.id},on:{\"delete\":_vm.deleteHandler}}):_vm._e()],1),_c('div',{staticClass:\"l-content\"},[_vm._v(_vm._s(item.content))]),(item.paths)?_c('div',{staticClass:\"l-image-panel\"},_vm._l((item.paths),function(path){return _c('img',{key:path,staticClass:\"l-image\",attrs:{\"src\":path,\"alt\":\"\"},on:{\"click\":function($event){return _vm.previewImage(item.paths, path)}}})}),0):_vm._e()])}),(_vm.end)?_c('div',[(_vm.list.length === 0)?_c('div',{staticClass:\"l-end\"},[_vm._v(\"\\n      暂时没有数据\\n    \")]):(_vm.list.length > 10)?_c('div',{staticClass:\"l-end\"},[_vm._v(\"\\n      没有更多数据了\\n    \")]):_vm._e()]):(_vm.loading)?_c('div',[_c('div',{staticClass:\"l-loading\"},[_vm._v(\"正在加载\")])]):(_vm.list.length > 0)?_c('div',[_c('div',{staticClass:\"l-more\",on:{\"click\":_vm.loadMore}},[_vm._v(\"加载更多\")])]):_vm._e()],2)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('span',[(_vm.loading)?_c('span',{staticClass:\"loading\"},[_vm._v(\"正在删除\")]):[_c('span',{directives:[{name:\"show\",rawName:\"v-show\",value:(!_vm.show),expression:\"!show\"}],staticClass:\"delete\",on:{\"click\":_vm.confirm}},[_vm._v(\"删除\")]),_c('span',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.show),expression:\"show\"}],staticClass:\"delete\",on:{\"click\":_vm.submit}},[_vm._v(\"确定删除\")]),_c('span',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.show),expression:\"show\"}],staticClass:\"cancel-delete\",on:{\"click\":_vm.cancel}},[_vm._v(\"取消\")])]],2)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <span>\n    <span class=\"loading\" v-if=\"loading\">正在删除</span>\n    <template v-else>\n      <span class=\"delete\" @click=\"confirm\" v-show=\"!show\">删除</span>\n      <span class=\"delete\" @click=\"submit\" v-show=\"show\">确定删除</span>\n      <span class=\"cancel-delete\" @click=\"cancel\" v-show=\"show\">取消</span>\n    </template>\n  </span>\n</template>\n\n<script>\nimport commentService from '@/resources/service/comment.js'\n\nexport default {\n  name: 'delete-comment',\n  props: ['id'],\n  data () {\n    return {\n      show: false,\n      loading: false,\n    }\n  },\n  methods: {\n    confirm () {\n      this.show = true\n    },\n    cancel () {\n      this.show = false\n    },\n    submit () {\n      this.loading = true\n      commentService.deleteComment({id: this.id}).then(([status]) => {\n        this.loading = false\n        \n        if (!status) return false\n\n        this.$emit('delete', this.id)\n      })\n    },\n  }\n}\n</script>\n\n<style scoped>\n.delete {\n  font-size: 12px;\n  color: #e34336;\n  margin-left: 10px;\n}\n.cancel-delete {\n  font-size: 12px;\n  color: #999;\n  margin-left: 10px;\n}\n.loading {\n  font-size: 12px;\n  color: #999;\n  margin-left: 10px;\n}\n</style>", "import mod from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./delete.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./delete.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./delete.vue?vue&type=template&id=65cc4af7&scoped=true&\"\nimport script from \"./delete.vue?vue&type=script&lang=js&\"\nexport * from \"./delete.vue?vue&type=script&lang=js&\"\nimport style0 from \"./delete.vue?vue&type=style&index=0&id=65cc4af7&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"65cc4af7\",\n  null\n  \n)\n\nexport default component.exports", "<template>\n  <div class=\"l-panel\">\n    <div v-for=\"item in list\" :key=\"item.id\">\n      <div>\n        <span class=\"l-title\">\n          {{ item.commentByPersonName }}\n        </span>\n        <span class=\"l-time\">\n          {{ new Date(item.createDate) | format-date('YYYY-MM-DD HH:mm:ss' )}}\n        </span>\n        <delete-comment \n          v-if=\"item.commentBy === userInfo.userId\"\n          :id=\"item.id\"\n          @delete=\"deleteHandler\"/>\n      </div>\n\n      <div class=\"l-content\">{{ item.content }}</div>\n      <div \n        v-if=\"item.paths\"\n        class=\"l-image-panel\">\n        <img\n          class=\"l-image\"\n          v-for=\"path in item.paths\"\n          :key=\"path\"\n          :src=\"path\"\n          alt=\"\"\n          @click=\"previewImage(item.paths, path)\">\n      </div>\n    </div>\n\n    <div v-if=\"end\">\n      <div\n        v-if=\"list.length === 0\"\n        class=\"l-end\">\n        暂时没有数据\n      </div>\n      <div\n        v-else-if=\"list.length > 10\"\n        class=\"l-end\">\n        没有更多数据了\n      </div>\n    </div>\n    <div\n      v-else-if=\"loading\">\n      <div class=\"l-loading\">正在加载</div>\n    </div>\n    <div\n      v-else-if=\"list.length > 0\">\n      <div class=\"l-more\" @click=\"loadMore\">加载更多</div>\n    </div>\n  </div>\n</template>\n\n<script>\n// components\nimport DeleteComment from './delete'\n// service\nimport commentService from '@/resources/service/comment.js'\n// other\nimport { mapGetters } from 'vuex'\nimport bus from '@/resources/plugin/bus.js'\n\nexport default {\n  name: 'comment-list',\n  components: {\n    DeleteComment\n  },\n  props: ['id'],\n  data () {\n    return {\n      list    : [],\n      start   : 0,\n      end     : false,\n      loading : false,\n      busId   : []\n    }\n  },\n  computed: {\n    ...mapGetters(['dealerParams', 'dealerId', 'userInfo', 'integralUrl']),\n  },\n  watch: {\n    dealerParams () {\n      this.getComments()\n    },\n    id (value) {\n      this.list = []\n      this.start = 0\n      this.end = false\n      this.getComments()\n      this.onCommentUpdate(value)\n    },\n  },\n  created () {\n    this.getComments()\n    this.onCommentUpdate(this.id)\n  },\n  methods: {\n    onCommentUpdate (value) {\n      if (this.busId.indexOf(value) === -1) {\n        this.busId.push(value)\n\n        bus.$on('comment-'+this.id+'-update', () => {\n          this.list  = []\n          this.start = 0\n          this.end   = false\n\n          this.getComments()\n        })\n      }\n    },\n    getComments () {\n      if (this.loading) return false\n      this.loading = true\n\n      if (this.start === 0) {\n        this.list = []\n      }\n      commentService.getComments({\n        commentId : this.id,\n        dealerId  : this.dealerId,\n        start     : this.start,\n        limit     : 10\n      }).then(([status, data]) => {\n        this.loading = false\n\n        if (!status) return false\n\n        const comments = data.result.resultLst\n        if (comments.length < 10) {\n          this.end = true\n        }\n        if (comments.length !== 0) {\n          comments.map(item => {\n            if (item.paths) {\n              item.paths = item.paths.map(path => {\n                return this.integralUrl(path)\n              })\n            }\n            this.list.push(item)\n          })\n        }\n      })\n    },\n    loadMore () {\n      this.start = this.list[this.list.length-1].id\n      this.getComments()\n    },\n    previewImage (filesPath, file) {\n      this.$store.commit('SHOW_IMAGE', {\n        filesPath: filesPath,\n        file: file,\n      })\n    },\n    deleteHandler (id) {\n      const index = this.list.findIndex(item => item.id === id)\n      this.list.splice(index, 1)\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n.l-panel {\n  background-color: #eee;\n  padding: 10px 10px 0 10px;\n  height: 180px;\n  overflow-y: auto;\n  -webkit-overflow-scrolling: touch;\n}\n.l-title {\n  font-size: 14px;\n  color: #333;\n}\n.l-time {\n  font-size: 12px;\n  color: #999;\n  margin-left: 10px;\n}\n.l-content {\n  font-size: 16px;\n  color: #333;\n  margin-bottom: 20px;\n}\n.l-image-panel {\n  margin-top: -15px;\n  margin-bottom: 20px;\n}\n.l-image {\n  display: inline-block;\n  max-width: 40px;\n  max-height: 40px;\n  margin-right: 10px;\n}\n.l-loading {\n  font-size: 14px;\n  text-align: center;\n  color: #999;\n  padding: 10px 0;\n}\n.l-more {\n  font-size: 14px;\n  text-align: center;\n  color: #419EFB;\n  padding: 10px 0;\n}\n.l-end {\n  text-align: center;\n  font-size: 14px;\n  color: #999;\n  padding: 10px 0;\n}\n</style>", "import mod from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./list.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./list.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./list.vue?vue&type=template&id=62694798&scoped=true&\"\nimport script from \"./list.vue?vue&type=script&lang=js&\"\nexport * from \"./list.vue?vue&type=script&lang=js&\"\nimport style0 from \"./list.vue?vue&type=style&index=0&id=62694798&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"62694798\",\n  null\n  \n)\n\nexport default component.exports", "'use strict';\n// ******** Array.prototype.findIndex(predicate, thisArg = undefined)\nvar $export = require('./_export');\nvar $find = require('./_array-methods')(6);\nvar KEY = 'findIndex';\nvar forced = true;\n// Shouldn't skip holes\nif (KEY in []) Array(1)[KEY](function () { forced = false; });\n$export($export.P + $export.F * forced, 'Array', {\n  findIndex: function findIndex(callbackfn /* , that = undefined */) {\n    return $find(this, callbackfn, arguments.length > 1 ? arguments[1] : undefined);\n  }\n});\nrequire('./_add-to-unscopables')(KEY);\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',[_c('h2',[_vm._v(\"市场推广\")]),_c('comment',{attrs:{\"id\":\"text-promotion-one-090321\",\"title\":\"1. 经销商组织的市场活动\",\"has-file\":true}}),_c('comment',{attrs:{\"id\":\"text-promotion-two-090321\",\"title\":\"2. 雪佛龙组织的市场活动\",\"has-file\":true}}),_c('comment',{attrs:{\"id\":\"text-promotion-three-090321\",\"title\":\"3. 客户互动（会议）门头促销等\",\"has-file\":true}}),_c('h2',[_vm._v(\"商用油门头\")]),_c('doorhead'),_c('h2',[_vm._v(\"商用油市场活动\")]),_c('activity')],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('single-edit-table',{attrs:{\"id\":\"cio-doorhead-090321\",\"structure\":_vm.structure,\"loading\":_vm.loading},on:{\"update-data\":_vm.updateData}})}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "export default [{\n  type: 'normal',\n  inputType: 'number',\n  key: 'dieselMtk',\n  comment: true,\n  label: '柴机油（商用油）门头/商用油（市场部投资）',\n}, {\n  type: 'normal',\n  inputType: 'number',\n  key: 'otherMtk',\n  comment: true,\n  label: '其他（带雪佛龙LOGO的标识）（市场部投资）',\n}, {\n  type: 'normal',\n  inputType: 'number',\n  key: 'dieselDealer',\n  comment: true,\n  label: '柴机油（商用油）门头/商用油（经销商投资）',\n}, {\n  type: 'normal',\n  inputType: 'number',\n  key: 'otherDealer',\n  comment: true,\n  label: '其他（带雪佛龙LOGO的标识）（经销商投资）',\n},  {\n  type: 'calculate',\n  key: 'total',\n  value: function (table, scope) {\n    const label = scope.column.label\n    const quarterArray = ['第一季度', '第二季度', '第三季度', '第四季度']\n    const index = quarterArray.indexOf(label) + 1\n\n    return (Number(table[0]['value'+index]) || 0)\n       + (Number(table[1]['value'+index]) || 0)\n       + (Number(table[2]['value'+index]) || 0)\n       + (Number(table[3]['value'+index]) || 0)\n  },\n  label: '门头总数据',\n}, {\n  type: 'select',\n  key: 'evaluationId',\n  keyContent: 'evaluationContent',\n  label: '总体评估',\n  options: [{\n      value: 0,\n      label: '目前不需要'\n    }, {\n      value: 1,\n      label: '计划在下个季度开始'\n    }, {\n      value: 2,\n      label: '有所不足，但已有计划以弥补不足'\n    }, {\n      value: 3,\n      label: '表现达到或超过预期'\n    }]\n}, {\n  type: 'number',\n  min: 0,\n  max: 30,\n  key: 'point',\n  label: '评分（30 分）',\n}]", "<template>\n  <single-edit-table\n    id=\"cio-doorhead-090321\"\n    :structure=\"structure\"\n    @update-data=\"updateData\"\n    :loading=\"loading\"/>\n</template>\n\n<script>\n// compeonents\nimport SingleEditTable from '@/components/table/single-edit'\n// service\nimport TableService from '@/resources/service/table'\n// local\nimport structure from './_resources/structure'\nimport { exportParamsFromStructure, updateStructureOfValue, clearStructureOfValue } from '../../../_resources/utils'\n// other\nimport { mapGetters } from 'vuex'\n\nexport default {\n  name: 'text-promotion-doorhead',\n  components: {\n    SingleEditTable\n  },\n  data () {\n    return {\n      loading: false,\n      structure\n    }\n  },\n  computed: {\n    ...mapGetters(['dealerId', 'dealerYear', 'dealerQuarter', 'dealerParams'])\n  },\n  watch: {\n    dealerParams () {\n      this.getTableData()\n    }\n  },\n  created () {\n    this.getTableData()\n  },\n  methods: {\n    getTableData () {\n      this.loading = true\n      clearStructureOfValue(this.structure)\n      TableService.getDoorHead({\n        dealerId: this.dealerId,\n        year: this.dealerYear,\n        quarter: 4,\n        category: 'CIO'\n      }).then(([status, data]) => {\n        this.loading = false\n        \n        if (!status) return false\n\n        const resultLst = data.result.resultLst\n        updateStructureOfValue(this.structure, resultLst)\n      })\n    },\n    updateData (quarter) {\n      let params = {\n        dealerId: this.dealerId,\n        year: this.dealerYear,\n        quarter: quarter,\n        category: 'CIO'\n      }\n      params = exportParamsFromStructure(this.structure, params)\n      TableService.updateDoorHead(params).then(([status]) => {\n        if (!status) return this.$notify.error({\n            title: '错误提示',\n            duration: 5000,\n            message: '数据未保存成功，请刷新页面重试'\n          })\n      })\n    }\n  }\n}\n</script>\n", "import mod from \"-!../../../../../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../../../../../node_modules/thread-loader/dist/cjs.js!../../../../../../../node_modules/babel-loader/lib/index.js!../../../../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../../../../../node_modules/thread-loader/dist/cjs.js!../../../../../../../node_modules/babel-loader/lib/index.js!../../../../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./index.vue?vue&type=template&id=0fbf40d2&\"\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('single-edit-table',{attrs:{\"structure\":_vm.structure,\"loading\":_vm.loading},on:{\"update-data\":_vm.updateData}})}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "export default [{\n  type: 'normal',\n  key: 'interaction',\n  label: '客户互动（研讨会、推广会、订货会）（商用油）',\n}, {\n  type: 'normal',\n  key: 'localPromotion',\n  label: '本地促销（地促）（商用油）',\n}, {\n  type: 'normal',\n  key: 'other',\n  label: '其他（商用油）',\n}, {\n  type: 'select',\n  key: 'evaluationId',\n  keyContent: 'evaluationContent',\n  label: '总体评估',\n  options: [{\n      value: 0,\n      label: '目前不需要'\n    }, {\n      value: 1,\n      label: '计划在下个季度开始'\n    }, {\n      value: 2,\n      label: '有所不足，但已有计划以弥补不足'\n    }, {\n      value: 3,\n      label: '表现达到或超过预期'\n    }]\n}, {\n  type: 'number',\n  min: 0,\n  max: 30,\n  key: 'point',\n  label: '评分（30 分）',\n}]", "<template>\n  <single-edit-table\n    :structure=\"structure\"\n    @update-data=\"updateData\"\n    :loading=\"loading\"/>\n</template>\n\n<script>\n// compeonents\nimport SingleEditTable from '@/components/table/single-edit'\n// service\nimport TableService from '@/resources/service/table'\n// local\nimport structure from './_resources/structure'\nimport { exportParamsFromStructure, clearStructureOfValue, updateStructureOfValue } from '../../../_resources/utils'\n// other\nimport { mapGetters } from 'vuex'\n\nexport default {\n  name: 'text-promotion-activity',\n  components: {\n    SingleEditTable\n  },\n  data () {\n    return {\n      loading: false,\n      structure\n    }\n  },\n  computed: {\n    ...mapGetters(['dealerId', 'dealerYear', 'dealerQuarter', 'dealerParams'])\n  },\n  watch: {\n    dealerParams () {\n      this.getTableData()\n    }\n  },\n  created () {\n    this.getTableData()\n  },\n  methods: {\n    getTableData () {\n      this.loading = true\n      clearStructureOfValue(this.structure)\n      TableService.getActivity({\n        dealerId: this.dealerId,\n        year: this.dealerYear,\n        quarter: 4,\n        category: 'CIO'\n      }).then(([status, data]) => {\n        this.loading = false\n        \n        if (!status) return false\n\n        const resultLst = data.result.resultLst\n        updateStructureOfValue(this.structure, resultLst)\n      })\n    },\n    updateData (quarter) {\n      let params = {\n        dealerId: this.dealerId,\n        year: this.dealerYear,\n        quarter: quarter,\n        category: 'CIO'\n      }\n      params = exportParamsFromStructure(this.structure, params)\n      TableService.updateActivity(params).then(([status]) => {\n        if (!status) return this.$notify.error({\n            title: '错误提示',\n            duration: 5000,\n            message: '数据未保存成功，请刷新页面重试'\n          })\n      })\n    }\n  }\n}\n</script>\n", "import mod from \"-!../../../../../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../../../../../node_modules/thread-loader/dist/cjs.js!../../../../../../../node_modules/babel-loader/lib/index.js!../../../../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../../../../../node_modules/thread-loader/dist/cjs.js!../../../../../../../node_modules/babel-loader/lib/index.js!../../../../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./index.vue?vue&type=template&id=2f2b5f12&\"\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "<template>\n  <div>\n    <h2>市场推广</h2>\n    <comment\n      id=\"text-promotion-one-090321\"\n      title=\"1. 经销商组织的市场活动\"\n      :has-file=\"true\"/>\n    <comment\n      id=\"text-promotion-two-090321\"\n      title=\"2. 雪佛龙组织的市场活动\"\n      :has-file=\"true\"/>\n    <comment\n      id=\"text-promotion-three-090321\"\n      title=\"3. 客户互动（会议）门头促销等\"\n      :has-file=\"true\"/>\n\n    <h2>商用油门头</h2>\n    <doorhead/>\n\n    <h2>商用油市场活动</h2>\n    <activity/>\n  </div>\n</template>\n\n<script>\nimport Comment from '@/components/comment/text'\nimport Doorhead from './_pieces/doorhead'\nimport Activity from './_pieces/activity'\n\nexport default {\n  name: 'text-promotion',\n  components: {\n    Comment,\n    Doorhead,\n    Activity\n  }\n}\n</script>\n", "import mod from \"-!../../../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../../../node_modules/thread-loader/dist/cjs.js!../../../../../node_modules/babel-loader/lib/index.js!../../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../../../node_modules/thread-loader/dist/cjs.js!../../../../../node_modules/babel-loader/lib/index.js!../../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./index.vue?vue&type=template&id=2ac395a3&\"\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "import mod from \"-!../../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../node_modules/css-loader/index.js??ref--6-oneOf-1-1!../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./delete.vue?vue&type=style&index=0&id=65cc4af7&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../node_modules/css-loader/index.js??ref--6-oneOf-1-1!../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./delete.vue?vue&type=style&index=0&id=65cc4af7&scoped=true&lang=css&\"", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',[(_vm.title)?_c('h3',[_vm._v(_vm._s(_vm.title))]):_vm._e(),_c('el-row',[_c('el-col',{attrs:{\"span\":_vm.listSpan}},[_c('list-view',{attrs:{\"id\":_vm.id}})],1),_c('el-col',{attrs:{\"span\":_vm.formSpan,\"offset\":_vm.formOffset}},[_c('form-view',{attrs:{\"id\":_vm.id,\"has-file\":_vm.hasFile}})],1)],1)],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <div>\n    <h3 v-if=\"title\">{{ title }}</h3>\n    <el-row>\n      <el-col :span=\"listSpan\">\n        <list-view :id=\"id\"/>\n      </el-col>\n      <el-col :span=\"formSpan\" :offset=\"formOffset\">\n        <form-view :id=\"id\" :has-file=\"hasFile\"/>\n      </el-col>\n    </el-row>\n  </div>\n</template>\n\n<script>\n// components\nimport ListView from '../_components/list'\nimport FormView from '../_components/form'\n// other\nimport { mapGetters } from 'vuex'\n\nexport default {\n  name: 'comment-text',\n  components: {\n    FormView,\n    ListView\n  },\n  props: {\n    id: String,\n    title: String,\n    hasFile: {\n      type: Boolean,\n      default: false\n    }\n  },\n  computed: {\n    ...mapGetters(['env']),\n    listSpan () {\n      return this.env.mobile ? 24 : 11\n    },\n    formSpan () {\n      return this.env.mobile ? 24 : 10\n    },\n    formOffset () {\n      return this.env.mobile ? 0 : 1\n    }\n  }\n}\n</script>\n", "import mod from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./index.vue?vue&type=template&id=963e0c8e&\"\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "import mod from \"-!../../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../node_modules/css-loader/index.js??ref--8-oneOf-1-1!../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../../../../node_modules/sass-loader/lib/loader.js??ref--8-oneOf-1-3!../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./list.vue?vue&type=style&index=0&id=62694798&scoped=true&lang=scss&\"; export default mod; export * from \"-!../../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../node_modules/css-loader/index.js??ref--8-oneOf-1-1!../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../../../../node_modules/sass-loader/lib/loader.js??ref--8-oneOf-1-3!../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./list.vue?vue&type=style&index=0&id=62694798&scoped=true&lang=scss&\"", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('el-form',{ref:\"ruleForm\",attrs:{\"model\":_vm.ruleForm,\"rules\":_vm.rules}},[_c('el-form-item',{staticStyle:{\"margin-bottom\":\"10px\"},attrs:{\"label\":_vm.title,\"prop\":\"content\"}},[_c('el-input',{attrs:{\"type\":\"textarea\",\"placeholder\":\"请输入\"},model:{value:(_vm.ruleForm.content),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"content\", $$v)},expression:\"ruleForm.content\"}})],1),(_vm.hasFile)?_c('el-form-item',[_c('upload-file',{model:{value:(_vm.fileList),callback:function ($$v) {_vm.fileList=$$v},expression:\"fileList\"}})],1):_vm._e(),_c('el-form-item',{staticStyle:{\"margin-top\":\"-10px\",\"margin-bottom\":\"5px !important\"}},[_c('el-button',{attrs:{\"type\":\"primary\",\"size\":\"small\",\"loading\":_vm.loading},on:{\"click\":_vm.submit}},[_vm._v(\"\\n      \"+_vm._s(_vm.loading ? '提交中' : '提交')+\"\\n    \")])],1)],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\">\n    <el-form-item :label=\"title\" prop=\"content\" style=\"margin-bottom: 10px;\">\n      <el-input type=\"textarea\" v-model=\"ruleForm.content\" placeholder=\"请输入\"/>\n    </el-form-item>\n    <el-form-item v-if=\"hasFile\">\n      <upload-file v-model=\"fileList\"/>\n    </el-form-item>\n    <el-form-item style=\"margin-top: -10px;margin-bottom: 5px !important;\">\n      <el-button type=\"primary\" @click=\"submit\" size=\"small\" :loading=\"loading\">\n        {{loading ? '提交中' : '提交'}}\n      </el-button>\n    </el-form-item>\n  </el-form>\n</template>\n\n<script>\n// components\nimport UploadFile from '@/components/upload/file.vue'\n// services\nimport commentService from '@/resources/service/comment.js'\n// other\nimport bus from '@/resources/plugin/bus.js'\n\nexport default {\n  name: 'comment-form',\n  props: ['id', 'title', 'hasFile'],\n  components: {\n    UploadFile\n  },\n  data () {\n    return {\n      ruleForm: {\n        content: ''\n      },\n      fileList: [],\n      loading: false,\n      rules: {\n        content: [{\n          required: true, message: '请输入内容', trigger: 'blur'\n        }]\n      }\n    }\n  },\n  methods: {\n    submit () {\n      this.$refs['ruleForm'].validate((valid) => {\n        if (valid) {\n          if (this.loading || !this.ruleForm.content) {\n            return\n          }\n          this.loading = true\n          commentService.saveComment({\n            commentId: this.id,\n            dealerId: this.$store.getters.dealerId,\n            content: this.ruleForm.content,\n            fileList: this.fileList\n          }).then(([status]) => {\n            this.loading = false\n\n            if (!status) return false\n\n            bus.$emit('comment-'+this.id+'-update')\n            this.ruleForm.content = ''\n            this.fileList = []\n          })\n        } else {\n          return false\n        }\n      })\n      \n    }\n  }\n}\n</script>\n", "import mod from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./form.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./form.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./form.vue?vue&type=template&id=367c5412&\"\nimport script from \"./form.vue?vue&type=script&lang=js&\"\nexport * from \"./form.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "import xhr from './xhr'\n\nclass CommentService {\n  saveComment (data) {\n    return xhr({\n      method: 'post',\n      path: 'wxPublicRpc.do',\n      data: {\n        jsonrpc: '2.0',\n        method: 'qbrBasicService.submitComment',\n        params: [{\n          targetId: data.commentId,\n          partnerId: data.dealerId,\n          content: data.content,\n          attIds: data.fileList\n        }],\n        id: 1\n      }\n    })\n  }\n\n  getComments (data) {\n    return xhr({\n      method: 'post',\n      path: 'wxPublicRpc.do',\n      data: {\n        jsonrpc: '2.0',\n        method: 'qbrBasicService.queryComments',\n        params: [{\n          targetId: data.commentId,\n          partnerId: data.dealerId,\n          start: data.start,\n          limit: data.limit\n        }],\n        id: 1\n      }\n    })\n  }\n\n  deleteComment (data) {\n    return xhr({\n      method: 'post',\n      path: 'wxPublicRpc.do',\n      data: {\n        jsonrpc: '2.0',\n        method: 'qbrBasicService.delComment',\n        params: [{\n          id: data.id\n        }],\n        id: 1\n      }\n    })\n  }\n}\n\nexport default new CommentService()\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('el-upload',{staticClass:\"upload-demo\",attrs:{\"action\":(_vm.BASEURL + \"/qbr/uploadForAppAttachmentFile.do?\" + _vm.TOKENPARAMS),\"on-remove\":_vm.handleRemove,\"on-success\":_vm.handleSuccess,\"file-list\":_vm.fileList,\"data\":_vm.data,\"name\":\"myFile\",\"multiple\":\"\"}},[_c('el-button',{attrs:{\"size\":\"small\",\"type\":\"info\"}},[_vm._v(\"点击上传附件\")])],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <el-upload\n    class=\"upload-demo\"\n    :action=\"`${BASEURL}/qbr/uploadForAppAttachmentFile.do?${TOKENPARAMS}`\"\n    :on-remove=\"handleRemove\"\n    :on-success=\"handleSuccess\"\n    :file-list=\"fileList\"\n    :data=\"data\"\n    name=\"myFile\"\n    multiple>\n    <el-button size=\"small\" type=\"info\">点击上传附件</el-button>\n  </el-upload>\n</template>\n\n<script>\nimport { mapGetters } from 'vuex'\n\nexport default {\n  name: 'upload-file',\n  props: ['value', 'data'],\n  data () {\n    return {\n      fileList: [],\n      BASEURL: process.env.VUE_APP_ROOT_API\n    }\n  },\n  computed: {\n    ...mapGetters(['userToken']),\n    TOKENPARAMS () {\n      return this.userToken ? `appToken=${this.userToken}` : ''\n    }\n  },\n  watch: {\n    value (value) {\n      if (value.length === 0) {\n        this.fileList = []\n      }\n    }\n  },\n  methods: {\n    handleSuccess (response, file, fileList) {\n      const list = fileList.map(item => item.response.data.attId)\n      this.$emit('input', list)\n    },\n    handleRemove (file, fileList) {\n      const list = fileList.map(item => item.response.data.attId)\n      this.$emit('input', list)\n    }\n  }\n}\n</script>", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./file.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./file.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./file.vue?vue&type=template&id=2c0ee414&\"\nimport script from \"./file.vue?vue&type=script&lang=js&\"\nexport * from \"./file.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "import Vue from 'vue'\nexport default new Vue()"], "sourceRoot": ""}