<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
	<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
	<html>

	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
		<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
		<title>销售业务设置</title>
		<%@include file="/common/jsp/common.jsp"%>
			<script type="text/javascript" src="${ctx }common/js/SelectItemCtrl.js?v=20161227"></script>
			<script type="text/javascript" src="${ctx }business/partnerresponsible/js/partnerresponsible.js?v=${version}"></script>
			<style type="text/css">
			.temp-config .temp-config-item {
				height: auto;
			}
			.temp-config-item {
				height: 0;
				overflow: hidden;
			}
			.unloading-tips{
				color: #999;
    			padding-left: 8px;
			}
			.temp-config #actualResponsibles {
				height: 80px;
			}
			#actualResponsibles {
				height: 180px;
			}
			#selectPartnersDialog div.query-panel .field-group {
				width: 40%;
			}
			#selectPartnersDialog label.field-label {
				width: 90px;
			}
			</style>
	</head>

	<body class="gray-bg">
		<div class="content-wrapper">
			<div id="queryPanel" class="content-panel query-panel">
				<div class="field-group key-search">
					<label class="field-label width-auto">搜索:</label>
					<div class="control-group">
						<input type="text" id="gKeyWord" placeholder="请输入用户登录名、用户姓名" class="control-text" />
					</div>
				</div>
				<div class="field-group cond-adv">
					<label class="field-label">用户登录名：</label>
					<div class="control-group">
						<input type="text" id="loginName" name="loginName" placeholder="全部" class="control-text">
					</div>
				</div>
				<div class="field-group cond-adv">
					<label class="field-label">用户姓名：</label>
					<div class="control-group">
						<input type="text" id="username" name="username" placeholder="全部" class="control-text">
					</div>
				</div>
				<div class="field-group cond-adv" style="display: none;" id="partnerCondWrapper">
					<label class="field-label">经销商：</label>
					<div class="control-group" id="partnerId_c">
						<input id="partnerId" name="partnerId" type="hidden" />
					</div>
				</div>
				<div class="field-group cond-adv">
					<label class="field-label">业务：</label>
					<div class="control-group">
						<input type="hidden" name="funFlag" id="funFlag" value="-1" class="input-small control-text">
					</div>
				</div>
				<div class="query-btns">
					<div class="query-btn field-label">
						<button onclick="queryByParam()" class="btn-query">查询</button>
					</div>
					<div class="adv-toggle-btn">
						<a href="javascript: void(0);" onclick="var el = $(this), queryPanel = el.parents('.query-panel:first');if(queryPanel.hasClass('query-adv')){queryPanel.removeClass('query-adv');el.text('高级搜索');}else{queryPanel.addClass('query-adv');el.text('收起');}">高级搜索</a>
					</div>
				</div>
			</div>
			<div class="content-panel tools-panel">
				<button class="btn-create" onclick="showCreateDialog();">配置销售业务</button>
			</div>
			<div class="content-panel" id="grid">
			</div>
		</div>
		<%@include file="/business/partnerresponsible/createDialog.jsp"%>
	</body>

	</html>