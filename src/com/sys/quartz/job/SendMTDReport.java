package com.sys.quartz.job;

import java.util.UUID;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import com.chevron.report.business.MtdBizService;
import com.chevron.report.model.MtdEmailConfig;
import com.common.exception.WxPltException;
import com.common.util.StringUtils;
import com.sys.log.util.LogUtils;

/**
 * 上一月财务达标邮件
 * <AUTHOR>
 *
 */
public class SendMTDReport {

	@Autowired
	private MtdBizService mtdBizService;
	
	private final static String SERVER_ID = UUID.randomUUID().toString();
	
	private static Logger log = LoggerFactory.getLogger(SendMTDReport.class);

	public synchronized void execute(){
		log.info("SendMTDReport job start....");
		try {
			MtdEmailConfig emailConfig = mtdBizService.getEmailConfig();
			String to = null;
			String cc = null;
			if("1".equals(emailConfig.getAutoToCustomer())) {
				to = emailConfig.getCustomerEmailTo();
				cc = emailConfig.getCustomerEmailCc();
			}else {
				to = emailConfig.getInternalEmailTo();
				to = emailConfig.getInternalEmailCc();
			}
			if(StringUtils.isBlank(to)) {
				LogUtils.addErrorLog(1l, "com.sys.quartz.job.SendMTDReport.execute", "发送MTD报表失败。未配置接收邮箱", null);
			}else {
				mtdBizService.sendMtdReport(null, to, cc, emailConfig.getWarningEmailHonorific(), emailConfig.getWarningEmailTo(), 
						emailConfig.getWarningEmailCc(), SERVER_ID, false, 1l,null,null,null,null, false);
			}
		} catch (WxPltException e) {
			e.printStackTrace();
			LogUtils.addErrorLog(1l, "com.sys.quartz.job.SendMTDReport.execute", "发送MTD报表失败。" + e.getMessage(), null);
		}
		log.info("SendMTDReport job finish....");
	}
}
