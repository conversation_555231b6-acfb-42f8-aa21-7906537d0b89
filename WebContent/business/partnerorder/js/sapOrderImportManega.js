
var rpcClient = new $.JsonRpcClient({
	ajaxUrl: '/wxPublicRpc.do'
});
$(document).ready(function () {
	initGrid();
});


//初始化Grid
var grid = null;
var store = null;
function initGrid() {
	BUI.use(['bui/grid', 'bui/data'], function (Grid, Data) {
		var Grid = Grid,
			Store = Data.Store,
			columns = [];
		columns.push(
			{
				title: '序号', width: 65, sortable: false, renderer: function (value, obj, index) {

					return index + 1;
				}
			},
			{ title: '导入区间范围', dataIndex: 'timeRange', width: '20%' },
			{ title: '导入结果', dataIndex: 'importResultDetail', width: '17%' },
			{ title: '导入时间', dataIndex: 'createTime', width: '20%' ,
				renderer: function(value, obj, index){
					return common.formatDate(new Date(value), 'yyyy-MM-dd hh:mm:ss');
				}
			},
			{ title: '状态', dataIndex: 'status', width: '17%'},	
			{ title: '用时', dataIndex: 'timeConsuming', width: '17%', 
				renderer: function (value, obj, index) {								
					return formatSeconds(value);										
				}
			},
			{ title: '重复导入条数', dataIndex: 'repeatAcount', width: '17%', 
				renderer: function (value, obj, index) {	
					if(null==value || value=='0' || value=='')	
					{
						return "<div>0条</div>";
					}	
					var btnHtml = '<a href="javascript:void(0);" onclick="orderDetail('+obj.id+',\''+obj.repeatIds+'\');">'+value+'条</a>';
					return btnHtml;											
				}
			},	
			{ title: '差异条数', dataIndex: 'diffTotalAcount', width: '17%', 
				renderer: function (value, obj, index) {	
					if(null==value || value=='0' || value=='')	
					{
						return "<div>0条</div>";
					}						
					var btnHtml = '<a href="javascript:void(0);" onclick="orderDiffDetail(' + obj.id + ');">'+value+'条</a>';
					return btnHtml;											
				}
			
			},	
			{ title: '文件名', dataIndex: 'fileName', width: '17%'},
			{ title: '执行人', dataIndex: 'executor', width: '17%' },
			{
				title: '操作', dataIndex: '', width: 140, sortable: false,
				renderer: function (value, obj, index) {								
					var btnHtml = '<a href="javascript:void(0);" onclick="orderDetail(' + obj.id + ',null);">查看</a>';
					return btnHtml;										
				}
			}
		);
		store = new Store({
			url: '/sappartnerorder/querySapPartnerOrder.do',
			autoLoad: false,
			totalProperty: 'totalRecord',
			params: { dayReportFlag: "-1" },
			errorProperty: 'errorMes',
			root: "resultLst",
			remoteSort: true,
			sortField: 'id',
			sortDirection: 'DESC',
			proxy: {
				method: 'post'
			},
			pageSize: 10,
			start: 0
		});
		grid = new Grid.Grid({
			render: '#grid',
			columns: columns,
			loadMask: false,
			store: store,
			bbar: {
				pagingBar: true
			}
		});
		store.on('beforeprocessload', function (e) {
			if (e.data.isError) {
				LoadMask.hide();
				common.alertMes(e.data.errorMsg, 'error');
			}
		});
		grid.render();
		common.initGrid(grid, null, true);
	});
}
function formatSeconds(value) {
    var theTime = parseInt(value);// 秒
    var theTime1 = 0;// 分
    var theTime2 = 0;// 小时
    if(theTime > 60) {
        theTime1 = parseInt(theTime/60);
        theTime = parseInt(theTime%60);
            if(theTime1 > 60) {
            theTime2 = parseInt(theTime1/60);
            theTime1 = parseInt(theTime1%60);
            }
    }
        var result = ""+parseInt(theTime)+"秒";
        if(theTime1 > 0) {
        result = ""+parseInt(theTime1)+"分"+result;
        }
        if(theTime2 > 0) {
        result = ""+parseInt(theTime2)+"小时"+result;
        }
    return result;
}

function orderDetail(id,repeatOrderLineIds) {
	if (id == null || id == '') {
		return;
	}
	LoadMask.show();
	rpcClient.call('sapPartnerOrderService.querySapOrderDetail', [""+id,repeatOrderLineIds],
		function (result) {
			if (result.code == "success") {
				LoadMask.hide();
				openDialog(result.orderVo,result.orderLineList);
			} else {
				common.alertMes(result.msg, 'error');
			}
		}
	);
}
var detailDialog = null, detailGrid = null;
var screenWidth = $(window).width();
var dialogWidth = 750; 
var gridWidth = 800;
if((screenWidth * 0.8) > 750){
	dialogWidth = screenWidth * 0.8;
	gridWidth = dialogWidth - 30;
	if(gridWidth <= 800){
		gridWidth = 800;
	}
}
function openDialog(order,items){
	//定义订单列表高度
	var gridHeight = 400;
	if(items.length > 8){
		$("#orderDetailViewGrid").css("height","400px");
	}else{
		gridHeight = 80 *  items.length + 34;
		$("#orderDetailViewGrid").css("height",gridHeight + "px");
	}	
	if(detailDialog == null){
		//初始化详情对话框
		BUI.use(['bui/overlay'],function(Overlay){
			//初始化对话框
			detailDialog = new Overlay.Dialog({
			    title:'',
			    width:dialogWidth,
			    //配置DOM容器的编号
			    contentId:'detailDialog',
			    buttons: [
			              {
			            	  text:'关闭',
			            	  elCls : 'button btn-close',
			            	  handler : function(){
			            	  	this.close();
			            	  }
			              }]
			});
		});
	}
	detailDialog.show();
	
	$('#timeRange').text(order.timeRange);
	$('#importResultDetail').text(order.importResultDetail);
	$('#status').text(order.status);
	$('#timeConsuming').text(formatSeconds(order.timeConsuming));
	$('#fileName').text(order.fileName);

	$('#orderDetailViewGrid').html("");
		BUI.use(['bui/grid','bui/data'],function(Grid,Data){
			//初始化订单明细表
			var Grid = Grid,
			Store = Data.Store,
				columns = [{
					title : '序号',
					dataIndex :'',
					sortable: false,
					width:65,
					renderer : function(value,obj,index){
						return index + 1;
		        	}
				},{ 
					title: 'Sales_Doucument', 
					sortable: false,
					dataIndex: 'salesDocument', 
					elCls: 'text-right',
					width : '14%'
				},{ 
					title: 'Material', 
					sortable: false,
					dataIndex: 'material', 
					elCls: 'text-right',
					width : '10%'
				},{ 
					title: 'Sold_To_Party', 
					dataIndex: 'soldToParty', 
					elCls: 'text-right',
					sortable: false,
					width : '16%'
				},{ 
					title: 'Billing_Year_Month', 
					dataIndex: 'billingYearMonth', 
					width : '16%', 
					elCls: 'text-right',
					sortable: false
				},{
					title : 'Material_Key',
					dataIndex : 'materialKey',
					elCls: 'text-right',
					width : '10%',
					sortable: false
				},{
					title : 'Sold_To_Party_Key',
					dataIndex : 'soldToPartyKey',
					elCls: 'text-right',
					width : '16%',
					sortable: false
				},{
					title : 'Qty_in_Sales_Unit',
					dataIndex : 'qtyInSalesUnit',
					elCls: 'text-right',
					width : '16%',
					sortable: false
				},{
					title : 'Bill_qty_in_SKU',
					dataIndex : 'billQtyInSku',
					elCls: 'text-right',
					width : '14%',
					sortable: false
				}],
				data = items; // 显示的数据
				store = new Store({
					data: data,
					autoLoad: true,
					pageSize: 10
				}),
				detailViewGrid = new Grid.Grid({
					render : '#orderDetailViewGrid',
					columns: columns,
					loadMask: true, // 加载数据时显示屏蔽层
					items : items,
					store: store,
					height: gridHeight,
					stripeRows: false,
					plugins: [Grid.Plugins.AutoFit,Grid.Plugins.Summary],
					width:gridWidth,
					bbar: {
						pagingBar: true
					}
				});
			detailViewGrid.render();
			$('#orderDetailViewGrid .bui-grid-hd-empty').css('width', '17px');
		});
		detailDialog.set('title', '查看订单');
}

var importDialog = null;
//修改品牌按钮
function importDialogOpen() {
    if (importDialog == null) {
        BUI.use('bui/overlay', function (Overlay) {
        	importDialog = new Overlay.Dialog({
                title: "导入SAP订单",
                width: 550,
                height: 400,
                //配置DOM容器的编号
                contentId: 'importDialog',
                success: function () {
					if ($("#uploadfile").find("input")[0].value == "") {
						common.alertMes("请选择要上传的文件", 'error');
						return;
					}
					var startTime = $('#startTime').val();
					if (startTime == "") {
						common.alertMes("请选择起始时间", 'error');
						return;
					}
					var endTime = $('#endTime').val();
					if (endTime == "") {
						common.alertMes("请选择截至时间", 'error');
						return;
					}
					var formData = new FormData($("#uploadfile")[0]);
					formData.append("startTime",startTime);
					formData.append("endTime",endTime);
					//LoadMask.show();
					$.ajax({
						url : '/sappartnerorder/importSapPartnerOrder.do',
						type : 'POST',
						data : formData,
						async : false,
						cache : false,
						contentType : false,
						processData : false,
						beforeSend: function () {
       	 						LoadMask.show();
        				},
						success : function(returndata) {
							LoadMask.hide();
							switch (returndata.code) {
							case "success":
								common.alertMes("导入成功！", 'success');							
								importDialog.close();
								store.load({
									start: 0
								});
								break;
							case "warning":
								common.alertMes(returndata.warning, 'warning');
								break;
							case "error":
								//$("#errorGrid").html("");
								common.alertMes(returndata.errorMsg, 'error');
								break;
							}
						},
						error : function(returndata) {
							LoadMask.hide();
						}
					});
                }
            });
        });
    }
    importDialog.show();
}
