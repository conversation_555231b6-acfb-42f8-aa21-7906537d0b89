package com.sys.quartz.business;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;

import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.context.ContextLoader;

import com.chevron.exportdata.Export;
import com.chevron.exportdata.ExportExcel;
import com.chevron.pms.dao.OrderVoMapper;
import com.chevron.pms.dao.PartnerResponsibleVoMapper;
import com.chevron.pms.model.OrderVo;
import com.chevron.pms.model.PartnerResponsibleVo;
import com.chevron.pms.service.OrderDDBXService;
import com.chevron.thirdorder.model.OrderCommonVo;
import com.common.config.MyPropertyConfigurer;
import com.common.constants.Constants;
import com.common.token.util.TokenUtil;
import com.common.util.CommonUtil;
import com.common.util.DateUtil;
import com.sys.auth.dao.WxTPropertiesMapper;
import com.sys.auth.model.WxTProperties;
import com.sys.email.service.EmailSenderService;

/**
 * 
 * @Author: bo.liu  2016-12-23 下午4:20:29 
 * @Version: $Id$
 * @Desc: <p></p>
 */
@Service
public class ScanOrderForConfirmService {
	public final Logger logerr = Logger.getLogger(this.getClass());
	@Resource
	OrderVoMapper orderVoMapper;
	@Resource
	PartnerResponsibleVoMapper partnerResponsibleVoMapper;
	@Resource 
	EmailSenderService emailSenderService;
	@Resource 
	Export exprotService;
	@Resource
	OrderDDBXService orderDDBXService;
	@Resource
	WxTPropertiesMapper propertiesMapper;
	
	public static final String orderrefundType = "orderrefund";
	public static final String orderconfirmType = "orderconfirm";
	
	/**
	 * 处理查询订单状态
	 * <AUTHOR> 2016-12-23 下午4:23:56
	 */
	@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
	public void doHandleOrderForConfirm()
	{
		Date currentDate = new Date();
		String beginDate = "";
		String endDate = "";
		try
		{
			/* delete by bo.liu 1019
			Date beginTime = DateUtil.addDays(currentDate, -1);
			beginDate = DateUtil.toDateStr(beginTime)+" 17:00:00";
			endDate = DateUtil.getCurrentDate(DateUtil.DEFAULT_DATE_PATTERN)+" 17:00:00";
			*/
			
			String timesuffix = "00:00:00";
	    	HashMap<String, Object> reqmap = new HashMap<String, Object>();
			reqmap.put("codetype","scan.yxc.order.time");
			WxTProperties properties  = propertiesMapper.selectByMap(reqmap);
			if(null!=properties)
			{
				timesuffix = properties.getCode();
				
			}
				
			Date beginTime = DateUtil.addDays(currentDate, -7);
			beginDate = DateUtil.toDateStr(beginTime)+" "+timesuffix;
			endDate = DateUtil.getCurrentDate(DateUtil.DEFAULT_DATE_PATTERN)+" "+timesuffix;
			
			//1.v1	获取需要查询待扫描的订单，状态为1 
			 //	v2	获取导出sap订单的新保订单		状态为3  1019
			List<OrderVo> lstOrders = getNeedConfirmOfOrder(beginDate,endDate);
			if(null==lstOrders || lstOrders.isEmpty())
			{
				logerr.error("=========doHandleOrderForConfirm----lstOrders is null 1019");
				return;
			}
			//2.组装数据，并发送邮件
			boolean isSuccess = sendEmailForConfirmOrder(lstOrders,beginDate,endDate);
			if(!isSuccess)
			{
				logerr.error("=========doHandleOrderForConfirm----false");
			}
		
		}catch(Exception ex)
		{
			TransactionAspectSupport.currentTransactionStatus()
			.setRollbackOnly();
			ex.printStackTrace();
			logerr.error(ex.getMessage());
			
		}
	}
	
	/**
	 * v1	获取需要确认的订单 新保订单，待确认(雪佛龙).....
	 * v2  	查询符合条件的新保订单用于导出sap	1019
	 * <AUTHOR> 2016-12-23 下午4:25:30
	 * @return
	 */
	public List<OrderVo> getNeedConfirmOfOrder(String beginDate,String endDate)
	{
		Map<String,Object> reqMap = new HashMap<String,Object>();
		reqMap.put("orderConfirmFlag", "1");
		reqMap.put("orderType", OrderCommonVo.ORDER_TYPE_DDXB);
		reqMap.put("orderStatus", OrderCommonVo.ORDER_STATUS[3]);  //mod by bo.liu ORDER_STATUS[3]
		reqMap.put("orderSource1", OrderCommonVo.ORDER_SOURCE_YXC_VALUE);  //add by bo.liu 1019
		reqMap.put("isexportflag", "0");  //add by bo.liu 1019
		reqMap.put("beginTime", beginDate);
		reqMap.put("endTime", endDate);
		List<OrderVo> lstOrders = orderVoMapper.getOrdersByStatus(reqMap);
		return lstOrders;
	}
	
	
	/**
	 * 更新订单状态
	 * <AUTHOR> 2016-12-23 下午4:54:33
	 * @param lstOrders
	 */
	public void updateOrderStatus(List<OrderVo> lstOrders,String orderStatus)
	{
		Map<String,Object> reqMap = new HashMap<String,Object>();
		List<OrderVo> updateOrderLst = new ArrayList<OrderVo>();
		for(OrderVo orderVo: lstOrders)
		{
			orderVo.setStatus(orderStatus);
			updateOrderLst.add(orderVo);
		}
		if(null==updateOrderLst||updateOrderLst.isEmpty())
		{
			return;
		}
		
		//liyu:分页更新订单
		int pageSize = 300;
		int totalRows = updateOrderLst.size();
		if (totalRows > pageSize) {
			int pagecount = 0;
			int m = totalRows % pageSize;
			if (m > 0) {
				pagecount = totalRows / pageSize + 1;
			} else {
				pagecount = totalRows / pageSize;
			}

			List<OrderVo> subList = null;
			for (int i = 1; i <= pagecount; i++) {
				int fromIndex = (i - 1) * pageSize;
				int toIndex = 0;
				if (m > 0) {
					if (i == pagecount) { // 最后一页
						toIndex = totalRows;
					} else {
						toIndex = pageSize * (i);
					}
				} else {
					toIndex = pageSize * (i);
				}
				subList = updateOrderLst.subList(fromIndex, toIndex);
				reqMap.put("orderLst", subList);
				orderVoMapper.batchUpdateOrderLstByOrderNo(reqMap);
			}
		} else {
			reqMap.put("orderLst", updateOrderLst);
			orderVoMapper.batchUpdateOrderLstByOrderNo(reqMap);
		}
	}
	
	
	
	
	/**
	 * 组装发送邮件信息
	 * <AUTHOR> 2016-12-23 下午4:54:33
	 * @param lstOrders
	 */
	public boolean sendEmailForConfirmOrder(List<OrderVo> lstOrders,String beginTime,String endTime)
	{
		boolean isSendResult = true;
		try
		{
		//接收者
		String[] accepters = null;
		//邮件抄送者
		String[] ccaccepters = null;
		Map<String,Object> reqMap = new HashMap<String,Object>();
		reqMap.put("funFlag", "order_confirm");
		//reqMap.put("organizationName", OrderCommonVo.ORDER_SP[1]);//多个的时候后面考虑用in查询
		List<String> orgNames = new ArrayList<String>();
		orgNames.add(OrderCommonVo.ORDER_SP[1]);
		reqMap.put("organizationNames",orgNames);
		List<PartnerResponsibleVo> lstPartnerResponsibleVo =  partnerResponsibleVoMapper.queryEmailInfoForFWBOrderConfirm(reqMap);
		if(null==lstPartnerResponsibleVo || lstPartnerResponsibleVo.isEmpty())
		{
			logerr.info("sendEmailForConfirmOrder lstPartnerResponsibleVoInfo is null");
			return false;
		}
		
		logerr.info("accepters-----:");
		logerr.info("ccaccepters-----:");
		//accepters = new String[lstPartnerResponsibleVo.size()];
		//ccaccepters = new String[lstPartnerResponsibleVo.size()];
		String acceptersAll = "";
		String ccacceptersAll = "";
		for(PartnerResponsibleVo partnerResponsibleVo:lstPartnerResponsibleVo)
		{
			String acceptUser = partnerResponsibleVo.getResponsiblePersonEmail();
			String ccUser = partnerResponsibleVo.getDayReportCc();
			
			if(null!=acceptUser && !acceptUser.trim().isEmpty() && !acceptersAll.contains(acceptUser.trim()))
			{
				acceptersAll+= partnerResponsibleVo.getResponsiblePersonEmail()+";";
			}
			if(null!=ccUser && !ccUser.trim().isEmpty() && !ccacceptersAll.contains(ccUser.trim()))
			{
				ccacceptersAll+= partnerResponsibleVo.getDayReportCc()+";";
			}
		}
		if(acceptersAll.isEmpty())
		{
			logerr.error("sendEmailForConfirmOrder=========acceptersAll----isEmpty");
			return false;
		}
		
		if(ccacceptersAll.isEmpty())
		{
			logerr.error("sendEmailForConfirmOrder=========ccacceptersAll----is null");
			ccacceptersAll = null;
		}
		
		acceptersAll = acceptersAll.substring(0, acceptersAll.length()-1);
		accepters = acceptersAll.split(";");
		
		if(null!=ccacceptersAll)
		{
			ccacceptersAll = ccacceptersAll.substring(0, ccacceptersAll.length()-1);
			ccaccepters = ccacceptersAll.split(";");
		}
		logerr.info("accepters-----:"+accepters);
		logerr.info("ccaccepters-----:"+ccaccepters);
		
		//组装邮件数据map
		Map<String,Object> dataMap = new HashMap<String,Object>();
		logerr.info("acceptname0-----:=========:");
		String acceptname = accepters[0].substring(0, accepters[0].lastIndexOf("@"));
		logerr.info("acceptname1-----:"+acceptname+"=========:");
		if(accepters.length>1)
		{
			acceptname = "ALL";
		}
		dataMap.put("acceptUserName", acceptname);
		dataMap.put("msgcontent", "附件是易修车"+beginTime+"到"+endTime+"需要导入SAP的新保订单，请处理！");
		
		/* v1 易修车订单确认流程
		//HttpServletRequest request = ((ServletRequestAttributes)RequestContextHolder.getRequestAttributes()).getRequest();
		String basePath = (String)Constants.getSystemPropertyByCodeType(Constants.APP_HOST)+"/";//request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() +"/";
		logerr.info("basePath-----:"+basePath+"=========:");
		String confirmLink = basePath+TokenUtil.generateTokenLink(String.valueOf(lstPartnerResponsibleVo.get(0).getResponsiblePersonId()), orderconfirmType, "", "", true, beginTime,endTime);
		logerr.info("confirmLink-----:"+confirmLink+"=========:"+String.valueOf(lstPartnerResponsibleVo.get(0).getResponsiblePersonId()));
		dataMap.put("confirmlink", confirmLink);
		String refudLink =  basePath+TokenUtil.generateTokenLink(String.valueOf(lstPartnerResponsibleVo.get(0).getResponsiblePersonId()), orderrefundType, "", "", true, beginTime,endTime);
		logerr.info("refudLink-----:"+refudLink+"=========:"+String.valueOf(lstPartnerResponsibleVo.get(0).getResponsiblePersonId()));
		dataMap.put("refudLink", refudLink);
		dataMap.put("msgtip", "注：如果您需要对部分订单进行确认，请登录雪佛龙合伙人系统进行操作！");
		logerr.info("confirmLink-----:"+confirmLink);
		logerr.info("refudLink-----:"+refudLink);
		*/
		
		//quartz中使用此获取上下文
		ServletContext context = ContextLoader.getCurrentWebApplicationContext().getServletContext();
		
		//2.组织发送邮件的信息
		String fileName = "易修车新保导出到SAP订单_"+CommonUtil.generateCode("EXPORT");
		String clazzName = "com.chevron.pms.model.OrderForEmailExportNew";
		String exportEmailTmpFolder = "EXPORT_EMAIL_YXC_ORDER_DATA";
	    String ftl = MyPropertyConfigurer.getVal("mail.yxc.order.sap.ftl");
		dataMap.put("fileName", fileName);
		dataMap.put("type", ExportExcel.EMAIL_TYPE);
		dataMap.put("className", clazzName);
		dataMap.put("exportEmailTmpFolder", exportEmailTmpFolder);
		dataMap.put("xycAccepters", accepters);
		dataMap.put("xycCccepters", ccaccepters);
		dataMap.put("emailSubject", "mail.yxc.xborder.sap.subject");
		dataMap.put("ftl", ftl);
		dataMap.put("orderSource", OrderCommonVo.ORDER_SOURCE_YXC_VALUE);
		
		List<Long> needExportOrderIds = new ArrayList<Long>();
		for(OrderVo tmpOrder:lstOrders)
		{
			needExportOrderIds.add(tmpOrder.getId());
		}
	    orderDDBXService.processExportOrders(needExportOrderIds,dataMap);
	    /*
		//组装导出excel表的数据
		List<XYCOrderForConfirmByEmailExport> lstExportData = new ArrayList<XYCOrderForConfirmByEmailExport>();
		int i = 1;
		for(OrderVo order:lstOrders)
		{
			String oilInjection = order.getOilInjection()
					.replace(".0", "L").trim();
			order.setCreattime(DateUtil.toDateStr(order.getCreateTime()));
			order.setEfftime(DateUtil.toDateStr(order.getEffectiveTime()));
			order.setInvlidtime(DateUtil.toDateStr(order.getInvalidTime()));
			order.setOilInjection(oilInjection);
			XYCOrderForConfirmByEmailExport exportData = new XYCOrderForConfirmByEmailExport();
			exportData.setIndexNo(""+i);
			exportData.setAmount(order.getCardEnableTimes());
			exportData.setBuyUser(order.getBuyUserName());
			exportData.setCarType(order.getCarType());
			exportData.setEffTime(order.getEfftime());
			exportData.setInvalidTime(order.getInvlidtime());
			exportData.setOilCost(oilInjection);
			exportData.setOilInjection(order.getPreferentialTypePrice());
			exportData.setOrderTime(order.getCreattime());
			exportData.setPlateNumber(order.getPlateNumber());
			exportData.setReginName(order.getRegionName());
			exportData.setRemark(order.getRemark());
			exportData.setTelPhone(order.getPhoneNo());
			exportData.setVinCode(order.getBuyerCardId());
			exportData.setViscosity(order.getViscosity());
			lstExportData.add(exportData);
			i++;
		}
		dataMap.put("lstOrders", lstOrders);
		Map<String,Object> returnMap = exprotService.exportDataForCommonAndCreateFile(lstExportData, "com.chevron.pms.model.XYCOrderForConfirmByEmailExport", "易修车待确认订单", MyPropertyConfigurer
				.getVal("EXPORT_EMAIL_YXC_ORDER_DATA"), "易修车待确认新保订单_"+CommonUtil.generateCode("EXPORT"), context);
		File[] attfiles = (File[]) returnMap.get("attfiles");
		isSendResult = emailSenderService.sendEmailForCommon(context,
				accepters, ccaccepters, MyPropertyConfigurer.getVal("mail.yxc.order.confirm.subject"),dataMap, attfiles, MyPropertyConfigurer.getVal("mail.yxc.order.confirm.ftl"));
		
		*/
		}catch (Exception e) {
			e.printStackTrace();
			logerr.error(e.getMessage());
			isSendResult = false;
		}
		return isSendResult;
	}
}
