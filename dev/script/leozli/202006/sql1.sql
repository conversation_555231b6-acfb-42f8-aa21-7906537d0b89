SELECT t_delo.*
FROM (
	SELECT t_delo_mgx.*
		, CASE 
			WHEN t_delo_mgx.ext_flag1 & 1024 = 0
			AND t_delo_mgx.task_time > t_delo_mgx.delo_400_xg_sj THEN 
				CASE 
					WHEN t_delo_mgx.ext_flag1 >= 2048
					AND t_delo_mgx.ext_flag1 & 2048 = 0 THEN t_delo_mgx.ext_flag1 - 2048
					ELSE t_delo_mgx.ext_flag1
				END
			ELSE t_delo_mgx.ext_flag1
		END AS ext_flag
	FROM (
		SELECT t1.work_shop_name, o.organization_name AS partner_name, o.id AS partner_id, t1.id, v1.region_name
			, t1.dms_key, pe.distributor_id, t1.ext_flag AS ext_flag1
			, (
				SELECT MAX(tm1.create_time)
				FROM wx_task_sub s
					LEFT JOIN wx_task_main tm1 ON s.task_main_id = tm1.task_main_id
				WHERE s.org_id = t1.id
					AND tm1.tmb_type_code IN ('TT_2_CLZX', 'TT_2_XD_CAI')
					AND s.task_status = 4
					AND tm1.create_time >= '2020-01-01'
			) AS task_time, t1.delo_400_xg_sj, u.ch_name AS execute_user_name, 1 AS customer_type
		FROM wx_t_work_shop t1
			LEFT JOIN wx_t_workshop_partner wp1 ON t1.id = wp1.workshop_id
			LEFT JOIN wx_t_partner_o2o_enterprise pe ON wp1.partner_id = pe.partner_id
			LEFT JOIN wx_t_organization o ON o.id = wp1.partner_id
			LEFT JOIN view_customer_region_sales_channel v1
			ON v1.distributor_id = pe.distributor_id
				AND v1.channel_weight & 2 > 0
			LEFT JOIN wx_t_user u ON u.user_id = t1.creator
		WHERE t1.delete_flag = 0
			AND t1.status = '3'
			--AND wp1.partner_id = 56408
			AND (EXISTS (
					SELECT 1
					FROM wx_task_sub ts1
						LEFT JOIN wx_task_main tm1 ON ts1.task_main_id = tm1.task_main_id
						LEFT JOIN wx_task_instance_check tic1 ON tic1.sub_task_id = ts1.task_id
						LEFT JOIN wx_task_check tc1 ON tc1.check_id = tic1.check_id
						LEFT JOIN wx_task_instance_check tic2 ON tic2.sub_task_id = ts1.task_id
						LEFT JOIN wx_task_check tc2 ON tc2.check_id = tic2.check_id
						LEFT JOIN wx_att_file af1
						ON af1.source_type = 3
							AND af1.source_id = tic1.id
					WHERE ts1.org_id = t1.id
						AND tm1.tmb_type_code IN ('TT_2_CLZX', 'TT_2_XD_CAI')
						AND ts1.task_status = 4
						AND tm1.create_time >= '2020-06-01'
						AND tc1.check_code = 'DELO400_MGX_PHOTO'
						AND af1.att_id IS NOT NULL
						AND tc2.check_code = 'SALE_DELO_PRODUCT'
						AND tic2.check_value IS NOT NULL
						AND charindex('德乐400', tic2.check_value) > 0
				)
				OR EXISTS (
					SELECT 1
					FROM wx_task_sub ts1
						LEFT JOIN wx_task_main tm1 ON ts1.task_main_id = tm1.task_main_id
						LEFT JOIN wx_task_instance_check tic1 ON tic1.sub_task_id = ts1.task_id
						LEFT JOIN wx_task_check tc1 ON tc1.check_id = tic1.check_id
						LEFT JOIN wx_task_instance_check tic2 ON tic2.sub_task_id = ts1.task_id
						LEFT JOIN wx_task_check tc2 ON tc2.check_id = tic2.check_id
						LEFT JOIN wx_att_file af1
						ON af1.source_type = 3
							AND af1.source_id = tic1.id
					WHERE ts1.org_id = t1.id
						AND tm1.tmb_type_code IN ('TT_2_CLZX', 'TT_2_XD_CAI')
						AND tm1.create_time >= '2020-01-01'
						AND tm1.create_time < '2020-06-01'
						AND tc1.check_code = 'WORK_SHOW_PHOTO'
						AND af1.att_id IS NOT NULL
						AND tc2.check_code = 'SALE_DELO_PRODUCT'
						AND tic2.check_value IS NOT NULL
						AND charindex('德乐400', tic2.check_value) > 0
				))
		UNION ALL
		SELECT wp1.fleet_name, o.organization_name AS partner_name, o.id AS partner_id, wp1.id, v1.region_name
			, NULL AS dms_key, pe.distributor_id, wp1.ext_flag AS ext_flag1
			, (
				SELECT MAX(tm1.create_time)
				FROM wx_task_sub s
					LEFT JOIN wx_task_main tm1 ON s.task_main_id = tm1.task_main_id
				WHERE s.org_id = wp1.id
					AND tm1.tmb_type_code IN ('TT_2_FLEET', 'TT_2_FLEET_ZF')
					AND s.task_status = 4
					AND tm1.create_time > '2020-06-01'
			) AS task_time, wp1.delo_400_xg_sj, u.ch_name AS execute_user_name, 2 AS customer_type
		FROM wx_t_fleet_info wp1
			LEFT JOIN wx_t_partner_o2o_enterprise pe ON wp1.partner_id = pe.partner_id
			LEFT JOIN wx_t_organization o ON o.id = wp1.partner_id
			LEFT JOIN view_customer_region_sales_channel v1
			ON v1.distributor_id = pe.distributor_id
				AND v1.channel_weight & 2 > 0
			LEFT JOIN wx_t_user u ON u.user_id = wp1.create_user_id
		WHERE wp1.delete_flag = 0
			AND wp1.status = '3'
			--AND wp1.partner_id = '56408'
			AND EXISTS (
				SELECT 1
				FROM wx_task_sub ts1
					LEFT JOIN wx_task_main tm1 ON ts1.task_main_id = tm1.task_main_id
					LEFT JOIN wx_task_instance_check tic1 ON tic1.sub_task_id = ts1.task_id
					LEFT JOIN wx_task_check tc1 ON tc1.check_id = tic1.check_id
					LEFT JOIN wx_att_file af1
					ON af1.source_type = 3
						AND af1.source_id = tic1.id
					LEFT JOIN wx_task_instance_check tic2 ON tic2.sub_task_id = ts1.task_id
					LEFT JOIN wx_task_check tc2 ON tc2.check_id = tic2.check_id
				WHERE ts1.org_id = wp1.id
					AND tm1.tmb_type_code IN ('TT_2_FLEET', 'TT_2_FLEET_ZF')
					AND ts1.task_status = 4
					AND tm1.create_time > '2020-06-01'
					AND tc1.check_code = 'DELO400_MGX_PHOTO'
					AND af1.att_id IS NOT NULL
					AND tc2.check_code = 'SALE_DELO_PRODUCT'
					AND tic2.check_value IS NOT NULL
					AND charindex('德乐400', tic2.check_value) > 0
			)
	) t_delo_mgx
) t_delo
WHERE 1 = 1