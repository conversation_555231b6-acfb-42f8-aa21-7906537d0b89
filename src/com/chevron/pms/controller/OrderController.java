package com.chevron.pms.controller;

import java.io.IOException;
import java.io.OutputStream;
import java.io.PrintWriter;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.codec.digest.DigestUtils;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.jbarcode.util.ImageUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import com.chevron.pms.business.OrderVoBizService;
import com.chevron.pms.dao.OrderVoMapper;
import com.chevron.pms.dao.WxtOrderYYFWSetsVoMapper;
import com.chevron.pms.model.GenericPaginationQueryParams;
import com.chevron.pms.model.OrderLineVo;
import com.chevron.pms.model.OrderVo;
import com.chevron.pms.model.WxtOrderYYFWSetsVo;
import com.chevron.pms.service.OrderBizService;
import com.chevron.pms.service.OrderService;
import com.common.constants.Constants;
import com.common.exception.WxPltException;
import com.common.util.BarCodeUtil;
import com.common.util.ContextUtil;
import com.common.util.DateUtils;
import com.common.util.JsonUtil;
import com.common.util.MessageResourceUtil;
import com.common.util.PDFUtil;
import com.common.util.ResponseMap;
import com.common.util.ResponseStatus;
import com.common.util.ServletUtils;
import com.common.util.StringUtils;
import com.common.util.ThrowableUtil;
import com.sys.auth.model.WxTUser;
import com.sys.organization.dao.OrganizationVoMapper;
import com.wordnik.swagger.annotations.Api;
import com.wordnik.swagger.annotations.ApiImplicitParam;
import com.wordnik.swagger.annotations.ApiOperation;
import com.wordnik.swagger.annotations.ApiParam;

@Controller
@Api(value = "门店订单Controller", tags = "门店订单Controller")
public class OrderController {
	private static final String E_ORDER = "Order";
	private static final String P_M_TYPE = "mType";
	private static final String M_ORDER_API_ES_ILLEGAL_PARAMETER = "order.api.es.illegal_parameter";
	private static final String E_CAUSE = "Cause";
	private static final String E_RESULT = "Result";
	private static final String BILL_ID = "BillID";
	private static final String P_SND_STYLE = "SndStyle";
	private static final String P_ORDER_NO = "OrderNO";
	private static final String A_DATE_TO = "dateTo";
	private static final String A_DATE_FROM = "dateFrom";
	private static final String A_WORKSHOP_ID = "workshopId";
	private static final String ROWS = "rows";
	private static final String TOTAL = "total";
	private static final String A_ORDER_TYPE = "orderType";
	private static final String A_ORDER_NO = "orderNo";
	private static final String RANK_TYPE = "direction";
	private static final String SORT_FILED = "field";
	private static final String LIMIT = "limit";
	private static final String START = "start";

	private static final String QUERY_FILED = "queryField";

	private static final String DA_ORDER_TYPE = "orderTypeDA";
	private static final String DP_ORDER_TYPE = "orderTypeDP";
	private static final String WORKSHOP_NAME = "workshopName";
	private static final String ORDER_PARTNER_ID = "mPartnerId";
	private static final String ORDER_SATTUS = "status";

	private Logger log = LoggerFactory.getLogger(OrderController.class);

	@Autowired
	private OrderVoMapper orderVoMapper;

	@Autowired
	private OrderBizService orderBizService;

	@Autowired
	private OrderService orderService;

	@Autowired
	private OrganizationVoMapper orgMapper;
	@Autowired
	private WxtOrderYYFWSetsVoMapper orderYYFWMapper;
	
	@Autowired
	private OrderVoBizService orderVoBizService;
	
	@ResponseBody
    @ApiOperation(value="保存订单信息接口",  httpMethod="POST", notes="保存订单信息接口")
    @ApiImplicitParam(paramType="query", name="appToken", value="token信息", required=true)
	@RequestMapping(value="/order/save.do")
	public ResponseMap save(@ApiParam(name="records", value="扫码出库订单对象", required=true) @RequestBody OrderVo record) {
		ResponseMap map = new ResponseMap();
		log.info("save: " + JsonUtil.writeValue(record));
		try {
			if(record.getId() == null) {
				orderVoBizService.insert(record);
			}else {
				orderVoBizService.update(record);
			}
			log.info("save success." );
		} catch (WxPltException e) {
			map.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
			map.setErrorMsg(e.getMessage());
		} catch (Exception e) {
			map.handleException(e, this, ContextUtil.getCurUserId(), 
					"com.chevron.pms.controller.OrderController.save", JsonUtil.writeValue(record));
		}
		return map;
	}

	@ResponseBody
	@RequestMapping(value = "/order/queryOrders.do", method = { RequestMethod.POST })
	public Map<String, Object> queryOrders(HttpServletRequest request, HttpServletResponse response) {
		Map<String, Object> resultMap = new HashMap<String, Object>();

		// 1, collect request params
		Map<String, String> requestParamMap = ServletUtils.collectParamValues(request);
		log.info("requestParamMap: {}", requestParamMap);

		// 2, convert request param to generic pagination query param
		GenericPaginationQueryParams genericPagQeryParam = convertRequestParamToQueryParam(requestParamMap);

		// 3, query data from db
		long ordersCount = orderVoMapper.queryOrdersCountByConditionWithPagination(genericPagQeryParam);
		List<OrderVo> orders = orderVoMapper.queryOrdersByConditionWithPagination(genericPagQeryParam);

		// 4, generate return data
		resultMap.put(TOTAL, ordersCount);
		resultMap.put(ROWS, orders);
		return resultMap;
	}

	/**
	 * 供网店管家处理订单业务
	 * 
	 * @param request
	 * @param response
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/order/eshop.do", method = { RequestMethod.POST })
	public void eshopOrderProcess(HttpServletRequest request, HttpServletResponse response) {
		String outputXml = "";

		// 1, collect request params
		Map<String, String> requestParamMap = ServletUtils.collectParamValues(request);
		log.info("requestParamMap: {}", requestParamMap);
		String mType = requestParamMap.get(P_M_TYPE);
		try {
			validateBasicData(requestParamMap);
		} catch (WxPltException e) {
			log.error("eshopOrderProcess exception", e);
			outputXml = generateFailOutputXml(ThrowableUtil.getStackTrace(e));
			responseAsXml(response, outputXml);
			return;
		}
		// String mUcode = requestParamMap.get("uCode");
		// String esApiSPIDUCode = (String)
		// Constants.getSystemPropertyByCodeType(Constants.ES_API_USPID_CODE);
		if ("mOrderSearch".equals(mType)) {

//			if (null == mUcode || StringUtils.isNull(mUcode)) {
//				outputXml = generateFailOutputXml("mUcode is null");
//				responseAsXml(response, outputXml);
//			}
//			if (mUcode.equals(esApiSPIDUCode)) {
//				outputXml = processQueryOrderForES(requestParamMap, OrderBizService.ES_GETORDER_SPID_TYPE_PP);
//			} else {
			outputXml = processQueryOrderForES(requestParamMap);
//			}
		}
		if ("mGetOrder".equals(mType)) {
			outputXml = processGetOrderDetailForES(requestParamMap);
		}

		if ("mSndGoods".equals(mType)) {
			outputXml = processReceiveWaybillInfo(requestParamMap);
		}
		log.info("outputXml: {}", outputXml);
		responseAsXml(response, outputXml);

	}

	private String processReceiveWaybillInfo(Map<String, String> requestParamMap) {
		String outputXml = "";

		try {
			// 2, validate request parameter illegal
			validateRequestParamForReceiveWaybillInfo(requestParamMap);

			// 3, query date from database
			orderBizService.updateWaybillInfo(requestParamMap.get(P_ORDER_NO), requestParamMap.get(P_SND_STYLE),
					requestParamMap.get(BILL_ID));

			outputXml = generateReceiveWaybillInfoSuccessOutputXml();

		} catch (WxPltException e) {
			log.error("processReceiveWaybillInfo WxPltException", e);
			outputXml = generateReceiveWaybillInfoFailOutputXml(e.getExpMsg());
		} catch (Exception e) {
			log.error("processReceiveWaybillInfo exception", e);
			outputXml = generateReceiveWaybillInfoFailOutputXml(ThrowableUtil.getStackTrace(e));
		}

		return outputXml;
	}

	private String processGetOrderDetailForES(Map<String, String> requestParamMap) {
		String outputXml = "";
		String orderNo = requestParamMap.get(P_ORDER_NO);
		try {
			// 2, validate request parameter illegal
			validateRequestParamForGetOrderDetail(requestParamMap);

			// 3, query date from database
			OrderVo order = orderBizService.queryOrderByOrderNo(orderNo);
			List<OrderLineVo> orderLines = null;
			if (order != null) {
				orderLines = orderBizService.queryOrderLinesByOrderId(order.getId());
			}
			outputXml = generateGetOrderDetailSuccessOutputXml(order, orderLines);

		} catch (WxPltException e) {
			log.error("processGetOrderDetailForES WxPltException", e);
			outputXml = generateFailOutputXml(e.getExpMsg());
		} catch (Exception e) {
			log.error("processGetOrderDetailForES exception", e);
			outputXml = generateFailOutputXml(ThrowableUtil.getStackTrace(e));
		}
		return outputXml;
	}

	private String processQueryOrderForES(Map<String, String> requestParamMap) {
		String outputXml;
		try {
			// 1, validate data
			// validateBasicData(requestParamMap); //delete by bo.liu 0526

			String orderStatus = requestParamMap.get("OrderStatus");
			if (!"1".equals(orderStatus)) {
				outputXml = generateQueryOrdersSuccessOutputXml(0L, Collections.<OrderVo>emptyList(), "1");
			} else {
				// 2, convert request param to generic pagination query param
				GenericPaginationQueryParams genericPagQeryParam = convertRequestParamToQueryParamForES(
						requestParamMap);

				// 3, query data from db
				// add by bo.liu
				Map<String, Object> reqMap = new HashMap<String, Object>();
				reqMap.put("reqmaps", genericPagQeryParam);
				// end by bo.liu
				long ordersCount = orderVoMapper.queryOrdersCountByConditionWithPaginationMap(reqMap);
				List<OrderVo> orders = orderVoMapper.queryOrdersByConditionWithPaginationMap(reqMap);

				outputXml = generateQueryOrdersSuccessOutputXml(ordersCount, orders, requestParamMap.get("Page"));
			}
		} catch (Exception e) {
			log.error("processQueryOrderForES exception", e);
			outputXml = generateFailOutputXml(ThrowableUtil.getStackTrace(e));
		}
		return outputXml;
	}

	private String generateReceiveWaybillInfoFailOutputXml(String failMessage) {
		Document doc = DocumentHelper.createDocument();
		Element rootEle = DocumentHelper.createElement("Rsp");
		doc.setRootElement(rootEle);
		rootEle.addElement(E_RESULT).addText("0");
		rootEle.addElement(E_CAUSE).addText(failMessage);
		return doc.asXML();
	}

	private String generateReceiveWaybillInfoSuccessOutputXml() {
		Document doc = DocumentHelper.createDocument();
		Element rootEle = DocumentHelper.createElement("Rsp");
		doc.setRootElement(rootEle);
		rootEle.addElement(E_RESULT).addText("1");
		return doc.asXML();
	}

	private void validateRequestParamForReceiveWaybillInfo(Map<String, String> requestParamMap) throws WxPltException {
		// validateBasicData(requestParamMap);delete by bo.liu 0526
		String logistics = requestParamMap.get(P_SND_STYLE);
		// 物流公式代码 SndCode

		String waybillNo = requestParamMap.get(BILL_ID);
		String orderNo = requestParamMap.get(P_ORDER_NO);

		if (StringUtils.isNull(orderNo) || StringUtils.isNull(logistics) || StringUtils.isNull(waybillNo)) {
			throw new WxPltException(MessageResourceUtil.getMessage(M_ORDER_API_ES_ILLEGAL_PARAMETER));
		}
	}

	private void responseAsXml(HttpServletResponse response, String xml) {
		response.setContentType("application/xml;charset=UTF-8");
		PrintWriter pw = null;
		try {
			pw = response.getWriter();
			pw.print(xml);
		} catch (IOException e) {
			log.error("response exception", e);
		} finally {
			if (pw != null) {
				pw.close();
			}
		}
	}

	private String generateQueryOrdersSuccessOutputXml(long ordersCount, List<OrderVo> orders, String page) {
		Document doc = DocumentHelper.createDocument();
		Element rootEle = DocumentHelper.createElement(E_ORDER);
		doc.setRootElement(rootEle);
		rootEle.addElement(E_RESULT).addText("1");
		rootEle.addElement(E_CAUSE);
		rootEle.addElement("OrderCount").setText(String.valueOf(ordersCount));
		rootEle.addElement("Page").addText(StringUtils.stringNullRep(page, "1"));
		Element orderListEle = rootEle.addElement("OrderList");
		if (orders != null) {
			for (OrderVo orderVo : orders) {
				orderListEle.addElement(P_ORDER_NO).addText(orderVo.getOrderNo());
			}
		}

		return doc.asXML();
	}

	private String generateFailOutputXml(String failMessage) {
		Document doc = DocumentHelper.createDocument();
		Element rootEle = DocumentHelper.createElement(E_ORDER);
		doc.setRootElement(rootEle);
		rootEle.addElement(E_RESULT).setText("0");
		rootEle.addElement(E_CAUSE).setText(failMessage);
		return doc.asXML();
	}

	private String generateGetOrderDetailSuccessOutputXml(OrderVo order, List<OrderLineVo> orderLines) {
		Document doc = DocumentHelper.createDocument();
		Element rootEle = DocumentHelper.createElement(E_ORDER);
		doc.setRootElement(rootEle);

		rootEle.addElement(E_RESULT).addText("1");
		rootEle.addElement(E_CAUSE);
		if (order != null) {
			rootEle.addElement(P_ORDER_NO).addText(order.getOrderNo());
			// 订单状态 OrderStatus

			rootEle.addElement("DateTime").addText(DateUtils.dateToString(order.getCreateTime()));
			Long buyerId = order.getBuyUserNo();
			String buyerName = String.valueOf(order.getReceiveUserName());
			log.info("buyerId: {}, buyerName: {}", buyerId, buyerName);
			if (buyerId == null) {
				rootEle.addElement("BuyerID").addCDATA(order.getBuyUserName());
			} else {
				rootEle.addElement("BuyerID").addCDATA(String.valueOf(buyerId));
			}
			// rootEle.addElement("BuyerName").addCDATA(buyerName);

			rootEle.addElement("BuyerName").addCDATA(buyerName);
			rootEle.addElement("Province").addCDATA(order.getRegionName());
			rootEle.addElement("Adr").addCDATA(order.getAddress());
			rootEle.addElement("Phone").addCDATA(order.getReceivePhoneNo());
			rootEle.addElement("Total").addText(String.valueOf(order.getTotalOrderPrice()));
			rootEle.addElement("Postage").addText(String.valueOf(order.getTotalDeliveryPrice()));
			rootEle.addElement("Chargetype").addCDATA(order.getPayType());
			rootEle.addElement("Remark").addCDATA(order.getRemark());// mod by bo.liu 0804 都用备注字段 替换没有用的车牌号字段

			if (orderLines != null) {
				for (OrderLineVo orderLineVo : orderLines) {
					Element itemEle = rootEle.addElement("Item");
					// 商品明细编号 oid
					String skuStr = orderLineVo.getSkuNew() == null || orderLineVo.getSkuNew().isEmpty()
							? orderLineVo.getSku()
							: orderLineVo.getSkuNew().trim();
					itemEle.addElement("GoodsID").addCDATA(skuStr);
					itemEle.addElement("GoodsName").addCDATA(orderLineVo.getProductName());
					// 商品状态，留空必填GoodsStatus

					itemEle.addElement("Count").addText(String.valueOf(orderLineVo.getAmount()));
					// 价格 Price

				}
			}
		}

		return doc.asXML();
	}

	private void validateRequestParamForGetOrderDetail(Map<String, String> requestParamMap) throws WxPltException {
		// validateBasicData(requestParamMap); delete by bo.liu 0526
		String orderNo = requestParamMap.get(P_ORDER_NO);
		if (StringUtils.isNull(orderNo)) {
			throw new WxPltException(MessageResourceUtil.getMessage(M_ORDER_API_ES_ILLEGAL_PARAMETER));
		}
	}

	// DDBX订单供网店管家业务处理-基础数据验证
	private void validateBasicData(Map<String, String> requestParamMap) throws WxPltException {
		String uCode = requestParamMap.get("uCode");
		String mType = requestParamMap.get(P_M_TYPE);
		String timeStamp = requestParamMap.get("TimeStamp");
		String pSign = requestParamMap.get("Sign");

		if (StringUtils.isBlank(uCode) || StringUtils.isBlank(mType) || StringUtils.isBlank(timeStamp)
				|| StringUtils.isBlank(pSign)) {
			throw new WxPltException(MessageResourceUtil.getMessage(M_ORDER_API_ES_ILLEGAL_PARAMETER));
		}

		String esApiUCode = (String) Constants.getSystemPropertyByCodeType(Constants.ES_API_U_CODE);

		// String esApiSPIDUCode = (String)
		// Constants.getSystemPropertyByCodeType(Constants.ES_API_USPID_CODE);
		String esApiSecret = (String) Constants.getSystemPropertyByCodeType(Constants.ES_API_SECRET);
		String tokenStr = new StringBuilder().append(esApiSecret).append(P_M_TYPE).append(mType).append("TimeStamp")
				.append(timeStamp).append("uCode").append(uCode).append(esApiSecret).toString();
		log.info("eshop tokenStr: {}", tokenStr);
		String sign = DigestUtils.md5Hex(tokenStr).toUpperCase();
		log.info("eshop_sign: {}", pSign);
		if (!sign.equals(pSign)) {
			throw new WxPltException(MessageResourceUtil.getMessage("order.api.es.invalid_sign"));
		}
		if (!esApiUCode.equals(uCode)) {
			throw new WxPltException(MessageResourceUtil.getMessage(M_ORDER_API_ES_ILLEGAL_PARAMETER));
		}

	}

	private GenericPaginationQueryParams convertRequestParamToQueryParam(Map<String, String> requestParamMap) {
		GenericPaginationQueryParams genericPagQueryParam = new GenericPaginationQueryParams();
		genericPagQueryParam.setStart(Long.parseLong(requestParamMap.get(START)));
		genericPagQueryParam.setLimit(Integer.parseInt(requestParamMap.get(LIMIT)));

		String orderByFiled = requestParamMap.get(SORT_FILED);
		String orderByRankType = requestParamMap.get(RANK_TYPE);
		/*
		 * String orderBy = StringUtils.camelToUnderline(orderByFiled) + " " +
		 * orderByRankType; genericPagQueryParam.setOrderBy(orderBy);
		 */
		if (orderByFiled.equals("workshopName")) {
			orderByFiled = "workShopName";
		}

		String orderBy = StringUtils.camelToUnderline(orderByFiled) + " " + orderByRankType;

		genericPagQueryParam.setOrderBy("tt_order." + orderBy);
		if (orderByFiled.equals("orgName")) {
			genericPagQueryParam.setOrderBy("tt_org.organization_name" + " " + orderByRankType);
		}

		Map<String, Object> queryParamsMap = new HashMap<String, Object>();
		queryParamsMap.put(A_ORDER_NO, requestParamMap.get(A_ORDER_NO));
		String orderType = requestParamMap.get(A_ORDER_TYPE);
		if (StringUtils.isNull(orderType)) {
			orderType = null;
			queryParamsMap.put(DA_ORDER_TYPE, OrderVo.DDFW_ORDER_TYPE);// 服务订单类型
			queryParamsMap.put(DP_ORDER_TYPE, OrderVo.DDXB_ORDER_TYPE);// 服务包订单类型
		}
		queryParamsMap.put(A_ORDER_TYPE, orderType);
		String workShopName = requestParamMap.get(WORKSHOP_NAME);
		queryParamsMap.put(WORKSHOP_NAME, StringUtils.isNull(workShopName) ? null : workShopName);

		String pDateFrom = requestParamMap.get(A_DATE_FROM);
		if (!StringUtils.isNull(pDateFrom)) {
			Date dateFrom = DateUtils.stringToDate(pDateFrom, 3);
			queryParamsMap.put(A_DATE_FROM, dateFrom);
		}

		String pDateTo = requestParamMap.get(A_DATE_TO);
		if (!StringUtils.isNull(pDateTo)) {
			Date dateTo = DateUtils.stringToDate(pDateTo, 3);
			queryParamsMap.put(A_DATE_TO, dateTo);
		}
		// 合伙人
		String partnerId = requestParamMap.get(ORDER_PARTNER_ID);
		WxTUser currentUser = ContextUtil.getCurUser();
		Long mUserType = currentUser.getmUserTypes();
		Long mUserPartnerId = currentUser.getOrgId();
		if (mUserType == WxTUser.CHEVRON_USER_ROLE) {

			if (null == partnerId || partnerId.equals("-999") || partnerId.trim().length() == 0) {
				mUserPartnerId = null;
			} else {
				mUserPartnerId = Long.parseLong(partnerId);
			}
		}

		queryParamsMap.put(ORDER_PARTNER_ID, mUserPartnerId);
		// 订单状态
		String orderStatus = requestParamMap.get(ORDER_SATTUS);
		if (null == orderStatus || orderStatus.trim().equals("-1")) {
			orderStatus = null;
		}
		queryParamsMap.put(ORDER_SATTUS, orderStatus);

		// 关键字查询
		String queryField = requestParamMap.get(QUERY_FILED);
		if (null == queryField || queryField.trim().length() == 0) {
			queryField = null;
		}
		queryParamsMap.put(QUERY_FILED, queryField);

		genericPagQueryParam.setQueryParamsMap(queryParamsMap);
		// add by bo.liu 1108
		/* genericPagQueryParam.setOrderBy("tt_order.create_time DESC"); */
		return genericPagQueryParam;
	}

	private GenericPaginationQueryParams convertRequestParamToQueryParamForES(Map<String, String> requestParamMap) {
		GenericPaginationQueryParams genericPagQueryParam = new GenericPaginationQueryParams();
		String pPage = requestParamMap.get("Page");
		String pPageSize = requestParamMap.get("PageSize");
		if (!StringUtils.isNull(pPage) && !StringUtils.isNull(pPageSize)) {
			int page = Integer.parseInt(pPage);
			int pageSize = Integer.parseInt(pPageSize);
			int start = (page - 1) * pageSize;
			genericPagQueryParam.setStart(start);
			genericPagQueryParam.setLimit(pageSize);
		} else {
			genericPagQueryParam.setStart(-1);
			genericPagQueryParam.setLimit(-1);
		}
		genericPagQueryParam.setOrderBy("tt_order.create_time");

		Map<String, Object> queryParamsMap = new HashMap<String, Object>();
		queryParamsMap.put("status", OrderBizService.ORDER_STATUS_TO_BE_SHIPPED);
		queryParamsMap.put("orderType", "DA");
		queryParamsMap.put("source", "DDBX");// add by bo.liu 0710
		// queryParamsMap.put("source_yxc", "ODYXC");//add by bo.liu 0725
		genericPagQueryParam.setQueryParamsMap(queryParamsMap);
		return genericPagQueryParam;
	}

	@ResponseBody
	@RequestMapping(value = "/order/queryOrdersDataTable.do", method = { RequestMethod.POST })
	public Map<String, Object> queryOrdersDataTable(HttpServletRequest request, HttpServletResponse response) {
		Map<String, Object> resultMap = new HashMap<String, Object>();

		// 1, collect request params
		Map<String, String> requestParamMap = ServletUtils.collectParamValues(request);
		log.info("requestParamMap: {}", requestParamMap);

		// 2, convert request param to generic pagination query param
		GenericPaginationQueryParams genericPagQeryParam = convertRequestParamToQueryParamDataTable(requestParamMap);

		// 3, query data from db
		long ordersCount = orderVoMapper.queryOrdersCountByConditionWithPagination(genericPagQeryParam);
		List<OrderVo> orders = orderVoMapper.queryOrdersByConditionWithPagination(genericPagQeryParam);

		// 4, generate return data
		resultMap.put("iTotalRecords", ordersCount);
		resultMap.put("iTotalDisplayRecords", ordersCount);
		resultMap.put("data", orders);
		return resultMap;
	}

	private GenericPaginationQueryParams convertRequestParamToQueryParamDataTable(Map<String, String> requestParamMap) {
		GenericPaginationQueryParams genericPagQueryParam = new GenericPaginationQueryParams();
		genericPagQueryParam.setStart(Long.parseLong(requestParamMap.get(START)));
		genericPagQueryParam.setLimit(Integer.parseInt(requestParamMap.get("length")));

		String orderColumnIndex = (String) requestParamMap.get("order[0][column]");
		String orderField = (String) requestParamMap.get("columns[" + orderColumnIndex + "][data]");
		if (orderField.equals("workshopName")) {
			orderField = "workShopName";
		}

		String direction = (String) requestParamMap.get("order[0][dir]");

		String orderByFiled = orderField;
		String orderByRankType = direction;
		String orderBy = StringUtils.camelToUnderline(orderByFiled) + " " + orderByRankType;

		genericPagQueryParam.setOrderBy("tt_order." + orderBy);
		if (orderField.equals("orgName")) {
			genericPagQueryParam.setOrderBy("tt_org.organization_name");
		}

		Map<String, Object> queryParamsMap = new HashMap<String, Object>();
		queryParamsMap.put(A_ORDER_NO, requestParamMap.get(A_ORDER_NO));
		String orderType = requestParamMap.get(A_ORDER_TYPE);
		if (StringUtils.isNull(orderType)) {
			orderType = null;
			queryParamsMap.put(DA_ORDER_TYPE, OrderVo.DDFW_ORDER_TYPE);// 服务订单类型
			queryParamsMap.put(DP_ORDER_TYPE, OrderVo.DDXB_ORDER_TYPE);// 服务包订单类型
		}
		queryParamsMap.put(A_ORDER_TYPE, orderType);
		String workShopName = requestParamMap.get(WORKSHOP_NAME);
		queryParamsMap.put(WORKSHOP_NAME, StringUtils.isNull(workShopName) ? null : workShopName);

		String pDateFrom = requestParamMap.get(A_DATE_FROM);
		if (!StringUtils.isNull(pDateFrom)) {
			Date dateFrom = DateUtils.stringToDate(pDateFrom, 3);
			queryParamsMap.put(A_DATE_FROM, dateFrom);
		}

		String pDateTo = requestParamMap.get(A_DATE_TO);
		if (!StringUtils.isNull(pDateTo)) {
			Date dateTo = DateUtils.stringToDate(pDateTo, 3);
			queryParamsMap.put(A_DATE_TO, dateTo);
		}
		// 合伙人
		String partnerId = requestParamMap.get(ORDER_PARTNER_ID);
		WxTUser currentUser = ContextUtil.getCurUser();
		Long mUserType = currentUser.getmUserTypes();
		Long mUserPartnerId = currentUser.getOrgId();
		if (mUserType == WxTUser.CHEVRON_USER_ROLE) {

			if (null == partnerId || partnerId.equals("-999")) {
				mUserPartnerId = null;
			} else {
				mUserPartnerId = Long.parseLong(partnerId);
			}
		}

		queryParamsMap.put(ORDER_PARTNER_ID, mUserPartnerId);
		// 订单状态
		String orderStatus = requestParamMap.get(ORDER_SATTUS);
		if (null == orderStatus || orderStatus.trim().equals("-1")) {
			orderStatus = null;
		}
		queryParamsMap.put(ORDER_SATTUS, orderStatus);

		genericPagQueryParam.setQueryParamsMap(queryParamsMap);
		return genericPagQueryParam;
	}

	@ResponseBody
	@RequestMapping(value = "order/downloadOutstockPDF.do", method = { RequestMethod.GET, RequestMethod.POST })
	public void PDFchange(HttpServletRequest request, HttpServletResponse response) throws Exception {
		Long orderId = Long.valueOf(request.getParameter("orderId"));
		// 从数据库中查询需要的条件

		Map<String, Object> variables = new HashMap<String, Object>();
		variables = orderService.generataOrderOutstockPDF(orderId);
		String ftlName = "outstock_templete.ftl";
		// 然后组装好之后调用该方法 FTLIMAGEPATH是用的配置模板中的内容实现的
		String FTLIMAGEPATH = "images\\logo2.png";
		String outstockNo = (String) variables.get("outstockNo");
		String basePath = request.getSession().getServletContext().getRealPath("/");// 绝对路径
		String imgFullName = BarCodeUtil.createBarCode(basePath + "images/barcodeimage/", outstockNo, ImageUtil.JPEG);
		log.info("basePath: " + basePath + ",imgFullName: " + imgFullName);
		variables.put("imgFullName", imgFullName);
		String baseUrl = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() + "/";
		variables.put("baseUrl", baseUrl);
		log.info("baseUrl", baseUrl);
		String stockNo = (String) variables.get("outstockNo");
		log.info("imgUrl", baseUrl + "images/barcodeimage/" + imgFullName);
		System.out.println(baseUrl + "images/barcodeimage/" + imgFullName);
		OutputStream bos = null;
		try {
			PDFUtil.setFileDownloadHeader(response, stockNo, ".pdf");
			bos = PDFUtil.createPDF(request, response, ftlName, variables, FTLIMAGEPATH);/** 字节 */
		} catch (Exception e) {
			log.error("出库单生成失败", e);
		} finally {
			if (bos != null)
				bos.close();
		}

	}

	// start for sp es order by bo.liu 0526 特别订单查询
	// 通过网店管家处理订单-基础数据验证
	private void validateBasicDataNew(Map<String, String> requestParamMap, String ucode, String secret)
			throws WxPltException {
		String uCode = requestParamMap.get("uCode");
		String mType = requestParamMap.get(P_M_TYPE);
		String timeStamp = requestParamMap.get("TimeStamp");
		String pSign = requestParamMap.get("Sign");

		if (StringUtils.isNull(uCode) || StringUtils.isNull(mType) || StringUtils.isNull(timeStamp)
				|| StringUtils.isNull(pSign)) {
			throw new WxPltException(MessageResourceUtil.getMessage(M_ORDER_API_ES_ILLEGAL_PARAMETER));
		}
		// String esApiSPIDUCode = (String)
		// Constants.getSystemPropertyByCodeType(ucode);
		// String esApiSecret = (String) Constants.getSystemPropertyByCodeType(secret);
		String tokenStr = new StringBuilder().append(secret).append(P_M_TYPE).append(mType).append("TimeStamp")
				.append(timeStamp).append("uCode").append(uCode).append(secret).toString();
		log.info("tokenStr: {}", tokenStr);
		String sign = DigestUtils.md5Hex(tokenStr).toUpperCase();
		log.info("_sign: {}", pSign);
		if (!sign.equals(pSign)) {
			throw new WxPltException(MessageResourceUtil.getMessage("order.api.es.invalid_sign"));
		}
		if (!ucode.equals(uCode)) {
			throw new WxPltException(MessageResourceUtil.getMessage(M_ORDER_API_ES_ILLEGAL_PARAMETER));
		}
	}

	/**
	 * 
	 * <AUTHOR> 2017-5-19 下午3:21:30
	 * @param requestParamMap
	 * @param mType
	 * @return
	 */
	private GenericPaginationQueryParams convertRequestParamToQueryParamForESNew(Map<String, String> requestParamMap,
			String mType) {
		GenericPaginationQueryParams genericPagQueryParam = new GenericPaginationQueryParams();
		String pPage = requestParamMap.get("Page");
		String pPageSize = requestParamMap.get("PageSize");
		if (!StringUtils.isNull(pPage) && !StringUtils.isNull(pPageSize)) {
			int page = Integer.parseInt(pPage);
			int pageSize = Integer.parseInt(pPageSize);
			int start = (page - 1) * pageSize;
			genericPagQueryParam.setStart(start);
			genericPagQueryParam.setLimit(pageSize);
		} else {
			genericPagQueryParam.setStart(-1);
			genericPagQueryParam.setLimit(-1);
		}
		genericPagQueryParam.setOrderBy("tt_order.create_time");

		Map<String, Object> queryParamsMap = new HashMap<String, Object>();
		queryParamsMap.put("status", OrderBizService.ORDER_STATUS_TO_BE_SHIPPED);
		queryParamsMap.put("orderType", "DA");
		if (OrderBizService.ES_GETORDER_SPID_TYPE_PP.equals(mType)) {
			/*
			 * Map<String, Object> reqMap = new HashMap<String, Object>();
			 * reqMap.put("partnerName", OrderBizService.ES_GETORDER_SPID_NAME);
			 * List<PartnerView> lstPartners = orgMapper.selectPartnersByParams(reqMap);
			 * Long spid = OrderBizService.ES_GETORDER_SPID_DEFAULT; for(PartnerView
			 * partner:lstPartners) {
			 * if(partner.getName().equals(OrderBizService.ES_GETORDER_SPID_NAME)) { spid =
			 * partner.getId(); } } queryParamsMap.put("mPartnerId", spid);
			 */
			queryParamsMap.put("orderType", "SPDA");
		} else if (OrderBizService.ES_GETORDER_DDYYFW_TYPE_PP.equals(mType)) {
			// 滴滴单独模板、待通用模板使用后，此不再使用
			queryParamsMap.put("source", "DDYYFW");// add by bo.liu 0710
		} else if (OrderBizService.ES_GETORDER_YYFW_TYPE_PP.equals(mType)) {
			// 需要查询预约服务订单中配置，所有的sources
			String sourceVlue = requestParamMap.get("msourceVlue");
			queryParamsMap.put("msourceVlue", sourceVlue);
			List<String> sourceVlues = orderYYFWMapper.getAllSourceValues(queryParamsMap);
			queryParamsMap.put("sourceValues", sourceVlues);// add by bo.liu 0803
		}
		genericPagQueryParam.setQueryParamsMap(queryParamsMap);
		return genericPagQueryParam;
	}

	private String processQueryOrderForESNew(Map<String, String> requestParamMap, String mType) {
		String outputXml;
		try {
			// 1, validate data
			// validateBasicData(requestParamMap);

			String orderStatus = requestParamMap.get("OrderStatus");
			if (!"1".equals(orderStatus)) {
				outputXml = generateQueryOrdersSuccessOutputXml(0L, Collections.<OrderVo>emptyList(), "1");
			} else {
				// 2, convert request param to generic pagination query param
				GenericPaginationQueryParams genericPagQeryParam = convertRequestParamToQueryParamForESNew(
						requestParamMap, mType);

				// 3, query data from db
				// add by bo.liu
				Map<String, Object> reqMap = new HashMap<String, Object>();
				reqMap.put("reqmaps", genericPagQeryParam);
				// end by bo.liu
				long ordersCount = orderVoMapper.queryOrdersCountByConditionWithPaginationMap(reqMap);
				List<OrderVo> orders = orderVoMapper.queryOrdersByConditionWithPaginationMap(reqMap);

				outputXml = generateQueryOrdersSuccessOutputXml(ordersCount, orders, requestParamMap.get("Page"));
			}
		} catch (Exception e) {
			log.error("processQueryOrderForES exception", e);
			outputXml = generateFailOutputXml(ThrowableUtil.getStackTrace(e));
		}
		return outputXml;
	}

	/**
	 * 供网店管家处理订单业务 处理指定SP（合伙人的销售订单信息）
	 * 
	 * @param request
	 * @param response
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/order/eshopsp.do", method = { RequestMethod.POST })
	public void eshopSPOrderProcess(HttpServletRequest request, HttpServletResponse response) {
		String outputXml = "";

		// 1, collect request params
		Map<String, String> requestParamMap = ServletUtils.collectParamValues(request);
		log.info("SPEshopOrderProcess requestParamMap: {}", requestParamMap);

		String mType = requestParamMap.get(P_M_TYPE);
		String uCode = requestParamMap.get("uCode");
		try {

			Map<String, Object> reqMap = new HashMap<String, Object>();
			reqMap.put("uCode", uCode);

			String esApiSPIDUCode = (String) Constants.getSystemPropertyByCodeType(Constants.ES_API_SPID_UCODE);
			String esApiSecret = (String) Constants.getSystemPropertyByCodeType(Constants.ES_API_SPID_SECRET);
			validateBasicDataNew(requestParamMap, esApiSPIDUCode, esApiSecret);
		} catch (WxPltException e) {
			log.error("SPEshopOrderProcess exception", e);
			outputXml = generateFailOutputXml(ThrowableUtil.getStackTrace(e));
			responseAsXml(response, outputXml);
			return;
		}
		if ("mOrderSearch".equals(mType)) {
			outputXml = processQueryOrderForESNew(requestParamMap, OrderBizService.ES_GETORDER_SPID_TYPE_PP);
		}
		if ("mGetOrder".equals(mType)) {
			outputXml = processGetOrderDetailForES(requestParamMap);
		}

		if ("mSndGoods".equals(mType)) {
			outputXml = processReceiveWaybillInfo(requestParamMap);
		}

		log.info("SPEshopOrderProcess outputXml: {}", outputXml);
		responseAsXml(response, outputXml);

	}
	// end for sp es order by bo.liu 0526

	// 滴滴预约服务订单通过网店管家接口 start
	/**
	 * 后续不再使用， 使用通用模板
	 * 
	 * <AUTHOR> 2017-7-10 下午4:13:15
	 * @param request
	 * @param response
	 */
	@ResponseBody
	@RequestMapping(value = "/order/eshopddyyfw.do", method = { RequestMethod.POST })
	public void eshopDDYYFWOrderProcess(HttpServletRequest request, HttpServletResponse response) {
		String outputXml = "";

		// 1, collect request params
		Map<String, String> requestParamMap = ServletUtils.collectParamValues(request);
		log.info("eshopDDYYFWOrderProcess requestParamMap: {}", requestParamMap);

		String mType = requestParamMap.get(P_M_TYPE);
		try {
			String ddUCode = (String) Constants.getSystemPropertyByCodeType(Constants.ES_API_DDYYC_UCODE);
			String ddSecret = (String) Constants.getSystemPropertyByCodeType(Constants.ES_API_DDYYC_SECRET);
			validateBasicDataNew(requestParamMap, ddUCode, ddSecret);
		} catch (WxPltException e) {
			log.error("eshopDDYYFWOrderProcess exception", e);
			outputXml = generateFailOutputXml(ThrowableUtil.getStackTrace(e));
			responseAsXml(response, outputXml);
			return;
		}
		if ("mOrderSearch".equals(mType)) {
			outputXml = processQueryOrderForESNew(requestParamMap, OrderBizService.ES_GETORDER_DDYYFW_TYPE_PP);
		}
		if ("mGetOrder".equals(mType)) {
			outputXml = processGetOrderDetailForES(requestParamMap);
		}

		if ("mSndGoods".equals(mType)) {
			outputXml = processReceiveWaybillInfo(requestParamMap);
		}

		log.info("eshopDDYYFWOrderProcess outputXml: {}", outputXml);
		responseAsXml(response, outputXml);

	}

	/**
	 * 通用模板，预约服务订单
	 * 
	 * <AUTHOR> 2017-8-3 下午3:06:01
	 * @param request
	 * @param response
	 */
	@ResponseBody
	@RequestMapping(value = "/order/eshopyyfw.do", method = { RequestMethod.POST })
	public void eshopYYFWOrderProcess(HttpServletRequest request, HttpServletResponse response) {
		String outputXml = "";

		// 1, collect request params
		Map<String, String> requestParamMap = ServletUtils.collectParamValues(request);
		log.info("eshopYYFWOrderProcess requestParamMap: {}", requestParamMap);

		String mType = requestParamMap.get(P_M_TYPE);
		String uCode = requestParamMap.get("uCode");
		try {
			Map<String, Object> reqMap = new HashMap<String, Object>();
			reqMap.put("uCode", uCode);
			List<WxtOrderYYFWSetsVo> lstYYFW = orderYYFWMapper.queryOrderYYFWInfoByMap(reqMap);
			String ucode = lstYYFW.get(0).getuCode().trim();
			String secret = lstYYFW.get(0).getSecret().trim();
			requestParamMap.put("msourceVlue", lstYYFW.get(0).getSourceValue().trim());
			validateBasicDataNew(requestParamMap, ucode, secret);// 这个使用之前单独定义的滴滴
		} catch (WxPltException e) {
			log.error("eshopYYFWOrderProcess exception", e);
			outputXml = generateFailOutputXml(ThrowableUtil.getStackTrace(e));
			responseAsXml(response, outputXml);
			return;
		}
		if ("mOrderSearch".equals(mType)) {
			outputXml = processQueryOrderForESNew(requestParamMap, OrderBizService.ES_GETORDER_YYFW_TYPE_PP);// 通用模板
																												// 预约服务订单
		}
		if ("mGetOrder".equals(mType)) {
			outputXml = processGetOrderDetailForES(requestParamMap);
		}

		if ("mSndGoods".equals(mType)) {
			outputXml = processReceiveWaybillInfo(requestParamMap);
		}

		log.info("eshopYYFWOrderProcess outputXml: {}", outputXml);
		responseAsXml(response, outputXml);

	}
	// 滴滴预约服务订单通过网店管家接口 end
}
