package com.chevron.task.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class WxQjbsBillExample {
    /**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table wx_qjbs_bill
	 * @mbggenerated  Mon Oct 12 16:55:00 CST 2015
	 */
	protected String orderByClause;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table wx_qjbs_bill
	 * @mbggenerated  Mon Oct 12 16:55:00 CST 2015
	 */
	protected boolean distinct;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table wx_qjbs_bill
	 * @mbggenerated  Mon Oct 12 16:55:00 CST 2015
	 */
	protected List<Criteria> oredCriteria;

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table wx_qjbs_bill
	 * @mbggenerated  Mon Oct 12 16:55:00 CST 2015
	 */
	public WxQjbsBillExample() {
		oredCriteria = new ArrayList<Criteria>();
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table wx_qjbs_bill
	 * @mbggenerated  Mon Oct 12 16:55:00 CST 2015
	 */
	public void setOrderByClause(String orderByClause) {
		this.orderByClause = orderByClause;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table wx_qjbs_bill
	 * @mbggenerated  Mon Oct 12 16:55:00 CST 2015
	 */
	public String getOrderByClause() {
		return orderByClause;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table wx_qjbs_bill
	 * @mbggenerated  Mon Oct 12 16:55:00 CST 2015
	 */
	public void setDistinct(boolean distinct) {
		this.distinct = distinct;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table wx_qjbs_bill
	 * @mbggenerated  Mon Oct 12 16:55:00 CST 2015
	 */
	public boolean isDistinct() {
		return distinct;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table wx_qjbs_bill
	 * @mbggenerated  Mon Oct 12 16:55:00 CST 2015
	 */
	public List<Criteria> getOredCriteria() {
		return oredCriteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table wx_qjbs_bill
	 * @mbggenerated  Mon Oct 12 16:55:00 CST 2015
	 */
	public void or(Criteria criteria) {
		oredCriteria.add(criteria);
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table wx_qjbs_bill
	 * @mbggenerated  Mon Oct 12 16:55:00 CST 2015
	 */
	public Criteria or() {
		Criteria criteria = createCriteriaInternal();
		oredCriteria.add(criteria);
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table wx_qjbs_bill
	 * @mbggenerated  Mon Oct 12 16:55:00 CST 2015
	 */
	public Criteria createCriteria() {
		Criteria criteria = createCriteriaInternal();
		if (oredCriteria.size() == 0) {
			oredCriteria.add(criteria);
		}
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table wx_qjbs_bill
	 * @mbggenerated  Mon Oct 12 16:55:00 CST 2015
	 */
	protected Criteria createCriteriaInternal() {
		Criteria criteria = new Criteria();
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table wx_qjbs_bill
	 * @mbggenerated  Mon Oct 12 16:55:00 CST 2015
	 */
	public void clear() {
		oredCriteria.clear();
		orderByClause = null;
		distinct = false;
	}

	/**
	 * This class was generated by MyBatis Generator. This class corresponds to the database table wx_qjbs_bill
	 * @mbggenerated  Mon Oct 12 16:55:00 CST 2015
	 */
	protected abstract static class GeneratedCriteria {
		protected List<Criterion> criteria;

		protected GeneratedCriteria() {
			super();
			criteria = new ArrayList<Criterion>();
		}

		public boolean isValid() {
			return criteria.size() > 0;
		}

		public List<Criterion> getAllCriteria() {
			return criteria;
		}

		public List<Criterion> getCriteria() {
			return criteria;
		}

		protected void addCriterion(String condition) {
			if (condition == null) {
				throw new RuntimeException("Value for condition cannot be null");
			}
			criteria.add(new Criterion(condition));
		}

		protected void addCriterion(String condition, Object value,
				String property) {
			if (value == null) {
				throw new RuntimeException("Value for " + property
						+ " cannot be null");
			}
			criteria.add(new Criterion(condition, value));
		}

		protected void addCriterion(String condition, Object value1,
				Object value2, String property) {
			if (value1 == null || value2 == null) {
				throw new RuntimeException("Between values for " + property
						+ " cannot be null");
			}
			criteria.add(new Criterion(condition, value1, value2));
		}

		public Criteria andQjbsIdIsNull() {
			addCriterion("qjbs_id is null");
			return (Criteria) this;
		}

		public Criteria andQjbsIdIsNotNull() {
			addCriterion("qjbs_id is not null");
			return (Criteria) this;
		}

		public Criteria andQjbsIdEqualTo(Long value) {
			addCriterion("qjbs_id =", value, "qjbsId");
			return (Criteria) this;
		}

		public Criteria andQjbsIdNotEqualTo(Long value) {
			addCriterion("qjbs_id <>", value, "qjbsId");
			return (Criteria) this;
		}

		public Criteria andQjbsIdGreaterThan(Long value) {
			addCriterion("qjbs_id >", value, "qjbsId");
			return (Criteria) this;
		}

		public Criteria andQjbsIdGreaterThanOrEqualTo(Long value) {
			addCriterion("qjbs_id >=", value, "qjbsId");
			return (Criteria) this;
		}

		public Criteria andQjbsIdLessThan(Long value) {
			addCriterion("qjbs_id <", value, "qjbsId");
			return (Criteria) this;
		}

		public Criteria andQjbsIdLessThanOrEqualTo(Long value) {
			addCriterion("qjbs_id <=", value, "qjbsId");
			return (Criteria) this;
		}

		public Criteria andQjbsIdIn(List<Long> values) {
			addCriterion("qjbs_id in", values, "qjbsId");
			return (Criteria) this;
		}

		public Criteria andQjbsIdNotIn(List<Long> values) {
			addCriterion("qjbs_id not in", values, "qjbsId");
			return (Criteria) this;
		}

		public Criteria andQjbsIdBetween(Long value1, Long value2) {
			addCriterion("qjbs_id between", value1, value2, "qjbsId");
			return (Criteria) this;
		}

		public Criteria andQjbsIdNotBetween(Long value1, Long value2) {
			addCriterion("qjbs_id not between", value1, value2, "qjbsId");
			return (Criteria) this;
		}

		public Criteria andTaskMainIdIsNull() {
			addCriterion("task_main_id is null");
			return (Criteria) this;
		}

		public Criteria andTaskMainIdIsNotNull() {
			addCriterion("task_main_id is not null");
			return (Criteria) this;
		}

		public Criteria andTaskMainIdEqualTo(Long value) {
			addCriterion("task_main_id =", value, "taskMainId");
			return (Criteria) this;
		}

		public Criteria andTaskMainIdNotEqualTo(Long value) {
			addCriterion("task_main_id <>", value, "taskMainId");
			return (Criteria) this;
		}

		public Criteria andTaskMainIdGreaterThan(Long value) {
			addCriterion("task_main_id >", value, "taskMainId");
			return (Criteria) this;
		}

		public Criteria andTaskMainIdGreaterThanOrEqualTo(Long value) {
			addCriterion("task_main_id >=", value, "taskMainId");
			return (Criteria) this;
		}

		public Criteria andTaskMainIdLessThan(Long value) {
			addCriterion("task_main_id <", value, "taskMainId");
			return (Criteria) this;
		}

		public Criteria andTaskMainIdLessThanOrEqualTo(Long value) {
			addCriterion("task_main_id <=", value, "taskMainId");
			return (Criteria) this;
		}

		public Criteria andTaskMainIdIn(List<Long> values) {
			addCriterion("task_main_id in", values, "taskMainId");
			return (Criteria) this;
		}

		public Criteria andTaskMainIdNotIn(List<Long> values) {
			addCriterion("task_main_id not in", values, "taskMainId");
			return (Criteria) this;
		}

		public Criteria andTaskMainIdBetween(Long value1, Long value2) {
			addCriterion("task_main_id between", value1, value2, "taskMainId");
			return (Criteria) this;
		}

		public Criteria andTaskMainIdNotBetween(Long value1, Long value2) {
			addCriterion("task_main_id not between", value1, value2,
					"taskMainId");
			return (Criteria) this;
		}

		public Criteria andBillNoIsNull() {
			addCriterion("bill_no is null");
			return (Criteria) this;
		}

		public Criteria andBillNoIsNotNull() {
			addCriterion("bill_no is not null");
			return (Criteria) this;
		}

		public Criteria andBillNoEqualTo(String value) {
			addCriterion("bill_no =", value, "billNo");
			return (Criteria) this;
		}

		public Criteria andBillNoNotEqualTo(String value) {
			addCriterion("bill_no <>", value, "billNo");
			return (Criteria) this;
		}

		public Criteria andBillNoGreaterThan(String value) {
			addCriterion("bill_no >", value, "billNo");
			return (Criteria) this;
		}

		public Criteria andBillNoGreaterThanOrEqualTo(String value) {
			addCriterion("bill_no >=", value, "billNo");
			return (Criteria) this;
		}

		public Criteria andBillNoLessThan(String value) {
			addCriterion("bill_no <", value, "billNo");
			return (Criteria) this;
		}

		public Criteria andBillNoLessThanOrEqualTo(String value) {
			addCriterion("bill_no <=", value, "billNo");
			return (Criteria) this;
		}

		public Criteria andBillNoLike(String value) {
			addCriterion("bill_no like", value, "billNo");
			return (Criteria) this;
		}

		public Criteria andBillNoNotLike(String value) {
			addCriterion("bill_no not like", value, "billNo");
			return (Criteria) this;
		}

		public Criteria andBillNoIn(List<String> values) {
			addCriterion("bill_no in", values, "billNo");
			return (Criteria) this;
		}

		public Criteria andBillNoNotIn(List<String> values) {
			addCriterion("bill_no not in", values, "billNo");
			return (Criteria) this;
		}

		public Criteria andBillNoBetween(String value1, String value2) {
			addCriterion("bill_no between", value1, value2, "billNo");
			return (Criteria) this;
		}

		public Criteria andBillNoNotBetween(String value1, String value2) {
			addCriterion("bill_no not between", value1, value2, "billNo");
			return (Criteria) this;
		}

		public Criteria andBranchNameIsNull() {
			addCriterion("branch_name is null");
			return (Criteria) this;
		}

		public Criteria andBranchNameIsNotNull() {
			addCriterion("branch_name is not null");
			return (Criteria) this;
		}

		public Criteria andBranchNameEqualTo(String value) {
			addCriterion("branch_name =", value, "branchName");
			return (Criteria) this;
		}

		public Criteria andBranchNameNotEqualTo(String value) {
			addCriterion("branch_name <>", value, "branchName");
			return (Criteria) this;
		}

		public Criteria andBranchNameGreaterThan(String value) {
			addCriterion("branch_name >", value, "branchName");
			return (Criteria) this;
		}

		public Criteria andBranchNameGreaterThanOrEqualTo(String value) {
			addCriterion("branch_name >=", value, "branchName");
			return (Criteria) this;
		}

		public Criteria andBranchNameLessThan(String value) {
			addCriterion("branch_name <", value, "branchName");
			return (Criteria) this;
		}

		public Criteria andBranchNameLessThanOrEqualTo(String value) {
			addCriterion("branch_name <=", value, "branchName");
			return (Criteria) this;
		}

		public Criteria andBranchNameLike(String value) {
			addCriterion("branch_name like", value, "branchName");
			return (Criteria) this;
		}

		public Criteria andBranchNameNotLike(String value) {
			addCriterion("branch_name not like", value, "branchName");
			return (Criteria) this;
		}

		public Criteria andBranchNameIn(List<String> values) {
			addCriterion("branch_name in", values, "branchName");
			return (Criteria) this;
		}

		public Criteria andBranchNameNotIn(List<String> values) {
			addCriterion("branch_name not in", values, "branchName");
			return (Criteria) this;
		}

		public Criteria andBranchNameBetween(String value1, String value2) {
			addCriterion("branch_name between", value1, value2, "branchName");
			return (Criteria) this;
		}

		public Criteria andBranchNameNotBetween(String value1, String value2) {
			addCriterion("branch_name not between", value1, value2,
					"branchName");
			return (Criteria) this;
		}

		public Criteria andCmsCodeIsNull() {
			addCriterion("cms_code is null");
			return (Criteria) this;
		}

		public Criteria andCmsCodeIsNotNull() {
			addCriterion("cms_code is not null");
			return (Criteria) this;
		}

		public Criteria andCmsCodeEqualTo(String value) {
			addCriterion("cms_code =", value, "cmsCode");
			return (Criteria) this;
		}

		public Criteria andCmsCodeNotEqualTo(String value) {
			addCriterion("cms_code <>", value, "cmsCode");
			return (Criteria) this;
		}

		public Criteria andCmsCodeGreaterThan(String value) {
			addCriterion("cms_code >", value, "cmsCode");
			return (Criteria) this;
		}

		public Criteria andCmsCodeGreaterThanOrEqualTo(String value) {
			addCriterion("cms_code >=", value, "cmsCode");
			return (Criteria) this;
		}

		public Criteria andCmsCodeLessThan(String value) {
			addCriterion("cms_code <", value, "cmsCode");
			return (Criteria) this;
		}

		public Criteria andCmsCodeLessThanOrEqualTo(String value) {
			addCriterion("cms_code <=", value, "cmsCode");
			return (Criteria) this;
		}

		public Criteria andCmsCodeLike(String value) {
			addCriterion("cms_code like", value, "cmsCode");
			return (Criteria) this;
		}

		public Criteria andCmsCodeNotLike(String value) {
			addCriterion("cms_code not like", value, "cmsCode");
			return (Criteria) this;
		}

		public Criteria andCmsCodeIn(List<String> values) {
			addCriterion("cms_code in", values, "cmsCode");
			return (Criteria) this;
		}

		public Criteria andCmsCodeNotIn(List<String> values) {
			addCriterion("cms_code not in", values, "cmsCode");
			return (Criteria) this;
		}

		public Criteria andCmsCodeBetween(String value1, String value2) {
			addCriterion("cms_code between", value1, value2, "cmsCode");
			return (Criteria) this;
		}

		public Criteria andCmsCodeNotBetween(String value1, String value2) {
			addCriterion("cms_code not between", value1, value2, "cmsCode");
			return (Criteria) this;
		}

		public Criteria andAddressIsNull() {
			addCriterion("address is null");
			return (Criteria) this;
		}

		public Criteria andAddressIsNotNull() {
			addCriterion("address is not null");
			return (Criteria) this;
		}

		public Criteria andAddressEqualTo(String value) {
			addCriterion("address =", value, "address");
			return (Criteria) this;
		}

		public Criteria andAddressNotEqualTo(String value) {
			addCriterion("address <>", value, "address");
			return (Criteria) this;
		}

		public Criteria andAddressGreaterThan(String value) {
			addCriterion("address >", value, "address");
			return (Criteria) this;
		}

		public Criteria andAddressGreaterThanOrEqualTo(String value) {
			addCriterion("address >=", value, "address");
			return (Criteria) this;
		}

		public Criteria andAddressLessThan(String value) {
			addCriterion("address <", value, "address");
			return (Criteria) this;
		}

		public Criteria andAddressLessThanOrEqualTo(String value) {
			addCriterion("address <=", value, "address");
			return (Criteria) this;
		}

		public Criteria andAddressLike(String value) {
			addCriterion("address like", value, "address");
			return (Criteria) this;
		}

		public Criteria andAddressNotLike(String value) {
			addCriterion("address not like", value, "address");
			return (Criteria) this;
		}

		public Criteria andAddressIn(List<String> values) {
			addCriterion("address in", values, "address");
			return (Criteria) this;
		}

		public Criteria andAddressNotIn(List<String> values) {
			addCriterion("address not in", values, "address");
			return (Criteria) this;
		}

		public Criteria andAddressBetween(String value1, String value2) {
			addCriterion("address between", value1, value2, "address");
			return (Criteria) this;
		}

		public Criteria andAddressNotBetween(String value1, String value2) {
			addCriterion("address not between", value1, value2, "address");
			return (Criteria) this;
		}

		public Criteria andStartTimeIsNull() {
			addCriterion("start_time is null");
			return (Criteria) this;
		}

		public Criteria andStartTimeIsNotNull() {
			addCriterion("start_time is not null");
			return (Criteria) this;
		}

		public Criteria andStartTimeEqualTo(Date value) {
			addCriterion("start_time =", value, "startTime");
			return (Criteria) this;
		}

		public Criteria andStartTimeNotEqualTo(Date value) {
			addCriterion("start_time <>", value, "startTime");
			return (Criteria) this;
		}

		public Criteria andStartTimeGreaterThan(Date value) {
			addCriterion("start_time >", value, "startTime");
			return (Criteria) this;
		}

		public Criteria andStartTimeGreaterThanOrEqualTo(Date value) {
			addCriterion("start_time >=", value, "startTime");
			return (Criteria) this;
		}

		public Criteria andStartTimeLessThan(Date value) {
			addCriterion("start_time <", value, "startTime");
			return (Criteria) this;
		}

		public Criteria andStartTimeLessThanOrEqualTo(Date value) {
			addCriterion("start_time <=", value, "startTime");
			return (Criteria) this;
		}

		public Criteria andStartTimeIn(List<Date> values) {
			addCriterion("start_time in", values, "startTime");
			return (Criteria) this;
		}

		public Criteria andStartTimeNotIn(List<Date> values) {
			addCriterion("start_time not in", values, "startTime");
			return (Criteria) this;
		}

		public Criteria andStartTimeBetween(Date value1, Date value2) {
			addCriterion("start_time between", value1, value2, "startTime");
			return (Criteria) this;
		}

		public Criteria andStartTimeNotBetween(Date value1, Date value2) {
			addCriterion("start_time not between", value1, value2, "startTime");
			return (Criteria) this;
		}

		public Criteria andBsrContactIsNull() {
			addCriterion("bsr_contact is null");
			return (Criteria) this;
		}

		public Criteria andBsrContactIsNotNull() {
			addCriterion("bsr_contact is not null");
			return (Criteria) this;
		}

		public Criteria andBsrContactEqualTo(String value) {
			addCriterion("bsr_contact =", value, "bsrContact");
			return (Criteria) this;
		}

		public Criteria andBsrContactNotEqualTo(String value) {
			addCriterion("bsr_contact <>", value, "bsrContact");
			return (Criteria) this;
		}

		public Criteria andBsrContactGreaterThan(String value) {
			addCriterion("bsr_contact >", value, "bsrContact");
			return (Criteria) this;
		}

		public Criteria andBsrContactGreaterThanOrEqualTo(String value) {
			addCriterion("bsr_contact >=", value, "bsrContact");
			return (Criteria) this;
		}

		public Criteria andBsrContactLessThan(String value) {
			addCriterion("bsr_contact <", value, "bsrContact");
			return (Criteria) this;
		}

		public Criteria andBsrContactLessThanOrEqualTo(String value) {
			addCriterion("bsr_contact <=", value, "bsrContact");
			return (Criteria) this;
		}

		public Criteria andBsrContactLike(String value) {
			addCriterion("bsr_contact like", value, "bsrContact");
			return (Criteria) this;
		}

		public Criteria andBsrContactNotLike(String value) {
			addCriterion("bsr_contact not like", value, "bsrContact");
			return (Criteria) this;
		}

		public Criteria andBsrContactIn(List<String> values) {
			addCriterion("bsr_contact in", values, "bsrContact");
			return (Criteria) this;
		}

		public Criteria andBsrContactNotIn(List<String> values) {
			addCriterion("bsr_contact not in", values, "bsrContact");
			return (Criteria) this;
		}

		public Criteria andBsrContactBetween(String value1, String value2) {
			addCriterion("bsr_contact between", value1, value2, "bsrContact");
			return (Criteria) this;
		}

		public Criteria andBsrContactNotBetween(String value1, String value2) {
			addCriterion("bsr_contact not between", value1, value2,
					"bsrContact");
			return (Criteria) this;
		}

		public Criteria andDeptContactIsNull() {
			addCriterion("dept__contact is null");
			return (Criteria) this;
		}

		public Criteria andDeptContactIsNotNull() {
			addCriterion("dept__contact is not null");
			return (Criteria) this;
		}

		public Criteria andDeptContactEqualTo(String value) {
			addCriterion("dept__contact =", value, "deptContact");
			return (Criteria) this;
		}

		public Criteria andDeptContactNotEqualTo(String value) {
			addCriterion("dept__contact <>", value, "deptContact");
			return (Criteria) this;
		}

		public Criteria andDeptContactGreaterThan(String value) {
			addCriterion("dept__contact >", value, "deptContact");
			return (Criteria) this;
		}

		public Criteria andDeptContactGreaterThanOrEqualTo(String value) {
			addCriterion("dept__contact >=", value, "deptContact");
			return (Criteria) this;
		}

		public Criteria andDeptContactLessThan(String value) {
			addCriterion("dept__contact <", value, "deptContact");
			return (Criteria) this;
		}

		public Criteria andDeptContactLessThanOrEqualTo(String value) {
			addCriterion("dept__contact <=", value, "deptContact");
			return (Criteria) this;
		}

		public Criteria andDeptContactLike(String value) {
			addCriterion("dept__contact like", value, "deptContact");
			return (Criteria) this;
		}

		public Criteria andDeptContactNotLike(String value) {
			addCriterion("dept__contact not like", value, "deptContact");
			return (Criteria) this;
		}

		public Criteria andDeptContactIn(List<String> values) {
			addCriterion("dept__contact in", values, "deptContact");
			return (Criteria) this;
		}

		public Criteria andDeptContactNotIn(List<String> values) {
			addCriterion("dept__contact not in", values, "deptContact");
			return (Criteria) this;
		}

		public Criteria andDeptContactBetween(String value1, String value2) {
			addCriterion("dept__contact between", value1, value2, "deptContact");
			return (Criteria) this;
		}

		public Criteria andDeptContactNotBetween(String value1, String value2) {
			addCriterion("dept__contact not between", value1, value2,
					"deptContact");
			return (Criteria) this;
		}

		public Criteria andSuppliersIsNull() {
			addCriterion("suppliers is null");
			return (Criteria) this;
		}

		public Criteria andSuppliersIsNotNull() {
			addCriterion("suppliers is not null");
			return (Criteria) this;
		}

		public Criteria andSuppliersEqualTo(String value) {
			addCriterion("suppliers =", value, "suppliers");
			return (Criteria) this;
		}

		public Criteria andSuppliersNotEqualTo(String value) {
			addCriterion("suppliers <>", value, "suppliers");
			return (Criteria) this;
		}

		public Criteria andSuppliersGreaterThan(String value) {
			addCriterion("suppliers >", value, "suppliers");
			return (Criteria) this;
		}

		public Criteria andSuppliersGreaterThanOrEqualTo(String value) {
			addCriterion("suppliers >=", value, "suppliers");
			return (Criteria) this;
		}

		public Criteria andSuppliersLessThan(String value) {
			addCriterion("suppliers <", value, "suppliers");
			return (Criteria) this;
		}

		public Criteria andSuppliersLessThanOrEqualTo(String value) {
			addCriterion("suppliers <=", value, "suppliers");
			return (Criteria) this;
		}

		public Criteria andSuppliersLike(String value) {
			addCriterion("suppliers like", value, "suppliers");
			return (Criteria) this;
		}

		public Criteria andSuppliersNotLike(String value) {
			addCriterion("suppliers not like", value, "suppliers");
			return (Criteria) this;
		}

		public Criteria andSuppliersIn(List<String> values) {
			addCriterion("suppliers in", values, "suppliers");
			return (Criteria) this;
		}

		public Criteria andSuppliersNotIn(List<String> values) {
			addCriterion("suppliers not in", values, "suppliers");
			return (Criteria) this;
		}

		public Criteria andSuppliersBetween(String value1, String value2) {
			addCriterion("suppliers between", value1, value2, "suppliers");
			return (Criteria) this;
		}

		public Criteria andSuppliersNotBetween(String value1, String value2) {
			addCriterion("suppliers not between", value1, value2, "suppliers");
			return (Criteria) this;
		}

		public Criteria andDeptPssjIsNull() {
			addCriterion("dept_pssj is null");
			return (Criteria) this;
		}

		public Criteria andDeptPssjIsNotNull() {
			addCriterion("dept_pssj is not null");
			return (Criteria) this;
		}

		public Criteria andDeptPssjEqualTo(Date value) {
			addCriterion("dept_pssj =", value, "deptPssj");
			return (Criteria) this;
		}

		public Criteria andDeptPssjNotEqualTo(Date value) {
			addCriterion("dept_pssj <>", value, "deptPssj");
			return (Criteria) this;
		}

		public Criteria andDeptPssjGreaterThan(Date value) {
			addCriterion("dept_pssj >", value, "deptPssj");
			return (Criteria) this;
		}

		public Criteria andDeptPssjGreaterThanOrEqualTo(Date value) {
			addCriterion("dept_pssj >=", value, "deptPssj");
			return (Criteria) this;
		}

		public Criteria andDeptPssjLessThan(Date value) {
			addCriterion("dept_pssj <", value, "deptPssj");
			return (Criteria) this;
		}

		public Criteria andDeptPssjLessThanOrEqualTo(Date value) {
			addCriterion("dept_pssj <=", value, "deptPssj");
			return (Criteria) this;
		}

		public Criteria andDeptPssjIn(List<Date> values) {
			addCriterion("dept_pssj in", values, "deptPssj");
			return (Criteria) this;
		}

		public Criteria andDeptPssjNotIn(List<Date> values) {
			addCriterion("dept_pssj not in", values, "deptPssj");
			return (Criteria) this;
		}

		public Criteria andDeptPssjBetween(Date value1, Date value2) {
			addCriterion("dept_pssj between", value1, value2, "deptPssj");
			return (Criteria) this;
		}

		public Criteria andDeptPssjNotBetween(Date value1, Date value2) {
			addCriterion("dept_pssj not between", value1, value2, "deptPssj");
			return (Criteria) this;
		}

		public Criteria andXgSjIsNull() {
			addCriterion("xg_sj is null");
			return (Criteria) this;
		}

		public Criteria andXgSjIsNotNull() {
			addCriterion("xg_sj is not null");
			return (Criteria) this;
		}

		public Criteria andXgSjEqualTo(Date value) {
			addCriterion("xg_sj =", value, "xgSj");
			return (Criteria) this;
		}

		public Criteria andXgSjNotEqualTo(Date value) {
			addCriterion("xg_sj <>", value, "xgSj");
			return (Criteria) this;
		}

		public Criteria andXgSjGreaterThan(Date value) {
			addCriterion("xg_sj >", value, "xgSj");
			return (Criteria) this;
		}

		public Criteria andXgSjGreaterThanOrEqualTo(Date value) {
			addCriterion("xg_sj >=", value, "xgSj");
			return (Criteria) this;
		}

		public Criteria andXgSjLessThan(Date value) {
			addCriterion("xg_sj <", value, "xgSj");
			return (Criteria) this;
		}

		public Criteria andXgSjLessThanOrEqualTo(Date value) {
			addCriterion("xg_sj <=", value, "xgSj");
			return (Criteria) this;
		}

		public Criteria andXgSjIn(List<Date> values) {
			addCriterion("xg_sj in", values, "xgSj");
			return (Criteria) this;
		}

		public Criteria andXgSjNotIn(List<Date> values) {
			addCriterion("xg_sj not in", values, "xgSj");
			return (Criteria) this;
		}

		public Criteria andXgSjBetween(Date value1, Date value2) {
			addCriterion("xg_sj between", value1, value2, "xgSj");
			return (Criteria) this;
		}

		public Criteria andXgSjNotBetween(Date value1, Date value2) {
			addCriterion("xg_sj not between", value1, value2, "xgSj");
			return (Criteria) this;
		}

		public Criteria andTenantIdIsNull() {
			addCriterion("tenant_id is null");
			return (Criteria) this;
		}

		public Criteria andTenantIdIsNotNull() {
			addCriterion("tenant_id is not null");
			return (Criteria) this;
		}

		public Criteria andTenantIdEqualTo(Long value) {
			addCriterion("tenant_id =", value, "tenantId");
			return (Criteria) this;
		}

		public Criteria andTenantIdNotEqualTo(Long value) {
			addCriterion("tenant_id <>", value, "tenantId");
			return (Criteria) this;
		}

		public Criteria andTenantIdGreaterThan(Long value) {
			addCriterion("tenant_id >", value, "tenantId");
			return (Criteria) this;
		}

		public Criteria andTenantIdGreaterThanOrEqualTo(Long value) {
			addCriterion("tenant_id >=", value, "tenantId");
			return (Criteria) this;
		}

		public Criteria andTenantIdLessThan(Long value) {
			addCriterion("tenant_id <", value, "tenantId");
			return (Criteria) this;
		}

		public Criteria andTenantIdLessThanOrEqualTo(Long value) {
			addCriterion("tenant_id <=", value, "tenantId");
			return (Criteria) this;
		}

		public Criteria andTenantIdIn(List<Long> values) {
			addCriterion("tenant_id in", values, "tenantId");
			return (Criteria) this;
		}

		public Criteria andTenantIdNotIn(List<Long> values) {
			addCriterion("tenant_id not in", values, "tenantId");
			return (Criteria) this;
		}

		public Criteria andTenantIdBetween(Long value1, Long value2) {
			addCriterion("tenant_id between", value1, value2, "tenantId");
			return (Criteria) this;
		}

		public Criteria andTenantIdNotBetween(Long value1, Long value2) {
			addCriterion("tenant_id not between", value1, value2, "tenantId");
			return (Criteria) this;
		}
	}

	/**
	 * This class was generated by MyBatis Generator. This class corresponds to the database table wx_qjbs_bill
	 * @mbggenerated  Mon Oct 12 16:55:00 CST 2015
	 */
	public static class Criterion {
		private String condition;
		private Object value;
		private Object secondValue;
		private boolean noValue;
		private boolean singleValue;
		private boolean betweenValue;
		private boolean listValue;
		private String typeHandler;

		public String getCondition() {
			return condition;
		}

		public Object getValue() {
			return value;
		}

		public Object getSecondValue() {
			return secondValue;
		}

		public boolean isNoValue() {
			return noValue;
		}

		public boolean isSingleValue() {
			return singleValue;
		}

		public boolean isBetweenValue() {
			return betweenValue;
		}

		public boolean isListValue() {
			return listValue;
		}

		public String getTypeHandler() {
			return typeHandler;
		}

		protected Criterion(String condition) {
			super();
			this.condition = condition;
			this.typeHandler = null;
			this.noValue = true;
		}

		protected Criterion(String condition, Object value, String typeHandler) {
			super();
			this.condition = condition;
			this.value = value;
			this.typeHandler = typeHandler;
			if (value instanceof List<?>) {
				this.listValue = true;
			} else {
				this.singleValue = true;
			}
		}

		protected Criterion(String condition, Object value) {
			this(condition, value, null);
		}

		protected Criterion(String condition, Object value, Object secondValue,
				String typeHandler) {
			super();
			this.condition = condition;
			this.value = value;
			this.secondValue = secondValue;
			this.typeHandler = typeHandler;
			this.betweenValue = true;
		}

		protected Criterion(String condition, Object value, Object secondValue) {
			this(condition, value, secondValue, null);
		}
	}

	/**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table wx_qjbs_bill
     *
     * @mbggenerated do_not_delete_during_merge Fri Aug 21 17:28:36 CST 2015
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }
}