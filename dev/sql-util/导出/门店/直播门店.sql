select o.id "经销商ID", pe.sap_code "经销商编码", o.organization_name "经销商名称",
w.id "门店ID", w.customer_code "门店编码", w.work_shop_name "门店名称",
case when w.business_weight=1 then '金富力'
when w.business_weight=2 then '德乐'
else '工程机械' end "品牌",
r3.region_name "省",r2.region_name "市", r1.region_name "区", w.work_shop_address "地址",
w.latitude "纬度", w.longitude "经度",w.contact_person "联系人",w.contact_person_tel "联系电话",
w.type "门店类型"
 from wx_t_work_shop w 
left join wx_t_organization o on o.id=w.partner_id
left join wx_t_partner_o2o_enterprise pe on pe.partner_id=o.id
		LEFT JOIN wx_t_region r1 ON w.region_id = r1.id
		LEFT JOIN wx_t_region r2 ON r1.parent_id = r2.id
		LEFT JOIN wx_t_region r3 ON r2.parent_id = r3.id
where o.status=1 and o.id != 9
and w.delete_flag=0 and w.status=3
and w.customer_type=1 and w.business_weight in (1,2,4) and r3.id is not null
order by o.id,w.id