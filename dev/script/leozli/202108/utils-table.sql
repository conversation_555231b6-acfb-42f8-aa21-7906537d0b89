/*==============================================================*/
/* Module: UTILS                                                */
/*==============================================================*/

/*==============================================================*/
/* Table: wx_t_excel_export_conf                                */
/*==============================================================*/
create table wx_t_excel_export_conf (
	id                               bigint                            not null identity,
	file_name                        nvarchar(255)                              not null,
	file_name_postfix                nvarchar(255)                                  null,
	item_code                        nvarchar(64)                                   null,
	export_sql                       nvarchar(max)                                  null,
	enable_flag                      tinyint                                    not null,
	order_no                         int                                            null,
	primary key (id)
);

/*==============================================================*/
/* Table: wx_t_warn_config                                      */
/*==============================================================*/
create table wx_t_warn_config (
	id                               bigint                            not null identity,
	warn_title                       nvarchar(256)                              not null,
	notify_code                      nvarchar(64)                                   null,
	notify_type                      int                                        not null,
	warn_sql                         nvarchar(max)                                  null,
	warn_desc_field                  nvarchar(64)                                   null,
	warn_detail_field                nvarchar(64)                                   null,
	sort_numb                        decimal(20,10)                                 null,
	enable_flag                      tinyint                                    not null,
	delete_flag                      tinyint                                    not null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	primary key (id)
);

/*==============================================================*/
/* Table: wx_t_message_notify_schedule                          */
/*==============================================================*/
create table wx_t_message_notify_schedule (
	id                               bigint                            not null identity,
	source_id                        bigint                                         null,
	source_type                      nvarchar(64)                                   null,
	content                          nvarchar(512)                                  null,
	user_id                          bigint                                         null,
	phone_number                     nvarchar(64)                                   null,
	expected_time                    date                                           null,
	execution_time                   date                                           null,
	status                           int                                            null,
	batch_flag                       int                                            null,
	delete_flag                      bit                                            null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	primary key (id)
);

/*==============================================================*/
/* Table: wx_t_publish_photo_folder                             */
/*==============================================================*/
create table wx_t_publish_photo_folder (
	id                               bigint                            not null identity,
	PARENT_id                        bigint                                         null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	publish_photo_folder             nvarchar(128)                                  null,
	folder_code                      nvarchar(30)                                   null,
	level_code                       nvarchar(256)                                  null,
	remarks                          nvarchar(1024)                                 null,
	order_sequence                   decimal(10,6)                                  null,
	ext_flag                         int                                        not null,
	primary key (id)
);

/*==============================================================*/
/* Table: wx_att_file                                           */
/*==============================================================*/
create table wx_att_file (
	att_id                           bigint                            not null identity,
	upload_user                      bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	xg_sj                            datetime                                       null,
	source_id                        bigint                                         null,
	source_type                      nvarchar(5)                                    null,
	file_name                        nvarchar(100)                              not null,
	storage_name                     nvarchar(64)                                   null,
	file_type                        nvarchar(100)                                  null,
	store_path                       nvarchar(200)                                  null,
	file_size                        bigint                                         null,
	uuid                             nvarchar(30)                                   null,
	ext_flag                         int                                        not null,
	ext_property1                    nvarchar(64)                                   null,
	ext_property2                    nvarchar(64)                                   null,
	ext_property3                    nvarchar(64)                                   null,
	primary key (att_id)
);

/*==============================================================*/
/* Table: wx_t_wechat_message                                   */
/*==============================================================*/
create table wx_t_wechat_message (
	id                               bigint                            not null identity,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	event_id                         nvarchar(256)                                  null,
	alarm_name                       nvarchar(256)                                  null,
	alarm_type                       nvarchar(256)                                  null,
	entity_name                      nvarchar(256)                                  null,
	alarm_content                    nvarchar(max)                                  null,
	alarm_summary                    nvarchar(4000)                                 null,
	primary key (id)
);

/*==============================================================*/
/* Foreign Key(only index): ix_PAR_pub_photo_fol                */
/*==============================================================*/
CREATE INDEX ix_PAR_pub_photo_fol ON wx_t_publish_photo_folder (PARENT_id);
