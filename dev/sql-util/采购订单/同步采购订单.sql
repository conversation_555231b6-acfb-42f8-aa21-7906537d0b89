/****** <PERSON><PERSON><PERSON> for SelectTopNRows command from SSMS  ******/
drop table PP_MID.dbo.[wx_t_partner_order_line];
SELECT 8145 [partner_order_id]
      ,[product_id]
      ,[sku]
      ,[product_name]
      ,[amount]
      ,[units]
      ,[price]
      ,pl.[status]
      ,pl.[create_time]
      ,pl.[update_time]
      ,pl.[creator]
      ,pl.[remark]
      ,[actual_amount]
      ,[type]
      ,pl.[discount_fee]
      ,[discounted_price]
      ,[total_value]
      ,[discounted_total_value]
      ,pl.[order_no]
      ,[ladder_price]
      ,pl.[deductible_amount]
      ,pl.[total_value_after_deductible]
      ,[free_amount]
      ,[promotion_title]
      ,[promotion_sku]
      ,[package_sell]
      ,pl.[partner_sale_config_id]
	  into PP_MID.dbo.[wx_t_partner_order_line]
  FROM [pmpdb01].[dbo].[wx_t_partner_order_line] pl
  left join [pmpdb01].[dbo].wx_t_partner_order po on po.id=pl.partner_order_id where po.order_no='P07220926008489'
  
  SELECT c.*
  FROM wx_t_partner_order po 
  left join [PP_MID].[dbo].[mid_partner_sale_config] c on c.id=po.partner_sale_config_id
  where po.order_no='P07220316006885'