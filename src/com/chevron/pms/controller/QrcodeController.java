package com.chevron.pms.controller;

import java.io.File;
import java.io.IOException;
import java.net.URLEncoder;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import net.sf.json.JSONObject;

import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.fileupload.disk.DiskFileItem;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import com.chevron.localmarketing.business.MainLocalMarketingBizService;
import com.chevron.localmarketing.business.NonLocalUserReportBizService;
import com.chevron.localmarketing.model.LocMktProductInfo;
import com.chevron.localmarketing.model.MainLocalMarketing;
import com.chevron.localmarketing.model.NonLocalQueryParams;
import com.chevron.localmarketing.model.NonLocalUserReport;
import com.chevron.pms.business.QrCodeValidateBizService;
import com.chevron.pms.dao.OemProductPackagingCodeMapper;
import com.chevron.pms.model.OemProductPackagingCode;
import com.chevron.pms.model.ProductVo;
import com.chevron.pms.model.QrCodeDetail;
import com.chevron.pms.model.WechatFollow;
import com.chevron.pms.model.WxTProductChannelSet;
import com.chevron.pms.model.WxTQrValidateRecord;
import com.chevron.pms.model.ZsQrTrackInfo;
import com.chevron.pms.service.ProductChannelSetService;
import com.chevron.pms.service.QrCodeService;
import com.chevron.pms.service.QrCodeUploadService;
import com.chevron.pms.service.WxTQrValidateRecordService;
import com.common.constants.Constants;
import com.common.exception.WxPltException;
import com.common.util.ContextUtil;
import com.common.util.DateUtil;
import com.common.util.JsonUtil;
import com.common.util.MessageResourceUtil;
import com.common.util.WeixinMessageUtil;
import com.sys.auth.service.UserServiceI;
import com.sys.dic.model.DicItemVo;
import com.sys.dic.service.DicService;
import com.sys.email.service.EmailSenderService;
import com.sys.log.util.LogTask;
import com.sys.log.util.LogUtils;
import com.sys.master.business.ProductMasterBizService;
import com.sys.master.model.ProductMaster;
import com.sys.utils.business.BaiDuBizService;
import com.sys.utils.model.BaiDuAddress;

@Controller
public class QrcodeController {
	private Logger log = LoggerFactory.getLogger(QrcodeController.class);
	public static String UPLOAD_FLODER = "/QR Folder";
	public static final String QRCODE_UPLOAD_CONFIRM_TYPE = "qrcodeconfirm";
	public static final String TJ_QRCODE_UPLOAD_CONFIRM_TYPE = "qrcodeTjconfirm";
	public static final String CONFIRM_STATUS = "2";//已确认的状态
	@Autowired
	private QrCodeValidateBizService qrCodeValidateBizService;
//	@Autowired
//	private UserQrcodeMapper userQrcodeMapper;
	@Autowired
	private DicService dicService;
	@Autowired
	private QrCodeService qrCodeService;
	@Resource 
	private EmailSenderService emailSenderService;
	@Resource 
	private UserServiceI userService;
	@Autowired
	private QrCodeUploadService qrCodeUploadService;
	@Autowired
	private JdbcTemplate jdbcTemplate;	
	@Autowired
	private ProductChannelSetService channelSetService;
	@Autowired
	private ProductMasterBizService productMasterBizService;
	@Autowired
	private WxTQrValidateRecordService validateRecordService;
	@Autowired
	private OemProductPackagingCodeMapper oemProductPackagingCodeMapper;
	@Autowired
	private BaiDuBizService baiDuBizService;
//	@Autowired
//	private MainLocalMarketingMapper mainLocalMarketingMapper;
//	@Autowired
//	private LocMktProductInfoMapper locMktProductInfoMapper;
	@Autowired
	private MainLocalMarketingBizService mainLocalMarketingBizService;
	
	@Autowired
	private NonLocalUserReportBizService nonLocalUserReportBizService;
	
	//串货验证
	protected void checkFlee(final String trackNo, final String scanCode, final Double longitude, final Double latitude, ZsQrTrackInfo zsQrTrackInfo) {
		Long cityId = null;
		try {
			BaiDuAddress baiDuAddress = baiDuBizService.queryAddress(longitude, latitude, "wgs84ll");
			if(baiDuAddress != null) {
				cityId = baiDuAddress.getCityId();
			}
		} catch (Exception e) {
			log.error("用户验真查询坐标地址失败。" + e.getMessage(), e);
			LogUtils.addErrorLog(1l, "com.chevron.pms.controller.QrcodeController.checkFlee", "用户验真查询坐标地址失败。" + e.getMessage(), 
					trackNo + "," + longitude + "," + latitude);
		}
		if(cityId == null) {
			LogUtils.addErrorLog(1l, "com.chevron.pms.controller.QrcodeController.checkFlee", "用户验真未能解析坐标地址。", 
					trackNo + "," + longitude + "," + latitude);
			return;
		}
//		Map<String, Object> params = new HashMap<String, Object>(5);
//		params.put("code1", trackNo);
//		params.put("excludeKa", true);
//		params.put("exludeEc", true);
//		params.put("cityId", cityId);
		final NonLocalQueryParams params = new NonLocalQueryParams();
		params.setCode1(trackNo);
		params.setReportCityId(cityId);
		params.setReportType(MainLocalMarketing.LOCALMARKETING_REPORT_TYPE2);
		try {
			final MainLocalMarketing fleeingGoods = mainLocalMarketingBizService.queryNonLocalSale(params, zsQrTrackInfo);
			if(fleeingGoods != null) {
//				if((fleeingGoods.getExtFlag() & 3) == 0) {//1-窜货经销商KA, 2-窜货经销商EC
					LogUtils.addLog(new LogTask() {
						
						@Override
						public void execute() throws Exception {
//							LocMktProductInfoExample example = new LocMktProductInfoExample();
//							example.createCriteria().andLogisticsCodeEqualTo(trackNo);
//							if(locMktProductInfoMapper.countByExample(example) > 0) {
//								return;
//							}
							if((fleeingGoods.getExtFlag() & 1024) > 0) {
								//已上报
								LogUtils.addInfoLog(-1l, "com.chevron.pms.controller.QrcodeController.checkFlee", "重复上报：" + JsonUtil.writeValue(params));
								return;
							}
							LocMktProductInfo productInfo = fleeingGoods.getProductInfos().get(0);
							NonLocalUserReport nonLocalUserReport = new NonLocalUserReport();
							nonLocalUserReport.setAuthChannel(productInfo.getAuthChannel());
							nonLocalUserReport.setDisplayChannel(productInfo.getDisplayChannel());
							nonLocalUserReport.setExtFlag(fleeingGoods.getExtFlag());
							nonLocalUserReport.setExtProperty1(scanCode);
							nonLocalUserReport.setFromDistributorId(productInfo.getFromDistributorId());
							nonLocalUserReport.setLogisticsCode(trackNo);
							nonLocalUserReport.setOutDate(productInfo.getScanTime());
							nonLocalUserReport.setOutOrderNo(productInfo.getProductOutNo());
							nonLocalUserReport.setProductBatch(productInfo.getProductBatch());
							nonLocalUserReport.setProductDate(productInfo.getProductTime());
							nonLocalUserReport.setReportLatitude(latitude);
							nonLocalUserReport.setReportLongitude(longitude);
							nonLocalUserReport.setReportRegionId(params.getReportCityId());
							nonLocalUserReport.setReportTime(DateUtil.getCurrentDate());
							nonLocalUserReport.setSku(productInfo.getSku());
							nonLocalUserReportBizService.insert(nonLocalUserReport);
//							fleeingGoods.setReprotType(MainLocalMarketing.LOCALMARKETING_REPORT_TYPE2);
////							fleeingGoods.setReportName((String)Constants.getSystemPropertyByCodeType("MainLocalMarketing.reportName.type2"));
//							
//							productInfo.setAttribute1(scanCode);
//							productInfo.setLogisticsCode(trackNo);
//							mainLocalMarketingBizService.createMainLocalMarketing(fleeingGoods);
						}
					});
//				}
//				throw new WxPltException("EXP_FLEE", "对不起，无法获取该商品信息");
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			LogUtils.addErrorLog(ContextUtil.getCurUserId(), "com.chevron.pms.controller.QrcodeController.checkFlee", "查询窜货信息失败。" + e.getMessage(), trackNo + "," + longitude + "," + latitude);
		}
	}
	
	@ResponseBody
	@RequestMapping("/tempqrcode/test.do")
	public Map<String, Object> testTempQr(){
		Map<String, Object> resultMap = new HashMap<String, Object>(10);
		qrCodeService.generateWechatTempFollowInfo(WechatFollow.VERSION_NO_GENERAL);
		qrCodeService.generateWechatTempFollowInfo(WechatFollow.VERSION_NO_INDUSTRY);
		return resultMap;
	}
	
	@ResponseBody
	@RequestMapping("/anon/qrcode/getWechatTempFollowUrlForQrCode.do")
	public Map<String, Object> getWechatTempFollowUrlForQrCode(@RequestParam("qrcode")String qrcode, @RequestParam("codeType")String codeType, 
			@RequestParam(value="longitude", required=false)Double longitude, @RequestParam(value="latitude", required=false)Double latitude) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		try {
			log.info("qrcode: {}", qrcode);
			resultMap.put("code", "success");
			//0, 验证输入参数
			if (StringUtils.isEmpty(qrcode)){
				throw new WxPltException(MessageResourceUtil.getMessage("system.illegal_parameter"));
			}
			buildValidateInfo(qrcode, codeType, longitude, latitude, resultMap);
		} catch (WxPltException e){
			log.error("getWechatTempFollowUrlForQrCode exception", e);
			resultMap.put("code", StringUtils.isBlank(e.getExpCode()) ? "error" : e.getExpCode());
			resultMap.put("codeMsg", e.getMessage());
		} catch (Exception e) {
			log.error("getWechatTempFollowUrlForQrCode", e);
			resultMap.put("code", "error");
			resultMap.put("codeMsg", MessageResourceUtil.getMessage("system.unexpected_exception"));
		}
		return resultMap;
	}
	
	@RequestMapping("/anon/jsonp/getWechatTempFollowUrlForQrCode.do")
	public String getWechatTempFollowUrlForQrCodeJsonp(@RequestParam("qrcode")String qrcode, @RequestParam(value="codeType", required=false)String codeType, 
			@RequestParam("callback")String callback, @RequestParam(value="longitude", required=false)Double longitude, 
			@RequestParam(value="latitude", required=false)Double latitude, HttpServletResponse response) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		try {
			log.info("qrcode: {}", qrcode);
			resultMap.put("code", "success");
			//0, 验证输入参数
			if (StringUtils.isEmpty(qrcode)){
				throw new WxPltException(MessageResourceUtil.getMessage("system.illegal_parameter"));
			}
			buildValidateInfo(qrcode, codeType, longitude, latitude, resultMap);
//			if(qrcode.length() == 16) {
//				//瓶盖码验真直接取qrcode对应临时二维码
//				getWechatTempFollowUrlForCapQrCode(qrcode, null, resultMap);
//			}else {
//				getWechatTempFollowUrlForQrCode(qrcode, longitude, latitude, resultMap);
//			}
			
		} catch (WxPltException e){
			log.error("getWechatTempFollowUrlForQrCodeJsonp exception", e);
			resultMap.put("code", StringUtils.isBlank(e.getExpCode()) ? "error" : e.getExpCode());
			resultMap.put("codeMsg", e.getMessage());
		} catch (Exception e) {
			log.error("getWechatTempFollowUrlForQrCodeJsonp", e);
			resultMap.put("code", "error");
			resultMap.put("codeMsg", MessageResourceUtil.getMessage("system.unexpected_exception"));
		}
		try {
			response.setCharacterEncoding("UTF-8");
			response.getWriter().print(new StringBuilder(callback).append("(").append(JSONObject.fromObject(resultMap)).append(");").toString());
		} catch (IOException e) {
			e.printStackTrace();
			log.error("获取关注二维码失败", e);
			LogUtils.addErrorLog(1l, "com.chevron.pms.controller.QrcodeController.getWechatTempFollowUrlForQrCodeJsonp", "getWechatTempFollowUrlForQrCodeJsonp异常。" + e.getMessage(), qrcode);
		}
		return null;
	}
	
	protected Map<String, Object> getWechatTempFollowUrlForCapQrCode(String qrcode, String sku, Map<String, Object> resultMap) throws Exception {
		return resultMap;
	}
	protected void buildDeloValidateInfo(QrCodeDetail qrCodeDetail, ProductVo productVo, Map<String, Object> resultMap) throws Exception {
		//德乐产品验真
		WxTQrValidateRecord validateRecord = new WxTQrValidateRecord();
		validateRecord.setSku(productVo.getSku());
		validateRecord.setProductName(productVo.getName());
		validateRecord.setQrCode(qrCodeDetail.getQrCode());
		validateRecord.setCreateTime(new Date());
		validateRecordService.insertSelective(validateRecord);
		
		WxTQrValidateRecord firstValidate = validateRecordService.getTheFirstRecordByQrcode(qrCodeDetail.getQrCode());
		Long validateCount = validateRecordService.getRecordCountByQrcode(qrCodeDetail.getQrCode());
		
		String url = "/deloQrPage/zhengque.html";
		
		WxTProductChannelSet channelSetVo = channelSetService.getProductChannelSetBySku(productVo.getSku());
		
		String productName = channelSetVo.getProductName();
		//productName = productName.replaceAll("@@", "<img src=\"img/cp.png\">");
		String capacity = channelSetVo.getStandard();
		String firstTime = DateUtil.getDateTimeStr(firstValidate.getCreateTime());
		String deloUrl = url + "?productName=" + URLEncoder.encode(URLEncoder.encode(productName, "UTF-8"), "UTF-8") + "&capacity=" + capacity;
		deloUrl = deloUrl + "&firstTime=" + firstTime + "&validateCount=" + validateCount;
			
		Map<String, Object> deloUrlResultMap = dicService.getDicItemByDicTypeCode(Constants.DELO_QR_CODE_URL);
		if(deloUrlResultMap.get(Constants.RESULT_CODE_KEY) != null){
			String resultCode = (String)deloUrlResultMap.get(Constants.RESULT_CODE_KEY);
			if(resultCode.equals(Constants.SUCCESS_CODE) 
					&& deloUrlResultMap.get("data") != null){
				List<DicItemVo> dicItemList = (List<DicItemVo>)deloUrlResultMap.get("data");
				for(DicItemVo dicItem : dicItemList){
					if(dicItem.getDicItemCode().equals(Constants.DELO_QR_CODE_URL_PRODUCTDETAIL)){
						String deloProductUrl = dicItem.getDicItemName();
						deloUrl = deloUrl + "&deloProductUrl=" + URLEncoder.encode(URLEncoder.encode(deloProductUrl, "UTF-8"), "UTF-8"); 
					}
					if(dicItem.getDicItemCode().equals(Constants.DELO_QR_CODE_URL_OILSELECTOR)){
						String oilSelectUrl = dicItem.getDicItemName();
						deloUrl = deloUrl + "&oilSelectUrl=" + URLEncoder.encode(URLEncoder.encode(oilSelectUrl, "UTF-8"), "UTF-8"); 
					}
				}
			}
		}
		resultMap.put("ppUrl", deloUrl);							
	}
	
//	protected void buildIndustrialValidateInfo(QrCodeDetail qrCodeDetail, ProductMaster product, Map<String, Object> resultMap) throws Exception {
//		WxTQrValidateRecord validateRecord = new WxTQrValidateRecord();
//		validateRecord.setSku(product.getSku());
//		validateRecord.setProductName(product.getName());
//		validateRecord.setQrCode(qrCodeDetail.getQrCode());
//		validateRecord.setCreateTime(new Date());
//		validateRecordService.insertSelective(validateRecord);
//		
//		WxTQrValidateRecord firstValidate = validateRecordService.getTheFirstRecordByQrcode(qrCodeDetail.getQrCode());
//		Long validateCount = validateRecordService.getRecordCountByQrcode(qrCodeDetail.getQrCode());
//		
//		String url = "/industryQrPage/zhengque.html";
//		
//		WxTProductChannelSet channelSetVo = channelSetService.getProductChannelSetBySku(product.getSku());
//		
//		String productName = channelSetVo.getProductName();
//		//productName = productName.replaceAll("@@", "<img src=\"img/cp.png\">");
//		String capacity = channelSetVo.getStandard();
//		String firstTime = DateUtil.getDateTimeStr(firstValidate.getCreateTime());
//		String industryUrl = url + "?productName=" + URLEncoder.encode(URLEncoder.encode(productName, "UTF-8"), "UTF-8") + "&capacity=" + capacity;
//		industryUrl = industryUrl + "&firstTime=" + firstTime + "&validateCount=" + validateCount;
//			
//		resultMap.put("ppUrl", industryUrl);							
//	}
	
	protected void buildQrValidateInfo(QrCodeDetail qrCodeDetail, ProductMaster product, int versionNo, Map<String, Object> resultMap) throws Exception {
		//1, 根据qrCode获取关注url
		String followUrl = qrCodeValidateBizService.getWechatTempFollowUrlForQrCode(qrCodeDetail.getQrCode(), versionNo);
		//2, 返回信息
		resultMap.put("followUrl", followUrl);
		//2, 记录本次扫码记录
//		qrCodeValidateBizService.saveUserQrCodeValidateRecord(qrCodeDetail.getQrCode(), product.getSku(), "");
//		List<UserQrcode> userScan = userQrcodeMapper.selectUserScan(qrCodeDetail.getQrCode());
//		int times = 0;
//		for(int i = 0; i < userScan.size(); i++) {
//			times += userScan.get(i).getScanCount();
//		}
//		String capacity = product.getCapacity();
//		if(StringUtils.isNotBlank(capacity) && !"L".endsWith(capacity)) {
//			if(capacity.indexOf(".") >= 0) {
//				capacity = (int)(Double.parseDouble(capacity) * 1000) + "ML";
//			}else{
//				capacity += "L";
//			}
//		}
//		String firstTime = DateUtil.getDateTimeStr(userScan.get(0).getCreationTime());
//		String industryUrl = "/mobilePage/cdm.html?productName=" + URLEncoder.encode(URLEncoder.encode(product.getName(), "UTF-8"), "UTF-8") + "&capacity=" + capacity;
//		industryUrl = industryUrl + "&firstTime=" + firstTime + "&validateCount=" + times;
//		resultMap.put("ppUrl", industryUrl);							
	}
	
	protected void buildPpValidateInfo(QrCodeDetail qrCodeDetail, Double longitude, Double latitude, Map<String, Object> resultMap) throws Exception {
		String sku = qrCodeDetail.getSku();
		ZsQrTrackInfo zsQrTrackInfo = null;
		if(StringUtils.isBlank(sku)) {
			//qrcode未绑定产品，查询中商接口
			zsQrTrackInfo = qrCodeService.queryZsTrackInfo(qrCodeService.getLogisticsCode(qrCodeDetail.getCodeId()));
			if(zsQrTrackInfo != null && StringUtils.isBlank(zsQrTrackInfo.getProductId())) {
				zsQrTrackInfo = null;
			}else if(zsQrTrackInfo != null) {
				sku = zsQrTrackInfo.getProductId();
			}
		}
		
		if(StringUtils.isBlank(sku)) {
			//qrcode未绑定产品
			String errorMsg = "没有查询到对应商品";
			resultMap.put("ppUrl", "/mobilePage/failure.html?errorMsg=" + URLEncoder.encode(URLEncoder.encode(errorMsg, "UTF-8"), "UTF-8"));						
			WeixinMessageUtil.weixinAlarmMessagePush("QrCodeValidate", "QrCode校验异常", "QrCode校验异常", qrCodeDetail.getQrCode() 
					+ ("normal".equals(qrCodeDetail.getQrcodeType()) ? "," + qrCodeService.getLogisticsCode(qrCodeDetail.getCodeId()) : ""), "QRCode异常，QRCode未绑定产品");
			return;
		}
		if("normal".equals(qrCodeDetail.getQrcodeType()) && longitude != null && latitude != null) {
			//串货验证
			checkFlee(qrCodeService.getLogisticsCode(qrCodeDetail.getCodeId()), qrCodeDetail.getQrCode(), longitude, latitude, zsQrTrackInfo);
		}
		ProductMaster product = productMasterBizService.getBeanBySku(sku);
		if(product == null) {
			//报警没有找到产品表里没有找到相关产品
			WeixinMessageUtil.weixinAlarmMessagePush("QrCodeValidate", "QrCode校验异常", "QrCode校验异常", qrCodeDetail.getQrCode(), 
					"QRCode异常，没有查询到对应产品： " + sku);			
			String errorMsg = "没有查询到对应产品";
			resultMap.put("ppUrl", "/mobilePage/failure.html?errorMsg=" + URLEncoder.encode(URLEncoder.encode(errorMsg, "UTF-8"), "UTF-8"));
			return;
		}
		if("TCP".equals(product.getCategory())) {
			//特劲
			resultMap.put("type", "techron");
			buildQrValidateInfo(qrCodeDetail, product, WechatFollow.VERSION_NO_GENERAL, resultMap);
			return;
		}
		if(null!=product.getExtFlag() && (product.getExtFlag()&16)>0) {
			//加德士
			resultMap.put("type", "jiadeshi");
			buildQrValidateInfo(qrCodeDetail, product, WechatFollow.VERSION_NO_GENERAL, resultMap);
			return;
		}
		WxTProductChannelSet productChannelSet = channelSetService.getProductChannelSetBySku(sku);
		if(productChannelSet != null) {
			if(Constants.QR_SALES_FLAG_INDUSTRT.equals(productChannelSet.getSalesChannel())) {
				//工程机械产品验真
				resultMap.put("type", "industry");
				buildQrValidateInfo(qrCodeDetail, product, WechatFollow.VERSION_NO_INDUSTRY, resultMap);
//				buildIndustrialValidateInfo(qrCodeDetail, product, resultMap);
				return;
			}else if(Constants.QR_SALES_FLAG_TUHU.equals(productChannelSet.getSalesChannel())) {
				resultMap.put("type", "tuhu");
			}
		}else if(qrCodeDetail.getWithCap().equals(1)) {
			//金富力-有瓶盖外码
			resultMap.put("type", "havoline");
		}else if("C&I".equals(product.getProductChannel())) {
			//德乐
			resultMap.put("type", "delo");
		}else {
			//老页面
			resultMap.put("type", "others");
		}
		buildQrValidateInfo(qrCodeDetail, product, WechatFollow.VERSION_NO_GENERAL, resultMap);
	}
	
	private QrCodeDetail queryQrInfo(String code, String codeType) throws WxPltException {
		if("logistics".equals(codeType)) {
			Long codeId = qrCodeService.getCodeId(code);
			return jdbcTemplate.queryForObject(new StringBuilder("select code_id, qr_code, sku, qr_owner, 'normal' qrcode_type, case when exists (select 1 from wx_t_cap_track_no_rel r where r.track_code_id=d.code_id) then 1 else 0 end with_cap from wx_t_qr_code_detail d where d.code_id = ? ").toString(),
					new Object[] {codeId},new RowMapper<QrCodeDetail>() {
						@Override
						public QrCodeDetail mapRow(ResultSet arg0, int arg1) throws SQLException {
							QrCodeDetail qrCodeDetail = new QrCodeDetail();
							qrCodeDetail.setCodeId(arg0.getLong("code_id"));
//							qrCodeDetail.setBatchId(arg0.getLong("batch_id"));
							qrCodeDetail.setQrCode(arg0.getString("qr_code"));
//							qrCodeDetail.setStatus(arg0.getString("status"));
							qrCodeDetail.setSku(arg0.getString("sku"));
							qrCodeDetail.setQrcodeType(arg0.getString("qrcode_type"));
							qrCodeDetail.setQrOwner(arg0.getString("qr_owner"));
							qrCodeDetail.setWithCap(arg0.getInt("with_cap"));
							return qrCodeDetail;
						}
			});
			
		}else {
			return jdbcTemplate.queryForObject(new StringBuilder("select code_id, qr_code, sku, qr_owner, 'normal' qrcode_type, case when exists (select 1 from wx_t_cap_track_no_rel r where r.track_code_id=d.code_id) then 1 else 0 end with_cap from wx_t_qr_code_detail d where qr_code = ? ")
					.append(" union all select 0 code_id, d.qr_code, b.sku, null qr_owner, 'tcp' qrcode_type, 0 with_cap from wx_t_qrcode_detail_untrace d join wx_t_qr_code_batch b on d.batch_id = b.batch_id where d.qr_code = ?")
					.append(" union all select d.code_id, d.qr_code, b.sku, null qr_owner, 'cap' qrcode_type, 0 with_cap from wx_t_cap_code_detail d join wx_t_cap_code_batch b on d.batch_id = b.batch_id where d.qr_code = ? and b.version_no=1")
					.append(" union all select bd.code_id, case when bd.qr_owner='GQ' then bd.qr_code else d.qr_code end qr_code, bd.sku, bd.qr_owner, 'cap2'qrcode_type, 1 with_cap  from wx_t_cap_code_detail d join wx_t_cap_code_batch b on d.batch_id = b.batch_id left join wx_t_cap_track_no_rel ctr on ctr.cap_code_id=d.code_id left join wx_t_qr_code_detail bd on bd.code_id=ctr.track_code_id where d.qr_code = ? and b.version_no=2").toString(),
					new Object[] {code, code, code, code},new RowMapper<QrCodeDetail>() {
						@Override
						public QrCodeDetail mapRow(ResultSet arg0, int arg1) throws SQLException {
							QrCodeDetail qrCodeDetail = new QrCodeDetail();
							qrCodeDetail.setCodeId(arg0.getLong("code_id"));
//							qrCodeDetail.setBatchId(arg0.getLong("batch_id"));
							qrCodeDetail.setQrCode(arg0.getString("qr_code"));
//							qrCodeDetail.setStatus(arg0.getString("status"));
							qrCodeDetail.setSku(arg0.getString("sku"));
							qrCodeDetail.setQrcodeType(arg0.getString("qrcode_type"));
							qrCodeDetail.setQrOwner(arg0.getString("qr_owner"));
							qrCodeDetail.setWithCap(arg0.getInt("with_cap"));
							return qrCodeDetail;
						}
			});

		}
	}
	
	protected void buildValidateInfo(String code, String codeType, Double longitude, Double latitude, Map<String, Object> resultMap) throws Exception {
		QrCodeDetail qrCodeDetail = queryQrInfo(code, codeType);
		//qrcode不存在
		if(qrCodeDetail == null) {
			//报警没有查找到对应的码
			String errorMsg = "此数码不存在，请核对扫码位置是否正确，建议再试一次";
			resultMap.put("ppUrl", "/mobilePage/failure.html?errorMsg=" + URLEncoder.encode(URLEncoder.encode(errorMsg, "UTF-8"), "UTF-8"));						
			WeixinMessageUtil.weixinAlarmMessagePush("QrCodeValidate", "QrCode校验异常", "QrCode校验异常", code, "数码" + code + "不存在");
			return;
		}
		String owner = qrCodeDetail.getQrOwner();
		//高桥码
		if(Constants.GQ_VERIFICATION_OWNER.equals(owner)) {
			String gqUrl = (String)Constants.getSystemPropertyByCodeType(Constants.GQ_VERIFICATION_URL);
			String gqToken = (String)Constants.getSystemPropertyByCodeType(Constants.GQ_VERIFICATION_TOKEN);
			Long timestamp = System.currentTimeMillis() / 1000;
			String sign = DigestUtils.shaHex(qrCodeDetail.getQrCode() + timestamp + gqToken);
			gqUrl = gqUrl + "?code=" + qrCodeDetail.getQrCode() + "&timestamp=" + timestamp + "&sign=" + sign;
			resultMap.put("gqUrl", gqUrl);
		}else if(Constants.DIDI_VERIFICATION_OWNER.equals(owner)) {
			//DIDI码
			WxTQrValidateRecord validateRecord = new WxTQrValidateRecord();
			validateRecord.setSku("");
			validateRecord.setProductName("");
			validateRecord.setQrCode(qrCodeDetail.getQrCode());
			validateRecord.setCreateTime(new Date());
			validateRecordService.insertSelective(validateRecord);
			
			WxTQrValidateRecord firstValidate = validateRecordService.getTheFirstRecordByQrcode(qrCodeDetail.getQrCode());
			Long validateCount = validateRecordService.getRecordCountByQrcode(qrCodeDetail.getQrCode());
			
			String url = "/didiQrPage/zhengque.html";
			String firstTime = DateUtil.getDateTimeStr(firstValidate.getCreateTime());
			String didiUrl = url + "?firstTime=" + firstTime + "&validateCount=" + validateCount;
			
			resultMap.put("ppUrl", didiUrl);	
		}else {
			if("cap2".equals(qrCodeDetail.getQrcodeType())) {
				if(qrCodeDetail.getCodeId() <= 0) {
					String errorMsg = "没有查询到对应商品";
					resultMap.put("ppUrl", "/mobilePage/failure.html?errorMsg=" + URLEncoder.encode(URLEncoder.encode(errorMsg, "UTF-8"), "UTF-8"));						
					WeixinMessageUtil.weixinAlarmMessagePush("QrCodeValidate", "QrCode校验异常", "QrCode校验异常", code, "数码" + code + "异常，瓶盖码未绑定瓶码");
					return;
				}
				qrCodeDetail.setQrcodeType("normal");
			}
			buildPpValidateInfo(qrCodeDetail, longitude, latitude, resultMap);
		}
	}
	
	@RequestMapping("/anon/jsonp/getProductInfoByTrackNo.do")
	public String getProductInfoByTrackNoJsonp(@RequestParam("trackNo")String trackNo, @RequestParam(value="longitude", required=false)Double longitude, 
			@RequestParam(value="latitude", required=false)Double latitude, @RequestParam("callback")String callback, 
			HttpServletResponse response) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		try {
			log.info("trackNo: {}", trackNo);
			//0, 验证输入参数
			if (StringUtils.isEmpty(trackNo)){
				throw new WxPltException("物流码为空");
			}
			Map<String, Object> params = new HashMap<String, Object>(3);
			params.put("code1", trackNo);
			List<OemProductPackagingCode> list = oemProductPackagingCodeMapper.queryByParams(params);
			OemProductPackagingCode item = null;
			ZsQrTrackInfo zsQrTrackInfo = null;
			if(list.isEmpty()) {
				//qrcode未绑定产品，查询中商接口
				zsQrTrackInfo = qrCodeService.queryZsTrackInfo(trackNo);
				if(zsQrTrackInfo != null && StringUtils.isBlank(zsQrTrackInfo.getProductId())) {
					zsQrTrackInfo = null;
				}else if(zsQrTrackInfo != null) {
					ProductMaster productMaster = productMasterBizService.getBeanBySku(zsQrTrackInfo.getProductId());
					if(productMaster != null) {
						item = new OemProductPackagingCode();
						item.setCode1(trackNo);
						item.setProductName(StringUtils.isBlank(productMaster.getAliasName()) ? productMaster.getName() : productMaster.getAliasName());
						item.setProductCapacity(productMaster.getCapacity());
						item.setProductBatch(zsQrTrackInfo.getProductBatch());
						item.setProductDate(DateUtil.parseDate(zsQrTrackInfo.getProductDate(), "yyyy-MM-dd"));
						item.setProductId(zsQrTrackInfo.getProductId());
					}
				}
			}else {
				item = list.get(0);
			}
			
			if(item != null) {
				if(longitude != null) {
					checkFlee(trackNo, trackNo, longitude, latitude, zsQrTrackInfo);
				}
				String config = (String)Constants.getSystemPropertyByCodeType("Qrcode.TrackNoVerifyConfig." + item.getProductId());
				resultMap.put("data", item);
				resultMap.put("config", config);
				resultMap.put("code", "success");
			}else {
				resultMap.put("code", "unknown");
				WeixinMessageUtil.weixinAlarmMessagePush("QrCodeValidate", "物流码校验异常", "物流码校验异常", trackNo, "物流码无效，没有查询到对应产品");
			}
		} catch (WxPltException e){
			resultMap.put("code", "error");
			resultMap.put("codeMsg", e.getMessage());
		} catch (Exception e) {
			log.error("getProductInfoByTrackNoJsonp", e);
			resultMap.put("code", "error");
			resultMap.put("codeMsg", MessageResourceUtil.getMessage("system.unexpected_exception"));
			LogUtils.addErrorLog(1l, "com.chevron.pms.controller.QrcodeController.getProductInfoByTrackNoJsonp", "getProductInfoByTrackNoJsonp异常。" + e.getMessage(), trackNo);
		}
		try {
			response.setCharacterEncoding("UTF-8");
			response.getWriter().print(new StringBuilder(callback).append("(").append(JSONObject.fromObject(resultMap)).append(");").toString());
		} catch (IOException e) {
			e.printStackTrace();
			log.error("获取关注二维码失败", e);
			LogUtils.addErrorLog(1l, "com.chevron.pms.controller.QrcodeController.getProductInfoByTrackNoJsonp", "getProductInfoByTrackNoJsonp异常。" + e.getMessage(), trackNo);
		}
		return null;
	}
	
	@ResponseBody
	@RequestMapping(value = "/qrcodeServ/uploadQRCodeToSftp.do", method = RequestMethod.POST)
	public Object uploadQRCodeToSftp(
			@RequestParam("myfiles") MultipartFile[] myfiles,
			@RequestParam("batchId") String  batchId,
			HttpServletRequest request,HttpServletResponse response) throws Exception {
		log.info("batchId: {}", batchId);
		Map<String,Object> resultMap = new HashMap<String,Object>();
		try
		{	
			CommonsMultipartFile cf = (CommonsMultipartFile)myfiles[0];  
	        DiskFileItem fi = (DiskFileItem) cf.getFileItem();  
	        File file = fi.getStoreLocation();          
	        qrCodeUploadService.uploadQRCodeToSftp(file, batchId);
		}catch (Exception e) {
			log.error("uploadQRCodeToSftp", e);
			resultMap.put("code", "syserror");
			resultMap.put("codeMsg", "上传异常:"+e.getLocalizedMessage());
			return resultMap;
		}
		resultMap.put("code", "success");
		return resultMap;
	}
	@ResponseBody
	@RequestMapping(value = "/qrcodeServ/uploadQRCodeToTjSftp.do", method = RequestMethod.POST)
	public Object uploadQRCodeToTjSftp(
			@RequestParam("myfiles") MultipartFile[] myfiles,
			@RequestParam("batchId") String  batchId,
			HttpServletRequest request,HttpServletResponse response) throws Exception {
		log.info("batchId: {}", batchId);
		Map<String,Object> resultMap = new HashMap<String,Object>();
		try
		{	
			//上传到中商天津工厂
			CommonsMultipartFile cf = (CommonsMultipartFile)myfiles[0];  
	        DiskFileItem fi = (DiskFileItem) cf.getFileItem();  
	        File file = fi.getStoreLocation();          
	        qrCodeUploadService.uploadQRCodeToTjSftp(file, batchId);
		}catch (Exception e) {
			log.error("uploadQRCodeToTjSftp", e);
			resultMap.put("code", "syserror");
			resultMap.put("codeMsg", "上传异常:"+e.getLocalizedMessage());
			return resultMap;
		}
		resultMap.put("code", "success");
		return resultMap;
	}
	
	@ResponseBody
	@RequestMapping(value = "/qrcodeServ/getProductInfoForStockScan.do", method = RequestMethod.GET)
	public Map<String,Object> getProductInfoForStockScan(@RequestParam("logisticsCode") String  logisticsCode,
			@RequestParam("stockFlag") String  stockFlag,
			@RequestParam("orgType") String  orgType,
			@RequestParam("orgId") String  orgId,
			@RequestParam("stockNo") String  stockNo,
			HttpServletRequest request,HttpServletResponse response) throws Exception {
		Map<String,Object> resultMap = qrCodeService.getProductInfoForStockScan(logisticsCode, stockFlag, orgType, orgId, stockNo);
		
		return resultMap;
	}

	/**
	 * QR 手动调用该接口 补全sku以及瓶盖内外码绑定到qrku (AMS处理用，无其它地方调用该接口)
	 * */
	@ResponseBody
	@RequestMapping(value = "/qrcodeServ/fixSkuAndCapQrcode.do", method = RequestMethod.POST)
	public Map<String,Object> fixSkuAndCapQrcode() throws Exception {
		log.info("补全sku以及瓶盖内外码绑定到qrku start");
		Map<String, Object> resultMap = new HashMap<String, Object>();
		resultMap.put("code","success");
		 new Thread() {
			public void run() {
				try {
					qrCodeService.fixQrcodeSku();
					qrCodeService.bindCapQrcode();
				} catch (Exception e) {
					log.error("fixSkuAndCapQrcode error", e);
					resultMap.put("code", "syserror");
					resultMap.put("codeMsg", e.getLocalizedMessage());
				}
			}
		 }.start();
		return resultMap;
		}
}
