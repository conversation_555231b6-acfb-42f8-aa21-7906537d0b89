SELECT  [END_MARKET_NAME_CN]
      ,[MON<PERSON>]
      ,[PRODUCT_CHANNEL]
      ,[<PERSON>ND_MARKET_BUSINESS_PROPERTY]
      ,[END_MARKET_SUB_TYPE]
      ,[END_CUSTOMER_STATUS]
      ,[SELL_THROUGH REPORTING_VOLUME L]
      ,[DSR_NAME_CN]
	  into ['TEMP_SELLIN2']
  FROM [pmpdb1124].[dbo].['TEMP_SELLIN2']
  
  SELECT [MONTH]
      ,[END_MARKET_BUSINESS_PROPERTY]
      ,[END_MARKET_SUB_TYPE]
      ,[END_CUSTOMER_STATUS]
      ,[SELL_THROUGH REPORTING_VOLUME L]
      ,[DSR_NAME_CN]
	  into ['TEMP_SELLIN3']
  FROM [pmpdb1124].[dbo].['TEMP_SELLIN3']
  
  SELECT [END_MARKET_NAME_CN]
      ,[MONTH]
      ,[PRODUCT_CHANNEL]
      ,[END_MARKET_BUSINESS_PROPERTY]
      ,[END_MARKET_SUB_TYPE]
      ,[END_CUSTOMER_STATUS]
      ,[SELL_THROUGH REPORTING_VOLUME_L]
	  into ['TEMP_SUM_SELLIN1']
  FROM [pmpdb1124].[dbo].['TEMP_SUM_SELLIN1']
  
  SELECT [MONTH]
      ,[END_MARKET_BUSINESS_PROPERTY]
      ,[END_MARKET_SUB_TYPE]
      ,[END_CUSTOMER_STATUS]
      ,[INSPECTIONS_TIMES]
      ,[END_MARKET_QTY]
	  into ['TEMP_SUM_XD1']
  FROM [pmpdb1124].[dbo].['TEMP_SUM_XD1']
  
  SELECT [DSR_NAME_CN]
      ,[MONTH]
      ,[END_MARKET_BUSINESS_PROPERTY]
      ,[END_MARKET_SUB_TYPE]
      ,[END_CUSTOMER_STATUS]
      ,[INSPECTIONS_TIMES]
      ,[END_MARKET_QTY]
	  into ['TEMP_XD4']
  FROM [pmpdb1124].[dbo].['TEMP_XD4']
  
  SELECT [END_MARKET_NAME_CN]
      ,[MONTH]
      ,[END_MARKET_BUSINESS_PROPERTY]
      ,[END_MARKET_SUB_TYPE]
      ,[END_CUSTOMER_STATUS]
      ,[INSPECTIONS_TIMES]
      ,[DSR_NAME_CN]
	  into ['TEMP_XD7']
  FROM [pmpdb1124].[dbo].['TEMP_XD7']
  
  SELECT [MONTH]
      ,[END_MARKET_BUSINESS_PROPERTY]
      ,[END_MARKET_SUB_TYPE]
      ,[END_CUSTOMER_STATUS]
      ,[INSPECTIONS_TIMES]
      ,[DSR_NAME_CN]
	  into ['TEMP_XD8']
  FROM [pmpdb1124].[dbo].['TEMP_XD8']
  
  SELECT [END_MARKET_NAME_CN]
      ,[MONTH]
      ,[END_MARKET_BUSINESS_PROPERTY]
      ,[END_MARKET_SUB_TYPE]
      ,[END_CUSTOMER_STATUS]
      ,[INSPECTIONS_TIMES]
	  into ['TEMP_XD3']
  FROM [pmpdb1124].[dbo].['TEMP_XD3']
  
 SELECT t.* from (SELECT isnull(s1.end_market_name_cn,s2.end_market_name_cn) end_market_name_cn,
SUM(s1.[SELL_THROUGH REPORTING_VOLUME L]) sellIn,SUM(s2.INSPECTIONS_TIMES) times  
FROM (select end_market_name_cn,sum([SELL_THROUGH REPORTING_VOLUME L]) [SELL_THROUGH REPORTING_VOLUME L],month,
max(end_market_sub_type) end_market_sub_type, max(end_market_business_property) end_market_business_property,
 dsr_name_cn, max(end_customer_status) end_customer_status from
['TEMP_SELLIN2'] t
where (#Channel# is null or #Channel#='' or charindex(','+s1.product_channel  +',',','+  #Channel#+',')>0)
group by end_market_name_cn,month,dsr_name_cn ) s1 
full outer join ['TEMP_XD7'] s2 on s1.end_market_name_cn = s2.end_market_name_cn and s1.month = s2.month 
and s1.dsr_name_cn = s2.dsr_name_cn
where isnull(s1.month,s2.month) = #Month#
and charindex(','+isnull(s1.end_market_sub_type,s2.end_market_sub_type) +',',','+ #subType# +',')>0 
and charindex(','+isnull(s1.end_market_business_property,s2.end_market_business_property)  +',',','+  #BusinessProperty# +',')>0
and (#DsrName# is null or #DsrName#='' or charindex(','+isnull(s1.dsr_name_cn,s2.dsr_name_cn) +',',','+ #DsrName# +',')>0)
and charindex(','+isnull(s1.end_customer_status,s2.end_customer_status) +',',','+ #CustomerStatus# +',')>0 GROUP by isnull(s1.end_market_name_cn,s2.end_market_name_cn)) t order by t.sellIn DESC


