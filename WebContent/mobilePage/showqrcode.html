<!doctype html>
<html>
    <head>
	    <meta charset="utf-8">
		<meta name="format-detection" content="telephone=no" />
		<meta name="viewport" content="width=640, user-scalable=no, target-densitydpi=device-dpi">
	    <link type="text/css" rel="stylesheet" href="css/globle.css" />
		<script src="js/jquery.js"></script>
		<script src="js/jquery.mobile-1.4.5.min.js"></script>
		<script type="text/javascript" src="../common/hplus/js/plugins/layer/layer.min.js"></script>
		<style>
			html,body{height:100%;}
			.temp_code{height:100%;width:640px; background:#0078c0 url(http://www.techronworks.cn/m/images/techron.png) no-repeat; text-align:center; overflow:hidden;}
			.temp_code .tb{margin-top:200px;color:#fff; font-size:23px; line-height:1.6em;}
			.temp_code .tb h2{margin:0 0 15px 0;font-size:40px;color:#fff;}
			.temp_code .tb h3{margin:0 0 25px 0;font-size:25px;color:#a2dfff; font-weight:normal;}
			.cbox{ position:relative;}
			.temp_code .code{position:relative; z-index:22;top:-40px;}
			.temp_code .code2{position: absolute; z-index:22;top:-150px;left:100px; opacity:0;}
			.temp_code .code,.temp_code .code2{width:450px;padding:70px;}
			.ui-loader {display: none;}
			.hide {display: none;}
			#qrcode_area{display: none;}
		</style>
	    <!--移动端版本兼容 -->
	    <script>
/*	        var ua = navigator.userAgent;
	        if (/android/i.test(ua)) {
	            var screenWidth = window.screen.width;
	            var targetDensityDPI = 640 / screenWidth * window.devicePixelRatio * 160;
	            document.write('<meta name="viewport" content="width=640, target-densitydpi=' + targetDensityDPI + ', user-scalable=no, minimum-scale=1.0, maximum-scale=1.0">');
	        } else {   // 其他系统
	            document.write('<meta name="viewport" content="width=640, user-scalable=no, target-densitydpi=device-dpi">');
	        }*/
	        
	        LoadMask = {
	        		show: function(){
	        			LoadMask._loading = layer.load(1, {
	        		        shade: [0.1, '#fff']
	        		    });
	        		},
	        		hide: function(){
	        			layer.close(LoadMask._loading);
	        		}
	        };
	        var cxt = '../';
        	function alertMes (msg, callback, type){
        		if(callback){
        			if(Object.prototype.toString.call(callback) == '[object String]'){
        				type = callback;
        				callback = undefined;
        			}
        		}
        		var win = window;
        		if('error' == type){
        			win.layer.alert(msg, {
        	            icon: 2,
        	            shadeClose: true,
        	            title: '操作提示'
        	        }, callback);
        		}else if('success' == type){
        			win.layer.alert(msg, {
        	            icon: 1,
        	            shadeClose: true,
        	            title: '操作提示'
        	        }, callback);
        		}else if('info' == type){
        			win.layer.alert(msg, {
        	            icon: 0,
        	            shadeClose: true,
        	            title: '操作提示'
        	        }, callback);
        		}else{
        			alert(msg);
        		}
        	}
        	$.getUrlParam = function(name)
            {
                var reg = new RegExp("(^|&)"+ name +"=([^&]*)(&|$)");
                var r = window.location.search.substr(1).match(reg);
                if (r!=null) return unescape(r[2]); return null;
            }
            $(document).ready(function(){
                var id = $.getUrlParam('id');
                if(!id){
                	throw '请求参数qrcode为空';
                }
                if(id.lastIndexOf(',') > 0){
                    id = id.substring(0, id.lastIndexOf(','));
                }
            	LoadMask.show();
            	setTimeout(function(){
            		$.post(cxt + 'anon/qrcode/getWechatTempFollowUrlForQrCode.do', {qrcode: id}, function(result) {
            					LoadMask.hide();
            					if("success" == result.code){ 
            						if(result.didiUrl){
    	    							//跳转页面
    	    							location.href = result.didiUrl;
    	    						}else if(result.gqUrl){
    	    							//跳转页面
    	    							location.href = result.gqUrl;
    	    						}else if(result.industryUrl){
    	    							//跳转页面
    	    							location.href = result.industryUrl;
    	    						}else if(result.deloUrl){
    	    							//跳转页面
    	    							location.href = result.deloUrl;
    	    						}else if(result.followUrl){
    		    						$('.temp_code').removeClass('hide');
	                					$('#qrcode_area img').attr('src', result.followUrl);
	                					$('#qrcode_area').show();
    	    						}else{
    	    							location.href = result.errorUrl;
    	    						}
            					}else{
            						alertMes(result.codeMsg, 'error');
            					}
            			}, 'json');
                }, 0);
            });
	    </script>
	    <!--移动端版本兼容 end -->
	    <title>雪佛龙防伪验真</title>
    </head>
    <body>
		<div id="Rotation"></div> 
		<div class="temp_code hide">
				<div class="tb">
						<h2>欢迎访问</h2>
						<h3>雪佛龙产品防伪查询页面</h3>
						<p>请长按并识别下方二维码
							<br>关注雪佛龙爱车养护官方微信账号<br>即刻获得查询结果
						</p>
						<div class="cbox" id="qrcode_area">
							<img src="https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=gQFP8DoAAAAAAAAAASxodHRwOi8vd2VpeGluLnFxLmNvbS9xL2N6b0NpOGJsbW1MajF1SHFmaFNHAAIEh_%2F_VwMEEA4AAA%3D%3D" class="code" />
							<img src="https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=gQFP8DoAAAAAAAAAASxodHRwOi8vd2VpeGluLnFxLmNvbS9xL2N6b0NpOGJsbW1MajF1SHFmaFNHAAIEh_%2F_VwMEEA4AAA%3D%3D" class="code2" />
						</div>
				</div>
				
		</div>
		<script type="text/javascript">
			$(function() {
				var imglist = document.getElementsByTagName("img");
				//安卓4.0+等高版本不支持window.screen.width，安卓2.3.3系统支持
				var _width;
				//doDraw();
	
				window.onresize = function() {
					//捕捉屏幕窗口变化，始终保证图片根据屏幕宽度合理显示
					//doDraw();
				}
	
				function doDraw() {
					_width = window.innerWidth;
					for ( var i = 0, len = imglist.length; i < len; i++) {
						DrawImage(imglist[i], _width);
					}
				}
	
				function DrawImage(ImgD, _width) {
					var image = new Image();
					image.src = ImgD.src;
					image.onload = function() {
						//限制，只对宽高都大于30的图片做显示处理
						if (image.width > 30 && image.height > 30) {
							if (image.width > _width) {
								ImgD.width = _width;
								ImgD.height = (image.height * _width) / image.width;
							} else {
								ImgD.width = image.width;
								ImgD.height = image.height;
							}
						}
					}
				}
			})
		</script>
	</body>
</html>
