package com.sys.auth.model;

import java.util.Date;

public class WxTPermission {
    private Long permissionId;

    //等于调用方法名
    private String permissionKey;

    private String permissionName;

    //所有人都有的权限,1-是,2-否
    private Integer commonPermission;

    //是否前台显示,1-是，2-否
    private Integer visible;

    //权限控制对象,CONTROLLER,SERVICE
    private String permissionSource;

    //1-有效,2-无效
    private Integer status;

    private String creator;

    private Date createTime;

    private String updator;

    private Date updateTime;

    private String remark;

    //权限主体：WEB-在web上使用,APP-在APP上使用,WEBANDAPP-在web和APP上都会用到
    private String permissionSubject;

    private String usedPage;

    public String getUsedPage() {
        return usedPage;
    }

    public void setUsedPage(String usedPage) {
        this.usedPage = usedPage;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getPermissionSubject() {
        return permissionSubject;
    }

    public void setPermissionSubject(String permissionSubject) {
        this.permissionSubject = permissionSubject;
    }

    public Long getPermissionId() {
        return permissionId;
    }

    public void setPermissionId(Long permissionId) {
        this.permissionId = permissionId;
    }

    public String getPermissionKey() {
        return permissionKey;
    }

    public void setPermissionKey(String permissionKey) {
        this.permissionKey = permissionKey;
    }

    public String getPermissionName() {
        return permissionName;
    }

    public void setPermissionName(String permissionName) {
        this.permissionName = permissionName;
    }

    public Integer getCommonPermission() {
        return commonPermission;
    }

    public void setCommonPermission(Integer commonPermission) {
        this.commonPermission = commonPermission;
    }

    public Integer getVisible() {
        return visible;
    }

    public void setVisible(Integer visible) {
        this.visible = visible;
    }

    public String getPermissionSource() {
        return permissionSource;
    }

    public void setPermissionSource(String permissionSource) {
        this.permissionSource = permissionSource;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getUpdator() {
        return updator;
    }

    public void setUpdator(String updator) {
        this.updator = updator;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
