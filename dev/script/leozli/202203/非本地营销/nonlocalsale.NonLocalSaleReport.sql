select cos1.customer_name_cn from_distributor_name, cos1.region, cos1.sales_name_cn flsr, 
cos1.supervisor_name_cn asm, diac1.dic_item_name channel_text, 
(select count(1) from wx_t_non_local_mkt_flow_form if1
			left join wx_t_non_local_mkt_flow_pro_res ir3 on ir3.flow_id = if1.id and ir3.setp_no = 'ABM_CONFIRM'
			where if1.from_distributor_id=cos1.distributor_id  
			and ir3.channel & cos1.channel_weight>0 and ir3.kpi_result=0
			and (#VARCHAR,reportDateFrom# is null or #VARCHAR,reportDateFrom#='' or if1.req_time>=#VARCHAR,reportDateFrom#)
			and (#VARCHAR,reportDateTo# is null or #VARCHAR,reportDateTo#='' or if1.req_time<dateadd(day, 1, #VARCHAR,reportDateTo#))
			and (#INTEGER,channel# is null or ir3&#INTEGER,channel#>0)
			),
(select (select IIF(ir3.punish_flag & 1 > 0,'发警告信;','') 
+ IIF(ir3.punish_flag & 2 > 0 and ir3.deduct_bonus is not null, '扣除营销基金和增量奖励IVI总和的' + cast(convert(int,ir3.deduct_bonus) as varchar(10)) + '%;','')
+ IIF(ir3.punish_flag & 32 > 0 and ir3.attribute8 is not null, '扣除2021年第' + 
case when ir3.attribute7='1' then '一' when ir3.attribute7='2' then '二' 
when ir3.attribute7='3' then '三'
when ir3.attribute7='4' then '四' end + '季度的季度奖励的' + ir3.attribute8 + '%;','')
+ IIF(ir3.punish_flag & 16 > 0 and ir3.attribute6 is not null, '扣除2021年的年终奖励的' + ir3.attribute6 + '%;','') from wx_t_non_local_mkt_flow_form if1
			left join wx_t_non_local_mkt_flow_pro_res ir3 on ir3.flow_id = if1.id and ir3.setp_no = 'ABM_CONFIRM'
			where if1.from_distributor_id=cos1.distributor_id 
			and ir3.channel & cos1.channel_weight>0 and ir3.kpi_result=0
			and (#VARCHAR,reportDateFrom# is null or #VARCHAR,reportDateFrom#='' or if1.req_time>=#VARCHAR,reportDateFrom#)
			and (#VARCHAR,reportDateTo# is null or #VARCHAR,reportDateTo#='' or if1.req_time<dateadd(day, 1, #VARCHAR,reportDateTo#))
			and (#INTEGER,channel# is null or ir3&#INTEGER,channel#>0)
			 FOR XML PATH,TYPE
 ).value('.','varchar(max)'))
from dw_customer_org_sales cos1 
left join wx_t_dic_item diac1 on diac1.dic_type_code='non_local_channel' and diac1.dic_item_code=cos1.channel_weight
where cos1.channel_weight&3>0
and exists (select 1 from wx_t_non_local_mkt_flow_form if1
			left join wx_t_non_local_mkt_flow_pro_res ir3 on ir3.flow_id = if1.id and ir3.setp_no = 'ABM_CONFIRM'
			where if1.from_distributor_id=cos1.distributor_id 
			and ir3.channel & cos1.channel_weight>0 and ir3.kpi_result=0
			and (#VARCHAR,reportDateFrom# is null or #VARCHAR,reportDateFrom#='' or if1.req_time>=#VARCHAR,reportDateFrom#)
			and (#VARCHAR,reportDateTo# is null or #VARCHAR,reportDateTo#='' or if1.req_time<dateadd(day, 1, #VARCHAR,reportDateTo#))
			and (#INTEGER,channel# is null or ir3&#INTEGER,channel#>0)
			)
and (#VARCHAR,fromRegion# is null or #VARCHAR,fromRegion#='' or cos1.region=#VARCHAR,fromRegion#)
and (#BIGINT,fromDistributorId# is null or cos1.distributor_id=#BIGINT,fromDistributorId#)