.bui-menu {
  list-style: none;
  margin: 0;
}
.bui-menu .bui-menu-item {
  cursor: pointer;
}
.bui-side-menu {
  height: 100%;
  text-align: left;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 0 3px;
}
.bui-side-menu .bui-menu-title,
.bui-side-menu .bui-menu-title s,
.bui-side-menu .menu-leaf a,
.bui-side-menu .menu-leaf em {
  background: url("../../img/menu-500-188.png") -9999px -9999px no-repeat;
}
.bui-side-menu a {
  color: #5c637b;
  cursor: pointer;
  outline: none;
}
.bui-side-menu a:hover {
  color: #414da7;
  text-decoration: none;
}
.bui-side-menu a:active {
  color: #5c637b;
}
.bui-side-menu .bui-menu-item-collapsed .bui-menu {
  display: none;
}
.bui-side-menu .bui-menu-item-collapsed .bui-menu-title s {
  background-position: 0 -30px;
}
.bui-side-menu .menu-leaf {
  outline: 0;
  line-height: 20px;
}
.bui-side-menu .menu-leaf a {
  height: 20px;
  text-indent: 24px;
  overflow: hidden;
  margin-bottom: 5px;
  display: block;
}
.bui-side-menu .menu-leaf a em {
  display: block;
  margin: 0 1px;
  font-style: normal;
  text-align: left;
  overflow: hidden;
  height: 19px;
  line-height: 22px;
  _line-height: 20px;
}
.bui-side-menu .bui-menu-item-selected a {
  color: #ffffff;
  background-position: 0 -130px;
}
.bui-side-menu .bui-menu-item-selected a em {
  background-position: right -100px;
}
.bui-side-menu .menu-second {
  padding-top: 1px;
  outline: 0;
  cursor: pointer;
}
.bui-side-menu .menu-second .bui-menu {
  margin-top: 5px;
}
.bui-side-menu .bui-menu-title-text {
  float: left;
  overflow: hidden;
  height: 14px;
  line-height: 14px;
  display: block;
  _margin-top: -2px;
}
.bui-side-menu .bui-menu-title {
  margin: 0 1px;
  background-position: 0 15px;
  height: 17px;
  color: #636775;
  font-weight: bold;
  overflow: hidden;
  line-height: 25px;
  vertical-align: middle;
  padding: 8px 0 5px 5px;
}
.bui-side-menu .bui-menu-title s {
  width: 10px;
  height: 10px;
  overflow: hidden;
  display: block;
  float: left;
  margin-right: 5px;
  margin-top: 1px;
  background-position: 0 -71px;
}
.bui-pop-menu {
  border: 1px solid #c3c3d6;
  background-color: #ffffff;
  padding: 2px 0;
  position: absolute;
}
.bui-pop-menu .bui-menu-item {
  padding: 3px 15px;
}
.bui-pop-menu .bui-menu-item-hover {
  background-color: #319dfc;
  color: #ffffff;
}
.bui-context-menu {
  position: absolute;
  border: 1px solid #c3c3d6;
  background-color: #e8e9ef;
  padding: 2px;
  z-index: 1100;
  background: url("../../img/separator-2-340.gif") repeat-y 28px 0 #e8e9ef;
}
.bui-context-menu .bui-menu-item {
  white-space: nowrap;
  overflow: hidden;
  position: relative;
  padding: 0;
}
.bui-context-menu .bui-menu-item .x-caret {
  position: absolute;
  right: 10px;
  top: 10px;
}
.bui-context-menu .bui-menu-item-sparator {
  border-top: 1px solid #c3c3d6;
  height: 1px;
  background-color: white;
  margin-left: 27px;
}
.bui-context-menu .bui-menu-item-link {
  cursor: default;
  display: block;
  *zoom: 1;
  width: 134px;
  height: 24px;
  margin: 1px;
  padding: 0 2px;
  text-decoration: none;
  border: 1px solid transparent;
  _border-color: tomato;
  _filter: chroma(color=#ff6347);
}
.bui-context-menu .bui-menu-item-icon {
  display: inline-block;
  *display: inline;
  *zoom: 1;
  width: 16px;
  height: 16px;
  margin: 0 11px 0 2px;
  border: 0 solid white;
}
.bui-context-menu .bui-menu-item-hover {
  background-color: #e8e9ef;
}
.bui-context-menu .bui-menu-item-open .bui-menu-item-link,
.bui-context-menu .bui-menu-item-hover .bui-menu-item-link {
  background-color: #E0E6FC;
  border: 1px solid #A9B9F5;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  border-radius: 2px;
  cursor: pointer;
  overflow: hidden;
}
.bui-context-menu .bui-menu-item-text {
  color: #333333;
  font-size: 11px;
  _font-size: 12px;
  line-height: 20px;
  *zoom: 1;
}
.bui-context-menu .bui-menu-item-disabled {
  outline: none;
}
.bui-context-menu .bui-menu-item-disabled .bui-menu-item-text {
  color: #999999;
}
.bui-context-menu .bui-menu-item-disabled .bui-menu-item-icon {
  opacity: 50;
  filter: alpha(opacity=5000);
}
.nav-tabs .bui-tab-item-text {
  display: inline-block;
  *display: inline;
  /* IE7 inline-block hack */
  *zoom: 1;
  margin-right: 3px;
  padding: 5px 15px;
  border: 1px solid #c3c3d6;
  border-bottom-color: transparent;
  -webkit-border-radius: 2px 2px 0 0;
  -moz-border-radius: 2px 2px 0 0;
  border-radius: 2px 2px 0 0;
  background-color: #eeeeee;
  position: relative;
}
.nav-tabs .bui-tab-item-selected {
  background-color: #ffffff;
  -webkit-border-radius: 2px 2px 0 0;
  -moz-border-radius: 2px 2px 0 0;
  border-radius: 2px 2px 0 0;
}
.nav-tabs .bui-tab-item-selected .bui-tab-item-text {
  background-color: #ffffff;
  position: relative;
  border-bottom: -1px;
  z-index: 10;
}
.button-tabs {
  margin-bottom: 10px;
}
.button-tabs .bui-tab-item {
  margin-right: 5px;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
}
.button-tabs .bui-tab-item-text {
  border-radius: 4px;
  position: relative;
  display: block;
  padding: 10px 15px;
}
.button-tabs .bui-tab-item-hover {
  background-color: #eeeeee;
}
.button-tabs .bui-tab-item-selected {
  color: #ffffff;
  font-weight: bold;
}
.button-tabs .bui-tab-item-selected .bui-tab-item-text {
  color: #ffffff;
  background-color: #6cb5f4;
}
.link-tabs .bui-tab-item-selected a {
  color: #555555;
  background-color: #ffffff;
  border: 1px solid #ddd;
  border-bottom-color: transparent;
  cursor: default;
}
.link-tabs .bui-tab-item-selected a:hover {
  background-color: #ffffff;
  border: 1px solid #ddd;
  border-bottom-color: transparent;
}
.tab-nav-bar,
.tab-nav-arrow,
.tab-nav-wrapper,
.tab-nav-inner,
.bui-nav-tab-item,
.tab-item-close,
.bui-nav-tab-item .tab-item-inner,
.bui-nav-tab-item .l,
.bui-nav-tab-item .r {
  background: url("../../img/tab-140-120.gif") -9999px -9999px no-repeat;
}
.tab-nav-bar {
  position: relative;
  width: 100%;
  z-index: 1;
  overflow: hidden;
  height: 21px;
  background-position: 0 20px;
  background-repeat: repeat-x;
}
.tab-content-container {
  width: 100%;
}
.tab-content-container iframe {
  border: none;
}
.tab-content {
  height: 100%;
}
.tab-nav-arrow {
  display: block;
  position: absolute;
  text-decoration: none;
  overflow: hidden;
  width: 13px;
  height: 13px;
  text-indent: -99px;
  top: 4px;
}
.bui-tab-force .tab-nav-arrow {
  display: none;
}
.bui-tab-force .tab-nav-wrapper {
  margin: 0;
}
.bui-nav-tab .tab-nav-list {
  padding-left: 15px;
}
.arrow-left {
  background-position: 0 -60px;
  left: 5px;
}
.arrow-left-active .arrow-left {
  background-position: -40px -60px;
  cursor: pointer;
}
.arrow-right {
  background-position: -20px -60px;
  right: 5px;
}
.arrow-right-active .arrow-right {
  background-position: -100px -60px;
  cursor: pointer;
}
.tab-nav-wrapper {
  margin: 0 23px;
  background-position: 0 -100px;
  background-repeat: repeat-x;
}
.tab-nav-inner {
  background-position: 0 20px;
  background-repeat: repeat-x;
  background-color: #e2eaf4;
  margin: 0 2px;
  overflow: hidden;
  position: relative;
}
.tab-nav-list {
  position: relative;
  left: 0;
  padding: 0;
  margin: 0;
  *zoom: 1;
}
.tab-nav-list:before,
.tab-nav-list:after {
  content: " ";
  display: table;
}
.tab-nav-list:after {
  clear: both;
}
.tab-nav-list:before,
.tab-nav-list:after {
  content: " ";
  display: table;
}
.tab-nav-list:after {
  clear: both;
}
.bui-nav-tab-item {
  color: #263e74;
  float: left;
  width: 140px;
  height: 21px;
  position: relative;
  cursor: pointer;
  padding: 0;
  z-index: 1;
}
.bui-nav-tab-item .tab-item-inner {
  background-position: -15px -4px;
  margin: 0 20px 0 5px;
  background-color: transparent;
}
.bui-nav-tab-item .l,
.bui-nav-tab-item .r {
  position: absolute;
  display: block;
  height: 21px;
  top: 0;
}
.bui-nav-tab-item .l {
  background-position: 0 -4px;
  width: 20px;
  left: -15px;
}
.bui-nav-tab-item .r {
  background-position: right -4px;
  width: 33px;
  right: 0;
}
.tab-item-title {
  cursor: pointer;
}
.tab-nav-actived {
  z-index: 2;
}
.tab-nav-actived .tab-item-inner {
  background-position: -15px -29px;
}
.tab-nav-actived .l {
  background-position: 0 -29px;
  z-index: 2;
}
.tab-nav-actived .r {
  background-position: right -29px;
  z-index: 2;
}
.tab-item-close {
  display: block;
  position: absolute;
  text-decoration: none;
  overflow: hidden;
  cursor: pointer;
  width: 13px;
  height: 13px;
  text-indent: -99px;
  z-index: 3;
  top: 4px;
  right: 17px;
  background-position: 0 -80px;
}
.tab-item-close:hover {
  background-position: -20px -80px;
}
.bui-select-list {
  background-color: #fff;
  border: 1px solid #ccc;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 4px;
  -webkit-box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
  background-clip: padding-box;
}
.bui-select-list ul {
  margin: 10px 0;
}
.bui-select-list .bui-list-item {
  display: block;
  padding: 3px 20px;
  color: #333333;
  white-space: nowrap;
}
.bui-select-list .bui-list-item-hover {
  text-decoration: none;
  color: #262626;
  background-color: #f5f5f5;
}
.bui-select-list .bui-list-item-selected {
  color: #fff;
  text-decoration: none;
  outline: 0;
  background-color: #6cb5f4;
}
.bui-select-list .bui-list-item-disabled {
  color: #999999;
  cursor: not-allowed;
}
.bui-select {
  display: inline-block;
}
.bui-select .bui-select-input {
  width: 112px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.bui-select .x-icon {
  vertical-align: middle;
  cursor: pointer;
  height: 28px;
  width: 28px;
  border-left: none;
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  border-radius: 0;
  border-top-right-radius: 3px;
  border-bottom-right-radius: 3px;
}
/** list-checkbox **/
.x-checkbox {
  background: url("../../img/check_icon-100-100.gif") no-repeat 0px 0px transparent;
}
.x-radio {
  background: url("../../img/radio_icon-64-40.gif") no-repeat 0 0 transparent;
}
.x-checkbox,
.x-radio {
  width: 13px;
  height: 20px;
  *height: 17px;
  vertical-align: top;
  *vertical-align: baseline;
  margin-right: 5px;
  display: inline-block;
  *display: inline;
  /* IE7 inline-block hack */
  *zoom: 1;
}
.controls .x-checkbox,
.controls .x-radio {
  margin-top: 4px;
}
.x-checkbox:hover,
.x-radio:hover,
.bui-list-item-hover .x-checkbox,
.bui-list-item-hover .x-radio {
  background-position: -16px 0px;
}
.bui-list-item-selected .x-checkbox,
.bui-list-item-selected .x-radio,
.checked .x-checkbox {
  background-position: 0 -20px;
}
.bui-list-item-disabled .x-checkbox,
.bui-list-item-disabled .x-radio {
  background-position: -48px 0px;
}
.bui-simple-list-disabled .x-checkbox,
.bui-list-item-selected.bui-list-item-disabled .x-checkbox,
.bui-simple-list-disabled .x-radio,
.bui-list-item-selected.bui-list-item-disabled .x-radio {
  background-position: -48px -20px;
}
.bui-combox {
  border: 1px solid #c3c3d6;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
}
.bui-combox:focus {
  border-color: #66afe9;
  outline: 0;
  -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px rgba(102, 175, 233, 0.6);
  box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px rgba(102, 175, 233, 0.6);
}
.bui-combox ul {
  *zoom: 1;
}
.bui-combox ul:before,
.bui-combox ul:after {
  content: " ";
  display: table;
}
.bui-combox ul:after {
  clear: both;
}
.bui-combox ul:before,
.bui-combox ul:after {
  content: " ";
  display: table;
}
.bui-combox ul:after {
  clear: both;
}
.bui-combox .bui-list-item {
  display: inline-block;
  float: left;
  margin: 4px;
  padding: 4px 8px;
  transition: color 200ms;
  -moz-transition: color 200ms;
  /* Firefox 4 */
  -webkit-transition: color 200ms;
  /* Safari 鍜� Chrome */
  -o-transition: color 200ms;
  /* Opera */
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
  background-color: #5bc0de;
  color: #eeeeee;
}
.bui-combox .bui-list-item button {
  border: none;
  padding: 0;
  background: transparent;
  margin-left: 5px;
  cursor: pointer;
  color: #eeeeee;
}
.bui-combox .bui-list-item-warn {
  background-color: #d9534f;
}
.bui-combox .bui-list-item-active {
  background-color: #f0ad4e;
}
.bui-combox .bui-combox-input {
  border: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
.bui-combox .bui-combox-input:focus {
  border: none;
  outline: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
.bui-tag-follow .bui-simple-list {
  float: left;
}
.bui-tag-follow .bui-combox-input {
  float: left;
  margin-top: 4px;
  width: 50px;
}
.bui-overlay {
  position: absolute;
  left: -9999px;
  top: -9999px;
}
.bui-picker {
  z-index: 1200;
}
.bui-dialog {
  background-color: #fff;
  border: 1px solid #999;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 6px;
  -webkit-box-shadow: 0 3px 9px rgba(0, 0, 0, 0.5);
  box-shadow: 0 3px 9px rgba(0, 0, 0, 0.5);
  background-clip: padding-box;
  outline: 0;
  position: absolute;
  z-index: 1070;
  /*-----icon-------*/
}
.bui-dialog .bui-stdmod-header {
  padding: 15px;
  font-family: "ff-tisa-web-pro-1", "ff-tisa-web-pro-2", "Lucida Grande", "Helvetica Neue", Helvetica, Arial, "Hiragino Sans GB", "Hiragino Sans GB W3", "Microsoft YaHei UI", "Microsoft YaHei", "WenQuanYi Micro Hei", sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: #333333;
  border-bottom: 1px solid #e5e5e5;
  cursor: move;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
.bui-dialog .bui-stdmod-body {
  padding: 15px;
}
.bui-dialog .bui-stdmod-footer {
  padding: 15px;
  text-align: right;
  border-top: 1px solid #e5e5e5;
  *zoom: 1;
}
.bui-dialog .bui-stdmod-footer:before,
.bui-dialog .bui-stdmod-footer:after {
  content: " ";
  display: table;
}
.bui-dialog .bui-stdmod-footer:after {
  clear: both;
}
.bui-dialog .bui-stdmod-footer:before,
.bui-dialog .bui-stdmod-footer:after {
  content: " ";
  display: table;
}
.bui-dialog .bui-stdmod-footer:after {
  clear: both;
}
.bui-dialog .bui-stdmod-footer .button + .button {
  margin-left: 5px;
  margin-bottom: 0;
}
.bui-dialog .bui-stdmod-footer .button-group .button + .button {
  margin-left: -1px;
}
.bui-dialog .bui-stdmod-footer .button-block + .button-block {
  margin-left: 0;
}
.bui-dialog a.bui-ext-close {
  display: block;
  width: 22px;
  height: 22px;
  position: absolute;
  right: 15px;
  top: 15px;
  outline: none;
  overflow: hidden;
  cursor: pointer;
  text-decoration: none;
  z-index: 1;
}
.bui-dialog .bui-ext-close-x {
  display: block;
  font-size: 22px;
  line-height: 1;
  cursor: pointer;
  border: none;
}
.bui-dialog .bui-ext-close-x:hover {
  background-color: #fff;
}
.bui-message {
  padding: 15px 20px;
}
.bui-message .bui-stdmod-header {
  padding: 0;
  border-bottom: none;
}
.bui-message .header-title {
  padding-bottom: 15px;
}
.bui-message .header-title:empty {
  padding-bottom: 0;
}
.bui-message .bui-stdmod-body {
  padding: 0 30px 20px 0;
}
.bui-message .bui-stdmod-body .x-icon {
  float: left;
}
.bui-message .bui-message-content {
  margin-left: 40px;
  line-height: 25px;
}
.bui-message .bui-stdmod-footer {
  padding: 0;
  text-align: center;
  border-top: none;
}
.image-pbar button,
.bui-grid .bui-pagingbar button,
.image-pbar .bui-bar-item-button,
.bui-grid .bui-pagingbar .bui-bar-item-button,
.image-pbar .bui-pb-page,
.bui-grid .bui-pagingbar .bui-pb-page {
  background: url("../../img/table-191-450.gif") no-repeat -999px -999px transparent;
  overflow: hidden;
}
.image-pbar button,
.bui-grid .bui-pagingbar button {
  height: 16px;
  width: 16px;
  margin: 0;
  padding: 0;
  text-indent: -100px;
  *text-indent: 0;
  *font-size: 0;
  border: none;
  overfow: hidden;
  vertical-align: middle;
}
.image-pbar .bui-button-disabled,
.bui-grid .bui-pagingbar .bui-button-disabled {
  opacity: 100;
  filter: alpha(opacity=10000);
}
.image-pbar .bui-pb-first button,
.bui-grid .bui-pagingbar .bui-pb-first button {
  background-position: 3px 2px;
}
.image-pbar .bui-pb-first .bui-button-disabled,
.bui-grid .bui-pagingbar .bui-pb-first .bui-button-disabled {
  background-position: 3px -18px;
}
.image-pbar .bui-pb-prev button,
.bui-grid .bui-pagingbar .bui-pb-prev button {
  background-position: -57px 2px;
}
.image-pbar .bui-pb-prev .bui-button-disabled,
.bui-grid .bui-pagingbar .bui-pb-prev .bui-button-disabled {
  background-position: -57px -18px;
}
.image-pbar .bui-pb-next button,
.bui-grid .bui-pagingbar .bui-pb-next button {
  background-position: -37px 2px;
}
.image-pbar .bui-pb-next .bui-button-disabled,
.bui-grid .bui-pagingbar .bui-pb-next .bui-button-disabled {
  background-position: -37px -18px;
}
.image-pbar .bui-pb-last button,
.bui-grid .bui-pagingbar .bui-pb-last button {
  background-position: -17px 2px;
}
.image-pbar .bui-pb-last .bui-button-disabled,
.bui-grid .bui-pagingbar .bui-pb-last .bui-button-disabled {
  background-position: -17px -18px;
}
.image-pbar .bui-bar-item,
.bui-grid .bui-pagingbar .bui-bar-item {
  outline: none;
  margin: 0 4px;
  vertical-align: middle;
}
.image-pbar .bui-bar-item-button,
.bui-grid .bui-pagingbar .bui-bar-item-button {
  padding: 1px;
}
.image-pbar .bui-bar-item-button-hover,
.bui-grid .bui-pagingbar .bui-bar-item-button-hover {
  background-position: -130px 2px;
}
.image-pbar .bui-bar-item-button-disabled,
.bui-grid .bui-pagingbar .bui-bar-item-button-disabled {
  background-position: -999px -999px;
}
.image-pbar .bui-pb-page,
.bui-grid .bui-pagingbar .bui-pb-page {
  color: #9d261d;
  font: 11px tahoma, arial, verdana, sans-serif;
  height: 14px;
  margin: 0;
  width: 20px;
  vertical-align: baseline;
  *vertical-align: middle;
  background-color: white;
  padding: 0 2px;
}
.image-pbar .bui-bar-item-text,
.bui-grid .bui-pagingbar .bui-bar-item-text {
  vertical-align: middle;
}
.image-pbar .bui-pb-skip,
.bui-grid .bui-pagingbar .bui-pb-skip {
  background-position: -999px -999px;
}
.image-pbar .bui-pb-skip button,
.bui-grid .bui-pagingbar .bui-pb-skip button {
  text-indent: 0;
  background-position: -80px -20px;
  height: 20px;
  font-size: 11px;
  width: 41px;
}
.image-pbar .bui-pb-skip button:hover,
.bui-grid .bui-pagingbar .bui-pb-skip button:hover {
  background-position: -150px -20px;
}
.image-pbar .bui-bar-item-separator,
.bui-grid .bui-pagingbar .bui-bar-item-separator {
  margin: 0 5px;
}
.pagination .bui-bar-item {
  float: left;
  margin: 0;
}
.pagination .disabled {
  _background-color: #ffffff;
}
.pagination .disabled a:hover {
  text-decoration: none;
}
.bar-btn-add,
.bar-btn-del,
.bar-btn-edit,
.bar-btn-close,
.bar-btn-import,
.bar-btn-export,
.bar-btn-save,
.bar-btn-create,
.bui-bar-item-separator {
  background: url("../../img/table-191-450.gif") no-repeat -999px -999px transparent;
}
.bui-bar-item-separator {
  height: 14px;
  margin: 0 3px 0 2px;
  width: 2px;
  background-position: -80px 0;
  vertical-align: middle;
}
.bui-grid-button-bar {
  float: left;
}
.bui-grid-button-bar .bui-bar-item {
  margin-right: 10px;
}
.bui-grid-button-bar .bar-btn-add,
.bui-grid-button-bar .bar-btn-edit,
.bui-grid-button-bar .bar-btn-del,
.bui-grid-button-bar .bar-btn-close,
.bui-grid-button-bar .bar-btn-import,
.bui-grid-button-bar .bar-btn-export,
.bui-grid-button-bar .bar-btn-save,
.bui-grid-button-bar .bar-btn-create {
  padding-left: 18px;
}
.bui-grid-button-bar .bar-btn-edit {
  background-position: 2px -307px;
}
.bui-grid-button-bar .bar-btn-add {
  background-position: -48px -137px;
}
.bui-grid-button-bar .bar-btn-del {
  background-position: 2px -247px;
}
.bui-grid-button-bar .bar-btn-close {
  background-position: -48px -157px;
}
.bui-grid-button-bar .bar-btn-import {
  background-position: 2px -187px;
}
.bui-grid-button-bar .bar-btn-export {
  background-position: 2px -217px;
}
.bui-grid-button-bar .bar-btn-save {
  background-position: 2px -277px;
}
.bui-grid-button-bar .bar-btn-create {
  background-position: 2px -307px;
}
.bui-grid-button-bar .button-small [class^="icon-"] {
  margin: 0px 2px 0 -5px;
}
.bui-simple-list-focused {
  outline: none;
}
.bui-grid-table .sort-asc .bui-grid-sort-icon,
.bui-grid-table .sort-desc .bui-grid-sort-icon,
.bui-grid-table .bui-grid-hd-menu-trigger,
.bui-grid-table .bui-grid-cascade-icon {
  background-image: url("../../img/table-191-450.gif");
  background-repeat: no-repeat;
}
.bui-simple-grid .bui-grid-table {
  border: 1px solid #dddddd;
}
.bui-grid-header {
  border-top: 1px solid #dddddd;
}
.bui-drag-line {
  position: absolute;
  border-left: 1px solid #ccc;
}
.bui-grid-body,
.bui-grid-height .bui-grid-body .bui-grid-table {
  border-bottom: 1px solid #dddddd;
}
.bui-grid-body,
.bui-grid-header {
  border-right: 1px solid #dddddd;
  border-left: 1px solid #dddddd;
}
.bui-grid-header {
  overflow: hidden;
  *position: relative;
}
.bui-grid-header .table {
  margin: 0;
}
.bui-grid-table {
  width: 100%;
  table-layout: fixed;
  border-collapse: separate;
}
.bui-grid,
.bui-simple-grid {
  /*width:100%;    */
  background-color: #ffffff;
}
.bui-grid th.left,
.bui-simple-grid th.left,
.bui-grid td.left,
.bui-simple-grid td.left {
  text-align: left;
}
.bui-grid th.right,
.bui-simple-grid th.right,
.bui-grid td.right,
.bui-simple-grid td.right {
  text-align: right;
}
.bui-grid th.center,
.bui-simple-grid th.center,
.bui-grid td.center,
.bui-simple-grid td.center {
  text-align: center;
}
.bui-grid-border .bui-grid-hd,
.bui-grid-border .bui-grid-cell,
.bui-grid-border .bui-grid-header-row td {
  border-left: 1px solid #dddddd;
}
.bui-grid-border .bui-grid-cell-empty,
.bui-grid-border .bui-grid-hd-empty {
  border-left: none;
}
.bui-grid-header-row td,
.bui-grid-header-row th {
  padding: 0;
  margin: 0;
}
th.bui-grid-hd-empty,
td.bui-grid-cell-empty {
  padding: 0;
  margin: 0;
  border-left: none;
}
.bui-grid-group-header .bui-grid-hd-empty {
  display: none;
}
.bui-grid-table {
  /* row */
}
.bui-grid-table .bui-grid-hd {
  border-bottom: 1px solid #dddddd;
  text-align: left;
  cursor: default;
}
.bui-grid-table tr td:first-child,
.bui-grid-table tr th:first-child {
  border-left-width: 0;
}
.bui-grid-table .bui-grid-hd-inner {
  padding: 8px 0;
  height: 24px;
  overflow: hidden;
  font-weight: bold;
  background-position: 0 0;
  position: relative;
}
.bui-grid-table .bui-grid-db-hd .bui-grid-hd-inner {
  padding: 32px 0 33px;
}
.bui-grid-table .bui-grid-hd-title {
  line-height: 22px;
  font-size: 12px;
  padding: 0 8px;
}
.bui-grid-table .bui-grid-sort-icon {
  display: inline-block;
  *display: inline;
  *zoom: 1;
  height: 15px;
  width: 15px;
}
.bui-grid-table .bui-grid-hd-menu-trigger {
  cursor: pointer;
  display: none;
  position: absolute;
  right: 0;
  top: 0;
  width: 14px;
  height: 24px;
  background-position: 0 -140px;
}
.bui-grid-table th.sortable {
  cursor: pointer;
}
.bui-grid-table .bui-grid-hd-hover .bui-grid-hd-inner,
.bui-grid-table .bui-grid-hd-open .bui-grid-hd-inner {
  background-color: #f5f5f5;
}
.bui-grid-table .bui-grid-hd-hover .bui-grid-hd-menu-trigger,
.bui-grid-table .bui-grid-hd-open .bui-grid-hd-menu-trigger {
  display: block;
}
.bui-grid-table .sort-asc .bui-grid-sort-icon {
  background-position: 5px -52px;
}
.bui-grid-table .sort-asc:hover .bui-grid-sort-icon {
  background-position: 5px -92px;
}
.bui-grid-table .sort-desc .bui-grid-sort-icon {
  background-position: 5px -72px;
}
.bui-grid-table .sort-desc:hover .bui-grid-sort-icon {
  background-position: 5px -112px;
}
.bui-grid-table .grid-header-checked-column,
.bui-grid-table .bui-grid-row-checked-column {
  text-align: center;
  width: 30px;
  vertical-align: middle;
  border-left-width: 0;
}
.bui-grid-table .grid-header-checked-column .bui-grid-hd-inner,
.bui-grid-table .bui-grid-row-checked-column .bui-grid-cell-inner {
  width: 30px;
}
.bui-grid-table .bui-grid-cell {
  overflow: hidden;
  border-top: 1px solid #dddddd;
}
.bui-grid-table .bui-grid-row-group td {
  border-top: 1px solid #dddddd;
}
.bui-grid-table .bui-grid-cell-empty {
  border-top: 1px solid #dddddd;
}
.bui-grid-table .bui-grid-body .bui-grid-row-first td {
  border-top: none;
}
.bui-grid-table .bui-grid-body .bui-grid-table {
  border-bottom: 1px solid #dddddd;
}
.bui-grid-table .bui-grid-header-row .bui-grid-cell-empty {
  line-height: 0;
  border: none;
}
.bui-grid-table .bui-grid-cell-inner {
  padding: 6px 0;
  position: relative;
  overflow: hidden;
}
.bui-grid-table .bui-grid-cell-text {
  padding: 0 8px;
  display: block;
  min-height: 20px;
  min-width: 25px;
  _height: 20px;
}
.bui-grid-table .bui-grid-error-cell .bui-grid-cell-text {
  padding-right: 20px;
}
.bui-grid-table .bui-grid-error-cell {
  position: relative;
}
.bui-grid-table .bui-grid-cell,
.bui-grid-table .bui-grid-cell-empty {
  word-break: break-all;
  word-wrap: break-word;
}
.bui-grid-table .grid-command {
  color: #319dfc;
  cursor: pointer;
  display: inline-block;
  margin-right: 5px;
}
.bui-grid-table .grid-command:hover {
  color: #0377dd;
}
.bui-grid-table .grid-command.disable {
  color: #ccc;
}
td.bui-grid-cell-empty {
  height: 0;
  line-height: 0;
}
.bui-grid-width .bui-grid-body {
  overflow-x: auto;
  overflow-y: hidden;
}
.bui-grid-height .bui-grid-body {
  overflow-x: auto;
  overflow-y: scroll;
  position: relative;
}
/**stripe**/
.bui-grid-strip .bui-grid-row-odd {
  background-color: #ffffff;
}
.bui-grid-strip .bui-grid-row-even {
  background-color: #fafafa;
}
.bui-simple-grid .bui-grid-table .bui-grid-row-hover,
.bui-grid .bui-grid-table .bui-grid-row-hover {
  background-color: #f5f5f5;
}
.bui-simple-grid .bui-grid-table .bui-grid-row-selected,
.bui-grid .bui-grid-table .bui-grid-row-selected {
  background-color: #f5f5f5;
}
/**grid-bar**/
.bui-grid .bui-pagingbar {
  float: right;
  margin: 6px 0;
}
.bui-grid-tbar,
.bui-grid-bbar {
  *zoom: 1;
}
.bui-grid-tbar:before,
.bui-grid-bbar:before,
.bui-grid-tbar:after,
.bui-grid-bbar:after {
  content: " ";
  display: table;
}
.bui-grid-tbar:after,
.bui-grid-bbar:after {
  clear: both;
}
.bui-grid-tbar:before,
.bui-grid-bbar:before,
.bui-grid-tbar:after,
.bui-grid-bbar:after {
  content: " ";
  display: table;
}
.bui-grid-tbar:after,
.bui-grid-bbar:after {
  clear: both;
}
.bui-grid-tbar {
  height: 34px;
  line-height: 34px;
}
.bui-grid-bbar {
  border: 1px solid #dddddd;
  border-top: none;
}
.bui-grid-radio-container,
.bui-grid-checkBox-container {
  text-align: center;
}
.bui-grid .x-grid-checkbox {
  background: url("../../img/check_icon-100-100.gif") no-repeat 0px 3px transparent;
  width: 13px;
  height: 20px;
  display: inline-block;
  *display: inline;
  /* IE7 inline-block hack */
  *zoom: 1;
}
.x-grid-checkbox:hover {
  background-position: -16px 3px;
}
.bui-grid-row-selected .x-grid-checkbox,
.checked .x-grid-checkbox {
  background-position: 0 -17px;
}
.bui-grid-row-disabled .x-grid-checkbox {
  background-position: -48px 3px;
}
.bui-grid-row-selected.bui-grid-row-disabled .x-grid-checkbox {
  background-position: -48px -18px;
}
.bui-grid-cascade {
  vertical-align: middle;
}
.bui-grid-cascade-icon {
  cursor: pointer;
  display: inline-block;
  *display: inline;
  *zoom: 1;
  height: 10px;
  width: 10px;
  background-position: 0 -440px;
}
.bui-grid-cascade-expand .bui-grid-cascade-icon {
  background-position: -30px -440px;
}
.bui-grid-cascade-collapse {
  display: none;
}
.grid-column-menu .bui-menu-item-selected .icon {
  background-position: -144px -72px;
}
.bui-grid-cell-error {
  position: absolute;
  right: 10px;
  top: 6px;
}
.bui-grid-header .table {
  height: auto;
}
.bui-grid-summary-row {
  font-weight: bold;
}
.bui-grid-summary-row:first-child td {
  border-top: 2px solid #dddddd;
}
td.x-grid-rownumber {
  text-align: center;
  background-color: #f5f5f5;
}
.x-col-checkbox {
  background: url("../../img/check_icon-100-100.gif") 0 3px no-repeat;
  width: 13px;
  height: 20px;
  display: inline-block;
  cursor: pointer;
}
.x-col-checkbox-checked {
  background-position: 0 -17px;
}
.bui-grid-row-disabled .x-col-checkbox {
  background-position: -48px 3px;
}
.bui-grid-row-disabled .x-col-checkbox-checked {
  background-position: -48px -17px;
}
/*Mask*/
.lp-ext-mask,
.lp-el-mask,
.bui-ext-mask {
  height: 100%;
  left: 0;
  opacity: 0.5;
  filter: alpha(opacity=50);
  position: absolute;
  top: 0;
  width: 100%;
  z-index: 1040;
  background-color: #333333;
}
.x-masked SELECT {
  _visibility: hidden;
}
.x-masked .bui-dialog SELECT {
  _visibility: visible;
}
.x-masked-relative {
  position: relative;
}
.lp-el-mask-msg,
.bui-ext-mask-msg {
  background: none repeat-x scroll 0 -16px #e8e9ef;
  border: 1px solid #c3c3d6;
  left: 0;
  padding: 2px;
  position: absolute;
  top: 0;
  z-index: 1050;
}
.lp-el-mask-msg div {
  border: 1px solid;
  cursor: wait;
  padding: 5px 10px;
  background-color: #ffffff;
  border-color: #c3c3d6;
  color: #333333;
}
.x-mask-loading div {
  background: none no-repeat scroll 5px 5px #ffffff;
  line-height: 16px;
  padding: 5px 10px 5px 25px;
  background-image: url("../../img/load-16-16.gif");
}
/**chart**/
.node-info-container,
.node-title-container {
  position: absolute;
}
.node-info-container {
  border: 1px dashed #c3c3d6;
  padding: 5px;
  line-height: 14px;
  font-size: 11px;
  z-index: 10;
  opacity: 90;
  filter: alpha(opacity=9000);
  background-color: #ffffff;
}
.node-info-container li {
  line-height: 14px;
}
.node-info-container .bui-caret {
  position: absolute;
  top: -7px;
  *top: -12px;
  left: 2px;
}
.node-info-container .table {
  border: 1px solid #c3c3d6;
  border-left: 0;
  border-bottom: 0;
  border-collapse: separate;
  border-spacing: 0;
}
.node-info-container .table td,
.node-info-container .table th {
  border-left: 1px solid #c3c3d6;
  border-bottom: 1px solid #c3c3d6;
}
.node-info-container-selected {
  z-index: 11;
}
.node-pos-top .bui-caret {
  top: auto;
  left: auto;
  bottom: -7px;
  left: 2px;
}
input.bui-form-field-error,
textarea.bui-form-field-error,
.bui-form-field-error input[type="text"] {
  border: 1px dotted red;
}
.x-field-error {
  margin-left: 5px;
}
.x-field-error .x-icon {
  padding: 1px;
  -webkit-border-radius: 8px;
  -moz-border-radius: 8px;
  border-radius: 8px;
  position: relative;
  top: 3px;
}
.x-field-error .x-field-error-text {
  padding-left: 5px;
  color: #eb4f38;
}
.bui-form-field-disabled.calendar {
  background-color: #ebebe4;
}
.bui-form-field-checklist .bui-list-item,
.bui-form-field-radiolist .bui-list-item {
  display: inline-block;
  *display: inline;
  /* IE7 inline-block hack */
  *zoom: 1;
  margin-right: 10px;
}
.bui-form-tip-container {
  position: relative;
}
.bui-form-tip {
  padding-left: 5px;
  color: #999999;
}
.bui-form-tip .tip-text {
  margin-left: 5px;
  *white-space: nowrap;
}
.bui-form-tip .icon {
  opacity: 50;
  filter: alpha(opacity=5000);
}
.calendar {
  background: url("../../img/calendar-200-300.gif") no-repeat right -182px #ffffff;
}
.bui-calendar {
  padding: 4px 0px;
  margin: 2px 0 0;
  width: 232px !important;
  -webkit-box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
  color: #333333;
  background-color: #ffffff;
  font-size: 13px;
  border: 1px solid #ccc;
  position: relative;
}
.bui-calendar:before {
  content: '';
  display: inline-block;
  border-left: 7px solid transparent;
  border-right: 7px solid transparent;
  border-bottom: 7px solid #ccc;
  border-top: 0;
  border-bottom-color: rgba(0, 0, 0, 0.2);
  position: absolute;
  left: 5px;
  top: -7px;
}
.bui-calendar:after {
  content: '';
  display: inline-block;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-bottom: 6px solid #fff;
  border-top: 0;
  position: absolute;
  left: 6px;
  top: -6px;
}
.bui-calendar .bui-calendar-header {
  padding: 0 4px;
  position: relative;
  *zoom: 1;
}
.bui-calendar .bui-calendar-header:before,
.bui-calendar .bui-calendar-header:after {
  content: " ";
  display: table;
}
.bui-calendar .bui-calendar-header:after {
  clear: both;
}
.bui-calendar .bui-calendar-header:before,
.bui-calendar .bui-calendar-header:after {
  content: " ";
  display: table;
}
.bui-calendar .bui-calendar-header:after {
  clear: both;
}
.bui-calendar .bui-calendar-header .x-datepicker-prev,
.bui-calendar .bui-calendar-header .x-datepicker-next {
  width: 32px;
  height: 30px;
  position: absolute;
  top: 0;
  cursor: pointer;
  background-color: #ffffff;
  text-align: center;
}
.bui-calendar .bui-calendar-header .x-datepicker-prev:hover,
.bui-calendar .bui-calendar-header .x-datepicker-next:hover {
  background-color: #eeeeee;
}
.bui-calendar .bui-calendar-header .x-datepicker-prev .icon,
.bui-calendar .bui-calendar-header .x-datepicker-next .icon {
  vertical-align: text-bottom;
  background-image: url('../../img/sprite-469-300.png');
}
.bui-calendar .bui-calendar-header .x-datepicker-prev {
  left: 4px;
}
.bui-calendar .bui-calendar-header .x-datepicker-month {
  height: 30px;
  line-height: 30px;
  text-align: center;
  cursor: pointer;
  font-weight: bold;
}
.bui-calendar .bui-calendar-header .x-datepicker-month:hover {
  background-color: #eeeeee;
}
.bui-calendar .bui-calendar-header .x-datepicker-next {
  right: 4px;
}
.bui-calendar .bui-calendar-panel {
  padding: 0 4px;
}
.bui-calendar .bui-calendar-panel th {
  padding: 4px 5px;
  width: 22px;
}
.bui-calendar .bui-calendar-panel:focus {
  outline: none;
}
.bui-calendar .bui-calendar-panel td.x-datepicker-date em {
  font-style: normal;
}
.bui-calendar .bui-calendar-panel td.x-datepicker-date a {
  display: block;
  padding: 3px 0;
  text-align: center;
  color: #333333;
}
.bui-calendar .bui-calendar-panel td.x-datepicker-date a:hover,
.bui-calendar .bui-calendar-panel td.x-datepicker-date a:focus {
  background-color: #eeeeee;
  text-decoration: none;
}
.bui-calendar .bui-calendar-panel td.x-datepicker-prevday a {
  color: #999999;
}
.bui-calendar .bui-calendar-panel td.x-datepicker-disabled a {
  color: #999999;
  cursor: default;
}
.bui-calendar .bui-calendar-panel td.x-datepicker-disabled a:hover,
.bui-calendar .bui-calendar-panel td.x-datepicker-disabled a:focus {
  background-color: transparent;
}
.bui-calendar .bui-calendar-panel td.x-datepicker-today a {
  color: #000;
  background-color: #ffdb99;
}
.bui-calendar .bui-calendar-panel td.x-datepicker-today a:hover,
.bui-calendar .bui-calendar-panel td.x-datepicker-today a:focus {
  background-color: #ffcd70;
}
.bui-calendar .bui-calendar-panel td.x-datepicker-selected a {
  color: #ffffff;
  background-color: #6cb5f4;
  text-decoration: none;
}
.bui-calendar .bui-calendar-panel td.x-datepicker-selected a:hover,
.bui-calendar .bui-calendar-panel td.x-datepicker-selected a:focus {
  background-color: #6cb5f4;
}
.bui-calendar .bui-calendar-footer {
  margin-top: 2px;
  padding: 6px 4px 5px;
  border-top: 1px solid #ccc;
  text-align: center;
}
.bui-calendar .bui-calendar-footer .bui-bar-item-button {
  margin-right: 5px;
}
.bui-calendar .bui-calendar-footer input.x-datepicker-time {
  padding: 4px 10px;
  width: 18px;
  height: 15px;
  cursor: pointer;
}
.bui-calendar .bui-calendar-footer .x-datepicker-second {
  margin-right: 5px;
}
.bui-calendar .bui-monthpicker {
  position: absolute;
  width: 100%;
  top: 0px;
  left: 0px;
  background-color: #ffffff;
  border: none;
}
.x-timepicker .bui-simple-list {
  padding: 0px 2px;
  width: 228px;
  color: #333333;
  background-color: #ffffff;
  font-size: 13px;
  border: 1px solid #ccc;
  position: relative;
}
.x-timepicker ul {
  *zoom: 1;
}
.x-timepicker ul:before,
.x-timepicker ul:after {
  content: " ";
  display: table;
}
.x-timepicker ul:after {
  clear: both;
}
.x-timepicker ul:before,
.x-timepicker ul:after {
  content: " ";
  display: table;
}
.x-timepicker ul:after {
  clear: both;
}
.x-timepicker li {
  float: left;
  width: 32px;
}
.x-timepicker li a {
  display: block;
  padding: 3px 0;
  text-align: center;
  color: #333333;
}
.x-timepicker li a:hover,
.x-timepicker li a:focus {
  background-color: #eeeeee;
  text-decoration: none;
}
.bui-monthpicker {
  position: absolute;
  width: 232px !important;
  top: 0px;
  left: 0px;
  background-color: #ffffff;
  border: 1px solid #ccc;
}
.bui-monthpicker .bui-calendar-month-panel ul,
.bui-monthpicker .bui-calendar-year-panel ul {
  *zoom: 1;
}
.bui-monthpicker .bui-calendar-month-panel ul:before,
.bui-monthpicker .bui-calendar-year-panel ul:before,
.bui-monthpicker .bui-calendar-month-panel ul:after,
.bui-monthpicker .bui-calendar-year-panel ul:after {
  content: " ";
  display: table;
}
.bui-monthpicker .bui-calendar-month-panel ul:after,
.bui-monthpicker .bui-calendar-year-panel ul:after {
  clear: both;
}
.bui-monthpicker .bui-calendar-month-panel ul:before,
.bui-monthpicker .bui-calendar-year-panel ul:before,
.bui-monthpicker .bui-calendar-month-panel ul:after,
.bui-monthpicker .bui-calendar-year-panel ul:after {
  content: " ";
  display: table;
}
.bui-monthpicker .bui-calendar-month-panel ul:after,
.bui-monthpicker .bui-calendar-year-panel ul:after {
  clear: both;
}
.bui-monthpicker .bui-calendar-month-panel li,
.bui-monthpicker .bui-calendar-year-panel li {
  float: left;
  margin: 4px 0 0 6px;
  width: 44px;
  height: 32px;
}
.bui-monthpicker .bui-calendar-month-panel li a,
.bui-monthpicker .bui-calendar-year-panel li a {
  display: block;
  text-align: center;
  color: #333333;
  height: 32px;
  line-height: 32px;
}
.bui-monthpicker .bui-calendar-month-panel li a:hover,
.bui-monthpicker .bui-calendar-year-panel li a:hover {
  background-color: #eeeeee;
  text-decoration: none;
}
.bui-monthpicker .bui-calendar-month-panel .x-monthpicker-item-selected a,
.bui-monthpicker .bui-calendar-year-panel .x-monthpicker-item-selected a {
  color: #ffffff;
  background-color: #6cb5f4;
  text-decoration: none;
}
.bui-monthpicker .bui-calendar-month-panel .x-monthpicker-item-selected a:hover,
.bui-monthpicker .bui-calendar-year-panel .x-monthpicker-item-selected a:hover {
  background-color: #6cb5f4;
}
.bui-monthpicker .bui-calendar-month-panel {
  float: left;
  padding: 0 0 4px 1px;
  width: 114px;
  border-right: 1px solid #ccc;
}
.bui-monthpicker .bui-calendar-year-panel {
  margin-left: 118px;
  *margin-left: 117px;
}
.bui-monthpicker .bui-calendar-year-panel .x-monthpicker-yearnav {
  padding: 14px 0 0px 0;
  height: 26px;
}
.bui-monthpicker .bui-calendar-year-panel .x-monthpicker-yearnav .x-icon {
  margin: 0px 18px 0px 20px;
  cursor: pointer;
}
.bui-monthpicker .bui-calendar-year-panel .x-monthpicker-yearnav .x-icon .icon {
  margin-top: 2px;
}
.bui-monthpicker .bui-calendar-year-panel ul {
  *padding-bottom: 4px;
}
.bui-monthpicker .x-monthpicker-footer {
  padding: 6px 0 4px;
  border-top: 1px solid #ccc;
  text-align: center;
}
.bui-monthpicker .x-monthpicker-footer .bui-bar-item-button {
  margin-right: 5px;
}
.x-editor-tips {
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
  border: 1px solid #ebccd1;
  background-color: #ffffff;
  padding: 4px;
}
.bui-editor {
  z-index: 1080;
}
.bui-record-editor {
  padding: 3px 0 3px 3px;
  border: 1px solid #c3c3d6;
  background-color: white;
}
.bui-record-editor .button {
  *margin-top: 5px;
}
.bui-record-editor .bui-bar {
  width: 150px;
  position: absolute;
  left: 40%;
  background-color: white;
  border: 1px solid #c3c3d6;
  border-top: none;
  border-radius: 0 0 3px 3px;
  bottom: -37px;
}
.bui-record-editor .bui-form-field {
  padding-right: 3px;
}
.bui-record-editor .x-form-text {
  background-color: #ffffff;
  display: inline-block;
  height: 18px;
  padding: 1px 4px;
  border: 1px solid #c3c3d6;
}
.bui-editor .bui-form-check-field {
  background-color: #ffffff;
  text-align: center;
}
.bui-editor .bui-form-check-field input {
  vertical-align: middle;
}
.bui-tree-list {
  border: 1px solid #c3c3d6;
  overflow: auto;
  background-color: #ffffff;
}
.bui-tree-list ul {
  overflow: hidden;
}
.bui-tree-item {
  height: 20px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
}
.bui-tree-item-hover {
  background-color: #dee5ff;
}
.bui-tree-item-selected {
  background-color: #ccd7ff;
}
.bui-tree-item-disabled {
  color: #cccccc;
}
.bui-tree-list .x-tree-icon,
.bui-tree-grid .x-tree-icon {
  display: inline-block;
  *display: inline;
  /* IE7 inline-block hack */
  *zoom: 1;
  vertical-align: top;
  height: 20px;
  width: 16px;
}
.bui-tree-list .x-tree-elbow-expander,
.bui-tree-grid .x-tree-elbow-expander,
.bui-tree-list .x-tree-elbow-dir,
.bui-tree-grid .x-tree-elbow-dir,
.bui-tree-list .x-tree-elbow-leaf,
.bui-tree-grid .x-tree-elbow-leaf,
.bui-tree-list .x-tree-elbow-line,
.bui-tree-grid .x-tree-elbow-line,
.bui-tree-list .x-tree-elbow,
.bui-tree-grid .x-tree-elbow,
.bui-tree-list .x-tree-elbow-expander-end,
.bui-tree-grid .x-tree-elbow-expander-end,
.bui-tree-list .x-tree-elbow-end,
.bui-tree-grid .x-tree-elbow-end {
  background: url("../../img/tree_icon-80-100.gif") no-repeat -999px -999px transparent;
}
.bui-tree-list .x-tree-icon-checkbox,
.bui-tree-grid .x-tree-icon-checkbox {
  background: url("../../img/check_icon-100-100.gif") no-repeat 0px 0px transparent;
}
.bui-tree-list .x-tree-icon-radio,
.bui-tree-grid .x-tree-icon-radio {
  background: url("../../img/radio_icon-64-40.gif") no-repeat 0px 0px transparent;
}
.bui-tree-list .x-tree-elbow-expander,
.bui-tree-grid .x-tree-elbow-expander {
  background-position: 0 0;
}
.bui-tree-list .x-tree-elbow-expander:hover,
.bui-tree-grid .x-tree-elbow-expander:hover {
  background-position: -32px 0;
}
.bui-tree-list .x-tree-elbow-dir,
.bui-tree-grid .x-tree-elbow-dir,
.bui-tree-list .x-tree-elbow-leaf,
.bui-tree-grid .x-tree-elbow-leaf {
  margin: 2px 3px 0 0;
}
.bui-tree-list .x-tree-elbow-dir,
.bui-tree-grid .x-tree-elbow-dir {
  background-position: 0 -80px;
}
.bui-tree-list .x-tree-elbow-leaf,
.bui-tree-grid .x-tree-elbow-leaf {
  background-position: -40px -80px;
}
.bui-tree-item-expanded .x-tree-elbow-expander,
.bui-grid-row-expanded .x-tree-elbow-expander {
  background-position: -16px 0;
}
.bui-tree-item-expanded .x-tree-elbow-expander:hover,
.bui-grid-row-expanded .x-tree-elbow-expander:hover {
  background-position: -48px 0;
}
.bui-tree-item-expanded .x-tree-elbow-dir,
.bui-grid-row-expanded .x-tree-elbow-dir {
  background-position: -20px -80px;
}
.bui-tree-item-checked {
  font-style: italic;
}
.x-tree-icon-checkbox:hover,
.x-tree-icon-radio:hover {
  background-position: -16px 0px;
}
.bui-tree-item-checked .x-tree-icon-checkbox,
.bui-grid-row-checked .x-tree-icon-checkbox,
.bui-tree-item-checked .x-tree-icon-radio,
.bui-grid-row-checked .x-tree-icon-radio {
  background-position: 0 -20px;
}
.bui-tree-item-checked .x-tree-icon-checkbox:hover,
.bui-grid-row-checked .x-tree-icon-checkbox:hover,
.bui-tree-item-checked .x-tree-icon-radio:hover,
.bui-grid-row-checked .x-tree-icon-radio:hover {
  background-position: -16px -20px;
}
.bui-tree-item-partial-checked .x-tree-icon-checkbox,
.bui-grid-row-partial-checked .x-tree-icon-checkbox,
.bui-tree-item-partial-checked .x-tree-icon-radio,
.bui-grid-row-partial-checked .x-tree-icon-radio {
  background-position: 0 -40px;
}
.bui-tree-item-partial-checked .x-tree-icon-checkbox:hover,
.bui-grid-row-partial-checked .x-tree-icon-checkbox:hover,
.bui-tree-item-partial-checked .x-tree-icon-radio:hover,
.bui-grid-row-partial-checked .x-tree-icon-radio:hover {
  background-position: -16px -40px;
}
.bui-tree-item-disabled .bui-grid-row-disabled .x-tree-icon-checkbox,
.bui-tree-item-disabled .bui-grid-row-disabled .x-tree-icon-radio {
  background-position: -48px 0;
}
.bui-tree-item-checked.bui-tree-item-disabled .x-tree-icon-checkbox,
.bui-grid-row-checked.bui-grid-row-disabled .x-tree-icon-checkbox,
.bui-tree-item-checked.bui-tree-item-disabled .x-tree-icon-radio,
.bui-grid-row-checked.bui-grid-row-disabled .x-tree-icon-radio {
  background-position: -48px -20px;
}
.x-tree-show-line .x-tree-elbow {
  background-position: 0 -20px;
}
.x-tree-show-line .x-tree-elbow-end {
  background-position: -20px -20px;
}
.x-tree-show-line .x-tree-elbow-line {
  background-position: -40px -20px;
}
.x-tree-show-line .x-tree-elbow-expander,
.x-tree-show-line .x-tree-elbow-expander:hover {
  background-position: -60px -40px;
}
.x-tree-show-line .x-tree-elbow-expander-end {
  background-position: -20px -40px;
}
.x-tree-show-line .bui-tree-item-expanded .x-tree-elbow-expander,
.x-tree-show-line .bui-grid-row-expanded .x-tree-elbow-expander {
  background-position: -40px -40px;
}
.x-tree-show-line .bui-tree-item-expanded .x-tree-elbow-expander-end,
.x-tree-show-line .bui-grid-row-expanded .x-tree-elbow-expander-end {
  background-position: 0px -40px;
}
.bui-tree-list .bui-tree-item-loading .x-tree-elbow-expander,
.bui-grid .bui-grid-row-loading .x-tree-elbow-expander {
  background: url("../../img/load-16-16.gif") no-repeat 0 0 transparent;
}
.bui-tree-grid .bui-grid-table .bui-grid-cell-text {
  display: inline-block;
  *display: inline;
  /* IE7 inline-block hack */
  *zoom: 1;
}
.x-align-arrow,
.x-align-arrow-inner {
  border: 6px solid transparent;
  _border-color: tomato;
  _filter: chroma(color=#ff6347);
  position: absolute;
}
.x-align-top .x-align-arrow,
.x-align-top-left .x-align-arrow,
.x-align-top-right .x-align-arrow {
  border-top: 8px solid #c3c3d6;
  bottom: -15px;
}
.x-align-top .x-align-arrow-inner,
.x-align-top-left .x-align-arrow-inner,
.x-align-top-right .x-align-arrow-inner {
  border-top: 8px solid #ffffff;
  top: -9px;
  left: -6px;
}
.x-align-top .x-align-arrow,
.x-align-bottom .x-align-arrow {
  left: 50%;
}
.x-align-top-left .x-align-arrow,
.x-align-bottom-left .x-align-arrow {
  left: 10px;
}
.x-align-top-right .x-align-arrow,
.x-align-bottom-right .x-align-arrow {
  right: 10px;
}
.x-align-right .x-align-arrow {
  border-right: 8px solid #c3c3d6;
  top: 50%;
  left: -15px;
}
.x-align-right .x-align-arrow-inner {
  border-right: 8px solid #ffffff;
  top: -6px;
  left: -4px;
}
.x-align-left .x-align-arrow {
  border-left: 8px solid #c3c3d6;
  top: 50%;
  right: -15px;
}
.x-align-left .x-align-arrow-inner {
  border-left: 8px solid #ffffff;
  top: -6px;
  left: -9px;
}
.x-align-bottom .x-align-arrow,
.x-align-bottom-left .x-align-arrow,
.x-align-bottom-right .x-align-arrow {
  border-bottom: 8px solid #c3c3d6;
  top: -15px;
}
.x-align-bottom .x-align-arrow-inner,
.x-align-bottom-left .x-align-arrow-inner,
.x-align-bottom-right .x-align-arrow-inner {
  border-bottom: 8px solid #ffffff;
  top: -5px;
  left: -6px;
}
.tips[class^="x-align-top"] .x-align-arrow,
.tips[class*=" x-align-top"] .x-align-arrow {
  border-top-color: #dddddd;
}
.tips[class^="x-align-top"] .x-align-arrow-inner,
.tips[class*=" x-align-top"] .x-align-arrow-inner {
  border-top-color: #f5f5f5;
}
.tips.x-align-right .x-align-arrow {
  border-right-color: #dddddd;
}
.tips.x-align-right .x-align-arrow-inner {
  border-right-color: #f5f5f5;
}
.tips.x-align-left .x-align-arrow {
  border-left-color: #dddddd;
}
.tips.x-align-left .x-align-arrow-inner {
  border-left-color: #f5f5f5;
}
.tips[class^="x-align-bottom"] .x-align-arrow,
.tips[class*=" x-align-bottom"] .x-align-arrow {
  border-bottom-color: #dddddd;
}
.tips[class^="x-align-bottom"] .x-align-arrow-inner,
.tips[class*=" x-align-bottom"] .x-align-arrow-inner {
  border-bottom-color: #f5f5f5;
}
.tips-success[class^="x-align-top"] .x-align-arrow,
.tips-success[class*=" x-align-top"] .x-align-arrow {
  border-top-color: #d6e9c6;
}
.tips-success[class^="x-align-top"] .x-align-arrow-inner,
.tips-success[class*=" x-align-top"] .x-align-arrow-inner {
  border-top-color: #dff0d8;
}
.tips-success.x-align-right .x-align-arrow {
  border-right-color: #d6e9c6;
}
.tips-success.x-align-right .x-align-arrow-inner {
  border-right-color: #dff0d8;
}
.tips-success.x-align-left .x-align-arrow {
  border-left-color: #d6e9c6;
}
.tips-success.x-align-left .x-align-arrow-inner {
  border-left-color: #dff0d8;
}
.tips-success[class^="x-align-bottom"] .x-align-arrow,
.tips-success[class*=" x-align-bottom"] .x-align-arrow {
  border-bottom-color: #d6e9c6;
}
.tips-success[class^="x-align-bottom"] .x-align-arrow-inner,
.tips-success[class*=" x-align-bottom"] .x-align-arrow-inner {
  border-bottom-color: #dff0d8;
}
.tips-warning[class^="x-align-top"] .x-align-arrow,
.tips-warning[class*=" x-align-top"] .x-align-arrow {
  border-top-color: #ebccd1;
}
.tips-warning[class^="x-align-top"] .x-align-arrow-inner,
.tips-warning[class*=" x-align-top"] .x-align-arrow-inner {
  border-top-color: #f2dede;
}
.tips-warning.x-align-right .x-align-arrow {
  border-right-color: #ebccd1;
}
.tips-warning.x-align-right .x-align-arrow-inner {
  border-right-color: #f2dede;
}
.tips-warning.x-align-left .x-align-arrow {
  border-left-color: #ebccd1;
}
.tips-warning.x-align-left .x-align-arrow-inner {
  border-left-color: #f2dede;
}
.tips-warning[class^="x-align-bottom"] .x-align-arrow,
.tips-warning[class*=" x-align-bottom"] .x-align-arrow {
  border-bottom-color: #ebccd1;
}
.tips-warning[class^="x-align-bottom"] .x-align-arrow-inner,
.tips-warning[class*=" x-align-bottom"] .x-align-arrow-inner {
  border-bottom-color: #f2dede;
}
.tips-info[class^="x-align-top"] .x-align-arrow,
.tips-info[class*=" x-align-top"] .x-align-arrow {
  border-top-color: #bce8f1;
}
.tips-info[class^="x-align-top"] .x-align-arrow-inner,
.tips-info[class*=" x-align-top"] .x-align-arrow-inner {
  border-top-color: #d9edf7;
}
.tips-info.x-align-right .x-align-arrow {
  border-right-color: #bce8f1;
}
.tips-info.x-align-right .x-align-arrow-inner {
  border-right-color: #d9edf7;
}
.tips-info.x-align-left .x-align-arrow {
  border-left-color: #bce8f1;
}
.tips-info.x-align-left .x-align-arrow-inner {
  border-left-color: #d9edf7;
}
.tips-info[class^="x-align-bottom"] .x-align-arrow,
.tips-info[class*=" x-align-bottom"] .x-align-arrow {
  border-bottom-color: #bce8f1;
}
.tips-info[class^="x-align-bottom"] .x-align-arrow-inner,
.tips-info[class*=" x-align-bottom"] .x-align-arrow-inner {
  border-bottom-color: #d9edf7;
}
.tips-notice[class^="x-align-top"] .x-align-arrow,
.tips-notice[class*=" x-align-top"] .x-align-arrow {
  border-top-color: #fbeed5;
}
.tips-notice[class^="x-align-top"] .x-align-arrow-inner,
.tips-notice[class*=" x-align-top"] .x-align-arrow-inner {
  border-top-color: #fcf8e3;
}
.tips-notice.x-align-right .x-align-arrow {
  border-right-color: #fbeed5;
}
.tips-notice.x-align-right .x-align-arrow-inner {
  border-right-color: #fcf8e3;
}
.tips-notice.x-align-left .x-align-arrow {
  border-left-color: #fbeed5;
}
.tips-notice.x-align-left .x-align-arrow-inner {
  border-left-color: #fcf8e3;
}
.tips-notice[class^="x-align-bottom"] .x-align-arrow,
.tips-notice[class*=" x-align-bottom"] .x-align-arrow {
  border-bottom-color: #fbeed5;
}
.tips-notice[class^="x-align-bottom"] .x-align-arrow-inner,
.tips-notice[class*=" x-align-bottom"] .x-align-arrow-inner {
  border-bottom-color: #fcf8e3;
}
/** 鍏叡鐨勪竴浜涘畾涔� **/
.bui-uploader .bui-uploader-button-wrap {
  display: inline-block;
  padding: 0 5px;
  height: 24px;
  overflow: hidden;
  line-height: 24px;
  position: relative;
  z-index: 500;
  margin-right: 10px;
  text-decoration: none;
  font-size: 12px;
  text-align: center;
  cursor: pointer;
}
.bui-uploader .bui-uploader-button-wrap .file-input-wrapper {
  display: block;
  width: 200px;
  height: 26px;
  position: absolute;
  left: 0;
  top: 0;
  overflow: hidden;
  z-index: 300;
}
.bui-uploader .bui-uploader-button-wrap .file-input-wrapper .file-input {
  background: none repeat scroll 0 0 transparent;
  border: medium none;
  cursor: pointer;
  height: 200px;
  width: 200px;
  top: -50px;
  opacity: 0;
  filter: alpha(opacity=0);
  position: absolute;
  font-size: 100px;
  left: 0;
}
.bui-uploader .bui-uploader-button-wrap .uploader-button-swf {
  display: block;
  width: 200px;
  height: 26px;
  position: absolute;
  left: 0;
  top: 0;
  overflow: hidden;
  z-index: 300;
}
.bui-uploader .bui-uploader-button-wrap .uploader-button-swf .file-input {
  background: none repeat scroll 0 0 transparent;
  border: medium none;
  cursor: pointer;
  height: 200px;
  width: 200px;
  top: -50px;
  opacity: 0;
  filter: alpha(opacity=0);
  position: absolute;
  font-size: 100px;
  left: 0;
}
/**榛樿瀹氫箟鐨勬牱寮�**/
.defaultTheme .bui-uploader-button-wrap {
  display: inline-block;
  padding: 0 5px;
  height: 24px;
  overflow: hidden;
  line-height: 24px;
  position: relative;
  z-index: 500;
  margin-right: 10px;
  text-decoration: none;
  font-size: 12px;
  text-align: center;
  border: 1px solid #C4DAED;
  /** 娓愬彉 **/
  background: -webkit-gradient(linear, left top, left bottom, from(#fdfefe), to(#dfe7ef));
  background: -moz-linear-gradient(top, #fdfefe, #dfe7ef);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#FDFEFE', endColorstr='#DFE7EF');
}
.defaultTheme .bui-uploader-button-wrap:link,
.defaultTheme .bui-uploader-button-wrap:visited {
  color: #404040;
}
.defaultTheme .bui-uploader-button-wrap:hover {
  text-decoration: none;
  color: black;
  background: -webkit-gradient(linear, left top, left bottom, from(#dfe7ef), to(#fdfefe));
  background: -moz-linear-gradient(top, #dfe7ef, #fdfefe);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#DFE7EF', endColorstr='#FDFEFE');
}
.defaultTheme .bui-uploader-button-wrap:active {
  background: #DFE7EF;
  filter: none;
}
.defaultTheme .bui-uploader-htmlButton-disabled .bui-uploader-button-wrap {
  border: 1px solid #bfbfbf;
  color: #404040;
  cursor: default;
  background: -webkit-gradient(linear, left top, left bottom, from(#fafafa), to(#e5e5e5));
  background: -moz-linear-gradient(top, #fafafa, #e5e5e5);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#FAFAFA', endColorstr='#E5E5E5');
}
.defaultTheme .bui-queue-item {
  position: relative;
}
.defaultTheme .bui-queue-item .default,
.defaultTheme .bui-queue-item .success,
.defaultTheme .bui-queue-item .progress,
.defaultTheme .bui-queue-item .error {
  margin-right: 40px;
}
.defaultTheme .bui-queue-item .error .uploader-error {
  color: #fd7316;
}
.defaultTheme .bui-queue-item .action {
  display: block;
  position: absolute;
  padding-left: 10px;
  width: 30px;
  top: 0px;
  right: 0px;
}
.defaultTheme .bui-queue-item .action .bui-queue-item-del {
  cursor: pointer;
}
.defaultTheme .bui-queue-item .action .bui-queue-item-del:hover {
  color: #fd7316;
}
/**甯﹀浘鐗囬瑙堢殑涓婚鏍峰紡**/
.imageViewTheme .bui-uploader-button-wrap {
  color: #333;
  border: 1px solid #ddd;
  background-color: #fdfcfc;
  background-image: -webkit-linear-gradient(left, color-stop(#fdfcfc 0%), color-stop(#eeeeee 100%));
  background-image: -o-linear-gradient(left, #fdfcfc 0%, #eeeeee 100%);
  background-image: linear-gradient(to right, #fdfcfc 0%, #eeeeee 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#fffdfcfc', endColorstr='#ffeeeeee', GradientType=1);
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
}
.imageViewTheme .bui-uploader-button-wrap:hover {
  border-color: #369bd7;
  background-color: #fdfcfc;
  background-image: -webkit-linear-gradient(left, color-stop(#fdfcfc 0%), color-stop(#e2f1fc 100%));
  background-image: -o-linear-gradient(left, #fdfcfc 0%, #e2f1fc 100%);
  background-image: linear-gradient(to right, #fdfcfc 0%, #e2f1fc 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#fffdfcfc', endColorstr='#ffe2f1fc', GradientType=1);
}
.imageViewTheme .bui-uploader-htmlButton-disabled .bui-uploader-button-wrap {
  border-color: #eee;
  color: #404040;
  cursor: default;
  background-color: #eee;
}
.imageViewTheme .bui-uploader-htmlButton-disabled .bui-uploader-button-wrap:hover {
  border-color: #eee;
  background-color: #eee;
  background-image: -webkit-linear-gradient(left, color-stop(#fdfcfc 0%), color-stop(#eeeeee 100%));
  background-image: -o-linear-gradient(left, #fdfcfc 0%, #eeeeee 100%);
  background-image: linear-gradient(to right, #fdfcfc 0%, #eeeeee 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#fffdfcfc', endColorstr='#ffeeeeee', GradientType=1);
}
.imageViewTheme .bui-queue ul {
  *zoom: 1;
}
.imageViewTheme .bui-queue ul:before,
.imageViewTheme .bui-queue ul:after {
  content: " ";
  display: table;
}
.imageViewTheme .bui-queue ul:after {
  clear: both;
}
.imageViewTheme .bui-queue ul:before,
.imageViewTheme .bui-queue ul:after {
  content: " ";
  display: table;
}
.imageViewTheme .bui-queue ul:after {
  clear: both;
}
.imageViewTheme .bui-queue .bui-queue-item {
  display: inline;
  float: left;
  margin: 0 10px 10px 0;
  padding: 0;
  width: 120px;
  height: 120px;
  overflow: hidden;
  position: relative;
  border: solid 1px #e0e0e0;
  background-color: #fff;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
  -webkit-box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}
.imageViewTheme .bui-queue .bui-queue-item .error,
.imageViewTheme .bui-queue .bui-queue-item .progress {
  margin-top: -10px;
  width: 120px;
  position: absolute;
  top: 50%;
  left: 0;
  z-index: 30;
  color: #f00;
  text-align: center;
}
.imageViewTheme .bui-queue .bui-queue-item .action .bui-queue-item-del {
  display: none;
  color: #676767;
  width: 60px;
  height: 24px;
  line-height: 24px;
  position: absolute;
  top: 50px;
  left: 50px;
  margin-left: -20px;
  text-decoration: none;
  z-index: 3000;
  border: 1px solid #ddd;
  background-color: #fff;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
  text-align: center;
  cursor: pointer;
}
.imageViewTheme .bui-queue .bui-queue-item:hover .action .bui-queue-item-del {
  display: block;
}
.imageViewTheme .bui-queue .bui-queue-item-error {
  border-color: #f00;
}
.bui-slider {
  border: 1px solid #c3c3d6;
  position: relative;
  overflow: visible;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
}
.bui-slider .x-slider-back {
  position: absolute;
  background-color: #eeeeee;
}
.x-slider-handle {
  z-index: 1;
  cursor: pointer;
  display: inline-block;
  *display: inline;
  /* IE7 inline-block hack */
  *zoom: 1;
  position: absolute;
  background-image: -webkit-linear-gradient(top, #ffffff 0%, #e4e4e4 100%);
  background-image: -o-linear-gradient(top, #ffffff 0%, #e4e4e4 100%);
  background-image: linear-gradient(to bottom, #ffffff 0%, #e4e4e4 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffffffff', endColorstr='#ffe4e4e4', GradientType=0);
  border: 1px solid #d3d3d3;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
  height: 15px;
  width: 15px;
}
.x-slider-handle:hover {
  border: 1px solid #c3c3d6;
  background-color: #e6e6e6;
  background-position: 0 -5px;
  -webkit-transition: background-position 0.1s linear;
  -o-transition: background-position 0.1s linear;
  transition: background-position 0.1s linear;
}
.x-slider-horizontal {
  height: 10px;
}
.x-slider-horizontal .x-slider-handle {
  top: -3px;
  margin-left: -9px;
}
.x-slider-horizontal .x-slider-back {
  height: 100%;
}
.x-slider-vertical {
  width: 10px;
}
.x-slider-vertical .x-slider-handle {
  left: -3px;
  margin-left: 0;
  margin-bottom: -10px;
}
.x-slider-vertical .x-slider-back {
  width: 100%;
}
.clearfix {
  *zoom: 1;
}
.clearfix:before,
.clearfix:after {
  content: " ";
  display: table;
}
.clearfix:after {
  clear: both;
}
.pull-right {
  float: right;
}
.pull-left {
  float: left;
}
.pull-none {
  float: none;
}
.hide {
  display: none;
}
.show {
  display: block;
}
.invisible {
  visibility: hidden;
}
.bordered {
  border: 1px solid #c3c3d6;
}
.bordered-radius {
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
}
.centered {
  text-align: center;
}
.pull-right {
  float: right;
}
.pull-left {
  float: left;
}
.pull-none {
  float: none;
}
.hide {
  display: none;
}
.show {
  display: block;
}
.invisible {
  visibility: hidden;
}
/* inline block */
.bui-inline-block {
  display: inline-block;
  *display: inline;
  *zoom: 1;
}
.bui-overlay {
  position: absolute;
  left: -9999px;
  top: -9999px;
}
.x-relative {
  position: relative;
}
.x-absolute {
  position: absolute;
}
.bui-clear {
  *zoom: 1;
}
.bui-clear:before,
.bui-clear:after {
  content: " ";
  display: table;
}
.bui-clear:after {
  clear: both;
}
.bui-clear:before,
.bui-clear:after {
  content: " ";
  display: table;
}
.bui-clear:after {
  clear: both;
}
.bui-hidden {
  display: none;
}