<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.chevron.pms.dao.UserQrcodeMapper" >
  <resultMap id="BaseResultMap" type="com.chevron.pms.model.UserQrcode" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="qr_code" property="qrCode" jdbcType="VARCHAR" />
    <result column="sku" property="sku" jdbcType="VARCHAR" />
    <result column="user_wechat_openid" property="userWechatOpenid" jdbcType="VARCHAR" />
    <result column="is_effective" property="isEffective" jdbcType="VARCHAR" />
    <result column="creation_time" property="creationTime" jdbcType="TIMESTAMP" />
	<result column="scan_count" property="scanCount" jdbcType="INTEGER"/>
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, qr_code, sku, user_wechat_openid, is_effective, creation_time
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.chevron.pms.model.UserQrcodeExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'false' as QUERYID,
    <include refid="Base_Column_List" />
    from wx_t_user_qrcode
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from wx_t_user_qrcode
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from wx_t_user_qrcode
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.chevron.pms.model.UserQrcode" >
    insert into wx_t_user_qrcode (id, qr_code, sku, 
      user_wechat_openid, is_effective, creation_time
      )
    values (#{id,jdbcType=BIGINT}, #{qrCode,jdbcType=VARCHAR}, #{sku,jdbcType=VARCHAR}, 
      #{userWechatOpenid,jdbcType=VARCHAR}, #{isEffective,jdbcType=VARCHAR}, #{creationTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" parameterType="com.chevron.pms.model.UserQrcode" >
    insert into wx_t_user_qrcode
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="qrCode != null" >
        qr_code,
      </if>
      <if test="sku != null" >
        sku,
      </if>
      <if test="userWechatOpenid != null" >
        user_wechat_openid,
      </if>
      <if test="isEffective != null" >
        is_effective,
      </if>
      <if test="creationTime != null" >
        creation_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="qrCode != null" >
        #{qrCode,jdbcType=VARCHAR},
      </if>
      <if test="sku != null" >
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="userWechatOpenid != null" >
        #{userWechatOpenid,jdbcType=VARCHAR},
      </if>
      <if test="isEffective != null" >
        #{isEffective,jdbcType=VARCHAR},
      </if>
      <if test="creationTime != null" >
        #{creationTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.chevron.pms.model.UserQrcode" >
    update wx_t_user_qrcode
    <set >
      <if test="qrCode != null" >
        qr_code = #{qrCode,jdbcType=VARCHAR},
      </if>
      <if test="sku != null" >
        sku = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="userWechatOpenid != null" >
        user_wechat_openid = #{userWechatOpenid,jdbcType=VARCHAR},
      </if>
      <if test="isEffective != null" >
        is_effective = #{isEffective,jdbcType=VARCHAR},
      </if>
      <if test="creationTime != null" >
        creation_time = #{creationTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chevron.pms.model.UserQrcode" >
    update wx_t_user_qrcode
    set qr_code = #{qrCode,jdbcType=VARCHAR},
      sku = #{sku,jdbcType=VARCHAR},
      user_wechat_openid = #{userWechatOpenid,jdbcType=VARCHAR},
      is_effective = #{isEffective,jdbcType=VARCHAR},
      creation_time = #{creationTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectQrWith1stScanByOpenid" parameterType="map" resultMap="BaseResultMap" >
  	select * from (
	  SELECT
	    uq.*,
	    row_number() OVER (PARTITION BY uq.qr_code ORDER BY uq.creation_time ASC) AS sequence
	  FROM wx_t_user_qrcode uq
	  WHERE uq.user_wechat_openid = #{userOpenId}
	    AND uq.creation_time >= #{date}
	) t where t.sequence = 1 
	    order by t.creation_time asc
  
  </select>
  
  <select id="selectUserQrcodeByQrcode" resultMap="BaseResultMap" parameterType="java.lang.String">
  	select top 1 * from wx_t_user_qrcode where qr_code=#{qrCode} ORDER BY creation_time ASC
  </select>
  
  <select id="selectUserScan" resultMap="BaseResultMap" parameterType="java.lang.String">
  	select user_wechat_openid, count(1) scan_count from wx_t_user_qrcode 
  	where qr_code=#{qrCode} and user_wechat_openid is not null 
  	group by user_wechat_openid
  	order by min(creation_time) asc
  </select>
</mapper>