update [chemid].[CHE_MID].dbo.syn_master_vendor set is_enable=0, sold_to_party_code=-8389652, syn_update_time='2017-10-18 18:09:53.797' where id=83;
update [chemid].[CHE_MID].dbo.syn_master_vendor set sold_to_party='Shanghai Chengche Network', syn_update_time='2017-10-18 18:09:53.797' where id=73;

update smd set smd.material_type=(select m.MATERIAL_TYPE from wx_t_material m join wx_t_material_sku ms on ms.MATERIAL_ID=m.ID 
	where ms.MATERIAL_SKU_CODE=smd.material_sku) smd.syn_update_time='2017-10-18 18:09:53.797' from [chemid].[CHE_MID].dbo.syn_data_material_approval_det smd;
	
begin 
	set xact_abort on
	declare @start_time datetime /*同步开始时间*/
	declare @end_time datetime/*同步截止时间*/
	declare @syn_time datetime/*同步时间*/
	set @start_time='2017-09-21 02:49:59.080'
	set @end_time='2017-09-21 23:43:15.860'
	set @syn_time='2017-09-27 21:39:22.033'
	insert into [chemid].[CHE_MID].dbo.syn_data_dictionary (dict_type,dict_value,dict_text,order_no,is_enable,ext_property1,ext_property2,syn_create_time)
	select 'material.type', di.dic_item_code, di.dic_item_name, di.id, di.status, di.dic_item_desc,di.id,@end_time
 	from wx_t_dic_item di where di.dic_type_code in ('material.type')
 end