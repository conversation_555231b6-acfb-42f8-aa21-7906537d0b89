package com.sys.workflow.model;

import java.util.Date;
import java.util.List;

/**
 * 工作流-wx_t_workflow
 * <AUTHOR>
 * @version 1.0 2019-12-30 11:10
 */
public class Workflow {

	/**  流程编码  **/
    private String workflowCode;

	/**  流程名称  **/
    private String workflowName;

	/**  流程处理Bean  **/
    private String handleBean;

	/**  流程风格  **/
    private String pageThemes;

	/**  流程版本(兼容不同版本流程。格式yyyyMMdd)  **/
    private Integer workflowVersion;

	/**  流程结束提醒(提醒申请人。位标记。1-邮件提醒，2-短信提醒)  **/
    private Integer endNotify;

	/**  流程结束邮件模板  **/
    private String endNotifyTemplate;

	/**  流程结束邮件抄送  **/
    private String endEmailCc;

	/**  默认待办邮件模板  **/
    private String defTodoEmailTemp;

	/**  默认驳回邮件模板  **/
    private String defRejectNotifyTemplate;

	/**  短信提醒模板  **/
    private String smsNotifyTemplate;

	/**  默认短信提醒模板  **/
    private String defSmsNotifyTemp;

	/**  流程状态(0-新建，10-已发布。已发布流程的历史流程节点不能删除)  **/
    private Integer flowStatus;

	/**  属性查看权限1(1-63位属性查看权限)  **/
    private Long propertyViewWeight1;

	/**  属性编辑权限1(1-63位属性编辑权限)  **/
    private Long propertyEditWeight1;

	/**  属性必填权限1(1-63位属性必填权限)  **/
    private Long propertyRequiredWeight1;

	/**  属性查看权限2(64-126位属性查看权限)  **/
    private Long propertyViewWeight2;

	/**  属性编辑权限2(64-126位属性编辑权限)  **/
    private Long propertyEditWeight2;

	/**  属性必填权限2(64-126位属性必填权限)  **/
    private Long propertyRequiredWeight2;

	/**  属性查看权限3(127-189位属性查看权限)  **/
    private Long propertyViewWeight3;

	/**  属性编辑权限3(127-189位属性编辑权限)  **/
    private Long propertyEditWeight3;

	/**  属性必填权限3(127-189位属性必填权限)  **/
    private Long propertyRequiredWeight3;

	/**  删除标记(1-删除，0-未删除)  **/
    private Integer deleteFlag;

	/**  创建用户ID  **/
    private Long createUserId;

	/**  创建时间  **/
    private Date createTime;

	/**  最后修改用户ID  **/
    private Long updateUserId;

	/**  最后修改时间  **/
    private Date updateTime;
    
    /** 工作流业务权限 **/
    private List<WorkflowBizPermission> workFlowBizPermissions;

    public String getWorkflowCode() {
        return workflowCode;
    }

    public void setWorkflowCode(String workflowCode) {
        this.workflowCode = workflowCode;
    }

    public String getWorkflowName() {
        return workflowName;
    }

    public void setWorkflowName(String workflowName) {
        this.workflowName = workflowName;
    }

    public String getHandleBean() {
        return handleBean;
    }

    public void setHandleBean(String handleBean) {
        this.handleBean = handleBean;
    }

    public String getPageThemes() {
        return pageThemes;
    }

    public void setPageThemes(String pageThemes) {
        this.pageThemes = pageThemes;
    }

    public Integer getWorkflowVersion() {
        return workflowVersion;
    }

    public void setWorkflowVersion(Integer workflowVersion) {
        this.workflowVersion = workflowVersion;
    }

    public Integer getEndNotify() {
        return endNotify;
    }

    public void setEndNotify(Integer endNotify) {
        this.endNotify = endNotify;
    }

    public String getEndNotifyTemplate() {
        return endNotifyTemplate;
    }

    public void setEndNotifyTemplate(String endNotifyTemplate) {
        this.endNotifyTemplate = endNotifyTemplate;
    }

    public String getEndEmailCc() {
        return endEmailCc;
    }

    public void setEndEmailCc(String endEmailCc) {
        this.endEmailCc = endEmailCc;
    }

    public String getDefTodoEmailTemp() {
        return defTodoEmailTemp;
    }

    public void setDefTodoEmailTemp(String defTodoEmailTemp) {
        this.defTodoEmailTemp = defTodoEmailTemp;
    }

    public String getDefRejectNotifyTemplate() {
        return defRejectNotifyTemplate;
    }

    public void setDefRejectNotifyTemplate(String defRejectNotifyTemplate) {
        this.defRejectNotifyTemplate = defRejectNotifyTemplate;
    }

    public String getSmsNotifyTemplate() {
        return smsNotifyTemplate;
    }

    public void setSmsNotifyTemplate(String smsNotifyTemplate) {
        this.smsNotifyTemplate = smsNotifyTemplate;
    }

    public String getDefSmsNotifyTemp() {
        return defSmsNotifyTemp;
    }

    public void setDefSmsNotifyTemp(String defSmsNotifyTemp) {
        this.defSmsNotifyTemp = defSmsNotifyTemp;
    }

    public Integer getFlowStatus() {
        return flowStatus;
    }

    public void setFlowStatus(Integer flowStatus) {
        this.flowStatus = flowStatus;
    }

    public Integer getDeleteFlag() {
        return deleteFlag;
    }

    public void setDeleteFlag(Integer deleteFlag) {
        this.deleteFlag = deleteFlag;
    }

    public Long getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(Long createUserId) {
        this.createUserId = createUserId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getUpdateUserId() {
        return updateUserId;
    }

    public void setUpdateUserId(Long updateUserId) {
        this.updateUserId = updateUserId;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

	public List<WorkflowBizPermission> getWorkFlowBizPermissionList() {
		return workFlowBizPermissions;
	}

	public void setWorkFlowBizPermissionList(List<WorkflowBizPermission> workFlowBizPermissions) {
		this.workFlowBizPermissions = workFlowBizPermissions;
	}

	public Long getPropertyViewWeight1() {
		return propertyViewWeight1;
	}

	public void setPropertyViewWeight1(Long propertyViewWeight1) {
		this.propertyViewWeight1 = propertyViewWeight1;
	}

	public Long getPropertyEditWeight1() {
		return propertyEditWeight1;
	}

	public void setPropertyEditWeight1(Long propertyEditWeight1) {
		this.propertyEditWeight1 = propertyEditWeight1;
	}

	public Long getPropertyRequiredWeight1() {
		return propertyRequiredWeight1;
	}

	public void setPropertyRequiredWeight1(Long propertyRequiredWeight1) {
		this.propertyRequiredWeight1 = propertyRequiredWeight1;
	}

	public Long getPropertyViewWeight2() {
		return propertyViewWeight2;
	}

	public void setPropertyViewWeight2(Long propertyViewWeight2) {
		this.propertyViewWeight2 = propertyViewWeight2;
	}

	public Long getPropertyEditWeight2() {
		return propertyEditWeight2;
	}

	public void setPropertyEditWeight2(Long propertyEditWeight2) {
		this.propertyEditWeight2 = propertyEditWeight2;
	}

	public Long getPropertyRequiredWeight2() {
		return propertyRequiredWeight2;
	}

	public void setPropertyRequiredWeight2(Long propertyRequiredWeight2) {
		this.propertyRequiredWeight2 = propertyRequiredWeight2;
	}

	public Long getPropertyViewWeight3() {
		return propertyViewWeight3;
	}

	public void setPropertyViewWeight3(Long propertyViewWeight3) {
		this.propertyViewWeight3 = propertyViewWeight3;
	}

	public Long getPropertyEditWeight3() {
		return propertyEditWeight3;
	}

	public void setPropertyEditWeight3(Long propertyEditWeight3) {
		this.propertyEditWeight3 = propertyEditWeight3;
	}

	public Long getPropertyRequiredWeight3() {
		return propertyRequiredWeight3;
	}

	public void setPropertyRequiredWeight3(Long propertyRequiredWeight3) {
		this.propertyRequiredWeight3 = propertyRequiredWeight3;
	}
    
}
