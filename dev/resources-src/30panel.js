;(function($){
	UI.Panel = UI.extend(UI.DomBase, {
		key: 'ui.panel',
		configEvents: ['resize'],
		HEADER_CLS: 'ui-panel-header',
		CONTENT_CLS: 'ui-panel-content',
		FOOTER_CLS: 'ui-panel-footer',
		/**
		 * 初始化控件
		 * @param dom [必填]操作dom对象
		 * @param opts [可选]控件初始设置
		 * @return {object} 控件实例对象
		 */
		init: function(dom, opts){
			var $d = $(dom), p = this.newInstance($d, opts);
			if(p.content && p.content.length == 0){
				p.content = null;
			}
			if(p.header && p.header.length == 0){
				p.header = null;
			}
			if(p.footer && p.footer.length == 0){
				p.footer = null;
			}
			if(p.content == null){
				if(p.header != null || p.footer != null){
					throw 'UI.Panel异常。未设置面板conent属性';
				}else{
					p.content = $d;
				}
			}
			if(p.scrollType != 'none'){
				//面板可滚动
				p.scroll = UI.Scrollable.init(this.center[0], {
					scrollType: p.scrollType
				});
				//可滚动面板均要监听其内部元素尺寸变动事件
				p.listenResize = true;
			}
			if(p.fit){
				//添加panel的尺寸自适应功能
				UI.addFitAbility(p, p.scroll ? p.scroll.wrapper : $d, function(width, height){
					p.resize(width, height);
				});
			}
			return p;
		},
		createInitInstance: function($d){
			return $.extend({}, this.defaults, {
				header: $d.find('>.' + this.HEADER_CLS),
				content: $d.find('>.' + this.CONTENT_CLS),
				footer: $d.find('>.' + this.FOOTER_CLS)
			});
		},
		//调整滚动状态
		_fixScroll: function(props){
			if(props.scroll && !props._fixScrollClose){
				props.scroll.trigger('resize');
			}
		},
		base: {
			afterEvents: {
				resize: function(){
					//调整滚动
					UI.Panel._fixScroll(this);
					UI.triggerFitSize(this.element);
				}
			},
			/**
			 * 折叠panel内容区
			 */
			collapse: function(){
				this.content.hide();
				this.element.addClass(this.collapseCls);
				this.trigger('resize');
			},
			/**
			 * 展开panel内容区
			 */
			expand: function(){
				this.content.show();
				this.element.removeClass(this.collapseCls);
				this.trigger('resize');
			},
			/**
			 * 设置控件尺寸
			 * @param width [必填]控件新宽度。undefined表示控件宽度不变
			 * @param height [可选]控件新高度。不填表示高度不变
			 */
			resize: function(width, height){
				if(this._sizeInited && (this.width == width || width === undefined) 
						&& (this.height == height || height === undefined)){
					return;
				}
				this._fixScrollClose = true;
				//计算实际设置宽度
				if(width !== undefined && (!this._sizeInited || width != this.width)){
					this.setWidth(width);
					this.width = width;
				}
				//计算实际设置高度
				if(height !== undefined && (!this._sizeInited || height != this.height)){
					if(height > 0){
						//去除头部高度
						var _h = height;
						if(this.header){
							_h -= UI.getOuterHeight(this.header);
						}
						//去除面板尾高度
						if(this.footer){
							_h -= UI.getOuterHeight(this.footer);
						}
					}
					this.setHeight(height);
					this.height = height;
				}
				this.trigger('resize');
				this._fixScrollClose = false;
				this._sizeInited = true;
			},
			/**
			 * 获取元素高度
			 * @return {number} 返回元素高度
			 */
			getHeight: function(){
				if(this.scroll){
					return UI.getOuterHeight(this.scroll.wrapper);
				}else{
					return UI.getOuterHeight(this.element);
				}
			},
			/**
			 * 获取元素宽度
			 * @return {number} 返回元素宽度
			 */
			getWidth: function(){
				if(this.scroll){
					return UI.getOuterWidth(this.scroll.wrapper);
				}else{
					return UI.getOuterWidth(this.element);
				}
			},
			/**
			 * 设置面板宽度
			 * @param width 面板新宽度
			 */
			setWidth: function(width){
				if(this.scroll){
					return UI.setWidth(this.scroll.wrapper, width);
				}else{
					return UI.setWidth(this.element, width);
				}
			},
			/**
			 * 设置面板高度
			 * @param width 面板新高度
			 */
			setHeight: function(height){
				if(this.scroll){
					return UI.setHeight(this.scroll.wrapper, height);
				}else{
					return UI.setHeight(this.element, height);
				}
			}
		},
		defaults: {
			collapseCls: 'ui-panel-collapse',
			fit: false,
			height: -1, //-1表示面板高度自适应
			width: 0,
			minContentWidth: 0, 
			scrollType: 'none', //none, both, x, y
			header: null,
			content: null,
			footer: null
		}
	});
})(jQuery);