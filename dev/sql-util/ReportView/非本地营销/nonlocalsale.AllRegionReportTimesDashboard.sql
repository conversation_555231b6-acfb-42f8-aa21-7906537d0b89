select region,
CASE WHEN report_type = 1 THEN 'app_scan_times'
		 WHEN report_type = 2 THEN 'user_scan_times'
		 WHEN report_type = 3 THEN 'admin_record_time'
		 WHEN report_type = 4 THEN 'mechanic_scan_times'
		 ELSE 'Others'
		 END reporting_source_type,
		 sum(report_times) total_report_times
		 from(
select rv.region,rv.report_type,1 report_times
from view_nonlocal_sale_report rv
left join dw_customer_org_sales cs1 on cs1.distributor_id = rv.from_distributor_id and cs1.channel_weight&rv.channel_weight>0
left join wx_t_region r1 on r1.id = rv.reporting_region_id
left join wx_t_region r2 on r2.id = r1.parent_id
where (#VARCHAR,i_region# is null or #VARCHAR,i_region#='' or rv.region=#VARCHAR,i_region#)
and (#INTEGER,i_permission_weight#&3>0 or (#INTEGER,i_permission_weight#&1024>0 and #BIGINT,i_distributor_id# is not null) or (#INTEGER,i_permission_weight#&10>0 and exists (select 1 from dw_sales_role dp_001_sr1 where dp_001_sr1.[sales_cai]=cs1.sales_cai and dp_001_sr1.sales_cai_level like '%_'+#VARCHAR,i_sals_cai#+'%')))
and (#VARCHAR,i_date_from# is null or #VARCHAR,i_date_from#='' or rv.req_time>=#VARCHAR,i_date_from#)
and (#VARCHAR,i_date_to# is null or #VARCHAR,i_date_to#='' or rv.req_time<dateadd(day, 1, #VARCHAR,i_date_to#))
and (#INTEGER,i_channel# is null or rv.channel&#INTEGER,i_channel#>0)
and (#VARCHAR,i_T3_category# is null or #VARCHAR,i_T3_category#='' or rv.[t3_category] =#VARCHAR,i_T3_category#)
and (#BIGINT,i_distributor_id# is null or rv.from_distributor_id=#BIGINT,i_distributor_id#)
group by rv.region,rv.from_distributor_id,rv.to_distributor_id,rv.channel,convert(nvarchar(20),rv.req_time,112),r1.id,r2.id,rv.report_type 
) tt group by region,
CASE WHEN report_type = 1 THEN 'app_scan_times'
		 WHEN report_type = 2 THEN 'user_scan_times'
		 WHEN report_type = 3 THEN 'admin_record_time'
		 WHEN report_type = 4 THEN 'mechanic_scan_times'
		 ELSE 'Others'
		 END