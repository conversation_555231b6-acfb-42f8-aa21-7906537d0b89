package com.sys.auth.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.shiro.SecurityUtils;
import org.apache.shiro.subject.Subject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.common.constants.Constants;
import com.common.util.ContextUtil;
import com.common.util.EmptyChecker;
import com.common.util.MessageResourceUtil;
import com.common.util.StringUtils;
import com.common.util.json.JsonGenerator;
import com.sys.auth.business.RoleBizService;
import com.sys.auth.dao.WxTDataMapper;
import com.sys.auth.dao.WxTMenuMapper;
import com.sys.auth.dao.WxTOrgMapper;
import com.sys.auth.dao.WxTPlogMapper;
import com.sys.auth.dao.WxTRoleMapper;
import com.sys.auth.dao.WxTRolesourceMapper;
import com.sys.auth.dao.WxTUserbaseMapper;
import com.sys.auth.dao.WxTUserroleMapper;
import com.sys.auth.model.LigerUITreeNode;
import com.sys.auth.model.TreeNode;
import com.sys.auth.model.WxTData;
import com.sys.auth.model.WxTMenu;
import com.sys.auth.model.WxTOrg;
import com.sys.auth.model.WxTPlog;
import com.sys.auth.model.WxTRole;
import com.sys.auth.model.WxTRoleExample;
import com.sys.auth.model.WxTRolesource;
import com.sys.auth.model.WxTUser;
import com.sys.auth.model.WxTUserbase;
import com.sys.auth.model.WxTUserrole;
import com.sys.auth.model.WxTRoleExample.Criteria;
import com.sys.auth.service.UserServiceI;
import com.sys.auth.service.WxRoleServiceI;
import com.sys.auth.service.WxUserRoleService;

@SuppressWarnings("unchecked")
@Service
public class WxRoleServiceImpl implements WxRoleServiceI {

	private static Logger log = LoggerFactory.getLogger(WxRoleServiceImpl.class);
	@Resource
	WxTRoleMapper wxTRoleMapper;

	@Resource
	WxTUserroleMapper wxTUserroleMapper;


	@Resource
	WxUserRoleService wxUserRoleServiceImpl;

	@Resource
	WxTMenuMapper wxTMenuMapper;

	@Resource
	WxTOrgMapper wxTOrgMapper;

	@Resource
	WxTRolesourceMapper wxTRolesourceMapper;



	@Resource
	WxTUserbaseMapper wxTUserbaseMapper;

	@Resource
	WxTPlogMapper wxTPlogMapper;

	@Resource
	WxTDataMapper wxTDataMapper;

	@Resource
	UserServiceI userService;

	@Resource
	private RoleBizService roleBizService;

	@Override
	/**
	 * 创建新角色
	 */
	public Map creatWxRole(String chRoleName, String roleType,Integer salesLevel,String passwordPower,String roleDesc, String salesChannel, String blockInformer) throws Exception {
		Map map = new HashMap();
		try {
			Subject subject = SecurityUtils.getSubject();
			WxTUser cru = (WxTUser) subject.getPrincipal();
			WxTRole role = new WxTRole();
			role.setSalesLevel(salesLevel);
			role.setChRoleName(chRoleName);
			role.setRoleType(Integer.parseInt(roleType));
			role.setPasswordPower(Integer.parseInt(passwordPower));
			role.setRoleDescript(roleDesc);
			role.setTenantId(cru.getOrgId());//modify by bo.liu 0818 cru.getTenantId()
			// 查询当前角色类型下是否有同名角色信息
			WxTRole findRoleByName = wxTRoleMapper.selectByRoleName(role);
			if (null == findRoleByName) {
				role.setPresetBj(0);// 用户只允许创建非预置角色
				role.setXgUser(cru.getUserId());
				role.setTenantId(cru.getOrgId());//modify by bo.liu 0818 cru.getTenantId()
				role.setStatus(1);
				role.setXgSj(new Date());
				role.setSalesChannel(salesChannel);
				if(!EmptyChecker.isEmpty(blockInformer)) {
					role.setBlockInformer(blockInformer);
				}
				wxTRoleMapper.insertSelective(role);
				// 创建新角色后,将角色赋予创建者 以及 拥有最高权限的管理员账号
				// 检查角色是否创建成功,如果创建完成,取得本次创建角色的角色ID,用于赋权
				findRoleByName = wxTRoleMapper.selectByRoleName(role);
				if (null != findRoleByName) {
					roleToUser(cru.getUserId(), findRoleByName.getRoleId(),
							cru.getTenantId());
				}
				map.put("code", "success");
			} else {
				map.put("code", "roleIsExist");
			}
		} catch (Exception e) {
			map.put("code", "systemerror");
		}
		return map;
	}

	/**
	 * 将新建角色赋予创建者 和 超级管理员
	 */
	public void roleToUser(Long creatUserId, Long roleId, Long tenantId)
			throws Exception {
		WxTUserrole wxtur = new WxTUserrole();
		wxtur.setUserId(creatUserId);
		wxtur.setRoleId(roleId);
		wxtur.setStatus(1);
		wxtur.setTenantId(tenantId);
		wxtur.setGrantUserid(creatUserId);
		wxtur.setXgSj(new Date());
		wxtur.setXgUser(creatUserId);
		wxTUserroleMapper.insertSelective(wxtur);

		wxtur.setRoleId(new Long(1001));
		List<WxTUserrole> list = wxTUserroleMapper.selectByRoleId(wxtur);
		for (int i = 0; i < list.size(); i++) {
			// 如果创建者 本身就是超级管理员,不需要重复添加
			if (!creatUserId.equals(list.get(i).getUserId())) {
				wxtur.setUserId(list.get(i).getUserId());
				wxtur.setRoleId(roleId);
				wxtur.setStatus(1);
				wxtur.setTenantId(tenantId);
				wxtur.setGrantUserid(creatUserId);
				wxtur.setXgSj(new Date());
				wxtur.setXgUser(creatUserId);
				wxTUserroleMapper.insertSelective(wxtur);
			}
		}
	}

	/**
	 * 在用户自注册时，新建该公司的超级管理员角色
	 */
	public Long createCompanyAdmin(String companyName, Long tenantId){
		WxTRole role = new WxTRole();
		role.setChRoleName(companyName + "公司管理员");
		role.setTenantId(tenantId);
		role.setXgSj(new Date());
		role.setXgUser(new Long(1));
		role.setRoleType(3);
		role.setPresetBj(2);
		role.setStatus(1);
		wxTRoleMapper.insertSelective(role);
		WxTRolesource rs = new WxTRolesource();
		rs.setAddFlag("1");
		rs.setRoleId(role.getRoleId());
		rs.setRsType(2);
		List<WxTMenu> menuList = wxTMenuMapper.selAllMenu();
		for(WxTMenu menu : menuList){
			rs.setSourceId(menu.getMenuId());
			wxTRolesourceMapper.insertSelective(rs);
		}
		return role.getRoleId();
	}

	/**
	 * 删除角色. 逻辑删除角色本身信息. 物理删除用户拥有该角色的记录信息 同时删除角色的权限记录
	 */
	public Map delRoleByRoleId(String roleId) throws Exception {
		Map map = new HashMap();
		try {
			WxTRole role = new WxTRole();
			role.setRoleId(new Long(roleId));
			// 逻辑删除角色.
			wxTRoleMapper.delRoleById(role);
			// 同时物理删除用户拥有该角色的记录信息
			wxTUserroleMapper.delUserRoleByRoleId(role);
			WxTRolesource record = new WxTRolesource();
			record.setRoleId(new Long(roleId));
			wxTRolesourceMapper.deleteOldPermissions(record);
			map.put("code", "success");
		} catch (Exception e) {
			map.put("code", "systemerror");
		}
		return map;
	}

	public Map listToJsonByPageAndName(String roleType, String roleName)
			throws Exception {
		Map map = new HashMap();
		if (roleName != null) {
			roleName = roleName.trim();
		}
		try {
//			Subject subject = SecurityUtils.getSubject();
//			WxTUser cru = (WxTUser) subject.getPrincipal();
			WxTRole role = new WxTRole();
			role.setChRoleName(roleName);
			if ("USER_DEFINED".equals(roleType)){
				role.setPresetBj(0);
			} else if ("PRE_DEFINED".equals(roleType)){
				role.setPresetBj(2);
			}
			role.setRoleType(-1);
//			role.setXgUser(cru.getUserId());
			List<WxTRole> list = wxTRoleMapper.listToJsonByPageAndName(role);
			if (list.size() > 0) {
//				for (int i = 0; i < list.size(); i++) {
//					if (list.get(i).getRoleType() == 3) {
//						list.get(i).setOperationStr(
//								"<a href=\"javascript:;\" onclick=\"findBuiltInRoleDescript('"
//										+ list.get(i).getRoleId()
//										+ "')\">查看权限</a>");
//					} else {
//						list.get(i)
//								.setOperationStr(
//										"<a href=\"javascript:;\" onclick=\"editRole('"
//												+ list.get(i).getRoleId()
//												+ "',"
//												+ "'"
//												+ list.get(i).getChRoleName()
//												+ "')\">编辑</a>"
//												+ "&nbsp;&nbsp;&nbsp;&nbsp;<a href=\"javascript:;\" "
//												+ "onclick=\"editPermissions('"
//												+ list.get(i).getRoleId()
//												+ "','"
//												+ list.get(i).getRoleType()
//												+ "')\">授权</a>"
//												+ "&nbsp;&nbsp;&nbsp;&nbsp;"
//												+ "<a href=\"javascript:;\" onclick=\"delRole('"
//												+ list.get(i).getRoleId()
//												+ "')\">删除</a>");
//					}
//				}
				map.put("code", "success");
				map.put("list", list);
			} else {
				map.put("code", "notRole");
			}
		} catch (Exception e) {
			map.put("code", "systemerror");
		}
		return map;
	}

	public Map editRoleByRoleId(String roleId, String roleName, Integer salesLevel, String passwordPower, String roleDesc, String salesChannel, String blockInformer)
			throws Exception {
		Map map = new HashMap();
		try {
			WxTRole role = new WxTRole();
			role.setRoleId(new Long(roleId));
			role.setSalesLevel(salesLevel);
			role.setChRoleName(roleName);
			role.setPasswordPower(Integer.parseInt(passwordPower));
			role.setRoleDescript(roleDesc);
			if(StringUtils.isNotBlank(salesChannel)){
				role.setSalesChannel(salesChannel);
			}
			if(!EmptyChecker.isEmpty(blockInformer)) {
				role.setBlockInformer(blockInformer);
			}
			wxTRoleMapper.editRoleById(role);
			map.put("code", "success");
		} catch (Exception e) {
			map.put("code", "systemerror");
		}
		return map;
	}

	/**
	 * 给角色赋权
	 */
	public Map<String, Object> giveTheRolePermissions(String rsType, String roleId)
			throws Exception {//TODO
		Map<String, Object> resultMap = new HashMap<String, Object>();
		try {
			Subject subject = SecurityUtils.getSubject();
			WxTUser cru = (WxTUser) subject.getPrincipal();
			WxTRolesource wxTRolesource = new WxTRolesource();
			wxTRolesource.setRsType(Integer.parseInt(rsType));
			wxTRolesource.setXgUser(cru.getUserId());
			wxTRolesource.setTenantId(cru.getOrgId());//modify by bo.liu 0818 cru.getTenantId()
			wxTRolesource.setRoleId(new Long(roleId));
			String resultCode = "notData";
			List<TreeNode> resultJsonList = null;
			List<TreeNode> treeList = new ArrayList<TreeNode>();
			if ("2".equals(rsType)) {
				// 功能角色,查询当前登录用户拥有的所有菜单权限 预置角色不允许用户直接操作.所以功能菜单类角色只需要为非预置角色这一类赋权
				List<WxTMenu> list = wxTMenuMapper
						.selRoleSourceByPermissions(wxTRolesource);
				if (null != list && list.size() > 0) {
					resultCode = "isMenu";
					TreeNode tempObj = null;
					for (int i = 0; i < list.size(); i++) {
						tempObj = new TreeNode();
						tempObj.setId(list.get(i).getMenuId());
						tempObj.setText(list.get(i).getMenuName());
						tempObj.setPid(list.get(i).getMenuPid());
//						tempObj.setAddFlag(list.get(i).getAddFlag());
//						tempObj.setDelFlag(list.get(i).getDelFlag());
//						tempObj.setUpdateFlag(list.get(i).getUpdateFlag());
//						tempObj.setViewFlag(list.get(i).getViewFlag());
						tempObj.setChecked(list.get(i).getCheckFlag() == 1);
						treeList.add(tempObj);
					}
				}
//				// 功能菜单是否已经被授权
//				for (int i = 0; i < treeList.size(); i++) {
//					List<WxTMenu> haveList = wxTMenuMapper
//							.selMenuByHavePermissions(wxTRolesource);
//					for (int k = 0; k < haveList.size(); k++) {
//						if (haveList.get(k).getMenuId()
//								.equals(treeList.get(i).getId())) {
//							treeList.get(i).setChecked(true);
//							break;
//						}
//					}
//				}
				resultJsonList = JsonGenerator.listToTree(treeList);
			} else if ("5".equals(rsType)) {// 主数据表...
				List<WxTData> list = wxTDataMapper
						.selDataByPermissions(wxTRolesource);
				if (null != list && list.size() > 0) {
					resultCode = "isData";
					TreeNode tempObj = null;
					for (int i = 0; i < list.size(); i++) {
						tempObj = new TreeNode();
						tempObj.setId(list.get(i).getDataId());
						tempObj.setText(list.get(i).getDataName());
						tempObj.setPid(list.get(i).getDataPid());
						treeList.add(tempObj);
					}//
				}
				for (int i = 0; i < treeList.size(); i++) {
					List<WxTData> haveList = wxTDataMapper
							.selDataByHavePermissions(wxTRolesource);
					for (int k = 0; k < haveList.size(); k++) {
						if (haveList.get(k).getDataId()
								.equals(treeList.get(i).getId())) {
							treeList.get(i).setChecked(true);
							break;
						}
					}
				}
				resultJsonList = JsonGenerator.listToTree(treeList);
			} else {// 通讯录 或者 组织角色 都是控制角色拥有的组织权限,需要查询 wx_t_org 组织结构表
				List<LigerUITreeNode> resultJsonList2 = null;
				List<LigerUITreeNode> ligerUITreeList = new ArrayList<LigerUITreeNode>();
				List<WxTOrg> list = wxTOrgMapper
						.selOrgByPermissionsRole(wxTRolesource);
				if (null != list && list.size() > 0) {
					resultCode = "isOrg";
					LigerUITreeNode tempObj = null;
					for (WxTOrg org : list) {
						tempObj = new LigerUITreeNode();
						tempObj.setId(org.getOrgId());
						tempObj.setText(org.getOrgName());
						tempObj.setPid(org.getOrgPid());
						if (org.getChecked() != null) {
							if (org.getChecked() == 1) {// 组织权限是否已经被授权
								tempObj.setIschecked(true);
							}
						}
						ligerUITreeList.add(tempObj);
					}
				}
				resultJsonList2 = JsonGenerator
						.listToLigerUITree(ligerUITreeList);
				// 仅当resultCode = "isOrg"时返回结果放入resultData2中，前端需要特殊处理
				resultMap.put("resultData2", resultJsonList2);
			}
			resultMap.put("code", "success");
			resultMap.put("resultCode", resultCode);
			resultMap.put("resultData", resultJsonList);
		} catch (Exception e) {
			e.printStackTrace();
			resultMap.put("code", "systemerror");
		}
		return resultMap;
	}

	public Map giveRolePermissions(String roleId, String rsType,
			WxTRolesource[] source) throws Exception {
		Map map = new HashMap();

		try {
			Subject subject = SecurityUtils.getSubject();
			WxTUser cru = (WxTUser) subject.getPrincipal();
			WxTRolesource record = new WxTRolesource();
			record.setRoleId(new Long(roleId));
			// 先删除原有权限记录 -并记录删除日志
			List<WxTRolesource> curRsList = wxTRolesourceMapper
					.selectByRoleId(new Long(roleId));
			Integer rsTypeInt = Integer.valueOf(rsType);
			if (WxTRole.RsType_userSelect.equals(rsTypeInt)
					|| WxTRole.RsType_org.equals(rsTypeInt)) {
				// 组织和通讯录权限用比对方式授权
				List<WxTRolesource> sourceList  = new ArrayList<WxTRolesource>();
				if(source!=null && source.length>0){
					sourceList = Arrays.asList(source);
				}
				this.updateDataRole(new Long(roleId), rsTypeInt, cru, sourceList, curRsList);
			} else {
				// 其他类型权限采用全量删除后新增方式授权
				this.updateNotDataRole(roleId, rsType, source, curRsList, cru,
						record);

			}

			map.put("code", "success");
		} catch (Exception e) {
			e.printStackTrace();
			map.put("code", "systemerror");
			map.put("codeMsg", "错误信息:" + e.getMessage());
		}
		return map;
	}

	public void updateNotDataRole(String roleId, String rsType,
			WxTRolesource[] source, List<WxTRolesource> delList, WxTUser cru,
			WxTRolesource record) {
		int i = 0;
		for (i = 0; i < delList.size(); i++) {
			// 日志记录: 用户 删除 角色 xxx 资源
			creatLog(new Long(roleId), 2, 2, delList.get(i).getSourceId(),
					cru.getTenantId(), cru.getUserId(),
					Integer.parseInt(rsType));
			wxTRolesourceMapper.deleteByPrimaryKey(delList.get(i).getRsId());
		}

		if (source.length != 0) {
			// sourceId = sourceId.substring(0,sourceId.length() - 1);
			// String[] arr = sourceId.split(",");
			for (i = 0; i < source.length; i++) {
				if (source[i].getRsId() == null) {
					continue;
				}
				// 添加新记录
				record = new WxTRolesource();
				record.setRoleId(new Long(roleId));
				record.setRsType(Integer.parseInt(rsType));
				record.setSourceId(new Long(source[i].getRsId()));
				record.setAddFlag((source[i].getAddFlag() == "true") ? "1"
						: "0");
				record.setDelFlag((source[i].getDelFlag() == "true") ? "1"
						: "0");
				record.setUpdateFlag((source[i].getUpdateFlag() == "true") ? "1"
						: "0");
				record.setViewFlag((source[i].getViewFlag() == "true") ? "1"
						: "0");
				record.setXgUser(cru.getUserId());
				record.setTenantId(cru.getOrgId());////modify by bo.liu 0818 cru.getTenantId()
				record.setStatus(1);
				wxTRolesourceMapper.insertSelective(record);
				// 日志记录: 用户 授予 角色 xxx 资源
				creatLog(new Long(roleId), 2, 1, new Long(source[i].getRsId()),
						cru.getTenantId(), cru.getUserId(),
						Integer.parseInt(rsType));
			}
		}
	}
	/**
	 * 针对组织和通讯录数据量较大情况
	 * 采用比对法删除或新增权限
	 * @param roleId
	 * @param rsType
	 * @param cru
	 * @param sourceList 传入的WxTRolesource权限对象
	 * @param curRsList  已有的WxTRolesource权限对象
	 */
	public void updateDataRole(Long roleId,Integer rsType,WxTUser cru, List<WxTRolesource> sourceList,
			List<WxTRolesource> curRsList) {
		// 当前已有的权限资源id
		List<Long> curSourceIdList = new ArrayList<Long>();
		if (curRsList != null && curRsList.size() > 0) {
			for (WxTRolesource rs : curRsList) {
				curSourceIdList.add(rs.getSourceId());
			}
		}
		if (curSourceIdList.size() == 0) {
			curSourceIdList = null;
		}
		// 传入的权限资源id
		List<Long> newSourceIdList = new ArrayList<Long>();
		if (sourceList != null && sourceList.size() > 0) {
			for (WxTRolesource rs : sourceList) {
				newSourceIdList.add(rs.getRsId());
			}
		}
		if (newSourceIdList.size() == 0) {
			newSourceIdList = null;
		}
		/**
		 * 比对权限 先取出现有的权限sourceId 传入权限比现有多的部分 要insert 现有权限比传入的多的部分 要del
		 */
		if (curSourceIdList != null && curSourceIdList.size() > 0) {

			wxTRolesourceMapper.deleteByCompareOrg(roleId, curSourceIdList,
					newSourceIdList);

		}
		if (newSourceIdList != null && newSourceIdList.size() > 0) {
			wxTRolesourceMapper.insertByCompareOrg(roleId, rsType, cru.getUserId(),
					cru.getTenantId(), curSourceIdList, newSourceIdList);
		}
	}

	public Map findBuiltInRoleDescript(String roleId) throws Exception {
		Map map = new HashMap();
		try {
			WxTRole role = wxTRoleMapper.selectByPrimaryKey(new Long(roleId));
			map.put("code", "success");
			map.put("resultRole", role.getRoleDescript());
		} catch (Exception e) {
			map.put("code", "systemerror");
		}
		return map;
	}
/**
 * 根据用户id、角色类型、角色编码查找角色
 */
	public Map selRoleByUserId(String userId, String roleType, String chRoleName)
			throws Exception {
		Map map = new HashMap();
		if (chRoleName != null) {
			chRoleName = chRoleName.trim();
		}
		try {
			List<WxTRole> list = null;
			if ("2".equals(roleType)) {
				list = wxTRoleMapper.selRoleByUserId2(new Long(userId), Integer.parseInt(roleType), chRoleName);
			} else {
				list = wxTRoleMapper.selRoleByUserIdAndRoleType(new Long(userId), Integer.parseInt(roleType), chRoleName);
			}
			for (int i = 0; i < list.size(); i++) {
				list.get(i).setOperationStr(
								"<a href='javascript:;' onclick='removeRoleForUser(\""
										+ list.get(i).getRoleId()
										+ "\")'>移除权限</a>");
			}
			map.put("code", "success");
			map.put("resultList", list);
		} catch (Exception e) {
			map.put("code", "systemerror");
		}
		return map;
	}

	public Map<String, Object> selRoleByUserNotHave(String grantUserId, String userId,
			String roleType, String chRoleName, String orgType) throws Exception {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		if (chRoleName != null) {
			chRoleName = chRoleName.trim();
		}
		int _roleType = -1;
		try {
			WxTRolesource wxTRolesource = new WxTRolesource();
			if("1".equals(orgType)){
				_roleType = WxTRole.ROLE_TYPE_WORKSHOP;
			}else{
				WxTUser user = ContextUtil.getCurUser();
				boolean isAdmin = userService.isUserAdmin(user.getUserId());
				if (isAdmin){
					_roleType = WxTRole.ROLE_TYPE_ADMIN;
				} else {
					if (user.getmUserTypes() == 1){
						//雪佛龙
						_roleType = WxTRole.ROLE_TYPE_ORGANIZATION;
					} else if("1".equals(user.getType())){
						_roleType = WxTRole.ROLE_TYPE_WORKSHOP;
					} else {
						_roleType = WxTRole.ROLE_TYPE_PARTNER;
					}
				}
			}
			wxTRolesource.setRsType(_roleType);
			wxTRolesource.setXgUser(new Long(userId));
			wxTRolesource.setDelFlag(chRoleName);
			List<WxTRole> list = wxTRoleMapper.selRoleByUserNotHave(wxTRolesource);
			Iterator<WxTRole> roleIter = list.iterator();
			boolean isPartnerAdmin = roleBizService.checkUserIsSpecificRole(ContextUtil.getCurUserId(), WxTRole.ROLE_SERVICE_PARTNER_ADMIN);
			log.info("isPartnerAdmin: {}", isPartnerAdmin);
			while (roleIter.hasNext()) {
				WxTRole role = roleIter.next();
				//special rule added by lxia
				if (isPartnerAdmin){
					if (role.getChRoleName().equals(WxTRole.ROLE_SERVICE_PARTNER_MANAGER) || role.getChRoleName().equals(WxTRole.ROLE_SERVICE_PARTNER_ADMIN)){
						roleIter.remove();
					}
				}
				role.setOperationStr(
						"<a href='javascript:;' onclick='giveRoleToUser(\""
								+ role.getRoleId()
								+ "\")'>授予权限</a>");


			}
			resultMap.put("code", "success");
			resultMap.put("resultList", list);
		} catch (Exception e) {
			resultMap.put("code", "systemerror");
		}
		return resultMap;
	}

	public Map giveRoleToUser(String userId, String roleId) throws Exception {
		Map map = new HashMap();
		try {
			Subject subject = SecurityUtils.getSubject();
			WxTUser cru = (WxTUser) subject.getPrincipal();
			WxTUserrole wxtur = new WxTUserrole();
			wxtur.setUserId(new Long(userId));
			wxtur.setRoleId(new Long(roleId));
			wxtur.setStatus(1);
			wxtur.setTenantId(cru.getOrgId());////modify by bo.liu 0818 cru.getTenantId()
			wxtur.setGrantUserid(cru.getUserId());
			wxtur.setXgSj(new Date());
			wxtur.setXgUser(cru.getUserId());
			wxTUserroleMapper.insertSelective(wxtur);
			WxTRole role = wxTRoleMapper.selectByPrimaryKey(new Long(roleId));
			int temp = 0;
			if (role.getRoleType() == 1) {
				temp = 8;// 通讯录角色
			} else if (role.getRoleType() == 2) {
				temp = 9;// 菜单角色
			} else if (role.getRoleType() == 4) {
				temp = 10;// 组织角色
			} else {
				temp = 11;// 预置角色
			}
			// 日志记录: 用户 授予 用户 xxx 角色
			creatLog(new Long(userId), 1, 1, new Long(roleId),
					cru.getTenantId(), cru.getUserId(), temp);
			map.put("code", "success");
		} catch (Exception e) {
			map.put("code", "systemerror");
		}
		return map;
	}

	public Map removeRoleForUser(String userId, String roleId) throws Exception {
		Map map = new HashMap();
		try {
			Subject subject = SecurityUtils.getSubject();
			WxTUser cru = (WxTUser) subject.getPrincipal();
			WxTUserrole wxTUserrole = new WxTUserrole();
			wxTUserrole.setUserId(new Long(userId));
			wxTUserrole.setRoleId(new Long(roleId));
			wxTRoleMapper.removeTheUserRole(wxTUserrole);
			// 日志记录: 用户 移除 用户 xxx 角色
			creatLog(new Long(userId), 1, 2, new Long(roleId),
					cru.getTenantId(), cru.getUserId(), 7);
			map.put("code", "success");
		} catch (Exception e) {
			map.put("code", "systemerror");
		}
		return map;
	}

	public Map showThrRoleInfoById(String roleType, String roleId)
			throws Exception {
		Map map = new HashMap();
		try {
			Subject subject = SecurityUtils.getSubject();
			WxTUser cru = (WxTUser) subject.getPrincipal();
			WxTRolesource wxTRolesource = new WxTRolesource();
			wxTRolesource.setRsType(Integer.parseInt(roleType));
			wxTRolesource.setRoleId(new Long(roleId));
			wxTRolesource.setTenantId(cru.getOrgId());////modify by bo.liu 0818 cru.getTenantId()
			List<TreeNode> resultJsonList = null;
			List<TreeNode> treeList = new ArrayList<TreeNode>();
			if ("2".equals(roleType)) {// 查菜单
				wxTRolesource.setRsType(2);
				List<WxTMenu> list = wxTMenuMapper
						.selMenuByHavePermissions(wxTRolesource);
				if (null != list && list.size() > 0) {
					TreeNode tempObj = null;
					for (int i = 0; i < list.size(); i++) {
						tempObj = new TreeNode();
						tempObj.setId(list.get(i).getMenuId());
						tempObj.setText(list.get(i).getMenuName());
						tempObj.setPid(list.get(i).getMenuPid());
						treeList.add(tempObj);
					}
				}
			} else if ("3".equals(roleType)) {// 预置角色只显示说明
				WxTRole role = wxTRoleMapper
						.selectByPrimaryKey(new Long(roleId));
				TreeNode tempObj = new TreeNode();
				tempObj.setId(new Long(1));
				tempObj.setText(role.getRoleDescript());
				tempObj.setPid(new Long(0));
				treeList.add(tempObj);
			} else if ("5".equals(roleType)) {// 主数据角色
				List<WxTData> list = wxTDataMapper
						.selDataByHavePermissions(wxTRolesource);
				if (null != list && list.size() > 0) {
					TreeNode tempObj = null;
					for (int i = 0; i < list.size(); i++) {
						tempObj = new TreeNode();
						tempObj.setId(list.get(i).getDataId());
						tempObj.setText(list.get(i).getDataName());
						tempObj.setPid(list.get(i).getDataPid());
						treeList.add(tempObj);
					}
				}
			} else {// 查组织
				List<WxTOrg> list = wxTOrgMapper
						.selOrgByHavePermissions(wxTRolesource);
				if (null != list && list.size() > 0) {
					TreeNode tempObj = null;
					for (int i = 0; i < list.size(); i++) {
						tempObj = new TreeNode();
						tempObj.setId(list.get(i).getOrgId());
						tempObj.setText(list.get(i).getOrgName());
						tempObj.setPid(list.get(i).getOrgPid());
						treeList.add(tempObj);
					}
				}
			}
			resultJsonList = JsonGenerator.listToTree(treeList);
			map.put("code", "success");
			map.put("resultJsonList", resultJsonList);
		} catch (Exception e) {
			map.put("code", "systemerror");
		}
		return map;
	}

	public Map<String, Object> giveSourceToUser(String userId, String rsType) throws Exception {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		try {
			WxTUser cru = ContextUtil.getCurUser();
			WxTRolesource wxTRolesource = new WxTRolesource();
			wxTRolesource.setRsType(Integer.parseInt(rsType));
			wxTRolesource.setXgUser(cru.getUserId());
			wxTRolesource.setTenantId(cru.getOrgId());////modify by bo.liu 0818 cru.getTenantId()
			wxTRolesource.setRsId(Long.parseLong(userId));
			String resultCode = "notData";
			List<TreeNode> resultJsonList = null;
			List<TreeNode> treeList = new ArrayList<TreeNode>();
			if ("2".equals(rsType)) {
				// 查询当前登录用户拥有的所有菜单权限
				List<WxTMenu> list = wxTMenuMapper
						.selMenuByPermissions(wxTRolesource);
				if (null != list && list.size() > 0) {
					resultCode = "isMenu";
					TreeNode tempObj = null;
					for (int i = 0; i < list.size(); i++) {
						tempObj = new TreeNode();
						tempObj.setId(list.get(i).getMenuId());
						tempObj.setText(list.get(i).getMenuName());
						tempObj.setPid(list.get(i).getMenuPid());
						treeList.add(tempObj);
					}
				}
				WxTUserbase ubase = new WxTUserbase();
				ubase.setUserId(new Long(userId));
				ubase.setTenantId(cru.getOrgId());//modify by bo.liu 0818 cru.getTenantId()
				ubase.setRsType(Integer.parseInt(rsType));
				for (int i = 0; i < treeList.size(); i++) {
					List<WxTMenu> haveList = wxTMenuMapper.selMenuToUser(ubase);
					for (int k = 0; k < haveList.size(); k++) {
						if (haveList.get(k).getMenuId()
								.equals(treeList.get(i).getId())) {
							treeList.get(i).setChecked(true);
							break;
						}
					}
				}
			} else if ("5".equals(rsType)) {// 主数据
				List<WxTData> list = wxTDataMapper
						.selDataByPermissions(wxTRolesource);
				if (null != list && list.size() > 0) {
					resultCode = "isData";
					TreeNode tempObj = null;
					for (int i = 0; i < list.size(); i++) {
						tempObj = new TreeNode();
						tempObj.setId(list.get(i).getDataId());
						tempObj.setText(list.get(i).getDataName());
						tempObj.setPid(list.get(i).getDataPid());
						treeList.add(tempObj);
					}
				}
				WxTUserbase ubase = new WxTUserbase();
				ubase.setUserId(new Long(userId));
				ubase.setTenantId(cru.getOrgId());////modify by bo.liu 0818 cru.getTenantId()
				ubase.setRsType(Integer.parseInt(rsType));
				for (int i = 0; i < treeList.size(); i++) {
					List<WxTData> haveList = wxTDataMapper.selDataToUser(ubase);
					for (int k = 0; k < haveList.size(); k++) {
						if (haveList.get(k).getDataId()
								.equals(treeList.get(i).getId())) {
							treeList.get(i).setChecked(true);
							break;
						}
					}
				}
			} else {// 组织
				List<LigerUITreeNode> resultJsonList2 = null;
				List<LigerUITreeNode> ligerUITreeList = new ArrayList<LigerUITreeNode>();
				List<WxTOrg> list = wxTOrgMapper
						.selOrgByPermissionsUser(wxTRolesource);
				if (null != list && list.size() > 0) {
					resultCode = "isOrg";
					LigerUITreeNode tempObj = null;
					for (WxTOrg org : list) {
						tempObj = new LigerUITreeNode();
						tempObj.setId(org.getOrgId());
						tempObj.setText(org.getOrgName());
						tempObj.setPid(org.getOrgPid());
						System.out.println("org.checked =" + org.getChecked());
						if (org.getChecked() != null) {
							if (org.getChecked() == 1) {// 组织权限是否已经被授权
								tempObj.setIschecked(true);
							}
						}
						ligerUITreeList.add(tempObj);
					}
					System.out.println("ligerUITreeList.size ="
							+ ligerUITreeList.size());
				}
				resultJsonList2 = JsonGenerator
						.listToLigerUITree(ligerUITreeList);
				// 仅当resultCode = "isOrg"时返回结果放入resultData2中，前端需要特殊处理
				resultMap.put("resultData2", resultJsonList2);
			}
			resultJsonList = JsonGenerator.listToTree(treeList);
			resultMap.put("code", "success");
			resultMap.put("resultCode", resultCode);
			resultMap.put("resultData", resultJsonList);
		} catch (Exception e) {
			resultMap.put("code", "systemerror");
		}
		return resultMap;
	}

	public Map addUserscourceInfo(String userId, String rsType, String sourceId)
			throws Exception {
		Map map = new HashMap();
		try {
			Subject subject = SecurityUtils.getSubject();
			WxTUser cru = (WxTUser) subject.getPrincipal();
			// 清空原有资源记录
			WxTUserbase record = new WxTUserbase();
			record.setRsType(Integer.parseInt(rsType));
			record.setUserId(new Long(userId));
			List<WxTUserbase> delList = wxTUserbaseMapper
					.selectByUserId(record);
			for (int i = 0; i < delList.size(); i++) {
				wxTUserbaseMapper.deleteByPrimaryKey(delList.get(i).getUbId());
				// 日志记录: 用户 删除 用户 xxx 资源
				creatLog(new Long(userId), 1, 2, new Long(delList.get(i)
						.getSourceId()), cru.getTenantId(), cru.getUserId(),
						Integer.parseInt(rsType));
			}
			WxTUserbase ubase = null;
			String[] arr = null;
			if (!"".equals(sourceId)) {
				arr = sourceId.split(",");
				for (int i = 0; i < arr.length; i++) {
					ubase = new WxTUserbase();
					ubase.setUserId(new Long(userId));
					ubase.setRsType(Integer.parseInt(rsType));
					ubase.setSourceId(new Long(arr[i]));
					ubase.setStatus(1);
					ubase.setXgSj(new Date());
					ubase.setTenantId(cru.getOrgId());//modify by bo.liu 0818 cru.getTenantId()
					ubase.setXgUser(cru.getUserId());
					wxTUserbaseMapper.insertSelective(ubase);
					// 日志记录: 用户 授予 用户 xxx 资源
					creatLog(new Long(userId), 1, 1, new Long(arr[i]),
							cru.getTenantId(), cru.getUserId(),
							Integer.parseInt(rsType));
				}
			}
			map.put("code", "success");
		} catch (Exception e) {
			map.put("code", "systemerror");
		}
		return map;
	}

	public void creatLog(Long roleId, int objType, int perType, Long sourceId,
			Long tenantId, Long xgUser, int plogSourceType) {
		WxTPlog log = new WxTPlog();
		log.setPlogObjId(roleId);
		log.setPlogObjType(objType);
		log.setPlogPerType(perType);
		log.setPlogSourceid(sourceId);
		log.setPlogSourceType(plogSourceType);
		log.setStatus(1);
		log.setTenantId(tenantId);
		log.setXgSj(new Date());
		log.setXgUser(xgUser);
		wxTPlogMapper.insertSelective(log);
	}

	public Map selUserPerInfo(String rsType, String uid) throws Exception {
		Map map = new HashMap();
		try {
			Subject subject = SecurityUtils.getSubject();
			WxTUser cru = (WxTUser) subject.getPrincipal();
			WxTRolesource rs = new WxTRolesource();
			rs.setRsType(Integer.parseInt(rsType));
			rs.setXgUser(new Long(uid));
			rs.setTenantId(cru.getOrgId());////modify by bo.liu 0818 cru.getTenantId()
			List<TreeNode> resultJsonList = null;
			List<TreeNode> treeList = new ArrayList<TreeNode>();
			if ("2".equals(rsType)) {
				List<WxTMenu> list = wxTMenuMapper.selMenuByPermissions(rs);
				TreeNode tempObj = null;
				for (int i = 0; i < list.size(); i++) {
					tempObj = new TreeNode();
					tempObj.setId(list.get(i).getMenuId());
					tempObj.setText(list.get(i).getMenuName());
					tempObj.setPid(list.get(i).getMenuPid());
					treeList.add(tempObj);
				}
			} else if ("5".equals(rsType)) {// 主数据
				List<WxTData> list = wxTDataMapper.selDataByPermissions(rs);
				TreeNode tempObj = null;
				for (int i = 0; i < list.size(); i++) {
					tempObj = new TreeNode();
					tempObj.setId(list.get(i).getDataId());
					tempObj.setText(list.get(i).getDataName());
					tempObj.setPid(list.get(i).getDataPid());
					treeList.add(tempObj);
				}
			} else {
				List<WxTOrg> list = wxTOrgMapper.selOrgByPermissions(rs);
				if (null != list && list.size() > 0) {
					TreeNode tempObj = null;
					for (int i = 0; i < list.size(); i++) {
						tempObj = new TreeNode();
						tempObj.setId(list.get(i).getOrgId());
						tempObj.setText(list.get(i).getOrgName());
						tempObj.setPid(list.get(i).getOrgPid());
						treeList.add(tempObj);
					}
				}
			}
			resultJsonList = JsonGenerator.listToTree(treeList);

			map.put("code", "success");
			map.put("resultJsonList", resultJsonList);
		} catch (Exception e) {
			map.put("code", "systemerror");
		}
		return map;
	}

	public Map selDataByMaintenanceTheData() throws Exception {
		Map map = new HashMap();
		try {
			Subject subject = SecurityUtils.getSubject();
			WxTUser cru = (WxTUser) subject.getPrincipal();
			List<TreeNode> resultJsonList = null;
			List<TreeNode> treeList = new ArrayList<TreeNode>();
			WxTRolesource s = new WxTRolesource();
			s.setXgUser(cru.getUserId());
			s.setTenantId(cru.getOrgId());////modify by bo.liu 0818 cru.getTenantId()
			s.setRsType(5);
			List<WxTData> list = wxTDataMapper.selDataByPermissions(s);
			if (null != list && list.size() > 0) {
				TreeNode tempObj = null;
				for (int i = 0; i < list.size(); i++) {
					tempObj = new TreeNode();
					tempObj.setId(list.get(i).getDataId());
					tempObj.setText(list.get(i).getDataName());
					tempObj.setPid(list.get(i).getDataPid());
					treeList.add(tempObj);
				}
			}
			resultJsonList = JsonGenerator.listToTree(treeList);
			map.put("code", "success");
			map.put("resultJsonList", resultJsonList);
		} catch (Exception e) {
			map.put("code", "systemerror");
		}
		return map;
	}

	public Map<String,Object> delMaintenanceTheData(Long dataId) {
		Map map = new HashMap();
		try {
			// 删除主数据
			wxTDataMapper.deleteByPrimaryKey(dataId);
			// 删除角色拥有的本数据的记录
			wxTRolesourceMapper.deleteBySourceId(dataId);
			// 删除用户拥有本数据的记录
			wxTUserbaseMapper.deleteBySourceId(dataId);
			map.put("code", "success");
		} catch (Exception e) {
			map.put("code", "systemerror");
		}
		return map;
	}

	// 更新主数据
	public Map updateTheData(String dataName, String dataId, String dataCode,
			String dataOrder, String dataDescript) throws Exception {
		Map map = new HashMap();
		try {
			Subject subject = SecurityUtils.getSubject();
			WxTUser cru = (WxTUser) subject.getPrincipal();
			WxTData data = new WxTData();
			data.setDataName(dataName);
			data.setXgUser(cru.getUserId());
			data.setDataId(new Long(dataId));
			data.setDataCode(dataCode);
			data.setDataOrder(dataOrder);
			data.setDataDescript(dataDescript);
			WxTData checkData = wxTDataMapper.selectByPrimaryKey(new Long(
					dataId));
			List<WxTData> list1 = wxTDataMapper.selDataByDataCodeOnly(data);
			data.setDataPid(checkData.getDataPid());
			List<WxTData> list2 = wxTDataMapper
					.selDataByDataNameAndPidOnly(data);
			if (list1.size() > 0) {// 检查 code 是否唯一.
				map.put("code", "codeIsExist");
			} else if (list2.size() > 0) { // 检查 同级下 name 是否唯一
				map.put("code", "nameIsExist");
			} else {
				wxTDataMapper.updateByPrimaryKey(data);
				map.put("code", "success");
			}
		} catch (Exception e) {
			map.put("code", "systemerror");
		}
		return map;
	}

	// 新增主数据
	public Map creatNewData(Long dataPid, String dataName, String dataCode,
			String dataOrder, String dataDescript) throws Exception {
		Map map = new HashMap();
		try {
			Subject subject = SecurityUtils.getSubject();
			WxTUser cru = (WxTUser) subject.getPrincipal();
			WxTData data = new WxTData();
			data.setDataPid(dataPid);
			data.setDataName(dataName);
			data.setXgUser(cru.getUserId());
			data.setTenantId(cru.getOrgId());//modify by bo.liu 0818 cru.getTenantId()
			data.setStatus(1);
			data.setXgSj(new Date());
			data.setDataCode(dataCode);
			data.setDataOrder(dataOrder);
			data.setDataDescript(dataDescript);
			Long rootId = getDataRootIdByDataPid(data.getDataPid());
			data.setRootId(rootId);
			// 查询准备新增的项目是否存在同名
			WxTData exist = wxTDataMapper.selDataByDataName(data);
			// 查询代码是否唯一
			WxTData data2 = new WxTData();
			data2.setDataCode(dataCode);
			data2.setDataId(new Long(-1));
			List<WxTData> list1 = wxTDataMapper.selDataByDataCodeOnly(data2);
			if (null != exist) {// 存在同名项
				map.put("code", "nameIsExist");
			} else if (list1.size() > 0) {
				map.put("code", "codeIsExist");
			} else {
				try {
					wxTDataMapper.insertSelective(data);
				} catch (Exception ex) {
					ex.printStackTrace();
				}

				WxTData newData = wxTDataMapper.selDataByDataName(data);
				// 将新建项赋予创建者
				WxTUserbase ubase = new WxTUserbase();
				ubase.setUserId(cru.getUserId());
				ubase.setRsType(5);
				ubase.setSourceId(newData.getDataId());
				ubase.setXgSj(new Date());
				ubase.setXgUser(cru.getUserId());
				ubase.setStatus(1);
				ubase.setTenantId(cru.getOrgId());//modify by bo.liu 0818 cru.getTenantId()
				wxTUserbaseMapper.insertSelective(ubase);
				// 将新建项 赋予拥有超级管理员的用户 和 创建者本身
				// 获得超级管理员用户集合
				WxTUserrole wxTUserrole = new WxTUserrole();
				wxTUserrole.setRoleId(new Long(1001));
				List<WxTUserrole> sUserList = wxTUserroleMapper
						.selectByRoleId(wxTUserrole);
				for (int i = 0; i < sUserList.size(); i++) {
					// 如果创建者本身就是超级管理员,则不需要重复添加
					if (!cru.getUserId().equals(sUserList.get(i).getUserId())) {
						WxTUserbase ubase2 = new WxTUserbase();
						ubase2.setUserId(sUserList.get(i).getUserId());
						ubase2.setRsType(5);
						ubase2.setSourceId(newData.getDataId());
						ubase2.setXgSj(new Date());
						ubase2.setXgUser(cru.getUserId());
						ubase2.setStatus(1);
						ubase2.setTenantId(cru.getOrgId());//modify by bo.liu 0818 cru.getTenantId()
						wxTUserbaseMapper.insertSelective(ubase2);
					}
				}
				map.put("code", "success");
			}
		} catch (Exception e) {
			map.put("code", "systemerror");
		}
		return map;
	}

	// 新增主数据
	public Map<String,Object> creatNewData1(Long dataPid, String dataName, String dataCodePrefix, String dataCodePostFix,
			String dataOrder, String dataDescript){
		Map map = new HashMap();
		try {
			Subject subject = SecurityUtils.getSubject();
			WxTUser cru = (WxTUser) subject.getPrincipal();
			WxTData data = new WxTData();
			data.setDataPid(dataPid);
			data.setDataName(dataName);
			data.setXgUser(cru.getUserId());
			data.setTenantId(cru.getOrgId());//modify by bo.liu 0818 cru.getTenantId()
			data.setStatus(1);
			data.setXgSj(new Date());
			data.setDataCode("-1");
			data.setDataOrder(dataOrder);
			data.setDataDescript(dataDescript);
			Long rootId = getDataRootIdByDataPid(data.getDataPid());
			data.setRootId(rootId);
			if(WxTUser.USER_MODEL_CHEVRON.equals(cru.getUserModel())){
				//雪佛龙
				data.setOrganizationId(1l);
			}else{
				data.setOrganizationId(cru.getOrgId());
			}
			// 查询准备新增的项目是否存在同名
//			WxTData exist = wxTDataMapper.selDataByDataName(data);
//			if (null != exist) {// 存在同名项
//				map.put("code", "nameIsExist");
//			} else {
				try {
					wxTDataMapper.insertSelective(data);
				} catch (Exception ex) {
					ex.printStackTrace();
				}
				//更新新建主数据的编码
				WxTData newData1 = new WxTData();
				newData1.setDataId(data.getDataId());
				newData1.setDataCode(generateCode(data.getDataId(), dataCodePrefix, dataCodePostFix));
				wxTDataMapper.updateByPrimaryKeySelective(newData1);
				data.setDataCode(newData1.getDataCode());
				map.put("data", data);

//				WxTData newData = wxTDataMapper.selDataByDataName(data);
//				// 将新建项赋予创建者
//				WxTUserbase ubase = new WxTUserbase();
//				ubase.setUserId(cru.getUserId());
//				ubase.setRsType(5);
//				ubase.setSourceId(newData.getDataId());
//				ubase.setXgSj(new Date());
//				ubase.setXgUser(cru.getUserId());
//				ubase.setStatus(1);
//				ubase.setTenantId(cru.getOrgId());//modify by bo.liu 0818 cru.getTenantId()
//				wxTUserbaseMapper.insertSelective(ubase);
//				// 将新建项 赋予拥有超级管理员的用户 和 创建者本身
//				// 获得超级管理员用户集合
//				WxTUserrole wxTUserrole = new WxTUserrole();
//				wxTUserrole.setRoleId(new Long(1001));
//				List<WxTUserrole> sUserList = wxTUserroleMapper
//						.selectByRoleId(wxTUserrole);
//				for (int i = 0; i < sUserList.size(); i++) {
//					// 如果创建者本身就是超级管理员,则不需要重复添加
//					if (!cru.getUserId().equals(sUserList.get(i).getUserId())) {
//						WxTUserbase ubase2 = new WxTUserbase();
//						ubase2.setUserId(sUserList.get(i).getUserId());
//						ubase2.setRsType(5);
//						ubase2.setSourceId(newData.getDataId());
//						ubase2.setXgSj(new Date());
//						ubase2.setXgUser(cru.getUserId());
//						ubase2.setStatus(1);
//						ubase2.setTenantId(cru.getOrgId());//modify by bo.liu 0818 cru.getTenantId()
//						wxTUserbaseMapper.insertSelective(ubase2);
//					}
//				}
				map.put("code", "success");
//			}
		} catch (Exception e) {
			e.printStackTrace();
			map.put("code", "systemerror");
		}
		return map;
	}

	/**
	 * 自动生成数据编码(创建主数据时调用)
	 * @param dataId 主数据ID
	 * @param prefix 编码前缀
	 * @param postfix 编码后缀
	 * @return 系统唯一的数据编码
	 */
	private String generateCode(long dataId, String prefix, String postfix){
		if(prefix == null){
			prefix = "";
		}
		if(postfix == null){
			postfix = "";
		}
		String code = StringUtils.to64(dataId);
		String dataCode = prefix + code + postfix;
		int i = 1;
		do{
			WxTData data2 = new WxTData();
			data2.setDataCode(dataCode);
			data2.setDataId(new Long(-1));
			List<WxTData> list1 = wxTDataMapper.selDataByDataCodeOnly(data2);
			if(list1.size() == 0){
				break;
			}
			dataCode = prefix + code + "-" + i++ + postfix;
		}while(true);
		return dataCode;
	}

	public Map selectByPrimaryKey(String dataId) throws Exception {
		Map map = new HashMap();
		try {
			WxTData data = wxTDataMapper.selectByPrimaryKey(new Long(dataId));
			map.put("code", "success");
			map.put("resultData", data);
		} catch (Exception e) {
			map.put("code", "systemerror");
		}
		return map;
	}

	public Map findMaxId() {
		Map map = new HashMap();
		try {
			Long n = wxTDataMapper.selDataMaxID().getDataId();
			n = n + 1;
			map.put("code", "success");
			map.put("resultData", n);
		} catch (Exception e) {
			map.put("code", "systemerror");
		}
		return map;
	}

	/**
	 * 根据dataPd递归查询上级主数据记录，直到data_pid = 0为止 返回root_id
	 *
	 * @param dataPid
	 * @return
	 */
	public Long getDataRootIdByDataPid(Long dataPid) {
		WxTData data = wxTDataMapper.selectByPrimaryKey(dataPid);
		if (null != data) {
			if (data.getDataPid() == 0) {
				return data.getRootId();
			} else {
				return getDataRootIdByDataPid(data.getDataPid());
			}
		}
		return 0L;
	}

	/**
	 * 获取主数据中任务类型列表
	 * @return 主数据中任务类型列表
	 */
	public Map<String, Object> findTaskTypeList(){
		Map<String, Object> map = new HashMap<String, Object>(2);
		try {
			map.put("resultData", wxTDataMapper.selTaskTypes());
			map.put("code", "success");
		} catch (Exception e) {
			e.printStackTrace();
			map.put("code", "systemerror");
		}
		return map;
	}

	@Override
	public Map updateTheData1(Long dataId, String dataName, String dataCodePrefix, String dataCodePostfix,
			String dataOrder, String dataDescript) {
		Map map = new HashMap();
		try {
			Subject subject = SecurityUtils.getSubject();
			WxTUser cru = (WxTUser) subject.getPrincipal();
			WxTData data = new WxTData();
			data.setDataName(dataName);
			data.setXgUser(cru.getUserId());
			data.setDataId(dataId);
			data.setDataOrder(dataOrder);
			data.setDataDescript(dataDescript);
//			WxTData checkData = wxTDataMapper.selectByPrimaryKey(dataId);
//			data.setDataPid(checkData.getDataPid());
//			List<WxTData> list2 = wxTDataMapper
//					.selDataByDataNameAndPidOnly(data);
//			if (list2.size() > 0) { // 检查 同级下 name 是否唯一
//				map.put("code", "nameIsExist");
//			} else {
				wxTDataMapper.updateByPrimaryKey1(data);
				map.put("data", data);
				map.put("code", "success");
//			}
		} catch (Exception e) {
			map.put("code", "systemerror");
		}
		return map;
	}

	@Override
	public Map<String, Object> getRoleListWithCurrentUserCanGrantToOthers(int roleType) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		try {
			List<WxTRole> roleList = roleBizService.getRoleListCanBeGrantedToOthers(ContextUtil.getCurUserId(), roleType);
			resultMap.put("code", "success");
			resultMap.put("resultList", roleList);
		} catch (Exception e) {
			resultMap.put("code", "systemerror");
		}
		return resultMap;
	}

	@Override
	public WxTRole selectRoleByChRoleName(String roleName) {
		return wxTRoleMapper.selectRoleByChRoleName(roleName);
	}

	@Override
	public List<WxTRole> selRoleByUserId(Long userId) throws Exception {
		List <WxTUserrole> list = wxUserRoleServiceImpl.selectUserRoleByUserId(userId);
		List<Long> roleIds = new ArrayList<Long>();
		for(int i =0;i<list.size();i++){
			roleIds.add(list.get(i).getRoleId());
		}
		return batchSelectRoleByRoleIds(roleIds);
	}

	@Override
	public List<WxTRole> batchSelectRoleByRoleIds(List<Long> roleIds) {
		WxTRoleExample example = new WxTRoleExample();

		Criteria criteria = example.createCriteria();
		criteria.andRoleIdIn(roleIds);
		return wxTRoleMapper.selectByExample(example);
	}

	@Override
	public List<WxTRole> selRoleByUserId(Long userId, Long tenantId) {
		return wxTRoleMapper.selRoleByUserId(userId, tenantId);
	}


	@Override
	/**
	 * 查找当前登录人所拥有的角色
	 * @return
	 */
	public  List<WxTRole> getCurUserRoles(){
		List<WxTRole> roleList = new ArrayList<WxTRole>();
		long userId  = ContextUtil.getCurUserId();
		List<WxTUserrole> list = wxUserRoleServiceImpl.selectUserRoleByUserId(userId);
		if(null!=list && list.size()>0){
			for(int i=0;i<list.size();i++){
				WxTUserrole userRole = list.get(i);
				WxTRole role  = wxTRoleMapper.selectByPrimaryKey(userRole.getRoleId());
				if(null!=role){
					roleList.add(role);
				}
			}
		}
		return roleList;
	}

	/**
	 * 根据用户id查找拥有的角色
	 *
	 * @return
	 */
	@Override
	public List<WxTRole> getCurUserRoles(long userId) {
		List<WxTRole> roleList = wxTRoleMapper.selRoleByUserId3(userId);
//		List<WxTUserrole> list = wxUserRoleServiceImpl.selectUserRoleByUserId(userId);
//		if (null != list && !list.isEmpty()) {
//			for (int i = 0; i < list.size(); i++) {
//				WxTUserrole userRole = list.get(i);
//				WxTRole role = wxTRoleMapper.selectByPrimaryKey(userRole.getRoleId());
//				if (null != role) {
//					roleList.add(role);
//				}
//			}
//		}
		return roleList;
	}

	@Override
	public boolean isIncludeRole(Long userId, String chRoleName) {
		WxTRole trole = wxTRoleMapper.selectRoleByChRoleName(chRoleName);
		if(trole == null){
			return false;
		}
		long roleId = trole.getRoleId();
		WxTUserrole userRole = wxTUserroleMapper.selectUserRoleByUserIdAndRoleId(userId, roleId);
		return userRole != null;
	}

	@Override
	public Map<String, Object> getAvailableRoles(String roleType) {
		Map<String, Object> map = new HashMap<String, Object>(5);
		try {
			Map<String, Object> paramsMap = new HashMap<String, Object>();
			Long userId = ContextUtil.getCurUser().getUserId();
			if(userId != 1){
				paramsMap.put("userId", userId);
			}
			if(StringUtils.isNotBlank(roleType)){
				paramsMap.put("roleType", roleType);
			}
			paramsMap.put("presetBj", 0);
			map.put(Constants.RESULT_LST_KEY, wxTRoleMapper.queryAvailableRoles(paramsMap));
			map.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (Exception e) {
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error("getAvailableRoless fail. " + e.getMessage(), e);
		}
		return map;
	}
}