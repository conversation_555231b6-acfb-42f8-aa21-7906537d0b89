package com.chevron.qbr.controller;

import com.chevron.exportdata.ExportCol;
import com.chevron.exportdata.ExportColAlign;
import com.chevron.exportdata.IExportCol;
import com.chevron.exportdata.IPropertyHelper;
import com.chevron.exportdata.PoiWriteExcel;
import com.chevron.qbr.business.QbrCdmPerformanceAdjustBizService;
import com.chevron.qbr.dao.QbrCdmPerAdjLogMapper;
import com.chevron.qbr.model.QbrCdmPerformanceAdjust;
import com.common.exception.WxPltException;
import com.common.util.CommonUtil;
import com.common.util.ContextUtil;
import com.common.util.JsonUtil;
import com.common.util.ResponseMap;
import com.common.util.ResponseStatus;
import com.sys.utils.business.ReportViewBizService;
import com.wordnik.swagger.annotations.Api;
import com.wordnik.swagger.annotations.ApiImplicitParam;
import com.wordnik.swagger.annotations.ApiOperation;
import com.wordnik.swagger.annotations.ApiParam;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 经销商绩效调整Spring MVC Controller
 * <AUTHOR>
 * @version 1.0 2021-04-21 11:10
 */
@Controller
@Api(value = "经销商绩效调整Controller", tags = "经销商绩效调整Controller")
@RequestMapping(value="/qbrcdmperformanceadjust")
public class QbrCdmPerformanceAdjustController {
	@Autowired
	private QbrCdmPerformanceAdjustBizService qbrCdmPerformanceAdjustBizService;
	
	@Autowired
	private QbrCdmPerAdjLogMapper qbrCdmPerAdjLogMapper;
	
	@Autowired
	private ReportViewBizService reportViewBizService;
	
	private final static Logger log = Logger.getLogger(QbrCdmPerformanceAdjustController.class);

	/**
	 * 保存经销商绩效调整信息
	 * @param record 经销商绩效调整对象
	 * @return 处理结果
	 */
	@ResponseBody
    @ApiOperation(value="保存经销商绩效调整信息接口",  httpMethod="POST", notes="保存经销商绩效调整信息接口")
    @ApiImplicitParam(paramType="query", name="appToken", value="token信息", required=true)
	@RequestMapping(value="/save.do")
	public ResponseMap save(@ApiParam(name="record", value="经销商绩效调整对象", required=true) @RequestBody QbrCdmPerformanceAdjust record) {
		ResponseMap map = new ResponseMap();
		log.info("save: " + JsonUtil.writeValue(record));
		try {
			if(record.getId() == null){
				qbrCdmPerformanceAdjustBizService.insert(record);
			}else{
				qbrCdmPerformanceAdjustBizService.update(record);
			}
			log.info("save success." );
		} catch (WxPltException e) {
			map.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
			map.setErrorMsg(e.getMessage());
		} catch (Exception e) {
			map.handleException(e, this, ContextUtil.getCurUserId(), 
					"com.chevron.qbr.controller.QbrCdmPerformanceAdjustController.save", JsonUtil.writeValue(record));
		}
		return map;
	}

	@ResponseBody
    @ApiOperation(value="发放精英计划基金接口",  httpMethod="POST", notes="发放精英计划基金接口")
    @ApiImplicitParam(paramType="query", name="appToken", value="token信息", required=true)
	@RequestMapping(value="/deliveryfund.do")
	public ResponseMap deliveryFund(@ApiParam(name="adjustIds", value="调整ID集合以逗号间隔连接", required=true) @RequestParam("adjustIds") String adjustIds) {
		ResponseMap map = new ResponseMap();
		log.info("deliveryFund: " + adjustIds);
		try {
			qbrCdmPerformanceAdjustBizService.deliveryFund(adjustIds);
			log.info("deliveryFund success." );
		} catch (WxPltException e) {
			map.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
			map.setErrorMsg(e.getMessage());
		} catch (Exception e) {
			map.handleException(e, this, ContextUtil.getCurUserId(), 
					"com.chevron.qbr.controller.QbrCdmPerformanceAdjustController.deliveryFund", adjustIds);
		}
		return map;
	}

	/**
	 * 修改页面保存经销商绩效调整信息
	 * @param record 经销商绩效调整对象
	 * @return 处理结果
	 */
	@ResponseBody
    @ApiOperation(value="修改页面保存经销商绩效调整信息接口",  httpMethod="POST", notes="修改页面保存经销商绩效调整信息接口")
    @ApiImplicitParam(paramType="query", name="appToken", value="token信息", required=true)
	@RequestMapping(value="/saveforedit.do")
	public ResponseMap saveForEditPage(@ApiParam(name="record", value="经销商绩效调整对象", required=true) @RequestBody QbrCdmPerformanceAdjust record) {
		ResponseMap map = new ResponseMap();
		log.info("saveForEditPage: " + JsonUtil.writeValue(record));
		try {
			if(record.getId() == null){
				qbrCdmPerformanceAdjustBizService.insert(record);
			}else{
				qbrCdmPerformanceAdjustBizService.updateForEditPage(record);
			}
			log.info("saveForEditPage success." );
		} catch (WxPltException e) {
			map.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
			map.setErrorMsg(e.getMessage());
		} catch (Exception e) {
			map.handleException(e, this, ContextUtil.getCurUserId(), 
					"com.chevron.qbr.controller.QbrCdmPerformanceAdjustController.saveForEditPage", JsonUtil.writeValue(record));
		}
		return map;
	}

	/**
	 * 删除经销商绩效调整对象
	 * @param ids 被删除经销商绩效调整对象ID集合
	 * @return 处理结果
	 */
	@ResponseBody
    @ApiOperation(value="删除经销商绩效调整对象接口",  httpMethod="POST", notes="删除经销商绩效调整对象接口")
    @ApiImplicitParam(paramType="query", name="appToken", value="token信息", required=true)
	@RequestMapping(value="/delete.do")
	public ResponseMap delete(@ApiParam(name="ids", value="被删除经销商绩效调整对象ID集合", required=true) @RequestParam("ids") Long[] ids) {
		ResponseMap map = new ResponseMap();
		log.info("delete: " + JsonUtil.writeValue(ids));
		try {
			qbrCdmPerformanceAdjustBizService.delete(Arrays.asList(ids));
			log.info("delete success." );
		} catch (WxPltException e) {
			map.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
			map.setErrorMsg(e.getMessage());
		} catch (Exception e) {
			map.handleException(e, this, ContextUtil.getCurUserId(), 
					"com.chevron.qbr.controller.QbrCdmPerformanceAdjustController.delete", JsonUtil.writeValue(ids));
		}
		return map;
	}
	
	@ResponseBody
    @ApiOperation(value="获取调整历史接口",  httpMethod="POST", notes="获取调整历史接口")
    @ApiImplicitParam(paramType="query", name="appToken", value="token信息", required=true)
	@RequestMapping(value="/getadjustlogs.do")
	public ResponseMap getAdjustLogs(@ApiParam(name="adjustId", value="调整记录ID", required=true) @RequestParam("adjustId") Long adjustId, 
			@ApiParam(name="propertyName", value="调整属性名称", required=true) @RequestParam("propertyName")String propertyName) {
		ResponseMap map = new ResponseMap();
		log.info("getAdjustLogs: " + adjustId + "," + propertyName);
		try {
			Map<String, Object> params = new HashMap<String, Object>();
			params.put("adjustId", adjustId);
			params.put("propertyName", propertyName);
			params.put("orderBy", "id desc");
			map.setListResult(qbrCdmPerAdjLogMapper.queryByParams(params));
			log.info("getAdjustLogs success." );
//		} catch (WxPltException e) {
//			map.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
//			map.setErrorMsg(e.getMessage());
		} catch (Exception e) {
			map.handleException(e, this, ContextUtil.getCurUserId(), 
					"com.chevron.qbr.controller.QbrCdmPerformanceAdjustController.getAdjustLogs", adjustId + "," + propertyName);
		}
		return map;
	}
	
	@ApiOperation(value="列表导出接口",  httpMethod="POST", notes="列表导出接口")
	@ApiImplicitParam(paramType="query", name="appToken", value="token信息", required=true)
	@RequestMapping(value = "/excel/export.do")
	public String exportByExcel(HttpServletRequest request, HttpServletResponse response){
		try {
			//组装表头
			List<IExportCol> exportCols = new ArrayList<IExportCol>();
			ExportCol exportCol = new ExportCol("year", "年");
			exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
			exportCol.setWidth(10);
			exportCols.add(exportCol);

			exportCol = new ExportCol("quarter", "季度");
			exportCol.setPropertyHelper(new IPropertyHelper() {
				
				@SuppressWarnings("unchecked")
				@Override
				public Object getProperty(String propertyName, Object bean) throws Exception {
					return "Q" + ((Map<String, Object>)bean).get(propertyName);
				}
			});
			exportCol.setWidth(10);
			exportCols.add(exportCol);
			
			exportCol = new ExportCol("customer_name_cn", "经销商");
			exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
			exportCol.setWidth(40);
			exportCols.add(exportCol);
			
			exportCol = new ExportCol("region", "区域");
			exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
			exportCol.setWidth(15);
			exportCols.add(exportCol);

			exportCol = new ExportCol("fund_quarterly", "季度奖励(含税)");
			exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
			exportCol.setDataType(IExportCol.DATA_TYPE_NUMBER);
			exportCol.setAlign(ExportColAlign.RIGHT);
			exportCol.setDataFormat("#,##0.0#");
			exportCol.setWidth(30);
			exportCols.add(exportCol);
			
			exportCol = new ExportCol("annual_reward", "年终奖励(含税)");
			exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
			exportCol.setDataType(IExportCol.DATA_TYPE_NUMBER);
			exportCol.setAlign(ExportColAlign.RIGHT);
			exportCol.setDataFormat("#,##0.0#");
			exportCol.setWidth(30);
			exportCols.add(exportCol);
			if(!request.getParameter("year").equals("2024")){
				exportCol = new ExportCol("new_entry", "新店录入不达标扣减(含税)");
				exportCol.setPropertyHelper(new IPropertyHelper() {

					@SuppressWarnings("unchecked")
					@Override
					public Object getProperty(String propertyName, Object bean) throws Exception {
						Number value = (Number)((Map<String, Object>)bean).get("new_entry");
						if(value == null) {
							return "-";
						}
						return -value.doubleValue();
					}
				});
				exportCol.setDataType(IExportCol.DATA_TYPE_NUMBER);
				exportCol.setAlign(ExportColAlign.RIGHT);
				exportCol.setDataFormat("#,##0.0#");
				exportCol.setWidth(30);
				exportCols.add(exportCol);

				exportCol = new ExportCol("above_scan", "扫码不达标扣减(含税)");
				exportCol.setPropertyHelper(new IPropertyHelper() {

					@SuppressWarnings("unchecked")
					@Override
					public Object getProperty(String propertyName, Object bean) throws Exception {
						Number value = (Number)((Map<String, Object>)bean).get("above_scan");
						if(value == null) {
							return "-";
						}
						return -value.doubleValue();
					}
				});
				exportCol.setDataType(IExportCol.DATA_TYPE_NUMBER);
				exportCol.setAlign(ExportColAlign.RIGHT);
				exportCol.setDataFormat("#,##0.0#");
				exportCol.setWidth(30);
				exportCols.add(exportCol);
			}
			exportCol = new ExportCol("flee_goods", "非本地营销不达标扣减(含税)");
			exportCol.setPropertyHelper(new IPropertyHelper() {

				@SuppressWarnings("unchecked")
				@Override
				public Object getProperty(String propertyName, Object bean) throws Exception {
					return -((Number)((Map<String, Object>)bean).get("flee_goods")).doubleValue();
				}
			});
			exportCol.setDataType(IExportCol.DATA_TYPE_NUMBER);
			exportCol.setAlign(ExportColAlign.RIGHT);
			exportCol.setDataFormat("#,##0.0#");
			exportCol.setWidth(30);
			exportCols.add(exportCol);
			
			exportCol = new ExportCol("", "合计(含税)");
			exportCol.setPropertyHelper(new IPropertyHelper() {
				
				@SuppressWarnings("unchecked")
				@Override
				public Object getProperty(String propertyName, Object bean) throws Exception {
					Map<String, Object> data = (Map<String, Object>)bean;
					return ((Number)data.get("fund_quarterly")).doubleValue() + ((Number)data.get("annual_reward")).doubleValue()
						+ ((Number)data.get("new_entry")).doubleValue() + ((Number)data.get("above_scan")).doubleValue()
						+ ((Number)data.get("flee_goods")).doubleValue();
				}
			});
			exportCol.setDataType(IExportCol.DATA_TYPE_NUMBER);
			exportCol.setAlign(ExportColAlign.RIGHT);
			exportCol.setDataFormat("#,##0.0#");
			exportCol.setWidth(30);
			exportCols.add(exportCol);
			
			exportCol = new ExportCol("asm_comment", "ASM意见");
			exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
			exportCol.setWidth(40);
			exportCols.add(exportCol);
			
			exportCol = new ExportCol("flsr_comment", "FLSR意见");
			exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
			exportCol.setWidth(40);
			exportCols.add(exportCol);

			Map<String, Object> params = CommonUtil.buildRequestParamsByRequest(request);
			//组装数据
			List<Map<String, Object>> dataList = reportViewBizService.loadData("elite_fund", "fund_show", params);
			CommonUtil.setExportResponseHeader(request, response, "精英计划", "xlsx");
			PoiWriteExcel.exportLargeData(dataList, exportCols, response.getOutputStream(), "Sheet1", true);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			request.setAttribute("errorMsg", "导出失败：" + e.getMessage());
			return "forward:/common/jsp/downloadError.jsp";
		}
		return null;
	}
}
