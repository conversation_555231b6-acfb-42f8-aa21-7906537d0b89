<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="isEdit" value="${param.opFlag ne 'view'}"/>
<c:set var="title" value="${param.opFlag eq 'view' ? '查看销售预测' : '审批销售预测' }"/>
<!DOCTYPE html>
<html>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
		<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
		<title>${title }</title>
		<%@include file="/common/jsp/common.jsp"%>
		<script src="${ctx }common/hplus/js/plugins/echarts/echarts.min.js"></script>
		<script type="text/javascript" src="${ctx }business/forecasting/js/editForecastingApprove.js?v=${version}6"></script>
		<style type="text/css">
		#queryPanel .field-label {
			width: 100px;
		}
		.cols-1 .control-group{
			width: 50%;
		}
		.cols-1 input.control-text{
			width: 100%;
		}
		.cols-1 textarea.control-textarea{
			width: 110%;
		}
		.col-title {
		    font-weight: bold;
		}
		#grid td.customer-separator {
			border-bottom-width: 2px;
		}
		.tips-item .bui-select-input {
			padding: 3px 6px;
		    width: 50px!important;
		    vertical-align: baseline;
		    height: 16px!important;
		}
		#grid .def_forecast {
			color: #bbb;
		}
		#grid .ui-input-warning {
			background-color: rgb(255,192,0);
		}
		#grid .ui-input-error {
			background-color: #cc0000;
		}
		#grid .customer-forecast-block-input .control-text {
			width: 60px;
    		padding: 0 3px;
		}
		#grid .forecast-fix-block input.control-text {
			width: 40px;
		}
		.customer-forecast-block {
			float: left;
			width: 75px;
    		height: 58px;
		}
		#grid .forecast-fix-block {
			width: 55px;
		}
		.tips-item {
			float: left;
			line-height: 30px;
    		margin-right: 20px;
		}
		.tips-icon {
		    width: 16px;
		    height: 16px;
		    float: left;
		    margin: 7px 8px;
		}
		.tips-default .tips-icon {
		    background-color: #bbb;
		}
		.tips-warning .tips-icon {
		    background-color: rgb(255,192,0);
		}
		/*.tips-error .tips-icon {
		    background-color: #cc0000;
		}*/
		.ui-grid-unlocked .ui-grid-header-row .ui-grid-td-rowspan2 {
			border-left: 1px solid #fff;
		}
		.ui-grid-unlocked .ui-grid-td-rowspan4 {
			border-left: 1px solid #dddddd;
		}
		</style>
		<script type="text/javascript">
		var isEdit = ${isEdit}, id='${param.id}', forecastMonth = "${param.forecastMonth}", leftSales=${empty param.leftSales ? 0 : param.leftSales},
		worksheetId = ${param.worksheetId }, detailStatus = '${param.detailStatus}', weight='${param.weight}';
		</script>
	</head>
	<body class="gray-bg">
		<div class="content-wrapper ui-layout">
	    	<div class="content-panel header-panel ui-layout-north">
	    		<div class="header-title">${title }</div>
	    		<div class="header-btns">
	    			<c:if test="${isEdit}">
	    				<button type="button" class="btn-submit" onclick="submit();">提交</button>
	    				<button type="button" class="btn-submit" onclick="save();">暂存</button>
	    			</c:if>
						<button type="button" class="btn-back" onclick="window.location = '${ctx }business/forecasting/forecastingApprovePage.jsp?cacheParams=true&weight=${param.weight }&toItem=${param.id }';">返回</button>
	    		</div>
	    	</div>
			<div class="content-panel query-panel ui-layout-north content-panel-large" id="queryPanel">
				<div class="field-group key-search">
					<label class="field-label width-auto">搜索:</label>
					<div class="control-group">
						<input type="text" id="gKeyWord" placeholder="请输入产品名称、SKU" class="control-text" />
					</div>
				</div>
				<div class="field-group cond-adv">
					<label class="field-label">产品：</label>
					<div class="control-group">
						<input type="text" id="productSku" placeholder="请输入产品名称、首字母或SKU" name="productSku" class="control-text" />
					</div>
				</div>
				<div class="field-group cond-adv">
					<label class="field-label">产品等级：</label>
					<div class="control-group">
						<input type="hidden" id="gradeAbc" name="gradeAbc" />
					</div>
				</div>
				<div class="field-group cond-adv">
					<label class="field-label">产品包装：</label>
					<div class="control-group">
						<input type="text" id="productPack" placeholder="请输入产品包装值" class="control-text" />
					</div>
				</div>
				<div class="field-group cond-adv">
					<label class="field-label">产品创建日期：</label>
					<div class="control-group">
						<input type="text" id="dateFrom" name="dateFrom" placeholder="起始日期" class="control-text control-part2-calendar control-calendar"
								onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'dateTo\')}'});" /><span class=" control-part2-separator">至</span>
						<input type="text" id="dateTo" name="dateTo" placeholder="截止日期" class="control-text control-part2-calendar control-calendar"
								onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'dateFrom\')}'});" />		
					</div>
				</div>
				<div class="field-group cond-adv">
					<label class="field-label">预测值(L)：</label>
					<div class="control-group">
						<input type="text" id="startForecastValue" name="startForecastValue" placeholder="起始值" class="control-text control-part2-control"/><span class="control-part2-separator">至</span>
						<input type="text" id="endForecastValue" name="endForecastValue" placeholder="结束值" class="control-text control-part2-control"/>		
					</div>
				</div>
				<div class="field-group cond-adv">
					<label class="field-label">预测值异常：</label>
					<div class="control-group">
						<input type="hidden" id="forecastWarning" name="forecastWarning" value="-1"/>
					</div>
				</div>
				<div class="field-group cond-adv">
					<label class="field-label">预测月份：</label>
					<div class="control-group">
						<input type="hidden" id="forecastLeadingTime" name="forecastLeadingTime" value="-1"/>
					</div>
				</div>
				<div class="field-group cond-adv">
					<label class="field-label">经销商：</label>
					<div class="control-group">
						<input type="hidden" id="customerNameCn" name="customerNameCn" value=""/>
					</div>
				</div>
				<div class="query-btns">
					<div class="query-btn field-label">
						<button onclick="query();" class="btn-query">查询</button>
					</div>
					<div class="adv-toggle-btn">
						<a href="javascript: void(0);" onclick="var el = $(this), queryPanel = el.parents('.query-panel:first');if(queryPanel.hasClass('query-adv')){queryPanel.removeClass('query-adv');el.text('高级搜索');}else{queryPanel.addClass('query-adv');el.text('收起');}UI.Panel.getInstance($(this).parents('.ui-layout-north').first()).trigger('resize');">高级搜索</a>
					</div>
				</div>
			</div>
				<div class="content-panel ui-layout-north">
	    			<div style="float: right;">
	    				<div class="tips-item">
							<label class="field-label width-auto">单位：</label>
							<div class="control-group">
								<input type="hidden" id="packType" value="volume"/>
							</div>
						</div>
	    				<div class="tips-default tips-item"><div class="tips-icon"></div><span class="tips-desc">默认预测值</span></div>
	    				<div class="tips-warning tips-item hide"><div class="tips-icon"></div><span class="tips-desc">超出指定范围</span></div>
	    				<!-- <div class="tips-error tips-item"><div class="tips-icon"></div><span class="tips-desc">非数字</span></div> -->
	    				<div class="clear"></div>
	    			</div>
	    			<div class="clear"></div>
	    		</div>
			<div class="ui-layout-center">
				<div id="grid"></div>
			</div>
		</div>
    <div style="display:none;">
	    <div id="addSkuForecastDialog">
	    	<div id="addSkuForecastForm" class="cols-1">
	    		<input type="hidden" name="parentRowId"/>
	     		<div class="field-group">
	     			<label class="field-label"><span class="required-flag">*</span>产品：</label>
	     			<div class="control-group">
	     				<input type="hidden" name="sku" class="control-text"/>
	     			</div>
	     		</div>
	    	</div>  
	    </div>
	   </div>
		<div id="salesHistoryDialogWrapper" style="display: none;">
			<div id="salesHistoryDialog" class="hidden">
				<div class="row">
					<div class="col-sm-12">
						<div class="echarts" id="salesHistoryChart" style="height: 300px;"></div>
					</div>
				</div>
			</div>
		</div>
	</body>
</html>
