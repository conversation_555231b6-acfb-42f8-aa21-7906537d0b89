select p.name,p.pack/convert(float, p.capacity) "计算瓶数",(select count(1) from wx_t_oem_product_packaging_code pc2 where pc2.code2=pc.code2) "中商瓶数",pc.code2,
(select substring((select ','+pc3.code1 from wx_t_oem_product_packaging_code pc3
	where pc3.code2=pc.code2 FOR XML PATH,TYPE
 ).value('.','varchar(max)'),2,100000)) code1s,
 case when exists (select 1 from wx_t_out_stock_line osl where osl.code=pc.code2) then '是' else '否' end "是否已扫",
 pc.* from wx_t_oem_product_packaging_code pc left join wx_t_oem_product_packaging pp on pp.id=pc.packaging_id 
left join wx_t_product p on p.sku=pp.product_id
where convert(float, p.capacity)>0 and p.pack/convert(float, p.capacity)>2 and len(pc.code2)>1
and not exists (select 1 from wx_t_oem_product_packaging_code pc1 where pc1.code2=pc.code2 and pc1.code1>pc.code1)
and round(p.pack/convert(float, p.capacity),0)!=(select count(1) from wx_t_oem_product_packaging_code pc2 where pc2.code2=pc.code2)
--and pc.code2='102952010703211002501097'