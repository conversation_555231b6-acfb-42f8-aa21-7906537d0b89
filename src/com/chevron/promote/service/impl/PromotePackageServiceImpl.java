package com.chevron.promote.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.chevron.promote.dao.PromotePacksMaterialsMapper;
import com.chevron.promote.dao.PromotePlanBatchMapper;
import com.chevron.promote.model.BasePromote;
import com.chevron.promote.model.PromotePacksMaterials;
import com.chevron.promote.model.PromotePlanBatch;
import com.chevron.promote.service.IPromotePackageService;
import com.sys.file.model.WxAttFile;
@Service
public class PromotePackageServiceImpl extends BasePromote implements IPromotePackageService {
	private static Logger log = LoggerFactory.getLogger(PromotePackageServiceImpl.class);
	private static final String RDLB="rdlbLst";//入店礼包
	private static final String GDLB="gdlbLst";//攻店礼包
	private static final String TGHWLB="tghwlbLst";//推广会物料包
	private static final String XFZHDBCB="xfzhdbcbLst";//消费者互动补充包	
	private static final String LYWLB="lywlbLst";//消费者互动补充包	
	private static final String LST = "Lst";
	@Resource
	PromotePacksMaterialsMapper promotePacksMapper;
	@Resource
	PromotePlanBatchMapper promotePlanBatchMapper;
	@Override
	public Map<String, Object> getPromotePacksMaterials(String packageType,
			Long batchId) {
		log.info("PromotePackageServiceImpl getPromotePacksMaterials packageType：{},batchId:{}",packageType,batchId);
		Map<String,Object> resultMap = new HashMap<String,Object>(); 
		Map<String,Object> reqMap = new HashMap<String,Object>();
		try
		{
			//首先从批次表中判断是否存在暂存或已提交的，，如果有，直接给提示
			if(null==batchId)
			{
				reqMap.put("nowdate", new Date());
				List<PromotePlanBatch> tmplst = promotePlanBatchMapper.queryPromotePlanBatchList(reqMap);
				if(null!=tmplst && !tmplst.isEmpty())
				{
					resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
					resultMap.put(RESULT_ERROR_MSG_KEY, "当年计划已存在不需创建!");
					return resultMap;
				}
			}
			resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
			
			reqMap.put("packageType", packageType);
			reqMap.put("batchId", batchId);
			reqMap.put("AttSourceType", WxAttFile.SourceType_PromoteActivity);
			List<PromotePacksMaterials> lst =  promotePacksMapper.getPromotePacksMaterials(reqMap);
			//如果是已经有批次，看选了哪些礼包，，用于返回的时候，显示对应已经被选中的礼包
			if(null!=batchId)
			{
				for(PromotePacksMaterials promotePacksMaterial: lst)
				{
					Long planBatchSelectMaterialId = promotePacksMaterial.getPlanBatchSelectMaterialId();
					if(null!=planBatchSelectMaterialId)
					{
						promotePacksMaterial.setIsSelectedFlag(1);
						promotePacksMaterial.setMaterialsDefaultCount(promotePacksMaterial.getPlanBatchSelectMaterialCount());
						promotePacksMaterial.setMaterialsPrice(promotePacksMaterial.getMaterialsPriceNew());
					}
				}
			}
			
			//根据礼包code查询单个  对应礼包物料
			if(null!=packageType && !packageType.isEmpty())
			{
				resultMap.put(RESULT_LST_KEY, lst);
				return resultMap;
			}
			
			//如果没有批次，且非查询单个的情况，返回所有，并分组排序
			resultMap = doHandlePackageMaterial(lst,batchId);
			
		
		}catch (Exception e) {
			e.printStackTrace();
			log.error("getPromotePacksMaterials"+e.getLocalizedMessage());
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, RESULT_ERROR_MSG+e.getMessage());
		}
		return resultMap;
	}
	private Map<String, Object> doHandlePackageMaterial(
			List<PromotePacksMaterials> lst,Long batchId) {
		log.info("PromotePackageServiceImpl doHandlePackageMaterial lst:",lst);
		Map<String, Object> resultMap = new HashMap<String,Object>();
		//没有配置礼包物料
		if(null==lst || lst.isEmpty())
		{
			resultMap.put(RDLB, new ArrayList<PromotePacksMaterials>());
			resultMap.put(GDLB, new ArrayList<PromotePacksMaterials>());
			resultMap.put(TGHWLB, new ArrayList<PromotePacksMaterials>());
			resultMap.put(XFZHDBCB, new ArrayList<PromotePacksMaterials>());
			resultMap.put(LYWLB, new ArrayList<PromotePacksMaterials>());
			return resultMap;
		}
		
		String lastPacksCode = "";
		List<PromotePacksMaterials> tmpLst = null;
		for(PromotePacksMaterials promotePacksMaterials:lst)
		{
			String packsCode = promotePacksMaterials.getPacksCode();
			if(null==batchId)//首次添加
			{
				if(promotePacksMaterials.getMaterialsDefaultCount()>0)
				{
					promotePacksMaterials.setIsSelectedFlag(1);
				}
				
			}
			if(!lastPacksCode.equals(packsCode))
			{
				tmpLst = new ArrayList<PromotePacksMaterials>();
			}
			tmpLst.add(promotePacksMaterials);
			resultMap.put(packsCode.toLowerCase()+LST, tmpLst);
			lastPacksCode = packsCode;
			
		}
		resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
		return resultMap;
	}
}
