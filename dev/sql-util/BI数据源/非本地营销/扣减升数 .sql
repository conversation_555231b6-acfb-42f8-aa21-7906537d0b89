select nld.id/*扣减ID*/, nld.sku/*扣减产品编码*/, nld.quarter/*扣减季度，-1表示全年，此时effect_flag一定是2*/, nld.liters/*扣减升数*/,
nff.from_distributor_id/*处罚经销商*/,npr.channel/*扣减渠道，1-金富力，2-德乐*/,npr.attribute3/*扣减年份*/,nld.effect_flag,/*作用标记。1-季度，2-年奖，3-季度+年奖*/
case when nld.update_time>ei.deliver_time then nld.update_time else ei.deliver_time end effective_time/*生效时间*/
from wx_t_non_local_liters_deduction nld 
left join wx_t_non_local_mkt_flow_pro_res npr on nld.punish_id=npr.id
left join wx_t_non_local_mkt_flow_form nff on nff.id=npr.flow_id
left join wx_t_non_local_mkt_emaile_info ei on ei.source_id=npr.id and ei.delete_flag=0
where nld.status=1/*已发布*/ and nld.delete_flag=0/*未删除*/
and ei.deliver_status=2/*处罚邮件已发放*/