select --* 
case when d.dic_type_code='workshop.type1' then '金富力' 
when d.dic_type_code='workshop.type2' then '德乐'
when d.dic_type_code='workshop.type4' then '工程机械'
end "品牌",
dic_item_name "类型"
from wx_t_dic_item d where dic_type_code like 'workshop.type%' and status=1
order by d.dic_type_code, d.sort_numb

select distinct w.customer_type, w.business_weight,
case when w.business_weight=1 then '金富力'
when w.business_weight=2 then '德乐'
when w.business_weight=4 then '工程机械'
else convert(varchar(20),w.business_weight) end "品牌",
w.type "类型"
 from wx_t_work_shop w 
where w.delete_flag=0 and w.customer_type=1 and w.type!='' and 'workshop.type' + convert(varchar(20),w.business_weight)+'/' + w.type not in (select dic_type_code+'/'+dic_item_name name
from wx_t_dic_item d where dic_type_code like 'workshop.type%' and status=1)
order by w.customer_type, w.business_weight

select --* 
case when d.dic_type_code='Fleet.type1' then '金富力' 
when d.dic_type_code='Fleet.type2' then '德乐'
when d.dic_type_code='Fleet.type4' then '工程机械'
end "品牌",
dic_item_name "类型"
from wx_t_dic_item d where dic_type_code like 'Fleet.type%' and status=1
order by d.dic_type_code, d.sort_numb

select distinct w.customer_type, w.business_weight,
case when w.business_weight=1 then '金富力'
when w.business_weight=2 then '德乐'
when w.business_weight=4 then '工程机械'
else convert(varchar(20),w.business_weight) end "品牌",
w.type "类型"
 from wx_t_work_shop w 
where w.delete_flag=0 and w.customer_type=2 and w.type!='' and 'Fleet.type' + convert(varchar(20),w.business_weight)+'/' + w.type not in (select dic_type_code+'/'+dic_item_name name
from wx_t_dic_item d where dic_type_code like 'Fleet.type%' and status=1)
order by w.customer_type, w.business_weight

