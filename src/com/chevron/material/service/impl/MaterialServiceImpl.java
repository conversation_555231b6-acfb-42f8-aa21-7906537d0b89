package com.chevron.material.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.chevron.material.dao.CouponOptioMapper;
import com.chevron.material.dao.CouponOptionDetailMapper;
import com.chevron.material.dao.WXTMaterialApplicationDetailVoMapper;
import com.chevron.material.dao.WXTMaterialApplicationLogisticsVoMapper;
import com.chevron.material.dao.WXTMaterialApplicationVoMapper;
import com.chevron.material.dao.WXTMaterialImageVoMapper;
import com.chevron.material.dao.WXTMaterialInventoryLogVoMapper;
import com.chevron.material.dao.WXTMaterialInventoryVoMapper;
import com.chevron.material.dao.WXTMaterialSkuVoMapper;
import com.chevron.material.dao.WXTMaterialVoMapper;
import com.chevron.material.dao.WXTMaterialWarehouseOutDetailVoMapper;
import com.chevron.material.dao.WXTMaterialWarehouseOutVoMapper;
import com.chevron.material.dao.WXTMaterialWarehouseVoMapper;
import com.chevron.material.dto.SMCUpdateInventoryDetailDto;
import com.chevron.material.dto.SMCUpdateInventoryDto;
import com.chevron.material.dto.SMCUpdateLogisticsDetailDto;
import com.chevron.material.dto.SMCUpdateLogisticsDto;
import com.chevron.material.dto.SMCUpdateMaterialStatusDetailDto;
import com.chevron.material.dto.SMCUpdateMaterialStatusDto;
import com.chevron.material.dto.SmartCommItem;
import com.chevron.material.dto.SmartCommItemImage;
import com.chevron.material.dto.SmartCommItemPrice;
import com.chevron.material.dto.SmartCommItemSku;
import com.chevron.material.model.ApplicationState;
import com.chevron.material.model.CouponOptio;
import com.chevron.material.model.CouponOptionDetail;
import com.chevron.material.model.MaterialInventoryDTO;
import com.chevron.material.model.MaterialParams;
import com.chevron.material.model.MaterialSource;
import com.chevron.material.model.StockType;
import com.chevron.material.model.WXTMaterialApplicationLogisticsVo;
import com.chevron.material.model.WXTMaterialApplicationLogisticsVoExample;
import com.chevron.material.model.WXTMaterialCtrlVo;
import com.chevron.material.model.WXTMaterialImageVo;
import com.chevron.material.model.WXTMaterialImageVoExample;
import com.chevron.material.model.WXTMaterialInventoryLogVo;
import com.chevron.material.model.WXTMaterialInventoryVo;
import com.chevron.material.model.WXTMaterialInventoryVoExample;
import com.chevron.material.model.WXTMaterialSkuVo;
import com.chevron.material.model.WXTMaterialSkuVoExample;
import com.chevron.material.model.WXTMaterialVo;
import com.chevron.material.model.WXTMaterialVoExample;
import com.chevron.material.model.WXTMaterialWarehouseOutDetailVo;
import com.chevron.material.model.WXTMaterialWarehouseOutVo;
import com.chevron.material.model.WXTMaterialWarehouseVo;
import com.chevron.material.model.WXTMaterialWarehouseVoExample;
import com.chevron.material.service.ApplicationService;
import com.chevron.material.service.MaterialService;
import com.chevron.material.service.SmartCommService;
import com.chevron.material.util.ApplicationUtil;
import com.chevron.pms.model.RegionVo;
import com.chevron.pms.service.PartnerService;
import com.chevron.pms.service.RegionService;
import com.chevron.point.dto.PointType;
import com.chevron.sellin.model.CouPonInfo;
import com.chevron.sellin.model.PointBaseInfo;
import com.common.constants.Constants;
import com.common.exception.WxPltException;
import com.common.util.ContextUtil;
import com.common.util.DateUtil;
import com.common.util.ResponseMap;
import com.common.util.SpringUtils;
import com.common.util.StringUtils;
import com.google.gson.Gson;
import com.sys.auth.business.OperationPermissionBizService;
import com.sys.auth.model.WxTUser;
import com.sys.dealer.dao.DealerBusinessFunMapper;
import com.sys.dealer.model.DealerBusinessFun;
import com.sys.dic.dao.DicItemVoMapper;
import com.sys.dic.service.DicService;
import com.sys.file.web.FileManager;
import com.sys.log.model.Log;
import com.sys.log.service.LogService;
import com.sys.log.util.LogTask;
import com.sys.log.util.LogUtils;
import com.sys.organization.dao.OrganizationVoMapper;
import com.sys.organization.model.OrganizationVo;
import com.sys.organization.model.PartnerView;
import com.sys.properties.service.SequenceTypes;
import com.sys.properties.service.WxTPropertiesService;
import net.sf.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
@Service
public class MaterialServiceImpl implements MaterialService {

	private static Logger log = LoggerFactory.getLogger(MaterialServiceImpl.class);

	@Autowired
	LogService logService;

	@Autowired
	WXTMaterialInventoryVoMapper wxtMaterialInventoryVoMapper;

	@Autowired
	WXTMaterialInventoryLogVoMapper wxtMaterialInventoryLogVoMapper;

	@Autowired
	WXTMaterialApplicationLogisticsVoMapper wxtMaterialApplicationLogisticsVoMapper;

	@Autowired
	private WXTMaterialWarehouseOutVoMapper wxtMaterialWarehouseOutVoMapper;

	@Autowired
	ApplicationService applicationService;
	@Autowired
	WXTMaterialVoMapper wxtMaterialVoMapper;

	@Resource
	private WxTPropertiesService wxTPropertiesServiceImpl;

	@Autowired
	WXTMaterialImageVoMapper wxtMaterialImageVoMapper;

	@Autowired
	WXTMaterialApplicationDetailVoMapper applicationDetailVoMapper;

	@Autowired
	SmartCommService smartCommService;

	@Autowired
	FileManager fileManager;

	@Autowired
	RegionService regionService;

	@Autowired
	WXTMaterialSkuVoMapper wxtMaterialSkuVoMapper;

	@Autowired
	WXTMaterialWarehouseVoMapper wxtMaterialWarehouseVoMapper;

	@Autowired
	WXTMaterialWarehouseOutDetailVoMapper wxtMaterialWarehouseOutDetailVoMapper;

    @Resource
    OrganizationVoMapper organizationVoMapper;

    @Autowired
    WXTMaterialApplicationVoMapper wxtMaterialApplicationVoMapper;

    @Autowired
    private DealerBusinessFunMapper dealerBusinessFunMapper;

    @Autowired
    private DicItemVoMapper dicItemVoMapper;

    @Autowired
    private DicService dicService;

    @Autowired
    private CouponOptioMapper couponOptioMapper;

    @Autowired
    private CouponOptionDetailMapper couponOptionDetailMapper;

	@Autowired
	private OperationPermissionBizService operationPermissionBizService;

	@Resource
	private PartnerService partnerServiceImpl;


	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public Map<String, Object> updateInventoryInternal(String materialCode,String materialSkuCode,
			Long warehouseId, Long changeQty, Long originalQty) {

		Date now = DateUtil.getCurrentDate();
		Long stockQty = originalQty + changeQty;
		String logAttribute1 =  "管理员手动入库";
		String logAttribute2 =  "" + originalQty +" -> " + stockQty;
		boolean success = updateInventory(now, materialCode, materialSkuCode, warehouseId, changeQty, null,logAttribute1, logAttribute2, null);
		if(!success) {
			return getFailResultMap("库存更新失败, 该物料不存在, "+materialCode+", " + materialSkuCode);
		}
		return getSuccessResultMap("成功");
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public Map<String, Object> updateInventory(SMCUpdateInventoryDto dto) {
		// 写日志
		String data = "" + getHttpSource() + "|"+  JSONObject.fromObject(dto).toString();
		pushLog("updateInventory", "start", data);

		// 校验
		String vResult = validateInventoryDto(dto);
		if (null != vResult) {
			pushLog("updateInventory", "end: " +vResult, data);
			return getFailResultMap(vResult);
		}

		// 处理逻辑
		Date now = DateUtil.getCurrentDate();
		String chevronOrderCode = dto.getChevronOrderCode();
		boolean isOk = true;
		Long warehouseId = getEnjoygifsWarehouseId();
		List<SMCUpdateInventoryDetailDto> errorList= null;

		try {
			// 更新类型: "1": 因订单原因变化库存; "2":因其他原因变化库存(上架/进货等)
			if ("1".equals(dto.getUpdateType())) {
				// 订单扣除的情况, 只更新实际库存
				SMCUpdateInventoryDetailDto[] detailList = dto.getDetailList();
				errorList = new ArrayList<SMCUpdateInventoryDetailDto>();

				for (SMCUpdateInventoryDetailDto detail : detailList) {
					String materialCode = detail.getMaterialCode();
					String materialSkuCode = detail.getMaterialSkuCode();
					Long changeQty = detail.getChangeQty();
					String logAttribute1 = "SMC订单扣除" + chevronOrderCode;
					String logAttribute2 = "" + detail.getOriginalQty() +" -> " + detail.getStockQty();

					// 更新实际库存
					boolean success = updateInventory(now, materialCode, materialSkuCode, warehouseId, changeQty, StockType.REAL, logAttribute1, logAttribute2, chevronOrderCode);
					if (!success) {
						isOk = false;
						errorList.add(detail);
						pushLog("updateInventory", "end: 物料不存在, 或库存更新失败, " + materialCode + ", " + materialSkuCode, data);
						applicationService.updateApplicationStatus(null, chevronOrderCode, ApplicationState.OUTBOUNDFAILED, materialCode, materialSkuCode);
						continue;
					}
					applicationService.updateApplicationStatus(null, chevronOrderCode, ApplicationState.OUTBOUND, materialCode, materialSkuCode);
				}
			} else {
				// 库存同步的情况, 同时更新实际和虚拟库存
				SMCUpdateInventoryDetailDto[] detailList = dto.getDetailList();
				errorList = new ArrayList<SMCUpdateInventoryDetailDto>();

				for (SMCUpdateInventoryDetailDto detail : detailList) {
					String materialCode = detail.getMaterialCode();
					String materialSkuCode = detail.getMaterialSkuCode();
					Long changeQty = detail.getChangeQty();
					String logAttribute1 = "SMC库存同步变更(非订单)";
					String logAttribute2 = "" + detail.getOriginalQty() +" -> " + detail.getStockQty();

					boolean success = updateInventory(now, materialCode, materialSkuCode, warehouseId, changeQty, null, logAttribute1, logAttribute2, null);
					if (!success) {
						isOk = false;
						pushLog("updateInventory", "end: 物料不存在, 或库存更新失败, " + materialCode + ", " + materialSkuCode, data);
						errorList.add(detail);
						continue;
					}
				}
			}

			if (!isOk) {
				return getFailResultMap("库存更新失败, 该物料不存在,"+ new Gson().toJson(errorList));
			}

		} catch(Exception e) {
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			log.error("updateInventory, 异常发生: ", e);
			pushLog("updateInventory", "end: 异常发生:" + e.getMessage(), e.getStackTrace().toString());
			return getFailResultMap("内部错误");
		}

		pushLog("updateInventory", "end: 库存更新成功", data);
		return getSuccessResultMap("成功");
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public Map<String, Object> updateMaterialStatus(SMCUpdateMaterialStatusDto dto) {

		// 写日志
		String data = "" + getHttpSource() + "|"+ JSONObject.fromObject(dto).toString();
		pushLog("updateMaterialStatus", "start", data);

		// 校验
		String vResult = validateMaterialStatus(dto);
		if (null != vResult) {
			pushLog("updateMaterialStatus", "end: " +vResult, data);
			return getFailResultMap(vResult);
		}

		Date now = DateUtil.getCurrentDate();
		Long userId = ContextUtil.getCurUserId();

		try {
			// 处理逻辑
			SMCUpdateMaterialStatusDetailDto[] detailList = dto.getDetailList();
			for (SMCUpdateMaterialStatusDetailDto detail : detailList) {
				String materialCode = detail.getMaterialCode();
				WXTMaterialVoExample example = new WXTMaterialVoExample();
				example.createCriteria().andMaterialCodeEqualTo(materialCode);
				List<WXTMaterialVo> selectByExample = wxtMaterialVoMapper.selectByExample(example);
				if (null == selectByExample || selectByExample.isEmpty()) {
					pushLog("updateMaterialStatus", "end: 物料不存在:" + materialCode, data);
					return getFailResultMap("该物料不存在" + materialCode);
				}
			}

			for (SMCUpdateMaterialStatusDetailDto detail : detailList) {
				WXTMaterialVo record = new WXTMaterialVo();
				record.setSmcStatus(detail.getStatus());
				record.setLastUpdatedBy(userId);
				record.setLastUpdateTime(now);
				WXTMaterialVoExample example = new WXTMaterialVoExample();
				example.createCriteria().andMaterialCodeEqualTo(detail.getMaterialCode());
				wxtMaterialVoMapper.updateByExampleSelective(record, example);
			}

		} catch(Exception e) {
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			log.error("updateMaterialStatus, 异常发生: ", e);
			pushLog("updateMaterialStatus", "end: 异常发生:" + e.getMessage(), e.getStackTrace().toString());
			return getFailResultMap("内部错误");
		}

		pushLog("updateMaterialStatus", "end: 物料上下架状态更新成功", data);
		return getSuccessResultMap("成功");
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public Map<String, Object> updateLogisticsInfo(SMCUpdateLogisticsDto dto) {
		String data = "" + getHttpSource() + "|"+ JSONObject.fromObject(dto).toString();
		// 写日志
		pushLog("updateLogisticsInfo", "start", data);

		// 校验
		String vResult = validateLogisticsDto(dto);
		if (null != vResult) {
			pushLog("updateLogisticsInfo", "end: " + vResult, data);
			return getFailResultMap(vResult);
		}

		try {
			// 处理逻辑
			WXTMaterialApplicationLogisticsVoExample example = new WXTMaterialApplicationLogisticsVoExample();
			example.createCriteria().andShipmentCodeEqualTo(dto.getChevronOrderCode());
			int deleteNum = wxtMaterialApplicationLogisticsVoMapper.deleteByExample(example);
			SMCUpdateLogisticsDetailDto[] detailList = dto.getDetailList();
			Date now = DateUtil.getCurrentDate();
			int res = 0;
			for (SMCUpdateLogisticsDetailDto detail : detailList) {
				WXTMaterialApplicationLogisticsVo record = new WXTMaterialApplicationLogisticsVo();
				record.setShipmentCode(dto.getChevronOrderCode());
				record.setSmcOrderCode(dto.getSmcOrderCode());
	
				record.setExpressCompany(detail.getCom());
				record.setExpressNumber(detail.getNu());
				record.setExpressContext(detail.getContext());
				record.setExpressState(detail.getState());
				record.setExpressComments(detail.getComments());
				record.setExpressMessage(detail.getMessage());
				record.setTrackingTime(detail.getTime());
	
				record.setDeleteFlag(false);
				record.setLastUpdatedBy(999L);
				record.setLastUpdateTime(now);
				record.setCreatedBy(999L);
				record.setCreationTime(now);
				res += wxtMaterialApplicationLogisticsVoMapper.insertSelective(record);
			}
			if (res > 0 && deleteNum == 0) {
				applicationService.updateApplicationStatus(null, dto.getChevronOrderCode(), ApplicationState.SHIPPING, null, null);
			}
			pushLog("updateLogisticsInfo", "end: 物流更新成功", data);
		} catch(Exception e) {
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			log.error("updateLogisticsInfo, 异常发生: ", e);
			pushLog("updateLogisticsInfo", "end: 异常发生:" + e.getMessage(), e.getStackTrace().toString());
			return getFailResultMap("内部错误");
		}
		return getSuccessResultMap("成功");
	}

	/**
	 * 添加/修改物料信息
	 * @param params
	 * @return Map
	 */
	@Override
	public Map<String, Object> addMaterial( WXTMaterialVo params) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		HttpServletRequest request = ((ServletRequestAttributes)RequestContextHolder.getRequestAttributes()).getRequest();
		String materialName = params.getMaterialName();
		String materialType = params.getMaterialType();
		String materialSource = params.getMaterialSource();
		String materialImages = params.getMaterialImages();
		BigDecimal materialPrice = params.getMaterialPrice();
		if(null == materialPrice) {
			materialPrice = new BigDecimal("0.00");
		}
		String materialDesc = params.getMaterialDesc();
		String materialAfterSalesInfo = params.getMaterialAfterSalesInfo();
		String materialUnit = params.getMaterialUnit();
		Date lastUpdateTime = params.getLastUpdateTime();

		// 与SMC集成新添加录入字段
		String materialNameEn = params.getMaterialNameEn();
		String sellingPoint = params.getSellingPoint();
		String sellingPointEn = params.getSellingPointEn();
		Integer materialSizel = params.getMaterialSizel();
		if(null == materialSizel) {
			materialSizel =0;
		}
		Integer materialSizew = params.getMaterialSizew();
		if(null == materialSizew) {
			materialSizew =0;
		}
		Integer materialSizeh = params.getMaterialSizeh();
		if(null == materialSizeh) {
			materialSizeh =0;
		}
		BigDecimal materialWeight = params.getMaterialWeight();
		if(null == materialWeight) {
			materialWeight =new BigDecimal("0.00");
		}
		// 物料打样周期
		Integer materialMakePeriod =params.getMaterialMakePeriod();
		if(null == materialMakePeriod || 0 == materialMakePeriod) {
			materialMakePeriod = 30;
		}
		// 物料大货周期
		Integer materialProductionPeriod =params.getMaterialProductionPeriod();
		if(null == materialProductionPeriod || 0 == materialProductionPeriod) {
			materialProductionPeriod = 365;
		}
		Boolean isNew = null == params.getId();
		WXTMaterialVo record = new WXTMaterialVo();
		record.setMaterialName(materialName);
		record.setMaterialNameEn(materialNameEn);
		record.setMaterialType(materialType);
		record.setMaterialPicture(0L);
		record.setMaterialPrice(materialPrice);
		record.setMaterialDesc(materialDesc);
		record.setMaterialAfterSalesInfo(materialAfterSalesInfo);
		record.setMaterialDesc(materialDesc);
		record.setMaterialUnit(materialUnit);
		record.setSellingPoint(sellingPoint);
		record.setSellingPointEn(sellingPointEn);
		record.setMaterialSizel(materialSizel);
		record.setMaterialSizew(materialSizew);
		record.setMaterialSizeh(materialSizeh);
		record.setMaterialWeight(materialWeight);
		record.setMaterialMakePeriod(materialMakePeriod);
		record.setMaterialProductionPeriod(materialProductionPeriod);
		record.setMaterialSource(materialSource);
		record.setMaterialCategory(params.getMaterialCategory());
		record.setExtFlag(params.getExtFlag());
		record.setPointMode(params.getPointMode() != null && params.getPointMode());
		//查看是否是系统已经配置的积分类型，如果不是就是促销活动的积分类型
        PointType pointType = PointType.getByApplicationType(params.getPointType());
        if(pointType==null){
            record.setExtFlag(params.getExtFlag() == null ? 2 : (params.getExtFlag() | 2));
        }
        record.setPointType(params.getPointType());
		record.setOverLimit(params.getOverLimit() != null && params.getOverLimit());
		record.setIsLeftover(params.getIsLeftover() != null && params.getIsLeftover());
		record.setShowStockQty(params.getShowStockQty() != null && params.getShowStockQty());
		record.setIsPresale(params.getIsPresale() != null && params.getIsPresale());
        List<PartnerView> visiblePartners = params.getVisiblePartners();
        String invisibleDealer = "";
        if (CollectionUtil.isNotEmpty(visiblePartners)) {
            ArrayList<Long> visiblePartnerIds = new ArrayList<Long>();
            for (PartnerView visiblePartner : visiblePartners) {
                visiblePartnerIds.add(visiblePartner.getId());
            }
            invisibleDealer = "|" + CollectionUtil.join(visiblePartnerIds, "|") + "|";
        }
        record.setInvisibleDealer(invisibleDealer);
		record.setInvisibleGroup(params.getInvisibleGroup());
        if(Constants.VIRTUAL_MATERIAL_TYPE.equals(params.getMaterialType())){
            record.setAttribute2(params.getVirtualGiftBusiness());
            record.setAttribute3(params.getVirtualGiftSupplier());
            record.setAttribute4(params.getActualAmount().toString());
            record.setAttribute5(params.getVirtualGiftCode());
        }
		record.setDeleteFlag(false);
		Date now = DateUtil.getCurrentDate();
		Long userId = ContextUtil.getCurUserId();
		record.setLastUpdatedBy(userId);
		record.setLastUpdateTime(DateUtil.getCurrentDate());
		boolean isSuccess = false;
		if (isNew) {
			// 那个materialCode是getSequenceByType里面通过SELECT NEXT VALUE FOR获得的
			record.setMaterialCode("M" + wxTPropertiesServiceImpl.getSequenceByType(SequenceTypes.MATERIAL_CODE,7,1));
			record.setCreatedBy(userId);
			record.setCreationTime(now);
			record.setState("NEW");
			record.setCustEmail("<EMAIL>");
			record.setSupplierEmail("<EMAIL>");
			WXTMaterialVoExample example = new WXTMaterialVoExample();
			//example.createCriteria().andMaterialNameEqualTo(materialName);
			//add by bo.liu 0425
			// 这这样查找的英文名或者中文名是否重复
			WXTMaterialVoExample example2 = new WXTMaterialVoExample();
			if(null==params.getPointType() || params.getPointType().isEmpty())
			{
				example.createCriteria().andMaterialNameEqualTo(materialName).andPointTypeIsNull();
				example2.createCriteria().andMaterialNameEnEqualTo(materialNameEn).andPointTypeIsNull();
			}else
			{
				example.createCriteria().andMaterialNameEqualTo(materialName).andPointTypeEqualTo(params.getPointType());
				example2.createCriteria().andMaterialNameEnEqualTo(materialNameEn).andPointTypeEqualTo(params.getPointType());
			}
			List<WXTMaterialVo> selectByExample = wxtMaterialVoMapper.selectByExample(example);
			if (null != selectByExample && !selectByExample.isEmpty()) {
				isSuccess = false;
				resultMap.put("success", isSuccess);
				resultMap.put("msg", "创建物料失败, 物料名称重复.");
				return resultMap;
			}
			//end

			//WXTMaterialVoExample example2 = new WXTMaterialVoExample();
			//example2.createCriteria().andMaterialNameEnEqualTo(materialNameEn);
			List<WXTMaterialVo> selectByExample2 = wxtMaterialVoMapper.selectByExample(example2);
			if (null != selectByExample2 && !selectByExample2.isEmpty()) {
				isSuccess = false;
				resultMap.put("success", isSuccess);
				resultMap.put("msg", "创建物料失败, 物料英文名称重复.");
				return resultMap;
			}
			int insertMaterial = wxtMaterialVoMapper.insertSelective(record);

			isSuccess = insertMaterial == 1;
			resultMap.put("msg", isSuccess? "新建成功" : "创建物料失败,请刷新后再尝试.");
		} else {
			Long id = params.getId();
			WXTMaterialVoExample example = new WXTMaterialVoExample();
			example.createCriteria().andIdEqualTo(id).andLastUpdateTimeEqualTo(lastUpdateTime);
			record.setState("UPDATED");
			int updateMaterial = wxtMaterialVoMapper.updateByExampleSelective(record, example);
			record.setId(id);
			isSuccess = updateMaterial == 1;
			resultMap.put("msg", isSuccess? "更新成功" : "更新物料失败,请刷新后再尝试.");
		}

		if (isSuccess) {
			// 过来的"materialImages": "287405,0|287406,1|"是这样的内容
			updateMaterialImageList(record.getId(), materialImages);
			// 更新sku的列表
			updateMaterialSkuList(record.getId(), record.getMaterialCode(), params.getSkuList());

			// 更新成功, 则同步到SMC
			// 20171121 Ervin 只有礼享网的物料才需要同步到礼享网
			if (MaterialSource.isFromEnjoygifts(materialSource)) {
				Boolean result = sync2SmartComm(request, record.getId(), isNew);
				isSuccess = result;
				if (!result) {
					resultMap.put("msg","更新到数据库成功, 但是同步到SMC失败");
				}
			}

			// 20171121 Ervin 雪佛龙其他来源物料， 不需要推送礼享网，由本系统维护
			// 默认设置为上架 1——表示上架，0——表示下架
			if(!MaterialSource.isFromEnjoygifts(materialSource)) {
				WXTMaterialVo record2 = new WXTMaterialVo();
				record2.setId(record.getId());
				record2.setSmcMaterialCode(materialSource);
				record2.setState("SYNCED");
				// 新建物料 或 第一次同步物料的场合
				if (isNew) {
					record2.setSmcStatus(1);
				}
				wxtMaterialVoMapper.updateByPrimaryKeySelective(record2);
			}

		}
		resultMap.put("success", isSuccess);
		resultMap.put("data", record);
		return resultMap;
	}

	/**
	 * 更新库存
	 * @param time 时间
	 * @param userId 用户ID
	 * @param materialCode 物料编码
	 * @param materialSkuCode 物料SKU编码
	 * @param warehouseId 仓库ID
	 * @param changeQty
	 * @param stockType
	 * @return
	 */
	private boolean updateInventory(Date time, String materialCode, String materialSkuCode,Long warehouseId,
			Long changeQty, StockType stockType, String attribute1, String attribute2, String applicationCode) {

		Long userId = ContextUtil.getCurUserId();
		WXTMaterialInventoryVo record = new WXTMaterialInventoryVo();
		// materialCode，materialSkuCode，warehouseId这些都是wx_t_material_inventory表更新时候的查询条件
		record.setMaterialCode(materialCode);
		record.setMaterialSkuCode(materialSkuCode);
		record.setWarehouseId(warehouseId);
		record.setLastUpdatedBy(userId);
		record.setLastUpdateTime(time);

		int result = 0;
		if (null == stockType) {
			record.setStockQty(changeQty);
			record.setVirtualStockQty(changeQty);
			//  这个updateInventorySync是把STOCK_QTY，VIRTUAL_STOCK_QTY，加的这个changeQty的注意
			//  STOCK_QTY = STOCK_QTY + #{stockQty,jdbcType=BIGINT},
			//  VIRTUAL_STOCK_QTY =  VIRTUAL_STOCK_QTY + #{virtualStockQty,jdbcType=BIGINT},
			result = wxtMaterialInventoryVoMapper.updateInventorySync(record);
			if (result > 0) {
				pushInventoryLog(time, materialCode, materialSkuCode, warehouseId, StockType.REAL, changeQty, attribute1, attribute2, applicationCode);
				pushInventoryLog(time, materialCode, materialSkuCode, warehouseId, StockType.VIRTUAL, changeQty, attribute1, attribute2, applicationCode);
			}
		}

		if (StockType.REAL == stockType) {
			record.setStockQty(changeQty);
			result = wxtMaterialInventoryVoMapper.updateRealInventory(record);
			if (result > 0) {
				pushInventoryLog(time, materialCode, materialSkuCode, warehouseId, stockType, changeQty, attribute1, attribute2, applicationCode);
			}
		}

		return result > 0;
	}

	/**
	 * 推送出入库日志
	 * @param time 时间
	 * @param userId 用户ID
	 * @param materialCode 物料编码
	 * @param materialSkuCode 物料SKU编码
	 * @param stockType 库存类型
	 * @param changeQty 变更数量
	 * @param attribute1 备注1：库存变更业务原因
	 * @param attribute2 备注2：库存变化前后值
	 */
	private void pushInventoryLog(Date time, String materialCode, String materialSkuCode, Long warehouseId,
			StockType stockType, Long changeQty, String attribute1, String attribute2, String applicationCode) {
		String _stockType = "";
		if (null != stockType) {
			_stockType = stockType.getValue();
		}
		Long userId = ContextUtil.getCurUserId();
		WXTMaterialInventoryLogVo record3 = new WXTMaterialInventoryLogVo();
		record3.setMaterialCode(materialCode);
		record3.setMaterialSkuCode(materialSkuCode);
		record3.setApplicationCode(applicationCode); //TODO 若订单改为一个订单多个分配，应修改为发运单号
		record3.setWarehouseId(warehouseId);
		record3.setStockType(_stockType);
		record3.setChangeQty(changeQty);
		record3.setAttribute1(attribute1);
		record3.setAttribute2(attribute2);
		record3.setDeleteFlag(false);
		record3.setCreatedBy(userId);
		record3.setCreationTime(time);
		record3.setLastUpdatedBy(userId);
		record3.setLastUpdateTime(time);
		wxtMaterialInventoryLogVoMapper.insertWithMaterialCode(record3);
	}

	private Map<String, Object> getFailResultMap(String msg) {
		return getResultMap(Constants.FAIL, msg);
	}

	private Map<String, Object> getSuccessResultMap(String msg) {
		return getResultMap(Constants.SUCCESS, msg);
	}

	private Map<String, Object> getResultMap(String code, String msg) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		resultMap.put(Constants.RESULT_CODE_KEY, code);
		resultMap.put(Constants.RESULT_CODE_MSG_KEY, msg);
		return resultMap;
	}

	/**
	 * 获取礼享网仓库ID(虚拟)
	 * @return
	 */
	private Long getEnjoygifsWarehouseId() {
		Long warehouseId = 0L;
		WXTMaterialWarehouseVoExample queryWH = new WXTMaterialWarehouseVoExample();
		queryWH.createCriteria().andMaterialSourceEqualTo(MaterialSource.ENJOYGIFTS.getValue());
		List<WXTMaterialWarehouseVo> whs = wxtMaterialWarehouseVoMapper.selectByExample(queryWH);
		if (null != whs && !whs.isEmpty()) {
			warehouseId = whs.get(0).getId();
		}
		return warehouseId;
	}

	private String validateInventoryDto(SMCUpdateInventoryDto dto) {
		if (null == dto) {
			return "参数为空";
		}
		if (StringUtils.isEmpty(dto.getUpdateType())) {
			return "更新类型为空: '1': 因订单原因变化库存; '2':因其他原因变化库存(上架/进货等)";
		}

		if("1".equals(dto.getUpdateType())) {
			if (StringUtils.isEmpty(dto.getSmcOrderCode())) {
				return "更新类型为1(因订单原因变化库存)时, SMC订单CODE不能为空";
			}

			if (StringUtils.isEmpty(dto.getChevronOrderCode())) {
				return "更新类型为1(因订单原因变化库存)时, 雪佛龙订单号不能为空";
			}
		}

		if (null == dto.getUpdateTime()) {
			return "更新时间为空";
		}
		SMCUpdateInventoryDetailDto[] detailList = dto.getDetailList();
		if (null == dto.getDetailList() || dto.getDetailList().length == 0) {
			return "库存变化详情列表为空";
		}

		for (int i = 1; i < detailList.length+1; i++) {
			SMCUpdateInventoryDetailDto sdto = detailList[i-1];
			String sn = "库存变化详情第["+i+"]条:";
			if(null == sdto) {
				return sn + "库存变化详情数据为空";
			}

			if (StringUtils.isEmpty(sdto.getMaterialCode())) {
				return sn + "雪佛龙物料编码为空";
			}

			if (StringUtils.isEmpty(sdto.getMaterialSkuCode())) {
//				return sn + "雪佛龙物料SKU编码为空"; TODO
			}

			if (null == sdto.getStockQty()) {
				return sn + "最新库存数量为空";
			}
			if (null == sdto.getChangeQty()) {
				return sn + "库存变化数量为空";
			}
			if (null == sdto.getOriginalQty()) {
				return sn + "变更前库存数量为空";
			}
		}
		return null;
	}

	private String validateLogisticsDto(SMCUpdateLogisticsDto dto) {
		if (null == dto) {
			return "参数为空";
		}

		if (StringUtils.isEmpty(dto.getSmcOrderCode())) {
			return "SMC订单CODE为空";
		}

		if (StringUtils.isEmpty(dto.getChevronOrderCode())) {
			return "雪佛龙订单号为空";
		}

		if (null == dto.getUpdateTime()) {
			return "更新时间为空";
		}

		SMCUpdateLogisticsDetailDto[] detailList = dto.getDetailList();
		if (null == detailList || detailList.length == 0) {
			return "物流详情列表为空";
		}

		for (int i = 1; i <= detailList.length; i++) {
			SMCUpdateLogisticsDetailDto sdto = detailList[i-1];
			String sn = "物流详情第["+i+"]条:";
			if(null == sdto) {
				return sn + "物流详情数据为空";
			}

			if (StringUtils.isEmpty(sdto.getCom())) {
				return sn + "物流/快递公司名称为空";
			}

			if (StringUtils.isEmpty(sdto.getNu())) {
				return sn + "物流/快递单号为空";
			}
			if (null == sdto.getTime()) {
				return sn + "时间为空";
			}
			if (StringUtils.isEmpty(sdto.getContext())) {
				return sn + "描述为空";
			}
			if (StringUtils.isEmpty(sdto.getState())) {
				return sn + "状态为空";
			}
		}

		return null;
	}

	private String validateMaterialStatus(SMCUpdateMaterialStatusDto dto) {
		if (null == dto) {
			return "参数为空";
		}


		if (null == dto.getUpdateTime()) {
			return "更新时间为空";
		}
		SMCUpdateMaterialStatusDetailDto[] detailList = dto.getDetailList();
		if (null == dto.getDetailList() || dto.getDetailList().length == 0) {
			return "物料上下架状态变化详情列表为空";
		}

		for (int i = 1; i <= detailList.length; i++) {
			SMCUpdateMaterialStatusDetailDto sdto = detailList[i-1];
			String sn = "物料上下架状态变化详情第["+i+"]条:";
			if(null == sdto) {
				return sn + "物料上下架状态变化详情数据为空";
			}

			if (StringUtils.isEmpty(sdto.getMaterialCode())) {
				return sn + "雪佛龙物料编码为空";
			}

			if (null == sdto.getStatus() ) {
				return sn + "物料上下架状态为空";
			}

			if (0 != sdto.getStatus() && 1 != sdto.getStatus() && -1 != sdto.getStatus()) {
				return sn + "物料上下架状态异常(0: 下架, 1: 上架, -1: 删除): " + sdto.getStatus();
			}
		}
		return null;
	}
	private String getHttpSource() {
		HttpServletRequest request = ContextUtil.getHttpRequest();
		String source = "";
		if (null != request) {
			StringBuffer bf = new StringBuffer();
			String url = request.getRequestURL().toString();//获得客户端发送请求的完整url
			String ip = request.getRemoteAddr();//返回发出请求的IP地址
			String host=request.getRemoteHost();//返回发出请求的客户机的主机名
			int port =request.getRemotePort();//返回发出请求的客户机的端口号
			bf.append("url:").append(url).append("|")
			.append("ip:").append(ip).append("|")
			.append("host:").append(host).append("|")
			.append("port:").append(port).append("|");
			source = bf.toString();
		}
		return source;
	}

	/**
	 * 记录服务日志到数据库日志表
	 * @param 接口名
	 * @param 消息
	 * @param 数据
	 */
	private void pushLog(final String 接口名, final String 消息, final String 数据) {
		log.info("MaterialServiceImpl写日志开始: {}, {}, {}", 接口名, 消息, 数据);
		LogUtils.addLog(new LogTask() {
			@Override
			public void execute() throws Exception {
				try {
					Log record = new Log();
					final Date now = DateUtil.getCurrentDate();
					record.setCreateTime(now);
					record.setOperator(999999999L);
					record.setLogType("MaterialService");
					record.setExtProperty1(接口名);
					record.setExtProperty2(消息);
					record.setExtProperty3(数据);
					logService.save(record);
				} catch (Exception e) {
					log.error("向数据库写日志时, 异常发生: " + e.getMessage(), e);
				}
			}
		});

	}


	/**
	 * 维护materialId和skuList的对应关系
	 * @param materialId
	 * @param materialCode
	 * @param skuList
	 * @return
	 */
	private boolean updateMaterialSkuList(Long materialId, String materialCode, List<WXTMaterialSkuVo> skuList) {
		Long userId = ContextUtil.getCurUserId();
		Date now = DateUtil.getCurrentDate();

		/**
		 * skuList——用户传过来的是目前的界面上有的sku的列表
		 * 然后再查下查询数据库里面的目前的这个materialId对应的列表,来做删除，更新恢复，插入的操作
		 */

		// 如果skuList没有数据，那就是，必须要有个“默认-均码”的
		WXTMaterialVo materialVo = wxtMaterialVoMapper.selectByPrimaryKey(materialId);
		if (null == skuList || skuList.isEmpty()) {
			WXTMaterialSkuVo defaultSku = new WXTMaterialSkuVo();
			defaultSku.setMaterialSkuCode(materialVo.getMaterialCode() + "-默认-均码");
			defaultSku.setMaterialSkuPropColor("默认");
			defaultSku.setMaterialSkuPropSize("均码");
			skuList = new ArrayList<WXTMaterialSkuVo>();
			skuList.add(defaultSku);
		}

		// 把skuList的sku item的MaterialSkuCode弄好，后面需要使用
		for (WXTMaterialSkuVo sku: skuList) {
			String skuCode = materialVo.getMaterialCode() + '-' + sku.getMaterialSkuPropColor() + '-' + sku.getMaterialSkuPropSize();
			sku.setMaterialSkuCode(skuCode);
		}

		// 获取这个materialId已存在SKU，包括哪些deleteFlag是true(也就是逻辑删除的)
		WXTMaterialSkuVoExample searchCurrentSkuExample = new WXTMaterialSkuVoExample();
		searchCurrentSkuExample.createCriteria().andMaterialIdEqualTo(materialId);
		List<WXTMaterialSkuVo> storedSkuList = wxtMaterialSkuVoMapper.selectByExample(searchCurrentSkuExample);

		Map<String, WXTMaterialSkuVo> store = new HashMap<String, WXTMaterialSkuVo>();
		if (null != storedSkuList && !storedSkuList.isEmpty()) {
			for (WXTMaterialSkuVo oSku: storedSkuList) {
				store.put(oSku.getMaterialSkuCode(), oSku);
			}
		}

		// 计算出更新列表
		// 需要插入的的sku列表
		List<WXTMaterialSkuVo> insertList = new ArrayList<WXTMaterialSkuVo>();
		// 需要删除的sku列表
		List<WXTMaterialSkuVo> deleteList = new ArrayList<WXTMaterialSkuVo>();
		// 需要逻辑恢复的sku列表（由逻辑删除变会正常的）
		List<WXTMaterialSkuVo> recoveryList = new ArrayList<WXTMaterialSkuVo>();

		for (WXTMaterialSkuVo sku: skuList) {
			String skuCode = sku.getMaterialSkuCode();
			// 如果当前的存储的表里面不存在这个sku的，就加入到insert表里面
			if (!store.containsKey(skuCode)) {
				insertList.add(sku);
			} else if(store.get(skuCode).getDeleteFlag()){
				// 如果当前的存储的表里面包含了这个类型的sku，但是deleteFlag又为空的话，那就应该进入recoveryList，这样来重生
				recoveryList.add(sku);
			}
		}

		// 如果提交的submittedSkuMap,如果storedSkuList有超出这些sku，则应该删除这些sku，加入deleteList
		Map<String, WXTMaterialSkuVo> submittedSkuMap = new HashMap<String, WXTMaterialSkuVo>();
		if (null != skuList && !skuList.isEmpty()) {
			for (WXTMaterialSkuVo oSku: skuList) {
				submittedSkuMap.put(oSku.getMaterialSkuCode(), oSku);
			}
		}

		for (WXTMaterialSkuVo sku: storedSkuList) {
			// 如果当前存储的有不在提交的内容里面的，并且本身也不是delete的状态，那就是需要加入到deleteList
			if (!submittedSkuMap.containsKey(sku.getMaterialSkuCode()) && !sku.getDeleteFlag()) {
				deleteList.add(sku);
			}
		}

		// 插入
		if(!insertList.isEmpty()) {
			for (WXTMaterialSkuVo sku : insertList) {
				WXTMaterialSkuVo record = new WXTMaterialSkuVo();
				record.setMaterialId(materialId);
				record.setMaterialSkuCode(sku.getMaterialSkuCode());
				record.setMaterialSkuPropColor(sku.getMaterialSkuPropColor());
				record.setMaterialSkuPropSize(sku.getMaterialSkuPropSize());
				record.setDeleteFlag(false);
				record.setLastUpdatedBy(userId);
				record.setLastUpdateTime(now);
				record.setCreatedBy(userId);
				record.setCreationTime(now);
				wxtMaterialSkuVoMapper.insertSelective(record);

				WXTMaterialInventoryVo inventory = new WXTMaterialInventoryVo();
				inventory.setMaterialId(materialId);
				inventory.setMaterialSkuCode(sku.getMaterialSkuCode());
				inventory.setStockQty(0L);
				inventory.setVirtualStockQty(0L);
				inventory.setDeleteFlag(false);
				inventory.setCreatedBy(userId);
				inventory.setCreationTime(now);
				inventory.setLastUpdatedBy(userId);
				inventory.setLastUpdateTime(now);
				WXTMaterialWarehouseVoExample queryWH = new WXTMaterialWarehouseVoExample();
				queryWH.createCriteria().andMaterialSourceEqualTo(materialVo.getMaterialSource());
				// 先查询这MaterialSource是否对应到仓库，然后插入库存
				List<WXTMaterialWarehouseVo> whs = wxtMaterialWarehouseVoMapper.selectByExample(queryWH);
				if (null != whs && !whs.isEmpty()) {
					for (WXTMaterialWarehouseVo v : whs) {
						inventory.setWarehouseId(v.getId());
						wxtMaterialInventoryVoMapper.insertSelective(inventory);
					}
				} else {
					// 该物料的来源没有匹配到具体仓库的情况，默认设置为0
					log.warn("该SKU{}的物料来源没有配置具体仓库，新建时指向默认仓库0", sku.getMaterialSkuCode());
					inventory.setWarehouseId(0L);
					wxtMaterialInventoryVoMapper.insertSelective(inventory);
				}

			}
		}

		// 删除
		if(!deleteList.isEmpty()) {
			for (WXTMaterialSkuVo sku : deleteList) {
				// 先查询根据StockQty和VirtualStockQty不为0的库存,这里是查询所有的，包括deleteFlag为true的记录
				WXTMaterialInventoryVoExample inventoryEx = new WXTMaterialInventoryVoExample();
				inventoryEx.createCriteria()
						.andMaterialIdEqualTo(materialId)
						.andMaterialSkuCodeEqualTo(sku.getMaterialSkuCode())
						.andStockQtyNotEqualTo(0L)
						.andVirtualStockQtyNotEqualTo(0L);
				List<WXTMaterialInventoryVo> inventory = wxtMaterialInventoryVoMapper.selectByExample(inventoryEx);
				// 如果存在，hasStocked就是true
				boolean hasStocked = (null != inventory &&  !inventory.isEmpty());

				// 逻辑删除掉sku
				WXTMaterialSkuVo record = new WXTMaterialSkuVo();
				record.setDeleteFlag(true);
				WXTMaterialSkuVoExample deleteEx = new WXTMaterialSkuVoExample();
				deleteEx.createCriteria().andMaterialIdEqualTo(materialId)
						.andMaterialSkuCodeEqualTo(sku.getMaterialSkuCode())
						.andMaterialSkuPropColorEqualTo(sku.getMaterialSkuPropColor())
						.andMaterialSkuPropSizeEqualTo(sku.getMaterialSkuPropSize());
				wxtMaterialSkuVoMapper.updateByExampleSelective(record, deleteEx);

				if (hasStocked) {
					// 有库存的情况，不能删除该SKU, 而是逻辑删除
					WXTMaterialInventoryVoExample updateInventoryExample = new WXTMaterialInventoryVoExample();
					updateInventoryExample.createCriteria().andMaterialIdEqualTo(materialId).andMaterialSkuCodeEqualTo(sku.getMaterialSkuCode());
					WXTMaterialInventoryVo toDeleteRecord = new WXTMaterialInventoryVo();
					// 逻辑删除
					toDeleteRecord.setDeleteFlag(true);
					int updateInventoryCount = wxtMaterialInventoryVoMapper.updateByExampleSelective(toDeleteRecord, updateInventoryExample);
					log.debug("updateInventoryCount {}", updateInventoryCount);
					// 那些StockQty和VirtualStockQty为0的还是需要删除
//					WXTMaterialInventoryVoExample deleteInventoryExample = new WXTMaterialInventoryVoExample();
//					deleteInventoryExample.createCriteria()
//							.andMaterialIdEqualTo(materialId).andMaterialSkuCodeEqualTo(sku.getMaterialSkuCode())
//							.andStockQtyEqualTo(0L).andVirtualStockQtyEqualTo(0L);
//				 	int deleteInventoryCount = wxtMaterialInventoryVoMapper.deleteByExample(deleteInventoryExample);
//				 	log.debug("updateInventoryCount {},deleteInventoryCount {}",updateInventoryCount,deleteInventoryCount);
				} else {
					// 如果不存在库存，那就是物理删除该SKU库存
					WXTMaterialInventoryVoExample deleteInventoryExample = new WXTMaterialInventoryVoExample();
					deleteInventoryExample.createCriteria().andMaterialIdEqualTo(materialId).andMaterialSkuCodeEqualTo(sku.getMaterialSkuCode());
					int deleteInventoryCount = wxtMaterialInventoryVoMapper.deleteByExample(deleteInventoryExample);
					log.debug("deleteInventoryCount {}",deleteInventoryCount);
				}
			}
		}

		// 更新，逻辑恢复一些
		if(!recoveryList.isEmpty()) {
			for (WXTMaterialSkuVo sku : recoveryList) {
				// 逻辑恢复
				WXTMaterialSkuVo record = new WXTMaterialSkuVo();
				record.setDeleteFlag(false);
				record.setLastUpdatedBy(userId);
				record.setLastUpdateTime(now);
				record.setCreatedBy(userId);
				record.setCreationTime(now);
				// 恢复的查询条件
				WXTMaterialSkuVoExample recoveryEx = new WXTMaterialSkuVoExample();
				recoveryEx.createCriteria().andMaterialIdEqualTo(materialId)
						.andMaterialSkuCodeEqualTo(sku.getMaterialSkuCode())
						.andMaterialSkuPropColorEqualTo(sku.getMaterialSkuPropColor())
						.andMaterialSkuPropSizeEqualTo(sku.getMaterialSkuPropSize());
				// 执行逻辑的恢复
				wxtMaterialSkuVoMapper.updateByExampleSelective(record, recoveryEx);

				// 查询当前当前这个sku对应的库存的情况，看是恢复之前的，还是insert
				WXTMaterialInventoryVoExample inventoryEx = new WXTMaterialInventoryVoExample();
				inventoryEx.createCriteria()
						.andMaterialIdEqualTo(materialId)
						.andMaterialSkuCodeEqualTo(sku.getMaterialSkuCode());
				List<WXTMaterialInventoryVo> inventoryList = wxtMaterialInventoryVoMapper.selectByExample(inventoryEx);
                if(inventoryList!=null && !inventoryList.isEmpty()){
                	// 需要恢复
					for(WXTMaterialInventoryVo inventoryVo:inventoryList){
						WXTMaterialInventoryVo newInventoryVo = new WXTMaterialInventoryVo();
						newInventoryVo.setDeleteFlag(false);
						newInventoryVo.setLastUpdatedBy(userId);
						newInventoryVo.setLastUpdateTime(now);
						newInventoryVo.setCreatedBy(userId);
						newInventoryVo.setCreationTime(now);
						WXTMaterialInventoryVoExample recoverInventoryVoExample = new WXTMaterialInventoryVoExample();
						recoverInventoryVoExample.createCriteria()
								.andMaterialIdEqualTo(inventoryVo.getMaterialId())
								.andMaterialSkuCodeEqualTo(inventoryVo.getMaterialSkuCode())
								.andWarehouseIdEqualTo(inventoryVo.getWarehouseId());
						wxtMaterialInventoryVoMapper.updateByExampleSelective(newInventoryVo,recoverInventoryVoExample);
					}
				} else {
					// 需要插入
					WXTMaterialInventoryVo inventory = new WXTMaterialInventoryVo();
					inventory.setMaterialId(materialId);
					inventory.setMaterialSkuCode(sku.getMaterialSkuCode());
					inventory.setStockQty(0L);
					inventory.setVirtualStockQty(0L);
					inventory.setDeleteFlag(false);
					inventory.setCreatedBy(userId);
					inventory.setCreationTime(now);
					inventory.setLastUpdatedBy(userId);
					inventory.setLastUpdateTime(now);
					WXTMaterialWarehouseVoExample queryWH = new WXTMaterialWarehouseVoExample();
					queryWH.createCriteria().andMaterialSourceEqualTo(materialVo.getMaterialSource());
					List<WXTMaterialWarehouseVo> whs = wxtMaterialWarehouseVoMapper.selectByExample(queryWH);
					if (null != whs && !whs.isEmpty()) {
						for (WXTMaterialWarehouseVo v : whs) {
							inventory.setWarehouseId(v.getId());
							wxtMaterialInventoryVoMapper.insertSelective(inventory);
						}
					} else {
						// 该物料的来源没有匹配到具体仓库的情况，默认设置为0
						log.warn("该SKU{}的物料来源没有配置具体仓库，新建时指向默认仓库0", sku.getMaterialSkuCode());
						inventory.setWarehouseId(0L);
						wxtMaterialInventoryVoMapper.insertSelective(inventory);
					}
				}
			}
		}


		return true;
	}

	/**
	 * 把materialImages的内容和materialId对应上，存到wx_t_material_image表里面
	 * @param materialId
	 * @param materialImages
	 * @return
	 */
	private List<WXTMaterialImageVo> updateMaterialImageList(Long materialId, String materialImages) {
		if (StringUtils.isEmpty(materialImages)) {
			return null;
		}
		// "287405,0|287406,1|"这样的数据split为数组
		String [] imageArr = materialImages.split("\\|");
		if (null == imageArr || imageArr.length < 1) {
			return null;
		}
		List<WXTMaterialImageVo> list = new ArrayList<WXTMaterialImageVo>();
		Date now = DateUtil.getCurrentDate();
		Long userId = ContextUtil.getCurUserId();

		for (String imagePair : imageArr) {
			if (StringUtils.isEmpty(imagePair)) {
				continue;
			}
			// 287405,0这样的数据，获得imageId和每个图片的顺序
			String [] seqNImageId = imagePair.split(",");
			if (null == seqNImageId || seqNImageId.length < 1) {
				continue;
			}
			String seq = seqNImageId[0];
			String imageId = seqNImageId[1];
			if (StringUtils.isNotEmpty(seq) && StringUtils.isNotEmpty(imageId) &&
					org.apache.commons.lang.StringUtils.isNumeric(seq) &&
					org.apache.commons.lang.StringUtils.isNumeric(imageId)) {
				WXTMaterialImageVo vo = new WXTMaterialImageVo();
				vo.setImageId(Long.valueOf(imageId));
				vo.setImageSeq(Integer.valueOf(seq));
				vo.setMaterialId(materialId);
				vo.setDeleteFlag(false);
				vo.setCreatedBy(userId);
				vo.setCreationTime(now);
				vo.setLastUpdatedBy(userId);
				vo.setLastUpdateTime(now);
				list.add(vo);
			}
		}

		// 批量的插入到wx_t_material_image
		if (null != list && !list.isEmpty()) {
			WXTMaterialImageVoExample example = new WXTMaterialImageVoExample();
			example.createCriteria().andMaterialIdEqualTo(materialId);
			// 先删除wx_t_material_image老的对应关系
			wxtMaterialImageVoMapper.deleteByExample(example);
			// 再批量插入
			wxtMaterialImageVoMapper.insertBatch(list);
		}
		return list;

	}
	private Boolean sync2SmartComm(HttpServletRequest request, Long materialId, boolean isNew) {
		WXTMaterialVo m = wxtMaterialVoMapper.selectByPrimaryKey(materialId);
		if (null == m) {
			log.error("同步到SMC失败, 查询不到物料信息, id:{}", materialId);
			return false;
		}
		isNew = isNew || StringUtils.isEmpty(m.getSmcMaterialCode());

		SmartCommItem item = new SmartCommItem();
		item.setOperType(isNew?0:1);
		item.setMaterialCode(m.getMaterialCode());
		item.setUnitItemCd(m.getSmcMaterialCode());
		item.setUnitItemName(m.getMaterialName());
		item.setUnitItemNameEn(m.getMaterialNameEn());
		item.setCustEmail(m.getCustEmail());
		item.setSupplierEmail(m.getSupplierEmail());
		item.setSellingPoint(m.getSellingPoint());
		item.setSellingPointEn(m.getSellingPointEn());
		item.setItemDesc(m.getMaterialDesc());
		item.setItemSizeL(m.getMaterialSizel());
		item.setItemSizeH(m.getMaterialSizeh());
		item.setItemSizeW(m.getMaterialSizew());
		item.setItemWeight(null == m.getMaterialWeight() ? 0.00d:m.getMaterialWeight().doubleValue());
		item.setItemMakePeriod(m.getMaterialMakePeriod());
		item.setItemProductionPeriod(m.getMaterialProductionPeriod());

		List<SmartCommItemPrice> priceList = new ArrayList<SmartCommItemPrice>();
		SmartCommItemPrice smcPrice = new SmartCommItemPrice();
		smcPrice.setPrice(null == m.getMaterialPrice()? 0.00d:m.getMaterialPrice().doubleValue());
		smcPrice.setUnitPriceLimit1(1);
		smcPrice.setUnitPriceLimit2(999999999);
		priceList.add(smcPrice);
		item.setPriceList(priceList);

		// 同步图片
		WXTMaterialImageVoExample imageExample = new WXTMaterialImageVoExample();
		imageExample.createCriteria().andMaterialIdEqualTo(materialId);
		List<WXTMaterialImageVo> imageVoList = wxtMaterialImageVoMapper.selectByExample(imageExample);
		List<SmartCommItemImage> imageList = new ArrayList<SmartCommItemImage>();
		if (null != imageVoList && !imageVoList.isEmpty()) {
			for (WXTMaterialImageVo imageVo : imageVoList) {
				SmartCommItemImage  smcImage = new SmartCommItemImage();
				smcImage.setImgUrl(genImageUrl(request, imageVo.getImageId()));
//				smcImage.setImgUrl("http://partnerstg.chinacloudapp.cn:80/publicImage/62768/12/download.do");
				smcImage.setSeq( imageVo.getImageSeq());
				imageList.add(smcImage);
			}
		}

		item.setImageList(imageList);

		// 同步SKU
		WXTMaterialSkuVoExample example = new WXTMaterialSkuVoExample();
		example.createCriteria().andMaterialIdEqualTo(materialId).andDeleteFlagEqualTo(false);
		List<WXTMaterialSkuVo> skuVoList = wxtMaterialSkuVoMapper.selectByExample(example);
		List<SmartCommItemSku> smcSkuList = new ArrayList<SmartCommItemSku>();
		if (null != skuVoList && !skuVoList.isEmpty()) {
			for (WXTMaterialSkuVo vo : skuVoList) {
				SmartCommItemSku  smcSku = new SmartCommItemSku();
				smcSku.setUnitSkuCd(vo.getMaterialSkuCode());
				smcSku.setUnitSkuPropC(vo.getMaterialSkuPropColor());
				smcSku.setUnitSkuPropS(vo.getMaterialSkuPropSize());
				smcSkuList.add(smcSku);
			}
		} else {
			SmartCommItemSku  smcSku = new SmartCommItemSku();
			smcSku.setUnitSkuCd(m.getMaterialCode() + "-默认-均码");
			smcSku.setUnitSkuPropC("默认");
			smcSku.setUnitSkuPropS("均码");
			smcSkuList.add(smcSku);
		}
		item.setSkuList(smcSkuList);

		String smcMaterialCode = smartCommService.saveItem(item);
		if (StringUtils.isEmpty(smcMaterialCode)) {
			log.error("同步到SMC失败, 返回的SMC物料编码为空, id:{}", materialId);
			return false;
		}
		WXTMaterialVo record = new WXTMaterialVo();
		record.setId(materialId);
		record.setSmcMaterialCode(smcMaterialCode);
		record.setState("SYNCED");
		// 新建物料 或 第一次同步物料的场合
		if (isNew || StringUtils.isEmpty(m.getSmcMaterialCode())) {
			// 新建物料同步到SMC成功, 默认设置为上架
			record.setSmcStatus(1);
		}
		wxtMaterialVoMapper.updateByPrimaryKeySelective(record);
		return true;
	}

	private String genImageUrl(HttpServletRequest request, Long imageId) {
		String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() + "/";
		return basePath + "publicImage/"+imageId+"/12/download.do";
	}

	@Override
	public Map<String, Object> getSkuList(Long materialId) {
		WXTMaterialSkuVoExample example = new WXTMaterialSkuVoExample();
		example.createCriteria().andMaterialIdEqualTo(materialId).andDeleteFlagEqualTo(false);
		List<WXTMaterialSkuVo> skuVoList = wxtMaterialSkuVoMapper.selectByExample(example);

		Map<String, Object> map = getSuccessResultMap("成功");
		map.put("data", skuVoList);
		return map;
	}

	@Override
	public Map<String, Object> getWarehouseList() {
		WXTMaterialWarehouseVoExample queryWH = new WXTMaterialWarehouseVoExample();

		queryWH.createCriteria()
		.andMaterialSourceIn(Arrays.asList("caltexWarehouseTJ", "caltexWarehouseGZ"))
		.andDeleteFlagEqualTo(false);
		List<WXTMaterialWarehouseVo> whs = wxtMaterialWarehouseVoMapper.selectByExample(queryWH);
		Map<String, Object> map = getSuccessResultMap("成功");
		map.put("data", whs);
		return map;
	}

	@Override
	public Map<String, Object> generateOutboundPdf(Long id) {
		Map<String,Object> resultMap=new HashMap<String,Object>();

		//获取订单头
		WXTMaterialWarehouseOutVo vo = wxtMaterialWarehouseOutVoMapper.selectByPrimaryKey(id);
		Map<String,Object> headMap = new HashMap<String,Object>();
		headMap.put("applicationCode",vo.getApplicationCode());
		if(!StringUtils.isEmpty(vo.getAddressRegion())) {
			headMap.put("addressRegionName",getRegionName(Long.parseLong(vo.getAddressRegion())));
		}
		headMap.put("addressDetail",vo.getAddressDetail());
		headMap.put("contactNumber",vo.getContactNumber());
		headMap.put("contacts", vo.getContacts());
		headMap.put("applicationPersonName", vo.getApplicationPersonName());
		headMap.put("applicationOrgName", vo.getApplicationOrgName());
		headMap.put("comments", vo.getComments());
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		headMap.put("applicationTimeFMT", sdf.format(vo.getApplicationTime()));
		sdf = new SimpleDateFormat("yyyy-MM-dd");
		headMap.put("recipientDateFMT", sdf.format(vo.getRecipientDate()));

		//获取订单行
		List<WXTMaterialWarehouseOutDetailVo> lineVoList=wxtMaterialWarehouseOutDetailVoMapper.selectWithMaterialInfo(id);
		List<Map<String,String>> lineList = new ArrayList<Map<String,String>>();
		for(WXTMaterialWarehouseOutDetailVo lineVo:lineVoList) {
			Map<String,String> lineMap = new HashMap<String,String>();
			lineMap.put("materialName", lineVo.getMaterialName());
			lineMap.put("materialCode", lineVo.getMaterialCode());
			String colorAndSize = lineVo.getMaterialSkuCode().split("-")[1]+","+lineVo.getMaterialSkuCode().split("-")[2];
			lineMap.put("colorAndSize", colorAndSize);
			lineMap.put("amount",lineVo.getApplicationQty().toString());

			lineList.add(lineMap);
		}

		//组装map
		resultMap.put("fileName", vo.getApplicationCode());
		resultMap.put("head",headMap);
		resultMap.put("lineList", lineList);
		return resultMap;
	}

	@Override
	public Map<String, Object> addSkuInventoryRecord(Long materialId, String skuCode, Long warehouseId) {
		Long userId = ContextUtil.getCurUserId();
		Date now = DateUtil.getCurrentDate();

		WXTMaterialInventoryVoExample example = new WXTMaterialInventoryVoExample();
		example.createCriteria().andMaterialIdEqualTo(materialId)
		.andMaterialSkuCodeEqualTo(skuCode)
		.andWarehouseIdEqualTo(warehouseId);
		Map<String, Object> map =null;
		List<WXTMaterialInventoryVo> selectByExample = wxtMaterialInventoryVoMapper.selectByExample(example);

		if(selectByExample != null && !selectByExample.isEmpty()) {
			log.error("添加库存记录失败， materialId:{},skuCode:{},warehouseId:{}", materialId, skuCode, warehouseId);
			map = getFailResultMap("添加库存记录失败， 库存记录已存在，不能重复添加");
			return map;
		}

		int res = 0;
		try {
			WXTMaterialInventoryVo inventory = new WXTMaterialInventoryVo();
			inventory.setMaterialId(materialId);
			inventory.setMaterialSkuCode(skuCode);
			inventory.setStockQty(0L);
			inventory.setVirtualStockQty(0L);
			inventory.setDeleteFlag(false);
			inventory.setCreatedBy(userId);
			inventory.setCreationTime(now);
			inventory.setLastUpdatedBy(userId);
			inventory.setLastUpdateTime(now);
			inventory.setWarehouseId(warehouseId);
			res = wxtMaterialInventoryVoMapper.insertSelective(inventory);
		} catch (Exception e) {
			log.error("添加库存记录失败, 系统异常, materialId:{},skuCode:{},warehouseId:{}", materialId, skuCode, warehouseId);
			log.error("添加库存记录失败, 系统异常", e);
		}

		if (1 == res) {
			map = getSuccessResultMap("操作成功");
		} else {
			log.error("添加库存记录失败， materialId:{},skuCode:{},warehouseId:{}", materialId, skuCode, warehouseId);
			map = getFailResultMap("添加库存记录失败， 请稍后再试或者请求系统支持服务");
		}
		return map;
	}

	@Override
	public Map<String, Object> removeSkuInventoryRecord(Map<String, Object>[] params) {
		Map<String, Object> map;

		if (params != null && params.length > 0) {
			for(Map<String, Object> param : params) {
				if(null != param) {
					Integer materialId = (Integer)param.get("materialId");
					String skuCode = (String) param.get("materialSkuCode");
					Integer warehouseId = (Integer)param.get("warehouseId");
					WXTMaterialInventoryVoExample example = new WXTMaterialInventoryVoExample();
					example.createCriteria().andMaterialIdEqualTo(materialId.longValue())
					.andMaterialSkuCodeEqualTo(skuCode)
					.andWarehouseIdEqualTo(warehouseId.longValue())
					.andStockQtyNotEqualTo(0L)
					.andVirtualStockQtyNotEqualTo(0L);
					List<WXTMaterialInventoryVo> selectByExample = wxtMaterialInventoryVoMapper.selectByExample(example);
					if(selectByExample != null && !selectByExample.isEmpty()) {
						log.error("添加库存记录失败， 库存记录不为0，不能删除该库存记录， materialId:{},skuCode:{},warehouseId:{}", materialId, skuCode, warehouseId);
						map = getFailResultMap("添加库存记录失败， 库存记录必须清零才能删除。");
						return map;
					}
				}
			}
			for(Map<String, Object> param : params) {
				if(null != param) {
					Integer materialId = (Integer) param.get("materialId");
					String skuCode = (String) param.get("materialSkuCode");
					Integer warehouseId = (Integer)param.get("warehouseId");
					//物理删除该SKU库存
					WXTMaterialInventoryVoExample _example = new WXTMaterialInventoryVoExample();
					_example.createCriteria()
					.andMaterialIdEqualTo(materialId.longValue())
					.andMaterialSkuCodeEqualTo(skuCode)
					.andWarehouseIdEqualTo(warehouseId.longValue());
					wxtMaterialInventoryVoMapper.deleteByExample(_example);
				}
			}
		}


		map = getSuccessResultMap("操作成功");
		return map;
	}
	/**
	 * 获取地区名
	 *
	 * @param distCode
	 * @return Region Name
	 */
	private String getRegionName(Long distCode) {
		List<RegionVo> provCityDist = regionService.getProvCityDistByRegionId(distCode);
		String regionName = "";
		String name = "";
		for (RegionVo region : provCityDist) {
			if (com.common.util.StringUtils.equals(region.getRegionType(), RegionVo.REGION_TYPE_PROVINCE)) {
				name = region.getRegionName();
				regionName = name;
				log.debug("getProvinceName: " + region.getRegionName());
				break;
			}
		}
		for (RegionVo region : provCityDist) {
			if (com.common.util.StringUtils.equals(region.getRegionType(), RegionVo.REGION_TYPE_CITY)) {
				name = region.getRegionName();
				regionName = regionName + "-" + name;
				log.debug("getCityName : " + region.getRegionName());
				break;
			}
		}
		for (RegionVo region : provCityDist) {
			if (com.common.util.StringUtils.equals(region.getRegionType(), RegionVo.REGION_TYPE_DISTRICT)) {
				name = region.getRegionName();
				regionName = regionName + "-" + name;
				log.debug("getRegionName : " + region.getRegionName());
				break;
			}
		}
		return regionName;
	}

	@Override
	public Map<String, Object> modifyLimitQty(Long materialId, String materialCode, String materialSkuCode, Long modifyLimitQty) {
		Map<String, Object> map = new HashMap<String, Object>();
		Long userId = ContextUtil.getCurUserId();
		Date now = DateUtil.getCurrentDate();
		WXTMaterialSkuVo record = new WXTMaterialSkuVo();
		record.setMaterialId(materialId);
		record.setMaterialSkuCode(materialSkuCode);
		record.setLimitQty(modifyLimitQty);
		record.setLastUpdatedBy(userId);
		record.setLastUpdateTime(now);
		wxtMaterialSkuVoMapper.updateByPrimaryKeySelective(record);


		return getSuccessResultMap("单次限购数量更新成功");
	}

	@Override
	public Map<String, Object> modifyTradeQty(Long materialId, String materialCode, String materialSkuCode, Long modifyTradeQty) {
		Map<String, Object> map = new HashMap<String, Object>();
		Long userId = ContextUtil.getCurUserId();
		Date now = DateUtil.getCurrentDate();
		WXTMaterialSkuVo record = new WXTMaterialSkuVo();
		record.setMaterialId(materialId);
		record.setMaterialSkuCode(materialSkuCode);
		record.setTradeQty(modifyTradeQty);
		record.setLastUpdatedBy(userId);
		record.setLastUpdateTime(now);
		wxtMaterialSkuVoMapper.updateByPrimaryKeySelective(record);

		return getSuccessResultMap("合伙人限购年内总数更新成功");
	}

    @Override
    public Map<String, Object> getPickingList(MaterialParams params) {
        WxTUser user = ContextUtil.getCurUser();
        Map<String, Object> resultMap = new HashMap<String, Object>();
        params.setIs4picking(true);
        params.setIsAdmin(WxTUser.CHEVRON_USER_ROLE.equals(user.getmUserTypes()));

        // 加德士经销商用户，根据合伙人属性配置获取其对应的物料仓库（1天津仓/2广州仓）
        if (params.getPointMode()) {
            String assignedWarehouse = null;
            boolean isSPuser = WxTUser.USER_MODEL_SP.equals(ContextUtil.getCurUser().getUserModel());
            if (isSPuser) {
                Map<String, Object> _params = new HashMap<String, Object>();
                Long partnerId = user.getOrgId();
                _params.put("partnerId", partnerId);
                List<PartnerView> list = organizationVoMapper.selectPartnersByParams(_params);
                if (null != list && 1 == list.size()) {
                    assignedWarehouse = list.get(0).getMaterialStock();
                }
            }
            params.setPartnerAssignedWarehouse(assignedWarehouse);
        }

        // 加德士和德勒的经销商适用于积分兑换规则,物料类别和仓库
        List<WXTMaterialVo> list = wxtMaterialVoMapper.selectMaterialPickingList4Pagination(params);
//        //获取商品库存信息
//        HashSet<String> codes = new HashSet<String>();
//        for (WXTMaterialVo wxtMaterialVo : list) {
//        	codes.add(wxtMaterialVo.getAttribute1() + "/" + wxtMaterialVo.getMaterialCode());
//        }
//        List<WXTMaterialVo> inventorys = wxtMaterialVoMapper.selectMaterialInventoryByMaterialCodes(codes);
//        //将库存信息写入商品信息中
//        for (WXTMaterialVo item : list) {
//            ArrayList<MaterialInventoryDTO> skus = new ArrayList<MaterialInventoryDTO>();
//            for (WXTMaterialVo inventory : inventorys) {
//                if(inventory.getMaterialCode().equals(item.getMaterialCode())){
//                    MaterialInventoryDTO materialInventoryDTO = new MaterialInventoryDTO();
//                    BeanUtil.copyProperties(inventory, materialInventoryDTO);
//                    skus.add(materialInventoryDTO);
//                }
//            }
//            item.setInventorys(skus);
//        }

        resultMap.put("rows", list);
        resultMap.put("total", params.getTotalCount());
        resultMap.put("code", "success");
        resultMap.put("errorMsg", "成功");
        return resultMap;
    }

    @Override
    public Map<String, Object> goodsList(MaterialParams params){
        WxTUser user = ContextUtil.getCurUser();
        Map<String, Object> resultMap = new HashMap<String, Object>();
        params.setIs4picking(true);
        params.setIsAdmin(WxTUser.CHEVRON_USER_ROLE.equals(user.getmUserTypes()));
        boolean SP = WxTUser.USER_MODEL_SP.equals(user.getUserModel());
        if (SP) {
            params.setVisibleDealer("|" + user.getOrgId() + "|");
        }
        //2021-04-27 add
		if(!SP){
			if(StringUtils.isNotBlank(params.getVisibleDealer())){
				params.setVisibleDealer("|" + params.getVisibleDealer() + "|");
			}
		}
		//分销商
		if(OrganizationVo.ORG_TYPE_RETAILER.equals(user.getOrgType())) {
			params.setRetailerId(user.getOrgId());
		}
//		boolean isBU = false;
//		int permissionWeight = 0;
//		try {
//			permissionWeight = operationPermissionBizService.getPermissionWeight(user.getUserId(), "POINT_GOODS_2021_WEIGHT");
//		} catch (WxPltException e) {
//			e.printStackTrace();
//		}
//		if(permissionWeight == -1){
//			permissionWeight = 1;
//		}
//		if((permissionWeight & 30) > 0){
//			isBU = true;
//		}
//		if (isBU) {
			//查询当前BU负责的经销商用户
//			List<String> visibleDealers = null;
//			PartnerParams partnerParams = new PartnerParams();
//			partnerParams.setPaging(false);
//			partnerParams.setField("name");
//			Map<String, Object> partnersMap = partnerServiceImpl.queryPartnersNew(partnerParams);
//			List<PartnerView> lstPartners = (List<PartnerView>) partnersMap.get("resultLst");
//			if(!CollectionUtils.isEmpty(lstPartners)){
//				visibleDealers = new ArrayList<String>();
//				for (PartnerView lstPartner : lstPartners) {
//					visibleDealers.add("|" + lstPartner.getId() + "|");
//				}
//			}
//			params.setVisibleDealers(visibleDealers);
//		}

//        // 加德士经销商用户，根据合伙人属性配置获取其对应的物料仓库（1天津仓/2广州仓）
//        if (params.getPointMode()) {
//            String assignedWarehouse = null;
//            boolean isSPuser = WxTUser.USER_MODEL_SP.equals(ContextUtil.getCurUser().getUserModel());
//            if (isSPuser) {
//                Map<String, Object> _params = new HashMap<String, Object>();
//                Long partnerId = user.getOrgId();
//                _params.put("partnerId", partnerId);
//                List<PartnerView> list = organizationVoMapper.selectPartnersByParams(_params);
//                if (null != list && 1 == list.size()) {
//                    assignedWarehouse = list.get(0).getMaterialStock();
//                }
//            }
//            params.setPartnerAssignedWarehouse(assignedWarehouse);
//        }
        // 获取商品
        List<WXTMaterialVo> list = wxtMaterialVoMapper.selectMaterialListByParams(params);

        if(!list.isEmpty()) {
            //获取商品库存信息
            HashSet<String> codes = new HashSet<String>();
            for (WXTMaterialVo wxtMaterialVo : list) {
            	codes.add(wxtMaterialVo.getAttribute1() + "/" + wxtMaterialVo.getMaterialCode());
            }
            List<WXTMaterialVo> inventorys = wxtMaterialVoMapper.selectMaterialInventoryByMaterialCodes(codes);
            //将库存信息写入商品信息中
            for (WXTMaterialVo item : list) {
                ArrayList<MaterialInventoryDTO> skus = new ArrayList<MaterialInventoryDTO>();
                for (WXTMaterialVo inventory : inventorys) {
                    if(inventory.getMaterialCode().equals(item.getMaterialCode())){
                        MaterialInventoryDTO materialInventoryDTO = new MaterialInventoryDTO();
                        BeanUtil.copyProperties(inventory, materialInventoryDTO);
                        skus.add(materialInventoryDTO);
                    }
                }
                item.setInventorys(skus);
            }
        }
        resultMap.put("rows", list);
        resultMap.put("total", params.getTotalCount());
        resultMap.put("code", "success");
        resultMap.put("errorMsg", "成功");
        return resultMap;
    }

    @Override
    public ResponseMap materialCtrl(MaterialParams params) {
        ResponseMap resultMap = new ResponseMap();
        params.setPaging(false);
        params.clearOrderField();
        // 获取商品
        List<WXTMaterialVo> list = wxtMaterialVoMapper.materialCtrl(params);
        ArrayList<WXTMaterialCtrlVo> result = new ArrayList<WXTMaterialCtrlVo>();
        for (WXTMaterialVo wxtMaterialVo : list) {
            WXTMaterialCtrlVo wxtMaterialCtrlVo = new WXTMaterialCtrlVo();
            BeanUtil.copyProperties(wxtMaterialVo,wxtMaterialCtrlVo);
            result.add(wxtMaterialCtrlVo);
        }
        resultMap.put("rows", result);
        resultMap.put("total", result.size());
        resultMap.put("code", "success");
        resultMap.put("errorMsg", "成功");
        return resultMap;
    }

    @Override
    public Map<String, Object> promotionGiftList(MaterialParams params) {
        WxTUser user = ContextUtil.getCurUser();
        Map<String, Object> resultMap = new HashMap<String, Object>();
        Long partnerId = user.getOrgId();
        params.setPartnerId(partnerId);
        // 获取商品
        PointType pointType = ApplicationUtil.getPointTypeByAppType(params.getPointType());
        params.setPointType(pointType != null ? pointType.getValue() : params.getPointType());
        List<WXTMaterialVo> list = wxtMaterialVoMapper.selectPromotionGiftListByParams(params);
        List<WXTMaterialVo> inventorys = new ArrayList<WXTMaterialVo>();
        if(CollectionUtil.isNotEmpty(list)){
            //获取商品库存信息
            HashSet<String> codes = new HashSet<String>();
            for (WXTMaterialVo wxtMaterialVo : list) {
            	codes.add(wxtMaterialVo.getAttribute1() + "/" + wxtMaterialVo.getMaterialCode());
            }
            inventorys = wxtMaterialVoMapper.selectMaterialInventoryByMaterialCodes(codes);
        }
        //将库存信息写入商品信息中
        for (WXTMaterialVo item : list) {
            ArrayList<MaterialInventoryDTO> skus = new ArrayList<MaterialInventoryDTO>();
            for (WXTMaterialVo inventory : inventorys) {
                if(inventory.getMaterialCode().equals(item.getMaterialCode())){
                    MaterialInventoryDTO materialInventoryDTO = new MaterialInventoryDTO();
                    BeanUtil.copyProperties(inventory, materialInventoryDTO);
                    materialInventoryDTO.setVirtualStockQty(item.getAvailableQuantity().toString());
                    materialInventoryDTO.setStockQty(item.getAvailableQuantity().toString());
                    skus.add(materialInventoryDTO);
                }
            }
            item.setVirtualStockQty(item.getAvailableQuantity().toString());
            item.setInventorys(skus);
        }
        resultMap.put("rows", list);
        resultMap.put("total", params.getTotalCount());
        resultMap.put("code", "success");
        resultMap.put("errorMsg", "成功");
        return resultMap;
    }

    @Override
    public ResponseMap couponGiftList(MaterialParams params) throws WxPltException {
    	throw new RuntimeException("暂不支持");
//        ResponseMap resultMap = new ResponseMap();
//	    //将优惠券信息转成json
//        if(StringUtils.isNotBlank(params.getCouponApplyParamsStr())){
//            params.setCouponApplyParams(JSONUtil.toList(JSONUtil.parseArray(params.getCouponApplyParamsStr()), CouponApplyParams.class));
//        }else {
//            resultMap.setErrorMsg("缺少优惠券信息。");
//            resultMap.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
//        }
//        WxTUser user = ContextUtil.getCurUser();
//        Long partnerId = user.getOrgId();
//        params.setPartnerId(partnerId);
//        // 获取商品
//        PointType pointType = ApplicationUtil.getPointTypeByAppType(params.getPointType());
//        params.setPointType(pointType != null ? pointType.getValue() : params.getPointType());
//        List<WXTMaterialVo> list = wxtMaterialVoMapper.selectCouponGiftListByParams(params);
//        if(CollectionUtil.isEmpty(list)){
//            resultMap.put("rows", list);
//            return resultMap;
//        }
//        List<WXTMaterialVo> inventorys = new ArrayList<WXTMaterialVo>();
//        if (CollectionUtil.isNotEmpty(list)) {
//            //获取商品库存信息
//            HashSet<String> codes = new HashSet<String>();
//            for (WXTMaterialVo wxtMaterialVo : list) {
//            	codes.add(wxtMaterialVo.getAttribute1() + "/" + wxtMaterialVo.getMaterialCode());
//            }
//            inventorys = wxtMaterialVoMapper.selectMaterialInventoryByMaterialCodes(codes);
//        }
//
//        //开始合计每个礼品的数量
//        List<CouponApplyParams> couponApplyParams = params.getCouponApplyParams();
//        HashSet<Long> couponIds = new HashSet<Long>();
//        ArrayList<Long> conponOptionIds = new ArrayList<Long>();
//        for (CouponApplyParams couponApplyParam : couponApplyParams) {
//            couponIds.add(couponApplyParam.getCouponId());
//            conponOptionIds.addAll(couponApplyParam.getOptions());
//        }
//        List<CouponOptionDetail> couponOptionDetails = couponOptionDetailMapper.couponDetailByCouponIds(couponIds);
//        for (WXTMaterialVo item : list) {
//            long count = 0;
//            for (Long conponOptionId : conponOptionIds) {
//                for (CouponOptionDetail couponOptionDetail : couponOptionDetails) {
//                    if (conponOptionId.equals(couponOptionDetail.getCouponOptionId()) && item.getId().equals(couponOptionDetail.getMaterialId())) {
//                        count += couponOptionDetail.getQuantity();
//                    }
//                }
//                item.setAvailableQuantity((int)count);
//            }
//        }
//
//        //将库存信息写入商品信息中
//        for (WXTMaterialVo item : list) {
//            ArrayList<MaterialInventoryDTO> skus = new ArrayList<MaterialInventoryDTO>();
//            for (WXTMaterialVo inventory : inventorys) {
//                if (inventory.getMaterialCode().equals(item.getMaterialCode())) {
//                    MaterialInventoryDTO materialInventoryDTO = new MaterialInventoryDTO();
//                    BeanUtil.copyProperties(inventory, materialInventoryDTO);
//                    materialInventoryDTO.setVirtualStockQty(item.getAvailableQuantity().toString());
//                    materialInventoryDTO.setStockQty(item.getAvailableQuantity().toString());
//                    skus.add(materialInventoryDTO);
//                }
//            }
//            item.setInventorys(skus);
//        }
//        resultMap.put("rows", list);
//        resultMap.put("total", params.getTotalCount());
//        resultMap.put("code", "success");
//        resultMap.put("errorMsg", "成功");
//        return resultMap;
    }

    @Override
    public ResponseMap getDealerTabInfo() {
        ResponseMap jsonResponse = new ResponseMap();
        //按照积分大类汇总数据
        List<PointBaseInfo> pointBaseInfo = dicService.getPointBaseInfo();
        List<String> resultPoints = new ArrayList<String>();
        try {
            resultPoints = this.checkPointPermission(pointBaseInfo);
        } catch (WxPltException e) {
            e.printStackTrace();
        }
        jsonResponse.setListResult(new ArrayList<String>(resultPoints));
        return jsonResponse;
    }

    /**
     * 判断积分类型权限分配(返回积分权限CODE)
     *
     * @return
     */
    public List<String> checkPointPermission(List<PointBaseInfo> points) throws WxPltException {
        HashSet<String> resultPoints = new HashSet<String>();
        WxTUser curUser = ContextUtil.getCurUser();
        HashSet<String> ciPoints = new HashSet<String>();
        HashSet<String> cdmPoints = new HashSet<String>();
        HashSet<String> permissionPoints = new HashSet<String>();
        for (PointBaseInfo point : points) {
            if ((point.getChannel() & 1) > 0) {
                cdmPoints.add(point.getPermissionCode());
            }
            if ((point.getChannel() & 2) > 0) {
                ciPoints.add(point.getPermissionCode());
            }
            //获取积分类型是否需要权限配置等信息
            if (point.getPartnerPermission() != null && point.getPartnerPermission()) {
                permissionPoints.add(point.getPermissionCode());
            }
        }
        int permissionWeight = 0;
        try {
            permissionWeight = SpringUtils.getBean(OperationPermissionBizService.class).getPermissionWeight(curUser.getUserId(), "point.exchange.type.permission");
        } catch (WxPltException e) {
            throw new WxPltException("权限异常。");
        }
        if (permissionWeight == 0) {
            return new ArrayList<String>();
        }
        if (permissionWeight == -1 || (permissionWeight & 1) == 1) {
            for (PointBaseInfo point : points) {
                resultPoints.add(point.getPermissionCode());
            }
            return new ArrayList<String>(resultPoints);
        }
        if ((permissionWeight & 2) > 0) {
            resultPoints.addAll(ciPoints);
        }
        if ((permissionWeight & 4) > 0) {
            resultPoints.addAll(cdmPoints);
        }
        for (PointBaseInfo point : points) {
            if ((permissionWeight & point.getWeight()) > 0) {
                resultPoints.add(point.getPermissionCode());
            }
        }
        //不需要按照经销商判断权限
        if ((permissionWeight & 4096) == 0 || (permissionWeight & 4) > 0 || (permissionWeight & 2) > 0) {
            return new ArrayList<String>(resultPoints);
        }
        //下面开始判断经销商权限
        Long dealerId = curUser.getOrgId();
        //业务功能权限
        ArrayList<String> hasPoint = new ArrayList<String>();
        Map<String, Object> paramMap = new HashMap<String, Object>();
        paramMap.put("dealerId", dealerId);
        List<DealerBusinessFun> dealerBusinessFuns = dealerBusinessFunMapper.queryActiveFunCodesByParams(paramMap);
        for (DealerBusinessFun dealerBusinessFun : dealerBusinessFuns) {
            hasPoint.add(dealerBusinessFun.getBusinessFunCode());
        }
        //剔除需要权限控制的积分类型中没有配置权限的积分类型
        hasPoint = new ArrayList<String>(CollectionUtil.intersection(permissionPoints, hasPoint));
        //求赋予的权限和经销商配置的权限交集
        Collection<String> intersection = CollectionUtil.intersection(resultPoints, hasPoint);
        resultPoints.removeAll(permissionPoints);
        resultPoints.addAll(intersection);
        return new ArrayList<String>(resultPoints);
    }
    
    private List<WXTMaterialVo> setFakeVirtualStock(List<WXTMaterialVo> list) {

        if (list == null || list.isEmpty()) {
            return list;
        }

        WxTUser user = ContextUtil.getCurUser();
        Long partnerId = user.getOrgId();
        java.math.BigDecimal scale = getVirtualStockScale(partnerId);

        for (WXTMaterialVo vo : list) {
            String virtualStockQty = vo.getVirtualStockQty();
            Double vStock = Double.valueOf(virtualStockQty);
            if (!StringUtils.isNumeric(virtualStockQty)) {
                continue;
            }

            BigDecimal fakeStock = new BigDecimal(0);
            fakeStock = scale.multiply(new BigDecimal(vStock)).divide(new BigDecimal(100D));
            vo.setVirtualStockQty(String.valueOf(fakeStock.longValue()));
        }
        return list;
    }

    private BigDecimal getVirtualStockScale (Long partnerId) {

        // SP分区域申请
        boolean isRegionDevided = false;
        Long regionNumber = wxtMaterialApplicationVoMapper.countSPRegionNumber(partnerId);
        if (null != regionNumber && regionNumber.intValue() > 0) {
            isRegionDevided = true;
        }

        Map<String, Object> resMap = wxtMaterialApplicationVoMapper.selectSellInSacle(partnerId);
        if(null == resMap || !resMap.containsKey("isPartner")) {
            return new BigDecimal(100D);
        }
        Integer isPartner = (Integer)resMap.get("isPartner");
        if (isPartner ==null || isPartner < 1) {
            return new BigDecimal(100D);
        }
        java.math.BigDecimal scale = (java.math.BigDecimal)resMap.get("scale");
        Integer diffMonth =  (Integer)resMap.get("diffMonth");
        Integer diffDay =  (Integer)resMap.get("diffDay");
        java.math.BigDecimal total =  (java.math.BigDecimal)resMap.get("total");

        if (diffMonth < 1 || (diffMonth == 1 && diffDay< 1)) {
            scale = new BigDecimal(30);
        } else {
            if (null == scale || scale.equals(new BigDecimal(0)) || null == total || total.compareTo(BigDecimal.ZERO) == 0) {
                scale = new BigDecimal(10);
            } else {
                scale = scale.multiply(new BigDecimal(0.85d));
            }
        }
        if (scale.compareTo(new BigDecimal(10)) < 0){
            scale = new BigDecimal(10);
        }

        if (scale.compareTo(new BigDecimal(60)) > 0){
            scale = new BigDecimal(60);
        }

        // SP分区域申请， 比例均分
        if (isRegionDevided) {
            scale =scale.divide(new BigDecimal(regionNumber), 2, RoundingMode.HALF_UP);
        }

        return scale;
    }


    /**
     * 添加/修改物料信息
     * @param params
     * @return Map
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public WXTMaterialVo createCoupon(WXTMaterialVo params) throws Exception {
        try {
            this.couponCreateCheck(params);
            WXTMaterialVo record = new WXTMaterialVo();
            record.setMaterialName(params.getMaterialName());
            record.setMaterialNameEn(params.getMaterialName());
            record.setMaterialDesc(params.getMaterialDesc());
            record.setMaterialSource("COUPON");
            record.setMaterialType("COUPON");
            record.setMaterialPicture(0L);
            record.setMaterialPrice(new BigDecimal(9999999));
            record.setMaterialUnit("张");
            record.setMaterialSizel(0);
            record.setMaterialSizew(0);
            record.setMaterialSizeh(0);
            record.setPointMode(true);
            record.setMaterialWeight(new BigDecimal(0));
            record.setMaterialMakePeriod(30);
            record.setMaterialProductionPeriod(365);
            record.setMaterialCategory(params.getMaterialCategory());
            record.setAttribute2(params.getCouponChooseInfo());
            record.setAttribute3(params.getCouponChooseQuantity().toString());
            record.setAttribute4(params.getCouponEffectiveDate());
            record.setAttribute5(params.getCouponExpiryDate());
            //查看是否是系统已经配置的积分类型，如果不是就是促销活动的积分类型
            PointType pointType = PointType.getByApplicationType(params.getPointType());
            if (pointType == null) {
                record.setExtFlag(params.getExtFlag() == null ? 2 : (params.getExtFlag() | 2));
            }
            record.setPointType(params.getPointType());
            List<PartnerView> visiblePartners = params.getVisiblePartners();
            String invisibleDealer = "";
            if (CollectionUtil.isNotEmpty(visiblePartners)) {
                ArrayList<Long> visiblePartnerIds = new ArrayList<Long>();
                for (PartnerView visiblePartner : visiblePartners) {
                    visiblePartnerIds.add(visiblePartner.getId());
                }
                invisibleDealer = "|" + CollectionUtil.join(visiblePartnerIds, "|") + "|";
            }
            record.setInvisibleDealer(invisibleDealer);
            record.setDeleteFlag(false);
            Date now = DateUtil.getCurrentDate();
            Long userId = ContextUtil.getCurUserId();
            record.setMaterialCode("M" + wxTPropertiesServiceImpl.getSequenceByType(SequenceTypes.MATERIAL_CODE, 7, 1));
            record.setLastUpdatedBy(userId);
            record.setLastUpdateTime(DateUtil.getCurrentDate());
            record.setCreatedBy(userId);
            record.setCreationTime(now);
            record.setState("SYNCED");
            record.setSmcStatus(0);
            record.setOverLimit(false);
            record.setCustEmail("<EMAIL>");
            record.setSupplierEmail("<EMAIL>");
            wxtMaterialVoMapper.insertSelective(record);
            updateMaterialImageList(record.getId(), params.getMaterialImages());
            updateMaterialSkuList(record.getId(), record.getMaterialCode(), params.getSkuList());
            //开始创建优惠券明细数据
            List<CouponOptio> couponOptios = params.getCouponOptions();
            for (CouponOptio couponOptio : couponOptios) {
                couponOptio.setId(null);
                couponOptio.setCouponId(record.getId());
                couponOptio.setCreateTime(now);
                couponOptio.setUpdateTime(now);
                couponOptio.setCreateUserId(userId);
                couponOptio.setUpdateUserId(userId);
                couponOptioMapper.insertSelective(couponOptio);
                for (CouponOptionDetail optionDetail : couponOptio.getOptionDetails()) {
                    optionDetail.setCouponOptionId(couponOptio.getId());
                    optionDetail.setId(null);
                    optionDetail.setCreateTime(now);
                    optionDetail.setUpdateTime(now);
                    optionDetail.setCreateUserId(userId);
                    optionDetail.setUpdateUserId(userId);
                    couponOptionDetailMapper.insertSelective(optionDetail);
                }
            }
            return record;
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            throw e;
        }
    }

    @Override
    public WXTMaterialVo getCouponById(Long couponId) throws Exception {
        WXTMaterialVo wxtMaterialVo = wxtMaterialVoMapper.selectByPrimaryKey(couponId);
        CouPonInfo couPonInfo = new CouPonInfo();
        couPonInfo.setId(wxtMaterialVo.getId().toString());
        couPonInfo.setChooseQuantity(Integer.parseInt(wxtMaterialVo.getAttribute3()));
        couPonInfo.setChooseInfo(wxtMaterialVo.getAttribute2());
        couPonInfo.setEffectiveDate(wxtMaterialVo.getAttribute4());
        couPonInfo.setExpiryDate(wxtMaterialVo.getAttribute5());
        couPonInfo.setName(wxtMaterialVo.getMaterialName());
        couPonInfo.setDescribe(wxtMaterialVo.getMaterialDesc());
        List<CouponOptio> couponOptios = couponOptioMapper.selectByCouponId(wxtMaterialVo.getId());
        wxtMaterialVo.setCouponOptions(couponOptios);
        return wxtMaterialVo;
    }

    private void couponCreateCheck(WXTMaterialVo params) throws WxPltException {
        WXTMaterialVoExample example = new WXTMaterialVoExample();
        if (null == params.getPointType() || params.getPointType().isEmpty()) {
            example.createCriteria().andMaterialNameEqualTo(params.getMaterialName()).andPointTypeIsNull();
        } else {
            example.createCriteria().andMaterialNameEqualTo(params.getMaterialName()).andPointTypeEqualTo(params.getPointType());
        }
        List<WXTMaterialVo> olds = wxtMaterialVoMapper.selectByExample(example);
        if (null != olds && !olds.isEmpty()) {
            throw new WxPltException("创建优惠券失败, 优惠券中文名称重复.");
        }
        if (CollectionUtil.isEmpty(params.getCouponOptions())) {
            throw new WxPltException("优惠券内容为空。");
        }
        for (CouponOptio couponOptio : params.getCouponOptions()) {
            if (CollectionUtil.isEmpty(couponOptio.getOptionDetails())) {
                throw new WxPltException(couponOptio.getOptionName() + " 关联物料为空。");
            }
        }
    }

}