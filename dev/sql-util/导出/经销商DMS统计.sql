--经销商合作门店DMS匹配情况
select b.id, max(b.organization_name) "经销商名称", 
sum(case when b.from_source=1 then b.active_count else 0 end) "乘用车合作门店数",
sum(case when b.from_source=1 then b.dms_count else 0 end) "乘用车DMS匹配数",
sum(case when b.from_source=2 then b.active_count else 0 end) "商用油合作门店数",
sum(case when b.from_source=2 then b.dms_count else 0 end) "商用油DMS匹配数" from (
select a.id, max(a.organization_name) organization_name, sum(a.dms_count) dms_count, a.from_source, count(1) active_count  from (
select o.id, o.organization_name, case when t1.dms_key is not null and t1.dms_key != '' then 1 else 0 end dms_count, t1.from_source  from wx_t_work_shop t1
 left join wx_t_workshop_partner wp on wp.workshop_id=t1.id 
left join wx_t_organization o on o.id=wp.partner_id
LEFT JOIN wx_t_partner_o2o_enterprise pe ON pe.partner_id = o.id
where o.id <> 9 and exists (select 1 from dw_dms_workshop dw where dw.distributor_id=pe.distributor_id)
and t1.delete_flag=0 and t1.status='3'
) a
group by a.id, a.from_source) b
group by b.id


--已匹配DMS合作门店详情
select o.organization_name "经销商名称", t1.id "门店ID", t1.work_shop_name "门店名称", t1.work_shop_address "门店地址",
ddw.Customer_Name "匹配DMS门店名称", ddw.[Customer_Code] "匹配DMS门店编码",
case when t1.from_source=1 then '乘用车业务' when t1.from_source=2 then '商用油业务' end "门店来源",  
(case when exists (select 1 from wx_t_workshop_employee we where we.version_no=1 and we.workshop_id=t1.id) then '是' else '否' end) "B2B店",
(case when t1.join_location_plan=1 then '是' else '否' end/*定位店*/) "定位店",
(case when t1.shop_recruitment=1 then '是' else '否' end/**/) "店招店",
(case when t1.shop_recruitment=1 then (select top 1 x1.settlement_amount from(
		(select ma.settlement_amount from wx_t_mkt_ci_apply ma where ma.store_id=t1.id and ma.form_status>=60
		union all select ma.settlement_amount from wx_t_mkt_cdm_apply ma where ma.store_id=t1.id and ma.form_status>=45
		union all select v.price from view_mkt_apply v where v.flow_finish3=1 and v.workshop_id=t1.id
		union all select t1.recruitment_amount)) x1) else null end/**/) "店招金额",
t1.create_time "扫店时间",
(select count(1)   
from wx_task_sub s join wx_task_main m on m.task_main_id = s.task_main_id 
where m.tmb_type_code in ('TT_2_XD', 'TT_2_XD_CAI')   and s.task_status = 4 and s.org_id=t1.id) "巡店次数",
x2.sj1 "最近1次巡店",x2.sj2 "最近2次巡店",x2.sj3 "最近3次巡店",x2.sj4 "最近4次巡店",x2.sj5 "最近5次巡店",x2.sj6 "最近6次巡店"
  from wx_t_work_shop t1
 left join wx_t_workshop_partner wp on wp.workshop_id=t1.id 
left join wx_t_organization o on o.id=wp.partner_id
LEFT JOIN wx_t_partner_o2o_enterprise pe ON pe.partner_id = o.id
left join dw_dms_workshop ddw on ddw.[Distributor_Code] + '/' + ddw.[Customer_Code]=t1.dms_key
left join (select xx1.org_id, max(case when xx1.rank=1 then xx1.xg_sj else null end) sj1
, max(case when xx1.rank=2 then xx1.xg_sj else null end) sj2
, max(case when xx1.rank=3 then xx1.xg_sj else null end) sj3
, max(case when xx1.rank=4 then xx1.xg_sj else null end) sj4
, max(case when xx1.rank=5 then xx1.xg_sj else null end) sj5
, max(case when xx1.rank=6 then xx1.xg_sj else null end) sj6 from (
select s.org_id,s.xg_sj,ROW_NUMBER() OVER (PARTITION BY s.org_id ORDER BY s.xg_sj DESC) AS rank   
from wx_task_sub s join wx_task_main m on m.task_main_id = s.task_main_id 
where m.tmb_type_code in ('TT_2_XD', 'TT_2_XD_CAI')   and s.task_status = 4) xx1
group by xx1.org_id) x2 on x2.org_id=t1.id
where o.id <> 9 and exists (select 1 from dw_dms_workshop dw where dw.distributor_id=pe.distributor_id)
and t1.delete_flag=0 and t1.status='3'
and t1.dms_key is not null and t1.dms_key != ''
order by o.id,t1.create_time
