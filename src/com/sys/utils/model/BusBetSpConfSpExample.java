package com.sys.utils.model;

import java.util.ArrayList;
import java.util.List;

/**
 * 合伙人之间合作设置合伙人单表查询条件
 * <AUTHOR>
 * @version 1.0 2017-10-25 10:50
 */
public class BusBetSpConfSpExample {
	protected String orderByClause;
	
	protected boolean distinct;
	
	protected List<Criteria> oredCriteria;
	
	public BusBetSpConfSpExample() {
		oredCriteria = new ArrayList<Criteria>();
	}
	
	public void setOrderByClause(String orderByClause) {
		this.orderByClause = orderByClause;
	}
	
	public String getOrderByClause() {
		return orderByClause;
	}
	
	public void setDistinct(boolean distinct) {
		this.distinct = distinct;
	}
	
	public boolean isDistinct() {
		return distinct;
	}
	
	public List<Criteria> getOredCriteria() {
		return oredCriteria;
	}
	
	public void or(Criteria criteria) {
		oredCriteria.add(criteria);
	}
	
	public Criteria or() {
		Criteria criteria = createCriteriaInternal();
		oredCriteria.add(criteria);
		return criteria;
	}
	
	public Criteria createCriteria() {
		Criteria criteria = createCriteriaInternal();
		if (oredCriteria.size() == 0) {
			oredCriteria.add(criteria);
		}
		return criteria;
	}
	
	protected Criteria createCriteriaInternal() {
		Criteria criteria = new Criteria();
		return criteria;
	}
	
	public void clear() {
		oredCriteria.clear();
		orderByClause = null;
		distinct = false;
	}
	
	protected abstract static class GeneratedCriteria {
		protected List<Criterion> criteria;
		
		protected GeneratedCriteria() {
			super();
			criteria = new ArrayList<Criterion>();
		}
		
		public boolean isValid() {
			return criteria.size() > 0;
		}
		
		public List<Criterion> getAllCriteria() {
			return criteria;
		}
		
		public List<Criterion> getCriteria() {
			return criteria;
		}
		
		protected void addCriterion(String condition) {
			if (condition == null) {
				throw new RuntimeException("Value for condition cannot be null");
			}
			criteria.add(new Criterion(condition));
		}
		
		protected void addCriterion(String condition, Object value, String property) {
			if (value == null) {
				throw new RuntimeException("Value for " + property + " cannot be null");
			}
			criteria.add(new Criterion(condition, value));
		}
		
		protected void addCriterion(String condition, Object value1, Object value2, String property) {
			if (value1 == null || value2 == null) {
				throw new RuntimeException("Between values for " + property + " cannot be null");
			}
			criteria.add(new Criterion(condition, value1, value2));
		}

		public Criteria andPartnerIdIsNull() {
			addCriterion("partner_id is null");
			return (Criteria) this;
		}
		
		public Criteria andPartnerIdIsNotNull() {
			addCriterion("partner_id is not null");
			return (Criteria) this;
		}
		
		public Criteria andPartnerIdEqualTo(Long value) {
			addCriterion("partner_id =", value, "partnerId");
			return (Criteria) this;
		}
		
		public Criteria andPartnerIdNotEqualTo(Long value) {
			addCriterion("partner_id <>", value, "partnerId");
			return (Criteria) this;
		}
		
		public Criteria andPartnerIdGreaterThan(Long value) {
			addCriterion("partner_id >", value, "partnerId");
			return (Criteria) this;
		}
		
		public Criteria andPartnerIdGreaterThanOrEqualTo(Long value) {
			addCriterion("partner_id >=", value, "partnerId");
			return (Criteria) this;
		}
		
		public Criteria andPartnerIdLessThan(Long value) {
			addCriterion("partner_id <", value, "partnerId");
			return (Criteria) this;
		}
		
		public Criteria andPartnerIdLessThanOrEqualTo(Long value) {
			addCriterion("partner_id <=", value, "partnerId");
			return (Criteria) this;
		}
		
		public Criteria andPartnerIdIn(List<Long> values) {
			addCriterion("partner_id in", values, "partnerId");
			return (Criteria) this;
		}
		
		public Criteria andPartnerIdNotIn(List<Long> values) {
			addCriterion("partner_id not in", values, "partnerId");
			return (Criteria) this;
		}
		
		public Criteria andPartnerIdBetween(Long value1, Long value2) {
			addCriterion("partner_id between", value1, value2, "partnerId");
			return (Criteria) this;
		}
		
		public Criteria andPartnerIdNotBetween(Long value1, Long value2) {
			addCriterion("partner_id not between", value1, value2, "partnerId");
			return (Criteria) this;
		}

		public Criteria andConfigIdIsNull() {
			addCriterion("config_id is null");
			return (Criteria) this;
		}
		
		public Criteria andConfigIdIsNotNull() {
			addCriterion("config_id is not null");
			return (Criteria) this;
		}
		
		public Criteria andConfigIdEqualTo(Long value) {
			addCriterion("config_id =", value, "configId");
			return (Criteria) this;
		}
		
		public Criteria andConfigIdNotEqualTo(Long value) {
			addCriterion("config_id <>", value, "configId");
			return (Criteria) this;
		}
		
		public Criteria andConfigIdGreaterThan(Long value) {
			addCriterion("config_id >", value, "configId");
			return (Criteria) this;
		}
		
		public Criteria andConfigIdGreaterThanOrEqualTo(Long value) {
			addCriterion("config_id >=", value, "configId");
			return (Criteria) this;
		}
		
		public Criteria andConfigIdLessThan(Long value) {
			addCriterion("config_id <", value, "configId");
			return (Criteria) this;
		}
		
		public Criteria andConfigIdLessThanOrEqualTo(Long value) {
			addCriterion("config_id <=", value, "configId");
			return (Criteria) this;
		}
		
		public Criteria andConfigIdIn(List<Long> values) {
			addCriterion("config_id in", values, "configId");
			return (Criteria) this;
		}
		
		public Criteria andConfigIdNotIn(List<Long> values) {
			addCriterion("config_id not in", values, "configId");
			return (Criteria) this;
		}
		
		public Criteria andConfigIdBetween(Long value1, Long value2) {
			addCriterion("config_id between", value1, value2, "configId");
			return (Criteria) this;
		}
		
		public Criteria andConfigIdNotBetween(Long value1, Long value2) {
			addCriterion("config_id not between", value1, value2, "configId");
			return (Criteria) this;
		}
	}
	
	public static class Criteria extends GeneratedCriteria {
		
		protected Criteria() {
			super();
		}
	}
	
	public static class Criterion {
		private String condition;
		
		private Object value;
		
		private Object secondValue;
		
		private boolean noValue;
		
		private boolean singleValue;
		
		private boolean betweenValue;
		
		private boolean listValue;
		
		private String typeHandler;
		
		public String getCondition() {
			return condition;
		}
		
		public Object getValue() {
			return value;
		}
		
		public Object getSecondValue() {
			return secondValue;
		}
		
		public boolean isNoValue() {
			return noValue;
		}
		
		public boolean isSingleValue() {
			return singleValue;
		}
		
		public boolean isBetweenValue() {
			return betweenValue;
		}
		
		public boolean isListValue() {
			return listValue;
		}
		
		public String getTypeHandler() {
			return typeHandler;
		}
		
		protected Criterion(String condition) {
			super();
			this.condition = condition;
			this.typeHandler = null;
			this.noValue = true;
		}
		
		protected Criterion(String condition, Object value, String typeHandler) {
			super();
			this.condition = condition;
			this.value = value;
			this.typeHandler = typeHandler;
			if (value instanceof List<?>) {
				this.listValue = true;
			} else {
				this.singleValue = true;
			}
		}
		
		protected Criterion(String condition, Object value) {
			this(condition, value, null);
		}
		
		protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
			super();
			this.condition = condition;
			this.value = value;
			this.secondValue = secondValue;
			this.typeHandler = typeHandler;
			this.betweenValue = true;
		}
		
		protected Criterion(String condition, Object value, Object secondValue) {
			this(condition, value, secondValue, null);
		}
	}
}
