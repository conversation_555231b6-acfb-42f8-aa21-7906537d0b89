package com.sys.organization.service.impl;

import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;

import javax.annotation.Resource;

import com.sys.organization.model.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowCallbackHandler;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import com.sys.auth.model.TreeNode;
import com.sys.auth.model.WxTUser;
import com.sys.auth.service.UserRoleService;
import com.sys.auth.service.WxUserServiceI;
import com.chevron.importdata.ImportDataPageModelUtil;
import com.chevron.pms.service.WorkShopService;
import com.common.util.ContextUtil;
import com.common.util.StringUtils;
import com.common.util.json.JsonGenerator;
import com.sys.organization.dao.ImprtDataSetsVoMapper;
import com.sys.organization.dao.OrganizationVoMapper;
import com.sys.organization.model.OrganizationVoExample.Criteria;
import com.sys.organization.service.OrganizationService;

@Service
public class OrganizationServiceImpl implements OrganizationService {

	@Resource
	OrganizationVoMapper organizationVoMapper;

	@Resource
	WorkShopService workShopServiceImpl;

	@Resource
	public UserRoleService userRoleService;

	@Resource
	WxUserServiceI wxUserService;

	@Resource
	ImprtDataSetsVoMapper imprtDataSetsVoMapper;

	@Autowired
	private JdbcTemplate wxJdbcTemplate;

	@Override
	public OrganizationVo getOrganizationByCode(String organizationCode) throws Exception {
		OrganizationVoExample example = new OrganizationVoExample();
		Criteria criteria = example.createCriteria();
		criteria.andOrganizationCodeEqualTo(organizationCode);
		List<OrganizationVo> organizationVoList = organizationVoMapper.selectByExample(example);
		if (null != organizationVoList && organizationVoList.size() > 0) {
			return organizationVoList.get(0);
		} else {
			return null;
		}
	}

	@Override
	public OrganizationVo selectByPrimaryKey(long id) {
		return organizationVoMapper.selectByPrimaryKey(id);
	}

	@Override
	public Map<String, String> delete(String id) {
		Map<String, String> map = new HashMap<String, String>();
		organizationVoMapper.deleteByPrimaryKey(Long.valueOf(id));
		map.put("result", "success");
		return map;
	}

	// add by bo.liu start
	@Override
	public Map<String, Object> getOrgTrees() {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		Map<String, Object> reqMap = new HashMap<String, Object>();
		WxTUser wxUser = ContextUtil.getCurUser();
		resultMap.put("code", "success");
		try {
			reqMap.put("orgCode", OrganizationVo.ROOT_ORG_CODE);
			OrganizationVo orgVo = organizationVoMapper.selectByOrgCode(reqMap);
			if (null == orgVo) {
				orgVo = new OrganizationVo();
				orgVo.setOrganizationCode(OrganizationVo.ROOT_ORG_CODE);
				orgVo.setOrganizationName(OrganizationVo.ROOT_ORG_NAME);
				orgVo.setParentId(0L);
				orgVo.setType(OrganizationVo.ORG_ZUZHI_TYPE);
				orgVo.setStatus(OrganizationVo.ORG_START_STATUS);
				orgVo.setSort(0);
				orgVo.setCreator(wxUser.getUserId());
				organizationVoMapper.insertSelective(orgVo);

			}

			WxTUser user = ContextUtil.getCurUser();
			if(WxTUser.USER_MODEL_CHEVRON.equals(user.getUserModel()) && user.getUserId() != 1){
				reqMap.put("loginUserId", user.getUserId());
				if(StringUtils.isNotBlank(user.getCai())){
					reqMap.put("loginCai", user.getCai());
				}
				reqMap.put("salesChannel", user.getSalesChannel());
				reqMap.put("orgLimit", user._isChevronLimitModel());
			}
			List<OrganizationVoTree> orgNodes = organizationVoMapper.selectByOrgCodeForTreeNodes(reqMap);
			List<OrganizationVoTree> children = JsonGenerator.listToTree(orgNodes);
			// 挂跟节点
			for (OrganizationVoTree child : children) {
				child.setPid(orgVo.getId());
				child.setText(child.getOrganizationName());
			}
			List<TreeNode> list = new ArrayList<TreeNode>();
			// 根节点
			OrganizationVoTree root = new OrganizationVoTree();
			root.setId(orgVo.getId());
			root.setText(orgVo.getOrganizationName());
			root.setOrganizationCode(orgVo.getOrganizationCode());
			root.setOrganizationName(orgVo.getOrganizationName());
			root.setSort(orgVo.getSort());
			root.setType(orgVo.getType());
			root.setStatus(orgVo.getStatus());
			root.setChildren(children);
			root.setLevel(1);
			list.add(root);
			resultMap.put("resultJsonList", list);

		} catch (Exception ex) {
			resultMap.put("code", "syserror");
			ex.printStackTrace();
		}
		return resultMap;
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public Map<String, Object> insertOrg(OrganizationVo organiztionVo) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		Map<String, Object> reqMap = new HashMap<String, Object>();
		WxTUser wxUser = ContextUtil.getCurUser();
		resultMap.put("code", "success");
		try {
			if (null != organiztionVo) {
				String newOrgCode = organiztionVo.getOrganizationCode() + generateOrgCode();// 父节点code +随机数
				reqMap.put("orgCode", newOrgCode);
				OrganizationVo orgVo = organizationVoMapper.selectByOrgCode(reqMap);
				while (true) {
					if (orgVo != null) {
						newOrgCode = organiztionVo.getOrganizationCode() + generateOrgCode();// 父节点code +随机数
						reqMap.put("orgCode", newOrgCode);
						orgVo = organizationVoMapper.selectByOrgCode(reqMap);
					} else {
						break;
					}
				}
				// System.out.println("-----reqMap.getOrgCode"+reqMap.get("orgCode"));
				organiztionVo.setOrganizationCode(reqMap.get("orgCode").toString());
				reqMap.put("pid", organiztionVo.getParentId());
				reqMap.put("orgName", organiztionVo.getOrganizationName());
				OrganizationVo orgVo1 = organizationVoMapper.selectByOrgNameAndPid(reqMap);
				if (orgVo1 != null) {
					resultMap.put("code", "nameIsExist");
					return resultMap;
				}
				organiztionVo.setCreator(wxUser.getUserId());
				organizationVoMapper.insertSelective(organiztionVo);
				resultMap.put("addedid", organiztionVo.getId());// organizationVoMapper.getMaxId());//modify
																// by bo.liu
																// 0816
			}

		} catch (Exception ex) {
			// 手动触发回滚
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			resultMap.put("code", "syserror");
			ex.printStackTrace();
		}
		return resultMap;
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public Map<String, Object> deleteOrg(Long id) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		Map<String, Object> reqMap = new HashMap<String, Object>();
		resultMap.put("code", "success");
		try {
			if (null != id) {
				reqMap.put("id", id);
				List<Long> orgIds = organizationVoMapper.getAllNewChildOrgId(id);
				reqMap.put("orgIds", orgIds);
				organizationVoMapper.modifyOrgAndChildsStatus(reqMap);
			} else {
				resultMap.put("code", "isNullDeleteObj");
			}
		} catch (Exception ex) {
			// 手动触发回滚
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			resultMap.put("code", "syserror");
			ex.printStackTrace();
		}
		return resultMap;
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public Map<String, Object> updateOrg(OrganizationVo organiztionVo) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		WxTUser wxUser = ContextUtil.getCurUser();
		resultMap.put("code", "success");
		try {
			if (null != organiztionVo) {
				organiztionVo.setUpdator(wxUser.getUserId());
				organizationVoMapper.updateByOrganizationCode(organiztionVo);
			} else {
				resultMap.put("code", "isNullUpdateObj");
			}

		} catch (Exception ex) {
			// 手动触发回滚
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			resultMap.put("code", "syserror");
			ex.printStackTrace();
		}
		return resultMap;
	}

	@Override
	public Map<String, Object> getUserByOrgId(Long id, String mUserNameOrAccount, String orgType) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		List<WxTUser> userLst = new ArrayList<WxTUser>();
		WxTUser currentUser = ContextUtil.getCurUser();
		resultMap.put("code", "success");
		try {
			if ("1".equals(orgType)) {
				// 查询门店内用户 add by lizhentao ********
				userLst = wxUserService.findUserByOrganizationId(id + "", "1");
			} else {
				Map<String, Object> reqMap = new HashMap<String, Object>();
				reqMap.put("userId", currentUser.getUserId());
				if (null == mUserNameOrAccount || mUserNameOrAccount.equals("")) {
					mUserNameOrAccount = null;
				}

				// 0.判断是否是根节点
				if (null == id) {
					// 代表根节点，查询所有用户
					reqMap.put("orgmodel", null);
					reqMap.put("mUserNameOrAccount", mUserNameOrAccount);
					userLst = wxUserService.getUsersByOrgList(reqMap);
				} else {
					// 根据id识别此org是否根节点，根节点的parent_id为0
					OrganizationVo orgVo = organizationVoMapper.selectByPrimaryKey(id);
					if (orgVo.getParentId() == 0L) {
						// 代表根节点查询所有用户
						reqMap.put("orgmodel", null);
						reqMap.put("mUserNameOrAccount", mUserNameOrAccount);
						userLst = wxUserService.getUsersByOrgList(reqMap);
					} else {
						// 获取组织id
						List<Long> orgIds = organizationVoMapper.getAllNewChildOrgId(id);
						// 获取，包含组织对应的所有用户
						reqMap.put("orgmodel", 1);// 查询选中的一个组织的所有用户
						reqMap.put("orgIdLst", orgIds);
						reqMap.put("mUserNameOrAccount", mUserNameOrAccount);
						ImportDataPageModelUtil newPage = new ImportDataPageModelUtil(orgIds, 1000);
						int toltalPage = newPage.getTotalPages();
						// 4.通过循环把数据插入数据库中
						for (int i = 1; i <= toltalPage; i++) {
							List<Long> orgIdsDemo = newPage.getObjects(i);
							reqMap.put("orgIdLst", orgIdsDemo);
							List<WxTUser> userLstDemo = new ArrayList<WxTUser>();
							userLstDemo = wxUserService.getUsersByOrgList(reqMap);
							userLst.addAll(userLstDemo);

						}

						// userLst = wxUserService.getUsersByOrgList(reqMap);
					}
				}

				if (currentUser.getmUserTypes() == WxTUser.SERVICE_PARTNER_ADMIN_USER_ROLE
						|| currentUser.getmUserTypes() == WxTUser.OTHER_USER_ROLE)// 过滤用户 针对partner admin用户
				{
					userLst = userRoleService.getUsersIsNotSPMandSPA(userLst, currentUser.getOrgId(),
							currentUser.getmUserTypes());
				}
			}

			resultMap.put("userList", userLst);
		} catch (Exception ex) {
			resultMap.put("userList", userLst);
			resultMap.put("code", "syserror");
			ex.printStackTrace();
		}
		return resultMap;
	}

	public String generateOrgCode() {
		Random random = new Random();
		String result = "";
		for (int i = 0; i < 4; i++) {
			result += random.nextInt(10);
		}
		// System.out.println("-------------by ljc"+result);
		return result;
	}

	@Override
	public List<OrganizationVo> getOrganizationByName(String organizationName) {
		OrganizationVoExample example = new OrganizationVoExample();
		Criteria criteria = example.createCriteria();
		criteria.andOrganizationNameLike(organizationName);
		List<OrganizationVo> organizationVoList = organizationVoMapper.selectByExample(example);
		if (null != organizationVoList && organizationVoList.size() > 0) {
			return organizationVoList;
		}
		return null;
	}

	@Override
	public Map<String, Object> getOrgTreesNew() {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		Map<String, Object> reqMap = new HashMap<String, Object>();
		WxTUser wxUser = ContextUtil.getCurUser();
		resultMap.put("code", "success");
		try {
			reqMap.put("orgCode", OrganizationVo.ROOT_ORG_CODE);
			OrganizationVo orgVo = organizationVoMapper.selectByOrgCode(reqMap);
			if (null == orgVo) {
				orgVo = new OrganizationVo();
				orgVo.setOrganizationCode(OrganizationVo.ROOT_ORG_CODE);
				orgVo.setOrganizationName(OrganizationVo.ROOT_ORG_NAME);
				orgVo.setParentId(0L);
				orgVo.setType(OrganizationVo.ORG_ZUZHI_TYPE);
				orgVo.setStatus(OrganizationVo.ORG_START_STATUS);
				orgVo.setSort(0);
				orgVo.setEsspflag(0);
				orgVo.setCreator(wxUser.getUserId());
				organizationVoMapper.insertSelective(orgVo);

			}
			reqMap.put("parentId", orgVo.getId());
			List<OrganizationVoTree> orgNodes = organizationVoMapper.selectByOrgByParentId(reqMap);
			List<OrganizationVoTree> resultNodes = new ArrayList<OrganizationVoTree>();
			for (OrganizationVoTree orgTree : orgNodes) {
				orgTree.setText(orgTree.getOrganizationName());
				// orgTree.setLeaf(false);
				orgTree.setEsspflag(orgTree.getEsspflag());
				orgTree.setExpanded(false);
				resultNodes.add(orgTree);
			}
			// System.out.println("--------resultNodes:"+resultNodes.size());
			List<OrganizationVoTree> list = new ArrayList<OrganizationVoTree>();
			OrganizationVoTree root = new OrganizationVoTree();
			root.setId(orgVo.getId());
			root.setText(orgVo.getOrganizationName());
			root.setOrganizationCode(orgVo.getOrganizationCode());
			root.setOrganizationName(orgVo.getOrganizationName());
			root.setSort(orgVo.getSort());
			root.setType(orgVo.getType());
			root.setStatus(orgVo.getStatus());
			root.setChildren(resultNodes);
			root.setEsspflag(orgVo.getEsspflag());
			root.setLevel(1);
			list.add(root);
			resultMap.put("resultJsonList", list);

		} catch (Exception ex) {
			resultMap.put("code", "syserror");
			ex.printStackTrace();
		}
		return resultMap;
	}

	@Override
	public Map<String, Object> getChildOrgTreesByOrgId(Long id) {
		// TODO Auto-generated method stub
		return null;
	}

//	// add by bo.liu end
//
//	@Override
//	public Map<String, Object> createOrganizationByWorkShopPartnerVo(List<WorkshopPartnerVo> insertList) {
//
//		Map<String, Object> map = new HashMap<String, Object>();
//		List<OrganizationVo> ovList = new ArrayList<OrganizationVo>();
//
//		for (int i = 0; i < insertList.size(); i++) {
//			OrganizationVo ov = new OrganizationVo();
//			WorkshopPartnerVo wpv = insertList.get(i);
//			ov.setParentId(wpv.getPartnerId());
//			WorkShopVo wsv = workShopServiceImpl.selectByPrimaryKey(wpv.getWorkshopId());
//			ov.setOrganizationCode(wsv.getWorkShopCode());
//			ov.setOrganizationName(wsv.getWorkShopName());
//			ov.setStatus(1);
//			ov.setType(2);// 新增一个类型为门店的组织类型
//			ov.setCreateTime(new Date());
//			ov.setSort(10);
//			ov.setUpdateTime(new Date());
//			boolean bool = isExsitOrganization(wsv.getWorkShopCode(), wpv.getPartnerId());
//			if (!bool) {
//				ovList.add(ov);
//			}
//		}
//		if (ovList.size() > 0) {
//			organizationVoMapper.batchInsert(ovList);
//		}
//		map.put("resultData", "success");
//		return map;
//	}

	/**
	 * 根据code 和 parentid 判断是否存在机构
	 * 
	 * @param organizationCode
	 * @param parentId
	 * @return true 存在，false 不存在
	 */
	private boolean isExsitOrganization(String organizationCode, Long parentId) {
		OrganizationVoExample example = new OrganizationVoExample();
		Criteria criteria = example.createCriteria();
		criteria.andOrganizationCodeEqualTo(organizationCode);
		criteria.andParentIdEqualTo(parentId);
		List<OrganizationVo> list = organizationVoMapper.selectByExample(example);
		if (null == list || list.size() == 0) {
			return false;
		}
		return true;
	}

	@Override
	public Map<String, Object> deleteByPrimaryKey(long id) {
		Map<String, Object> map = new HashMap<String, Object>();
		organizationVoMapper.deleteByPrimaryKey(id);
		map.put("result", "success");
		return map;
	}

	@Override
	public List<OrganizationVo> getAllPartnerByRegionId(Map<String, Object> reqMap) {
		return organizationVoMapper.getAllPartnerByRegionId(reqMap);
	}

	@Override
	public List<OrganizationVo> getAllPartnersByWorkshop(Long workshopId) {
		Map<String, Object> paramMap = new HashMap<String, Object>();
		paramMap.put("workshopId", workshopId);
		WxTUser user = ContextUtil.getCurUser();
		if (user != null) {
			paramMap.put("userType", WxTUser.USER_MODEL_CHEVRON.equals(user.getUserModel()) ? "1" : "0");
			paramMap.put("orgId", user.getOrgId());
		}
		return organizationVoMapper.selectOrgsByParams(paramMap);
	}

	@Override
	public String getAllPartnersStrByWorkshop(Long workshopId) {
		Map<String, Object> paramMap = new HashMap<String, Object>();
		paramMap.put("workshopId", workshopId);
		WxTUser user = ContextUtil.getCurUser();
		if (user != null) {
			paramMap.put("userType", WxTUser.USER_MODEL_CHEVRON.equals(user.getUserModel()) ? "1" : "0");
			paramMap.put("orgId", user.getOrgId());
		}
		List<OrganizationVo> ps = organizationVoMapper.selectOrgsByParams(paramMap);
		String pNames = null;
		if (ps != null) {
			for (OrganizationVo sp : ps) {
				if (pNames == null) {
					pNames = sp.getOrganizationName();
				} else {
					pNames += ("," + sp.getOrganizationName());
				}
			}
		}
		return pNames;
	}

	@Override
	public Map<String, Object> getImportOptionsByCurrentUser() {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		// 获取当前登陆用户
		WxTUser currentUser = ContextUtil.getCurUser();
		
		try {
			// 根据当前登录用户去查询
			ImprtDataSetsVoExample example = new ImprtDataSetsVoExample();
			ImprtDataSetsVoExample.Criteria criteria = example.createCriteria();
			if(currentUser.getOrgId()!=1) {
				criteria.andPartnerIdEqualTo(currentUser.getOrgId());
			}
			List<ImprtDataSetsVo> resultList = imprtDataSetsVoMapper.selectByExample(example);
			resultMap.put("code", "success");
			resultMap.put("data", resultList);
		} catch (Exception e) {
			resultMap.put("code", "sysError");
			e.printStackTrace();
		}
		return resultMap;
	}

	@Override
	public Map<String, Object> getDistributorRegion(Long distributorId, String bu) {
		final Map<String, Object> resultMap = new HashMap<String, Object>();
		List<Object> params = new ArrayList<Object>();
		StringBuilder sql = new StringBuilder(10000).append("select region_name from view_customer_region_sales_channel where 1=1");
		if(distributorId != null){
			sql.append(" and distributor_id = ?");
			params.add(distributorId);
		}
		if(!StringUtils.isNull(bu)){
			sql.append(" and bu = ?");
			params.add(bu);
		}
		wxJdbcTemplate.query(sql.toString(), params.toArray(new Object[params.size()]), new RowCallbackHandler() {
			@Override
			public void processRow(ResultSet arg0) throws SQLException {
				resultMap.put("region",arg0.getString("region_name"));
			}
		});

		return resultMap;
	}

	@Override
	public Map<String, Object> getDistributorRegionByChannel(Long distributorId, String productChannel) {
		final Map<String, Object> resultMap = new HashMap<String, Object>();
		List<Object> params = new ArrayList<Object>();
		StringBuilder sql = new StringBuilder(10000).append("select distinct region from dw_customer_org_sales where 1=1");
		if(distributorId != null){
			sql.append(" and distributor_id = ?");
			params.add(distributorId);
		}
		if(!StringUtils.isNull(productChannel)){
			sql.append(" and product_channel = ?");
			params.add(productChannel);
		}
		wxJdbcTemplate.query(sql.toString(), params.toArray(new Object[params.size()]), new RowCallbackHandler() {
			@Override
			public void processRow(ResultSet arg0) throws SQLException {
				resultMap.put("region",arg0.getString("region"));
			}
		});

		return resultMap;
	}

	@Override
	public List<DistributorBySapCodeVo> getDistributorBySapCode(String sapCode) {
		return organizationVoMapper.getDistributorBySapCode(sapCode);
	}

}
