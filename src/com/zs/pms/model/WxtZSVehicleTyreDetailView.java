package com.zs.pms.model;

public class WxtZSVehicleTyreDetailView {

	private String zstModelId;

    private String zstId;

    private String factoryName;

    private String brandName;

    private String dmpimp;

    private String vehicleType;

    private String vehicleSeries;

    private String vehicleSeriesSop;

    private String vehicleSeriesEop;

    private String zstTypeId;

    private String zstType;

    private String zstModelName;

    private String modelYear;

    private String zstModelMsrp;

    private String zstModelSop;

    private String zstModelEop;

    private String engine;

    private String engineDisplacementCc;

    private String engineDisplacementL;

    private String power;

    private String turboBoost;

    private String gearboxDesc;

    private String drivingModel;
    
    private String frontTyre;

    private String rearTyre;

	public String getZstModelId() {
		return zstModelId;
	}

	public void setZstModelId(String zstModelId) {
		this.zstModelId = zstModelId;
	}

	public String getZstId() {
		return zstId;
	}

	public void setZstId(String zstId) {
		this.zstId = zstId;
	}

	public String getFactoryName() {
		return factoryName;
	}

	public void setFactoryName(String factoryName) {
		this.factoryName = factoryName;
	}

	public String getBrandName() {
		return brandName;
	}

	public void setBrandName(String brandName) {
		this.brandName = brandName;
	}

	public String getDmpimp() {
		return dmpimp;
	}

	public void setDmpimp(String dmpimp) {
		this.dmpimp = dmpimp;
	}

	public String getVehicleType() {
		return vehicleType;
	}

	public void setVehicleType(String vehicleType) {
		this.vehicleType = vehicleType;
	}

	public String getVehicleSeries() {
		return vehicleSeries;
	}

	public void setVehicleSeries(String vehicleSeries) {
		this.vehicleSeries = vehicleSeries;
	}

	public String getVehicleSeriesSop() {
		return vehicleSeriesSop;
	}

	public void setVehicleSeriesSop(String vehicleSeriesSop) {
		this.vehicleSeriesSop = vehicleSeriesSop;
	}

	public String getVehicleSeriesEop() {
		return vehicleSeriesEop;
	}

	public void setVehicleSeriesEop(String vehicleSeriesEop) {
		this.vehicleSeriesEop = vehicleSeriesEop;
	}

	public String getZstTypeId() {
		return zstTypeId;
	}

	public void setZstTypeId(String zstTypeId) {
		this.zstTypeId = zstTypeId;
	}

	public String getZstType() {
		return zstType;
	}

	public void setZstType(String zstType) {
		this.zstType = zstType;
	}

	public String getZstModelName() {
		return zstModelName;
	}

	public void setZstModelName(String zstModelName) {
		this.zstModelName = zstModelName;
	}

	public String getModelYear() {
		return modelYear;
	}

	public void setModelYear(String modelYear) {
		this.modelYear = modelYear;
	}

	public String getZstModelMsrp() {
		return zstModelMsrp;
	}

	public void setZstModelMsrp(String zstModelMsrp) {
		this.zstModelMsrp = zstModelMsrp;
	}

	public String getZstModelSop() {
		return zstModelSop;
	}

	public void setZstModelSop(String zstModelSop) {
		this.zstModelSop = zstModelSop;
	}

	public String getZstModelEop() {
		return zstModelEop;
	}

	public void setZstModelEop(String zstModelEop) {
		this.zstModelEop = zstModelEop;
	}

	public String getEngine() {
		return engine;
	}

	public void setEngine(String engine) {
		this.engine = engine;
	}

	public String getEngineDisplacementCc() {
		return engineDisplacementCc;
	}

	public void setEngineDisplacementCc(String engineDisplacementCc) {
		this.engineDisplacementCc = engineDisplacementCc;
	}

	public String getEngineDisplacementL() {
		return engineDisplacementL;
	}

	public void setEngineDisplacementL(String engineDisplacementL) {
		this.engineDisplacementL = engineDisplacementL;
	}

	public String getPower() {
		return power;
	}

	public void setPower(String power) {
		this.power = power;
	}

	public String getTurboBoost() {
		return turboBoost;
	}

	public void setTurboBoost(String turboBoost) {
		this.turboBoost = turboBoost;
	}

	public String getGearboxDesc() {
		return gearboxDesc;
	}

	public void setGearboxDesc(String gearboxDesc) {
		this.gearboxDesc = gearboxDesc;
	}

	public String getDrivingModel() {
		return drivingModel;
	}

	public void setDrivingModel(String drivingModel) {
		this.drivingModel = drivingModel;
	}

	public String getFrontTyre() {
		return frontTyre;
	}

	public void setFrontTyre(String frontTyre) {
		this.frontTyre = frontTyre;
	}

	public String getRearTyre() {
		return rearTyre;
	}

	public void setRearTyre(String rearTyre) {
		this.rearTyre = rearTyre;
	}
    
    
}
