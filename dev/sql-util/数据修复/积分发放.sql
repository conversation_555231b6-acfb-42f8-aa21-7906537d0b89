select --top 100 sp.promotion_name, o.organization_name,d.delivery_status,d.ext_property7,d.* 
sum(award_quantity)
--update d set d.delivery_status=50
from wx_t_promotion_delivery_detail d 
left join wx_t_sell_in_promotion sp on d.promotion_id=sp.id
left join wx_t_partner_o2o_enterprise pe on pe.distributor_id=d.ext_property7
left join wx_t_organization o on o.id=pe.partner_id
where d.delivery_status=20
and o.organization_name in ('鄂尔多斯市大丁商贸有限责任公司','吉林省嘉泰汽车用品有限公司')

select vd.* 
--delete vd
--update b set b.RELATED_CODE=replace(b.RELATED_CODE,'CDM_PIT_PACK_20L', 'CONSUMER_HERO_PRODUCT_POINT'), ATTRIBUTE2='CONSUMER_HERO_PRODUCT_POINT',ATTRIBUTE3='consumer_hero_product'
--into wx_t_point_value_detail20210609
from wx_t_point_business b 
left join wx_t_point_value_detail vd on vd.business_id=b.id
left join wx_t_point_value_detail_log vdl on vd.id=vdl.POINT_VALUE_ID
where b.BUSINESS_TYPE_CODE= 'SELL_IN_PROMOTION_DELIVERY_V2'--B2021051818154/1123/94/CDM_SS_PRODUCT_POINT
and b.RELATED_CODE in ('B2021060823248/1235/104/CONSUMER_HERO_PRODUCT_POINT', 'B2021060823248/1231/104/CONSUMER_HERO_PRODUCT_POINT')

--order by id desc;

select top 100 * 
--delete ei
--into wx_t_budget2021_expense_import20210609
from wx_t_budget2021_expense_import ei where ei.distributor_id in (1235,1231)
and create_time>'2021-06-08'
--order by id desc;