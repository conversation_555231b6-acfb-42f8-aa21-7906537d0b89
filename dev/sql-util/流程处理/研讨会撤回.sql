--清除已执行待办列表
select wsi1.step_instance_id, wsi1.workflow_instance_id, wsi1.step_no, wsi1.todo_numb,
			 wsi1.enable_flag, wiel1.execute_status, wiel1.executor, wiel1.actual_executor, 
			 u1.ch_name executor_name, u1.email executor_email, u2.ch_name actual_executor_name, t1.*
			 --select wiel1.* into wx_t_wor_ins_exe_list2022071589
			 --delete wiel1
		  from wx_t_workflow_step_instance wsi1
		 left join wx_t_workflow_instance wi1 on wsi1.workflow_instance_id=wi1.flow_instance_id
		 left join wx_t_workflow_step t1 on t1.step_id=wsi1.step_id
		 left join wx_t_wor_ins_exe_list wiel1 on wiel1.step_instance_id=wsi1.step_instance_id 
		 left join wx_t_user u1 on u1.user_id=wiel1.executor
		 left join wx_t_user u2 on u2.user_id=wiel1.actual_executor
		 where wsi1.enable_flag=1 and wsi1.version_no=wi1.step_version_no 
		 and wi1.workflow_code='SEMINAR_APPLY' and wi1.form_key='89' and wsi1.step_no>6
--重置撤回到步骤待办列表	
select wsi1.step_instance_id, wsi1.workflow_instance_id, wsi1.step_no, wsi1.todo_numb,
			 wsi1.enable_flag, wiel1.execute_status, wiel1.executor, wiel1.actual_executor, 
			 u1.ch_name executor_name, u1.email executor_email, u2.ch_name actual_executor_name, t1.*
			 --update wiel1 set wiel1.execute_status=10
			 --update wsi1 set wsi1.todo_numb=1
		  from wx_t_workflow_step_instance wsi1
		 left join wx_t_workflow_instance wi1 on wsi1.workflow_instance_id=wi1.flow_instance_id
		 left join wx_t_workflow_step t1 on t1.step_id=wsi1.step_id
		 left join wx_t_wor_ins_exe_list wiel1 on wiel1.step_instance_id=wsi1.step_instance_id and wiel1.execute_status=30
		 left join wx_t_user u1 on u1.user_id=wiel1.executor
		 left join wx_t_user u2 on u2.user_id=wiel1.actual_executor
		 where wsi1.enable_flag=1 and wsi1.version_no=wi1.step_version_no 
		 and wi1.workflow_code='SEMINAR_APPLY' and wi1.form_key='89' and wsi1.step_no=6
--流程实例撤回到指定步骤
select * 
--update wi set next_step_no=6
from wx_t_workflow_instance wi where wi.workflow_code='SEMINAR_APPLY' and wi.form_key='89'	


select * 
--update wi set wi.status=0,next_step_no=2
--update ca set ca.form_status=0
from wx_t_mkt_seminar_apply ca 
left join wx_t_workflow_instance wi on wi.form_key=ca.id and wi.workflow_code='SEMINAR_APPLY' where ca.req_no='PCMO459420220909001'

		 