package com.chevron.pms.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import com.sys.auth.model.WxTUser;
import com.sys.dic.dao.DicItemVoMapper;
import com.sys.dic.model.DicItemVo;
import com.sys.file.dao.WxAttFileMapper;
import com.sys.file.model.WxAttFile;
import com.sys.organization.dao.OrganizationVoMapper;
import com.chevron.partnerorder.dao.PartnerInventoryVoMapper;
import com.chevron.partnerorder.model.PartnerInventoryQueryParams;
import com.chevron.partnerorder.model.PartnerInventoryView;
import com.chevron.pms.dao.InventoryVoMapper;
import com.chevron.pms.dao.ProductVoMapper;
import com.chevron.pms.model.InventoryQueryParams;
import com.chevron.pms.model.InventoryView;
import com.chevron.pms.model.ProductConditionQueryVo;
import com.chevron.pms.model.ProductParams;
import com.chevron.pms.model.ProductVo;
import com.chevron.pms.service.ProductService;
import com.common.constants.Constants;
import com.common.util.CommonUtil;
import com.common.util.ContextUtil;
import com.common.util.DateUtils;
import com.common.util.MessageResourceUtil;
import com.common.util.StringUtils;

@Service
public class ProductServiceImpl implements ProductService {

	@Resource
	ProductVoMapper productVoMapper;
	@Resource
	WxAttFileMapper wxAttFileMapper;
	@Resource
	InventoryVoMapper inventoryVoMapper;
	@Resource
	PartnerInventoryVoMapper partnerInventoryVoMapper;
	@Resource
	OrganizationVoMapper organizationVoMapper;
	@Resource
	DicItemVoMapper dicItemVoMapper;
	private Logger logger = Logger.getLogger(ProductServiceImpl.class);

	@Override
	public Map<String, String> insert(ProductVo productVo) throws Exception {
		Map<String, String> map = new HashMap<String, String>();
		productVo.setCreateTime(new Date());
		productVo.setUpdateTime(new Date());
		productVoMapper.insert(productVo);
		map.put("resultData", "success");
		return map;
	}

	@Override
	public Map<String, String> delete(ProductVo productVo) throws Exception {
		Map<String, String> map = new HashMap<String, String>();
		productVoMapper.deleteByPrimaryKey(productVo.getId());
		map.put("resultData", "success");
		return map;
	}

	@Override
	public Map<String, String> update(ProductVo productVo) throws Exception {
		Map<String, String> map = new HashMap<String, String>();
		productVo.setUpdateTime(new Date());
		productVoMapper.updateByPrimaryKey(productVo);
		map.put("resultData", "success");
		return map;
	}

	@Override
	public Map<String, Object> getProduct(String sku) throws Exception {
		Map<String, Object> map = new HashMap<String, Object>();
		ProductVo pVo = productVoMapper.selectBySku(sku);
		if(pVo!=null){
			map.put("productVo", pVo);
			map.put("code", "success");
		} else{
			map.put("code", "Product not found!");
		}
		return map;
	}

	@Override
	public Map<String, Object> getProducts() throws Exception {
		Map<String, Object> map = new HashMap<String, Object>();
		try {
			List<ProductVo> lst = productVoMapper.getAllProducts();
			map.put("lst", lst);
			map.put("code", "success");
		} catch (Exception e) {
			map.put("code", "syserror");
		}
		return map;
	}

	@Override
	public Map<String, String> insertProduct(String sku, String name,
			String category, String specification, String brand,
			String oiltype, Double price,Double salePrice, Double boxPrice, Double boxCapacity, String viscosity, String capacity,
			String oilGrade, String detailDesc, String packageType,
			String salesChannels, String barCode, String remark,Integer isCompeting,Integer isCollect,String units, WxAttFile[] attFiles)
			throws Exception {
		Map<String, String> map = new HashMap<String, String>();
		ProductVo productVo = new ProductVo();

		productVo.setSku(sku);
		productVo.setName(name);
		productVo.setCategory(category);
		productVo.setSpecification(specification);
		productVo.setBrand(brand);
		productVo.setOilType(oiltype);
		productVo.setPrice(price);
		productVo.setSalePrice(salePrice);
		productVo.setBoxPrice(boxPrice);
		productVo.setBoxCapacity(boxCapacity);
		productVo.setViscosity(viscosity);
		productVo.setCapacity(capacity);
		productVo.setOilGrade(oilGrade);
		productVo.setBarCode(barCode);
		productVo.setPackageType(packageType);
		productVo.setDetailDesc(detailDesc);
		productVo.setSalesChannels(salesChannels);
		productVo.setRemark(remark);
		productVo.setIsCompeting(isCompeting);
		productVo.setIsCollect(isCollect);
		productVo.setUnits(units);
		String mCurrentDate = DateUtils.getCurrentDateString();
		productVo.setCreateTime(DateUtils.stringToDate(mCurrentDate));
		productVo.setUpdateTime(DateUtils.stringToDate(mCurrentDate));
		String userName = ContextUtil.getCurUser().getLoginName();
		productVo.setCreator(userName);
		try {
			// 0.插入之前应该先 查询此产品编号是否存在
			ProductVo tmoProductVo = productVoMapper.selectBySku(sku);
			if (null == tmoProductVo) {
				// 1.插入产品
				productVoMapper.insert(productVo);
				productVo = productVoMapper.selectBySku(sku);
				// 2.更新附件source id
				if(attFiles != null){
					for(WxAttFile file : attFiles){
						file.setSourceId(productVo.getId());
						wxAttFileMapper.updateByPrimaryKeySelective(file);
					}
				}
				map.put("code", "success");
				map.put("id",String.valueOf(productVo.getId()));
			} else {
				map.put("code", "isExitProduct");
			}
		} catch (Exception e) {
			map.put("code", "syserror");
		}
		return map;
	}

	@Override
	public Map<String, String> deleteBySku(String sku) throws Exception {
		Map<String, String> resultMap = new HashMap<String, String>();
		try {
			productVoMapper.deleteBySku(sku);
			resultMap.put("code", "success");
		} catch (Exception ex) {
			resultMap.put("code", "syserror");
			resultMap.put("msg", ex.getMessage());
		}
		return resultMap;
	}

	@Override
	public Map<String, String> copyProduct(ProductVo productVo)
			throws Exception {

		Map<String, String> map = new HashMap<String, String>();
		String mCurrentDate = DateUtils.getCurrentDateString();
		// 设置创建日期
		productVo.setCreateTime(DateUtils.stringToDate(mCurrentDate));
		// 设置更新日期
		productVo.setUpdateTime(DateUtils.stringToDate(mCurrentDate));
		// 设置创建者
		String userName = ContextUtil.getCurUser().getLoginName();
		productVo.setCreator(userName);
		try {
			// 0.插入之前应该先 查询此产品编号是否存在
			ProductVo tmoProductVo = productVoMapper.selectBySku(productVo.getSku());
			if (null == tmoProductVo) {
				// 1.插入产品
				productVoMapper.insert(productVo);
				map.put("id", productVo.getId() + "");
				map.put("code", "success");
			} else {
				map.put("code", "isExistProduct");
			}
		} catch (Exception e) {
			map.put("code", "syserror");
		}
		return map;
	}

	@Override
	public Map<String, String> deletMoreProdoucts(String[] skus)
			throws Exception {
		Map<String, String> resultMap = new HashMap<String, String>();
		try {
			if (null != skus && skus.length > 0) {
				productVoMapper.deleteProductByCodes(skus);
				resultMap.put("code", "success");
			} else {
				resultMap.put("code", "errorParam");// 参数传递错误
			}

		} catch (Exception ex) {
			resultMap.put("code", "syserror");
		}
		return resultMap;
	}
/**
 * 更新产品信息
 * @see com.chevron.pms.service.ProductService#updateProductBySku(java.lang.Long, java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.Double, java.lang.Double, java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.Integer, java.lang.Integer)
 */
	@Override
	public Map<String, String> updateProductBySku(Long id,String sku, String name,
			String category, String specification, String brand,
			String oiltype, Double price,Double salePrice, Double boxPrice, Double boxCapacity, String viscosity, String capacity,
			String oilGrade, String detailDesc, String packageType,
			String salesChannels, String barCode, String remark,Integer isCompeting,Integer isCollect,String units)
			throws Exception {
		System.out.println("lelfkdjfoeusocvLLLLLLLLLLLLLLLLLLLl");
		Map<String, String> resultMap = new HashMap<String, String>();
		ProductVo reqProductVo = productVoMapper.selectByPrimaryKey(id);
		reqProductVo.setSku(sku);
		reqProductVo.setName(name);
		reqProductVo.setCategory(category);
		reqProductVo.setSpecification(specification);
		reqProductVo.setBrand(brand);
		reqProductVo.setOilType(oiltype);
		reqProductVo.setPrice(price);
		reqProductVo.setSalePrice(salePrice);
		reqProductVo.setBoxPrice(boxPrice);
		reqProductVo.setBoxCapacity(boxCapacity);
		reqProductVo.setViscosity(viscosity);
		reqProductVo.setCapacity(capacity);
		reqProductVo.setOilGrade(oilGrade);
		reqProductVo.setBarCode(barCode);
		reqProductVo.setPackageType(packageType);
		reqProductVo.setDetailDesc(detailDesc);
		reqProductVo.setSalesChannels(salesChannels);
		reqProductVo.setRemark(remark);
		reqProductVo.setIsCompeting(isCompeting);
		reqProductVo.setIsCollect(isCollect);
		reqProductVo.setUnits(units);
		String mCurrentDate = DateUtils.getCurrentDateString();
		reqProductVo.setUpdateTime(DateUtils.stringToDate(mCurrentDate));
		String userName = ContextUtil.getCurUser().getLoginName();
		reqProductVo.setCreator(userName);

		try {
			productVoMapper.updateByPrimaryKey(reqProductVo);
			resultMap.put("code", "success");
		} catch (Exception ex) {
			resultMap.put("code", "syserror");
		}
		return resultMap;
	}

	@Override
	public Map<String, Object> queryProductByMultiParam(String sku,
			String name, String isCompeting, ProductConditionQueryVo productMultiParamQueryVo, String keyWord) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		Map<String, Object> paramMap = new HashMap<String, Object>();
		try {
			if (StringUtils.isNotBlank(sku)) {
				paramMap.put("sku", sku);
			}
			if (StringUtils.isNotBlank(name)) {
				paramMap.put("name", name);
			}
			if(StringUtils.isNotBlank(keyWord)){
				paramMap.put("keyWord", "%" + keyWord + "%");
				paramMap.put("keyWordPy", "%" + keyWord + "%");
//				paramMap.put("keyWordPy", "%" + GetPinyinStringUtil.getPinYinHeadCharForSourceStr(keyWord) + "%");
			}
			if(StringUtils.isNotBlank(isCompeting)){
				paramMap.put("isCompeting", isCompeting);
			}

			if (null != productMultiParamQueryVo) {
				paramMap.put("brandList", productMultiParamQueryVo.getBrandList());
				paramMap.put("oilTypeList", productMultiParamQueryVo.getOilTypeList());
				if(null != productMultiParamQueryVo.getPriceList()){
					String prices[] = productMultiParamQueryVo.getPriceList();
					String[] priceArr = prices[0].split("-");
					Double startPrice = Double.valueOf(priceArr[0]);
					Double endPrice  = Double.valueOf(priceArr[1]);
					paramMap.put("startPrice", startPrice);
					paramMap.put("endPrice", endPrice);
				}
//				paramMap.put("priceList", productMultiParamQueryVo.getPriceList());
				paramMap.put("viscosityList", productMultiParamQueryVo.getViscosityList());
				paramMap.put("capacityList", productMultiParamQueryVo.getCapacityList());
				paramMap.put("supportOrder", productMultiParamQueryVo.getSupportOrder());
			} else {
				paramMap.put("brandList", null);
				paramMap.put("oilTypeList", null);
//				paramMap.put("priceList", null);
				paramMap.put("startPrice", null);
				paramMap.put("endPrice", null);
				paramMap.put("viscosityList", null);
				paramMap.put("capacityList", null);
				paramMap.put("supportOrder", null);
			}

			WxTUser user = ContextUtil.getCurUser();
			if(WxTUser.USER_MODEL_SP.equals(user.getUserModel())){
				paramMap.put("partnerId", user.getOrgId());
			}
			List<ProductVo> tmplst = productVoMapper.getProductsByParam(paramMap);
			List<ProductVo> lst = new ArrayList<ProductVo>();
			int j = 1;
			for (int i = 0; i < tmplst.size(); i++) {
				ProductVo tmpProduct = tmplst.get(i);
				if ("HC".equals(tmpProduct.getOilType())) {
					tmpProduct.setOilType("合成");
				} else if ("BHC".equals(tmpProduct.getOilType())) {
					tmpProduct.setOilType("半合成");
				} else if ("KW".equals(tmpProduct.getOilType())) {
					tmpProduct.setOilType("矿物");
				}

				if ("JY".equals(tmpProduct.getCategory())) {
					tmpProduct.setCategory("机油");
				} else if ("YYY".equals(tmpProduct.getCategory())) {
					tmpProduct.setCategory("机油添加剂");
				} else if ("LH".equals(tmpProduct.getCategory())) {
					tmpProduct.setCategory("礼盒");
				}else if ("QT".equals(tmpProduct.getCategory())) {
					tmpProduct.setCategory("其他");
				}

				if ("XFL".equals(tmpProduct.getBrand())) {
					tmpProduct.setBrand("雪佛龙");
				} else if ("JSD".equals(tmpProduct.getBrand())) {
					tmpProduct.setBrand("嘉实多");
				}else if ("MF".equals(tmpProduct.getBrand())) {
					tmpProduct.setBrand("美孚");
				} else if ("QT".equals(tmpProduct.getBrand())) {
					tmpProduct.setBrand("其他");
				}

				tmpProduct.setProductIndex(j);
				tmpProduct.setRoleID(1);// 注意此处值需要实际从登录的时候获取用户的权限ID
				lst.add(tmpProduct);
				j++;
			}
			resultMap.put("lst", lst);
			resultMap.put("code", "success");

		} catch (Exception ex) {
			ex.printStackTrace();
			resultMap.put("code", "syserror");
		}
		return resultMap;
	}

	@Override
	public Map<String, Object> queryProductAndInventoryByMultiParam(String queryField,String sku,
			String name, ProductConditionQueryVo productMultiParamQueryVo, Long workshopId) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		Map<String, Object> paramMap = new HashMap<String, Object>();
		try {
			//获取门店各个产品的库存
			InventoryQueryParams params = new InventoryQueryParams();
			params.setPaging(false);
			params.setWorkshopId(workshopId);
			List<InventoryView> inventoryList = inventoryVoMapper.selectInventory(params);
			Map<String,InventoryView> inventoryMap = new HashMap<String,InventoryView>();
			for(InventoryView temp : inventoryList){
				inventoryMap.put(temp.getSku(), temp);
			}
			paramMap.put("workshopId", workshopId);
			if (queryField.equals("")) {
				paramMap.put("queryField", null);
			} else {
				paramMap.put("queryField", queryField);
			}
			if (sku.equals("")) {
				paramMap.put("sku", null);
			} else {
				paramMap.put("sku", sku);
			}
			if (name.equals("")) {
				paramMap.put("name", null);
			} else {
				paramMap.put("name", name);
			}


			if (null != productMultiParamQueryVo) {
				paramMap.put("brandList", productMultiParamQueryVo.getBrandList());
				if(null != productMultiParamQueryVo.getOilTypeList() && productMultiParamQueryVo.getOilTypeList().length > 0){
					paramMap.put("oilTypeList", productMultiParamQueryVo.getOilTypeList());
				}
				if(null != productMultiParamQueryVo.getPriceList()){
					String prices[] = productMultiParamQueryVo.getPriceList();
					String[] priceArr = prices[0].split("-");
					Double startPrice = Double.valueOf(priceArr[0]);
					Double endPrice  = Double.valueOf(priceArr[1]);
					paramMap.put("startPrice", startPrice);
					paramMap.put("endPrice", endPrice);
				}
				if(null != productMultiParamQueryVo.getViscosityList() && productMultiParamQueryVo.getViscosityList().length > 0){
					paramMap.put("viscosityList", productMultiParamQueryVo.getViscosityList());
				}
				if(null != productMultiParamQueryVo.getCapacityList() && productMultiParamQueryVo.getCapacityList().length > 0){
					paramMap.put("capacityList", productMultiParamQueryVo.getCapacityList());
				}
				if(null != productMultiParamQueryVo.getCategoryList() && productMultiParamQueryVo.getCategoryList().length > 0){
					paramMap.put("categoryList", productMultiParamQueryVo.getCategoryList());
				}
			} else {
				paramMap.put("brandList", null);
				paramMap.put("oilTypeList", null);
				paramMap.put("startPrice", null);
				paramMap.put("endPrice", null);
				paramMap.put("viscosityList", null);
				paramMap.put("capacityList", null);
				paramMap.put("categoryList", null);
			}

			paramMap.put("supportOrder", "1");
			paramMap.put("workshopId", workshopId);
			List<ProductVo> tmplst = productVoMapper.getProductsByParam(paramMap);
			List<Map<String,Object>> lst = new ArrayList<Map<String,Object>>();

			//品牌
			List<DicItemVo> brandItems = dicItemVoMapper.selectByCode(Constants.PRODUCT_BRAND);
			Map<String,String> brandMap = new HashMap<String,String>();
			for(DicItemVo brand : brandItems){
				brandMap.put(brand.getDicItemCode(), brand.getDicItemName());
			}
			//机油类型
			List<DicItemVo> oilTypeItems = dicItemVoMapper.selectByCode(Constants.PRODUCT_OIL_TYPE);
			Map<String,String> oilTypeMap = new HashMap<String,String>();
			for(DicItemVo oilType : oilTypeItems){
				oilTypeMap.put(oilType.getDicItemCode(), oilType.getDicItemName());
			}
			//粘度
			List<DicItemVo> viscosityItems = dicItemVoMapper.selectByCode(Constants.PRODUCT_VISCOSITY);
			Map<String,String> viscosityMap = new HashMap<String,String>();
			for(DicItemVo viscosity : viscosityItems){
				viscosityMap.put(viscosity.getDicItemCode(), viscosity.getDicItemName());
			}
			//容量
			List<DicItemVo> capacityItems = dicItemVoMapper.selectByCode(Constants.PRODUCT_CAPACITY);
			Map<String,String> capacityMap = new HashMap<String,String>();
			for(DicItemVo capacity : capacityItems){
				capacityMap.put(capacity.getDicItemCode(), capacity.getDicItemName());
			}
			//产品单位
			List<DicItemVo> unitsItems = dicItemVoMapper.selectByCode(Constants.PRODUCT_UNITS);
			Map<String,String> unitsMap = new HashMap<String,String>();
			for(DicItemVo units : unitsItems){
				unitsMap.put(units.getDicItemCode(), units.getDicItemName());
			}

			//产品类别
			List<DicItemVo> categoryItems = dicItemVoMapper.selectByCode(Constants.PRODUCT_CATEGORY);
			Map<String,String> categoryMap = new HashMap<String,String>();
			for(DicItemVo category : categoryItems){
				categoryMap.put(category.getDicItemCode(), category.getDicItemName());
			}

			int j = 1;
			for (int i = 0; i < tmplst.size(); i++) {
				ProductVo tmpProduct = tmplst.get(i);
					/*if ("HC".equals(tmpProduct.getOilType())) {
					tmpProduct.setOilType("全合成");
				} else if ("BHC".equals(tmpProduct.getOilType())) {
					tmpProduct.setOilType("合成");
				} else if ("KW".equals(tmpProduct.getOilType())) {
					tmpProduct.setOilType("矿物");
				}*/
				tmpProduct.setOilType(oilTypeMap.get(tmpProduct.getOilType()));

				/*if ("JY".equals(tmpProduct.getCategory())) {
					tmpProduct.setCategory("机油");
				} else if ("YYY".equals(tmpProduct.getCategory())) {
					tmpProduct.setCategory("机油添加剂");
				} else if ("LH".equals(tmpProduct.getCategory())) {
					tmpProduct.setCategory("礼盒");
				}else if ("QT".equals(tmpProduct.getCategory())) {
					tmpProduct.setCategory("其他");
				}*/
				tmpProduct.setCategory(categoryMap.get(tmpProduct.getCategory()));

				/*if ("XFL".equals(tmpProduct.getBrand())) {
					tmpProduct.setBrand("雪佛龙");
				} else if ("JSD".equals(tmpProduct.getBrand())) {
					tmpProduct.setBrand("嘉实多");
				}else if ("MF".equals(tmpProduct.getBrand())) {
					tmpProduct.setBrand("美孚");
				} else if ("QT".equals(tmpProduct.getBrand())) {
					tmpProduct.setBrand("其他");
				}*/
				tmpProduct.setBrand(categoryMap.get(tmpProduct.getBrand()));

				tmpProduct.setProductIndex(j);
				tmpProduct.setRoleID(1);// 注意此处值需要实际从登录的时候获取用户的权限ID

				Map<String,Object> productMap = CommonUtil.objectToMap(tmpProduct);
				if(inventoryMap.get(tmpProduct.getSku()) != null){
					productMap.put("inventoryCount", inventoryMap.get(tmpProduct.getSku()).getQuantity());
				}else{
					productMap.put("inventoryCount", 0);
				}
				lst.add(productMap);
				j++;
			}
			resultMap.put("lst", lst);
			resultMap.put("code", "success");

		} catch (Exception ex) {
			resultMap.put("code", "syserror");
		}
		return resultMap;
	}

	@Override
	public Map<String, Object> queryProductAndPartnerInventoryByMultiParam(String queryField,String sku,
			String name, ProductConditionQueryVo productMultiParamQueryVo, Long partnerId) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		Map<String, Object> paramMap = new HashMap<String, Object>();
		try {
			//获取合伙人各个产品的库存
			PartnerInventoryQueryParams params = new PartnerInventoryQueryParams();
			params.setLimit(1000);
			params.setPartnerId(partnerId);
			List<PartnerInventoryView> inventoryList = partnerInventoryVoMapper.selectInventory(params);
			Map<String,PartnerInventoryView> inventoryMap = new HashMap<String,PartnerInventoryView>();
			for(PartnerInventoryView temp : inventoryList){
				inventoryMap.put(temp.getSku(), temp);
			}
			if (queryField.equals("")) {
				paramMap.put("queryField", null);
			} else {
				paramMap.put("queryField", queryField);
			}
			if (sku.equals("")) {
				paramMap.put("sku", null);
			} else {
				paramMap.put("sku", sku);
			}
			if (name.equals("")) {
				paramMap.put("name", null);
			} else {
				paramMap.put("name", name);
			}

			if (null != productMultiParamQueryVo) {
				paramMap.put("brandList", productMultiParamQueryVo.getBrandList());
				if(null != productMultiParamQueryVo.getOilTypeList() && productMultiParamQueryVo.getOilTypeList().length > 0){
					paramMap.put("oilTypeList", productMultiParamQueryVo.getOilTypeList());
				}
				if(null != productMultiParamQueryVo.getPriceList()){
					String prices[] = productMultiParamQueryVo.getPriceList();
					String[] priceArr = prices[0].split("-");
					Double startPrice = Double.valueOf(priceArr[0]);
					Double endPrice  = Double.valueOf(priceArr[1]);
					paramMap.put("startPrice", startPrice);
					paramMap.put("endPrice", endPrice);
				}
				if(null != productMultiParamQueryVo.getViscosityList() && productMultiParamQueryVo.getViscosityList().length > 0){
					paramMap.put("viscosityList", productMultiParamQueryVo.getViscosityList());
				}
				if(null != productMultiParamQueryVo.getCapacityList() && productMultiParamQueryVo.getCapacityList().length > 0){
					paramMap.put("capacityList", productMultiParamQueryVo.getCapacityList());
				}
				if(null != productMultiParamQueryVo.getCategoryList() && productMultiParamQueryVo.getCategoryList().length > 0){
					paramMap.put("categoryList", productMultiParamQueryVo.getCategoryList());
				}
				if(null != productMultiParamQueryVo.getProductChannelList() && productMultiParamQueryVo.getProductChannelList().length > 0){
					paramMap.put("productChannelList", productMultiParamQueryVo.getProductChannelList());
				}
				
			}

			paramMap.put("supportOrder", "1");
			paramMap.put("partnerId", partnerId);
			
//			PartnerView partnerView = organizationVoMapper.selectPartnerFullInfoByPartnerId(partnerId);
			/*if("NORMAL".equals(partnerView.getPartnerProperty()) && "1".equals(partnerView.getNewSpFlag())){
				paramMap.put("productProperty", Constants.PRODUCT_PROPERTY_SN_ABOVE);
			}*/
			
			List<ProductVo> tmplst = productVoMapper.getProductsByParam(paramMap);
			List<Map<String,Object>> lst = new ArrayList<Map<String,Object>>();
			//品牌
			List<DicItemVo> brandItems = dicItemVoMapper.selectByCode(Constants.PRODUCT_BRAND);
			Map<String,String> brandMap = new HashMap<String,String>();
			for(DicItemVo brand : brandItems){
				brandMap.put(brand.getDicItemCode(), brand.getDicItemName());
			}
			//机油类型
			List<DicItemVo> oilTypeItems = dicItemVoMapper.selectByCode(Constants.PRODUCT_OIL_TYPE);
			Map<String,String> oilTypeMap = new HashMap<String,String>();
			for(DicItemVo oilType : oilTypeItems){
				oilTypeMap.put(oilType.getDicItemCode(), oilType.getDicItemName());
			}
			//粘度
			List<DicItemVo> viscosityItems = dicItemVoMapper.selectByCode(Constants.PRODUCT_VISCOSITY);
			Map<String,String> viscosityMap = new HashMap<String,String>();
			for(DicItemVo viscosity : viscosityItems){
				viscosityMap.put(viscosity.getDicItemCode(), viscosity.getDicItemName());
			}
			//容量
			List<DicItemVo> capacityItems = dicItemVoMapper.selectByCode(Constants.PRODUCT_CAPACITY);
			Map<String,String> capacityMap = new HashMap<String,String>();
			for(DicItemVo capacity : capacityItems){
				capacityMap.put(capacity.getDicItemCode(), capacity.getDicItemName());
			}
			//产品单位
			List<DicItemVo> unitsItems = dicItemVoMapper.selectByCode(Constants.PRODUCT_UNITS);
			Map<String,String> unitsMap = new HashMap<String,String>();
			for(DicItemVo units : unitsItems){
				unitsMap.put(units.getDicItemCode(), units.getDicItemName());
			}

			//产品类别
			List<DicItemVo> categoryItems = dicItemVoMapper.selectByCode(Constants.PRODUCT_CATEGORY);
			Map<String,String> categoryMap = new HashMap<String,String>();
			for(DicItemVo category : categoryItems){
				categoryMap.put(category.getDicItemCode(), category.getDicItemName());
			}
			int j = 1;
			for (int i = 0; i < tmplst.size(); i++) {
				ProductVo tmpProduct = tmplst.get(i);
				tmpProduct.setOilType(oilTypeMap.get(tmpProduct.getOilType()));
				tmpProduct.setCategory(categoryMap.get(tmpProduct.getCategory()));
				tmpProduct.setBrand(categoryMap.get(tmpProduct.getBrand()));
				tmpProduct.setProductIndex(j);
				tmpProduct.setRoleID(1);// 注意此处值需要实际从登录的时候获取用户的权限ID

				Map<String,Object> productMap = CommonUtil.objectToMap(tmpProduct);
				if(inventoryMap.get(tmpProduct.getSku()) != null){
					productMap.put("inventoryCount", inventoryMap.get(tmpProduct.getSku()).getQuantity());
				}else{
					productMap.put("inventoryCount", 0);
				}
				lst.add(productMap);
				j++;
			}
			resultMap.put("lst", lst);
			resultMap.put("viscosity", viscosityItems);
			resultMap.put("oiltype", oilTypeItems);
			resultMap.put("capacity", capacityItems);
			resultMap.put("category", categoryItems);
			resultMap.put("code", "success");

		} catch (Exception ex) {
			resultMap.put("code", "syserror");
		}
		return resultMap;
	}

	@Override
	public Map<String, Object> queryProductDictItem() {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		try {
			//品牌
			List<DicItemVo> brandItems = dicItemVoMapper.selectByCode(Constants.PRODUCT_BRAND);

			//机油类型
			List<DicItemVo> oilTypeItems = dicItemVoMapper.selectByCode(Constants.PRODUCT_OIL_TYPE);

			//粘度
			List<DicItemVo> viscosityItems = dicItemVoMapper.selectByCode(Constants.PRODUCT_VISCOSITY);

			//容量
			List<DicItemVo> capacityItems = dicItemVoMapper.selectByCode(Constants.PRODUCT_CAPACITY);

			//产品单位
			List<DicItemVo> unitsItems = dicItemVoMapper.selectByCode(Constants.PRODUCT_UNITS);

			//产品类别
			List<DicItemVo> categoryItems = dicItemVoMapper.selectByCode(Constants.PRODUCT_CATEGORY);
			List<DicItemVo> categoryList = new ArrayList<DicItemVo>();
			for(DicItemVo category : categoryItems){
				if(category.getDicItemCode().equals("LH") || category.getDicItemCode().equals("QT")){
					continue;
				}else{
					categoryList.add(category);
				}
			}

			//订单起始量限制
			List<DicItemVo> oilMinLiterCountItems = dicItemVoMapper.selectByCode(Constants.PARTNER_ORDER_MIN_LITER_COUNT);
			Integer partnerOrderMinLiterCount = 0;
			if(oilMinLiterCountItems != null && !oilMinLiterCountItems.isEmpty()){
				partnerOrderMinLiterCount = Integer.valueOf(oilMinLiterCountItems.get(0).getDicItemName());
			}
			
			resultMap.put("code", "success");
			resultMap.put("partnerOrderMinLiterCount", partnerOrderMinLiterCount);
			resultMap.put("brand", brandItems);
			resultMap.put("oiltype", oilTypeItems);
			resultMap.put("viscosity", viscosityItems);
			resultMap.put("capacity", capacityItems);
			resultMap.put("units", unitsItems);
			resultMap.put("category", categoryList);
		}catch (Exception ex) {
			resultMap.put("code", "syserror");
		}
		return resultMap;
	}

	@Override
	public Map<String, Object> queryProductForPage(ProductParams params) {
		Map<String,Object> resultMap = new HashMap<String,Object>(5);
		try {
			WxTUser user = ContextUtil.getCurUser();
			if(WxTUser.USER_MODEL_SP.equals(user.getUserModel())){
				params.setPartnerId(user.getOrgId());
			}
			params.setSupportOrder("1");
			List<ProductVo> productList = productVoMapper.queryProductForPage(params);
			for(ProductVo productTemp : productList){
				productTemp.setPrice(null);
				productTemp.setSalePrice(null);
				productTemp.setSalesChannels(null);
			}		
			resultMap.put(Constants.RESULT_LST_KEY, productList);
			resultMap.put(Constants.RESULT_TOTAL_KEY, params.getTotalCount());
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (Exception e) {
			e.printStackTrace();
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			//add by bo.liu 180111
			resultMap.put(Constants.RESULT_CODE_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			logger.error(e.getMessage(), e);
		}
		return resultMap;
	}

	@Override
	public Map<String, Object> getCompetingProducts(Long productId) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		try {
			Map<String, Object> reqMap = new HashMap<String, Object>();
			reqMap.put("productId", productId);
			resultMap.put(Constants.RESULT_LST_KEY, productVoMapper.getCompetingProducts(reqMap));
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (Exception e) {
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			//add by bo.liu 180111
			resultMap.put(Constants.RESULT_CODE_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
		}
		return resultMap;
	}
}
