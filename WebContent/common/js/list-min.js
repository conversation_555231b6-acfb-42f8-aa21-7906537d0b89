/**
 * @fileOverview \u5217\u8868\u6a21\u5757\u5165\u53e3\u6587\u4ef6
 * @ignore
 */(function(){var e="bui/list/";define("bui/list",["bui/common",e+"list",e+"listitem",e+"simplelist",e+"listbox"],function(t){var n=t("bui/common"),r=n.namespace("List");return n.mix(r,{List:t(e+"list"),ListItem:t(e+"listitem"),SimpleList:t(e+"simplelist"),Listbox:t(e+"listbox")}),n.mix(r,{ListItemView:r.ListItem.View,SimpleListView:r.SimpleList.View}),r})})(),define("bui/list/domlist",["bui/common"],function(e){"use strict";function s(e,t){var n=t,r=n.get("itemCls"),i=n.get("itemStatusCls");return i&&i[e]?i[e]:r+"-"+e}function u(e,n){var i=e.attributes,o=n.get("itemStatusFields"),u={};return t.each(i,function(e){var t=e.nodeName;t.indexOf(r)!==-1&&(t=t.replace(r,""),u[t]=e.nodeValue)}),u.text=$(e).text(),t.each(o,function(t,r){var i=s(r,n);$(e).hasClass(i)&&(u[t]=!0)}),u}var t=e("bui/common"),n=t.Component.UIBase.Selection,r="data-",i=t.Component.UIBase.List,o=function(){};o.ATTRS={items:{}},o.prototype={clearControl:function(){var e=this,t=e.getItemContainer(),n=e.get("itemCls");t.find("."+n).remove()},addItem:function(e,t){return this._createItem(e,t)},getItems:function(){var e=this,n=e.getAllElements(),r=[];return t.each(n,function(t){r.push(e.getItemByElement(t))}),r},updateItem:function(e){var n=this,r=n.getItems(),i=t.Array.indexOf(e,r),s=null,o;return i>=0&&(s=n.findElement(e),o=n.getItemTpl(e,i),s&&$(s).html($(o).html())),s},removeItem:function(e,t){t=t||this.findElement(e),$(t).remove()},getItemContainer:function(){var e=this.get("itemContainer");return e.length?e:this.get("el")},getItemTpl:function(e,n){var r=this,i=r.get("itemTplRender"),s=r.get("itemTpl");return i?i(e,n):t.substitute(s,e)},_createItem:function(e,t){var n=this,r=n.getItemContainer(),i=n.get("itemCls"),s=n.get("dataField"),o=n.getItemTpl(e,t),u=$(o);if(t!==undefined){var a=r.find("."+i)[t];a?u.insertBefore(a):u.appendTo(r)}else u.appendTo(r);return u.addClass(i),u.data(s,e),u},getItemStatusCls:function(e){return s(e,this)},setItemStatusCls:function(e,t,n){var r=this,i=r.getItemStatusCls(e),s=n?"addClass":"removeClass";t&&$(t)[s](i)},hasStatus:function(e,t){var n=this,r=n.getItemStatusCls(e);return $(t).hasClass(r)},setItemSelected:function(e,t,n){var r=this;n=n||r.findElement(e),r.setItemStatusCls("selected",n,t)},getAllElements:function(){var e=this,t=e.get("itemCls"),n=e.get("el");return n.find("."+t)},getItemByElement:function(e){var t=this,n=t.get("dataField");return $(e).data(n)},getFirstElementByStatus:function(e){var t=this,n=t.getItemStatusCls(e),r=t.get("el");return r.find("."+n)[0]},getElementsByStatus:function(e){var t=this,n=t.getItemStatusCls(e),r=t.get("el");return r.find("."+n)},getSelectedElements:function(){var e=this,t=e.getItemStatusCls("selected"),n=e.get("el");return n.find("."+t)},findElement:function(e){var n=this,r=n.getAllElements(),i=null;return t.each(r,function(t){if(n.getItemByElement(t)==e)return i=t,!1}),i},isElementSelected:function(e){var t=this,n=t.getItemStatusCls("selected");return e&&$(e).hasClass(n)}};var a=function(){};return a.ATTRS=t.merge(!0,i.ATTRS,n.ATTRS,{dataField:{view:!0,value:"data-item"},itemContainer:{view:!0},itemStatusFields:{value:{}},itemCls:{view:!0},cancelSelected:{value:!1},textGetter:{},defaultLoaderCfg:{value:{property:"items",dataType:"json"}},events:{value:{itemrendered:!0,itemremoved:!0,itemupdated:!0,itemsshow:!1,beforeitemsshow:!1,itemsclear:!1,itemdblclick:!1,beforeitemsclear:!1}}}),a.PARSER={items:function(e){var n=this,r=[],i=n.get("itemCls"),s=n.get("dataField"),o=e.find("."+i);return o.length||(o=e.children(),o.addClass(i)),t.each(o,function(e){var t=u(e,n);r.push(t),$(e).data(s,t)}),r}},t.augment(a,i,n,{_uiSetItems:function(e){var t=this;if(t.get("srcNode")&&!t.get("rendered"))return;this.setItems(e)},__bindUI:function(){function i(t,n){var r=e.get("multipleSelect"),i;i=e.isItemSelected(t,n),i?r?e.setItemSelected(t,!1,n):e.get("cancelSelected")&&e.setSelected(null):(r||e.clearSelected(),e.setItemSelected(t,!0,n))}var e=this,t=e.get("selectedEvent"),n=e.get("itemCls"),r=e.get("view").getItemContainer();r.delegate("."+n,"click",function(n){if(e.get("disabled"))return;var r=$(n.currentTarget),s=e.getItemByElement(r);if(e.isItemDisabled(s,r))return;var o=e.fire("itemclick",{item:s,element:r[0],domTarget:n.target,domEvent:n});o!==!1&&t=="click"&&e.isItemSelectable(s)&&i(s,r)}),t!=="click"&&r.delegate("."+n,t,function(t){if(e.get("disabled"))return;var n=$(t.currentTarget),r=e.getItemByElement(n);if(e.isItemDisabled(r,n))return;e.isItemSelectable(r)&&i(r,n)}),r.delegate("."+n,"dblclick",function(t){if(e.get("disabled"))return;var n=$(t.currentTarget),r=e.getItemByElement(n);if(e.isItemDisabled(r,n))return;e.fire("itemdblclick",{item:r,element:n[0],domTarget:t.target})}),e.on("itemrendered itemupdated",function(t){var n=t.item,r=t.element;e._syncItemStatus(n,r)})},getValueByField:function(e,t){return e&&e[t]},_syncItemStatus:function(e,n){var r=this,i=r.get("itemStatusFields");t.each(i,function(t,i){e[t]!=null&&r.get("view").setItemStatusCls(i,n,e[t])})},getStatusValue:function(e,t){var n=this,r=n.get("itemStatusFields"),i=r[t];return e[i]},getCount:function(){var e=this.getItems();return e?e.length:0},getStatusField:function(e){var t=this,n=t.get("itemStatusFields");return n[e]},setStatusValue:function(e,t,n){var r=this,i=r.get("itemStatusFields"),s=i[t];s&&(e[s]=n)},getItemText:function(e){var t=this,n=t.get("textGetter");return e?n?n(e):$(t.findElement(e)).text():""},removeItem:function(e){var n=this,r=n.get("items"),i=n.findElement(e),s;s=t.Array.indexOf(e,r),s!==-1&&r.splice(s,1),n.get("view").removeItem(e,i),n.fire("itemremoved",{item:e,domTarget:$(i)[0],element:i})},addItemAt:function(e,t){var n=this,r=n.get("items");return t===undefined&&(t=r.length),r.splice(t,0,e),n.addItemToView(e,t),e},addItemToView:function(e,t){var n=this,r=n.get("view").addItem(e,t);return n.fire("itemrendered",{item:e,domTarget:$(r)[0],element:r}),r},updateItem:function(e){var t=this,n=t.get("view").updateItem(e);t.fire("itemupdated",{item:e,domTarget:$(n)[0],element:n})},setItems:function(e){var n=this;e!=n.getItems()&&n.setInternal("items",e),n.clearControl(),n.fire("beforeitemsshow"),t.each(e,function(e,t){n.addItemToView(e,t)}),n.fire("itemsshow")},getItems:function(){return this.get("items")},getItemByElement:function(e){return this.get("view").getItemByElement(e)},getSelected:function(){var e=this,t=e.get("view").getFirstElementByStatus("selected");return e.getItemByElement(t)||null},getItemsByStatus:function(e){var n=this,r=n.get("view").getElementsByStatus(e),i=[];return t.each(r,function(e){i.push(n.getItemByElement(e))}),i},findElement:function(e){var n=this;return t.isString(e)&&(e=n.getItem(e)),this.get("view").findElement(e)},findItemByField:function(e,n){var r=this,i=r.get("items"),s=null;return t.each(i,function(t){if(t[e]!=null&&t[e]==n)return s=t,!1}),s},setItemSelectedStatus:function(e,t,n){var r=this;n=n||r.findElement(e),r.setItemStatus(e,"selected",t,n)},setAllSelection:function(){var e=this,t=e.getItems();e.setSelection(t)},isItemSelected:function(e,t){var n=this;return t=t||n.findElement(e),n.get("view").isElementSelected(t)},isItemDisabled:function(e,t){return this.hasStatus(e,"disabled",t)},setItemDisabled:function(e,t){var n=this;n.setItemStatus(e,"disabled",t)},getSelection:function(){var e=this,n=e.get("view").getSelectedElements(),r=[];return t.each(n,function(t){r.push(e.getItemByElement(t))}),r},clearControl:function(){this.fire("beforeitemsclear"),this.get("view").clearControl(),this.fire("itemsclear")},hasStatus:function(e,t,n){if(!e)return!1;var r=this,i=r.getStatusField(t);return n=n||r.findElement(e),r.get("view").hasStatus(t,n)},setItemStatus:function(e,t,n,r){var i=this;e&&(r=r||i.findElement(e));if(!i.isItemDisabled(e,r)||t==="disabled")e&&(t==="disabled"&&n&&i.clearItemStatus(e),i.setStatusValue(e,t,n),i.get("view").setItemStatusCls(t,r,n),i.fire("itemstatuschange",{item:e,status:t,value:n,element:r})),t==="selected"&&i.afterSelected(e,n,r)},clearItemStatus:function(e,n,r){var i=this,s=i.get("itemStatusFields");r=r||i.findElement(e),n?i.setItemStatus(e,n,!1,r):(t.each(s,function(t,n){i.setItemStatus(e,n,!1,r)}),s.selected||i.setItemSelected(e,!1),i.setItemStatus(e,"hover",!1))}}),a.View=o,a}),define("bui/list/keynav",["bui/common"],function(e){"use strict";var t=e("bui/common"),n=function(){};return n.ATTRS={highlightedStatus:{value:"hover"}},t.augment(n,{setHighlighted:function(e,t){if(this.hasStatus(e,"hover",t))return;var n=this,r=n.get("highlightedStatus"),i=n._getHighLightedElement(),s=i?n.getItemByElement(i):null;s!==e&&(s&&this.setItemStatus(s,r,!1,i),this.setItemStatus(e,r,!0,t),n._scrollToItem(e,t))},_getHighLightedElement:function(){var e=this,t=e.get("highlightedStatus"),n=e.get("view").getFirstElementByStatus(t);return n},getHighlighted:function(){var e=this,t=e.get("highlightedStatus"),n=e.get("view").getFirstElementByStatus(t);return e.getItemByElement(n)||null},getColumnCount:function(){var e=this,t=e.getFirstItem(),n=e.findElement(t),r=$(n);return n?parseInt(r.parent().width()/r.outerWidth(),10):1},getRowCount:function(e){var t=this;return e=e||t.getColumnCount(),(this.getCount()+e-1)/e},_getNextItem:function(e,t,n){var r=this,i=r._getCurrentIndex(),s=r.getCount(),o=e?1:-1,u;return i===-1?e?r.getFirstItem():r.getLastItem():(e||(t*=o),u=(i+t+n)%n,u>s-1&&(e?u-=s-1:u+=t),r.getItemAt(u))},_getLeftItem:function(){var e=this,t=e.getCount(),n=e.getColumnCount();return!t||n<=1?null:e._getNextItem(!1,1,t)},_getCurrentItem:function(){return this.getHighlighted()},_getCurrentIndex:function(){var e=this,t=e._getCurrentItem();return this.indexOfItem(t)},_getRightItem:function(){var e=this,t=e.getCount(),n=e.getColumnCount();return!t||n<=1?null:this._getNextItem(!0,1,t)},_getDownItem:function(){var e=this,t=e.getColumnCount(),n=e.getRowCount(t);return n<=1?null:this._getNextItem(!0,t,t*n)},getScrollContainer:function(){return this.get("el")},isScrollVertical:function(){var e=this,t=e.get("el"),n=e.get("view").getItemContainer();return t.height()<n.height()},_scrollToItem:function(e,t){var n=this;if(n.isScrollVertical()){t=t||n.findElement(e);var r=n.getScrollContainer(),i=$(t).position().top,s=r.position().top,o=r.height(),u=i-s,a=$(t).height(),f=r.scrollTop();(u<0||u>o-a)&&r.scrollTop(f+u)}},_getUpperItem:function(){var e=this,t=e.getColumnCount(),n=e.getRowCount(t);return n<=1?null:this._getNextItem(!1,t,t*n)},handleNavUp:function(e){var t=this,n=t._getUpperItem();t.setHighlighted(n)},handleNavDown:function(e){this.setHighlighted(this._getDownItem())},handleNavLeft:function(e){this.setHighlighted(this._getLeftItem())},handleNavRight:function(e){this.setHighlighted(this._getRightItem())},handleNavEnter:function(e){var t=this,n=t._getCurrentItem(),r;n&&(r=t.findElement(n),$(r).trigger("click"))},handleNavEsc:function(e){this.setHighlighted(null)},handleNavTab:function(e){this.setHighlighted(this._getRightItem())}}),n}),define("bui/list/sortable",["bui/common","bui/data"],function(e){var t=e("bui/common"),n=e("bui/data").Sortable,r=function(){};return r.ATTRS=t.merge(!0,n.ATTRS,{}),t.augment(r,n,{compare:function(e,t,n,r){var i=this,s;return n=n||i.get("sortField"),r=r||i.get("sortDirection"),!n||!r?1:(s=r==="ASC"?1:-1,$.isPlainObject(e)||(e=i.getItemByElement(e)),$.isPlainObject(t)||(t=i.getItemByElement(t)),i.get("compareFunction")(e[n],t[n])*s)},getSortData:function(){return $.makeArray(this.get("view").getAllElements())},sort:function(e,n){var r=this,i=r.sortData(e,n),s=r.get("view").getItemContainer();r.get("store")||r.sortData(e,n,r.get("items")),t.each(i,function(e){$(e).appendTo(s)})}}),r}),define("bui/list/simplelist",["bui/common","bui/list/domlist","bui/list/keynav","bui/list/sortable"],function(e){var t=e("bui/common"),n=t.Component.UIBase,r=t.UA,i=e("bui/list/domlist"),s=e("bui/list/keynav"),o=e("bui/list/sortable"),u=t.prefix+"list-item",a=t.Component.View.extend([i.View],{setElementHover:function(e,t){var n=this;n.setItemStatusCls("hover",e,t)}},{ATTRS:{itemContainer:{valueFn:function(){return this.get("el").find(this.get("listSelector"))}}}},{xclass:"simple-list-view"}),f=t.Component.Controller.extend([i,n.Bindable,s,o],{bindUI:function(){var e=this,t=e.get("itemCls"),n=e.get("view").getItemContainer();n.delegate("."+t,"mouseover",function(t){if(e.get("disabled"))return;var n=t.currentTarget,i=e.getItemByElement(n);if(e.isItemDisabled(t.item,t.currentTarget))return;!(r.ie&&r.ie<8)&&e.get("focusable")&&e.get("highlightedStatus")==="hover"?e.setHighlighted(i,n):e.setItemStatus(i,"hover",!0,n)}).delegate("."+t,"mouseout",function(t){if(e.get("disabled"))return;var n=$(t.currentTarget);e.get("view").setElementHover(n,!1)})},onAdd:function(e){var t=this,n=t.get("store"),r=e.record;t.getCount()==0?t.setItems(n.getResult()):t.addItemToView(r,e.index)},onRemove:function(e){var t=this,n=e.record;t.removeItem(n)},onUpdate:function(e){this.updateItem(e.record)},onLocalSort:function(e){this.get("frontSortable")?this.sort(e.field,e.direction):this.onLoad(e)},onLoad:function(){var e=this,t=e.get("store"),n=t.getResult();e.set("items",n)},onFiltered:function(e){var t=this,n=e.data;t.set("items",n)}},{ATTRS:{frontSortable:{value:!1},focusable:{value:!1},items:{view:!0,value:[]},itemCls:{view:!0,value:u},idField:{value:"value"},listSelector:{view:!0,value:"ul"},itemTpl:{view:!0,value:'<li role="option" class="'+u+'">{text}</li>'},tpl:{value:"<ul></ul>"},xview:{value:a}}},{xclass:"simple-list",prority:0});return f.View=a,f}),define("bui/list/listbox",["bui/list/simplelist"],function(e){var t=e("bui/list/simplelist"),n=t.extend({bindUI:function(){var e=this;e.on("selectedchange",function(e){var t=e.item,n=$(e.domTarget),r=n.find("input");t&&r.attr("checked",e.selected)})}},{ATTRS:{itemTpl:{value:'<li><span class="x-checkbox"></span>{text}</li>'},multipleSelect:{value:!0}}},{xclass:"listbox"});return n}),define("bui/list/listitem",["bui/common"],function(e){var t=e("bui/common"),n=t.Component,r=n.UIBase,i=n.View.extend([r.ListItemView],{}),s=n.Controller.extend([r.ListItem],{},{ATTRS:{elTagName:{view:!0,value:"li"},xview:{value:i},tpl:{view:!0,value:"<span>{text}</span>"}}},{xclass:"list-item"});return s.View=i,s}),define("bui/list/list",["bui/common"],function(e){var t=e("bui/common"),n=t.Component,r=n.UIBase,i=n.Controller.extend([r.ChildList],{},{ATTRS:{elTagName:{view:!0,value:"ul"},idField:{value:"id"},defaultChildClass:{value:"list-item"}}},{xclass:"list"});return i});
