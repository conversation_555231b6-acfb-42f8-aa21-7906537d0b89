.not-top {
	padding: 0 15px 15px 15px;
}
.row {
	margin-top: 15px;
	margin-right: -15px;
    margin-left: -15px;
}
@media (min-width: 768px) {
	.block-col {
    width: 33.33333333%;
    float: left;
  }
	.block-col2 {
		width: 66.66666666%;
		float: left;
	}
}
.block-col {
    position: relative;
    min-height: 1px;
    padding-right: 15px;
    padding-left: 15px;
}
.content-box {
	background-color: #fff;
	border-top: 1px solid #e7eaec;
	padding: 0 15px 15px 15px;
	clear: both;
	min-height: 255px;
	max-height: 255px;
}

.progress {
	margin-left: 20px;
	margin-top: 30px;
	width: 90%;
	height: 30px;
}
.progress .bar {
	font-weight: 600;
	line-height: 30px;
}
.progress .usedBar {
    float: right;
    height: 100%;
    font-size: 12px;
    line-height: 20px;
    text-align: center;
}

.img-circle {
	border-radius: 50%;   
	height: 200px;    
	width: 200px;    
	display: inline-block;    
	background: #E21836;
	vertical-align: middle;
	text-align: center;
	margin: 25px auto auto auto;
	cursor: pointer
}
.cart-plus-icon {
	line-height: 140px;
    font-size: 40px;
    color: white;
    margin-left: -5px;
}
.img-circle-text {
	display: block;    
	color: #FFFFFF;    
	height: 200px;
	font-size: 16px;
	font-weight: 700;    
	text-align: center;
	margin-top: -30px;
}

.system-support .support-item i {
	margin-right: 5px;
	color: rgb(0,157,217);
}
.message-title {
    font-size: 14px;
  	font-weight: 700;
  	margin-left: 0px;
}
.support-item {
	margin-left: 6px;
}
.message-content {
	font-size: 13px;
}
.data-content {
	color:#c00000;
}
.click-url {
	cursor: pointer;
	text-decoration: underline;
}
.click-url-font {
	display: inline-block;
    float: right;
    font-size: 12px;
    font-weight: 100;
    color: #c00000;
    cursor: pointer;
	text-decoration: underline;
}

.score-panel {
	
}

.support-box {
	width: 48%;
	height: 80px;
	float:left;
	margin-left: 5px;
	margin-top: 5px;
	vertical-align: middle;
    text-align: center;
    color: #FFFFFF;
    font-weight: 900;
}
.support-box-icon {
	line-height: 40px;
	font-size:25px;
}

.store-box {
	width: 48%;
	height: 200px;
	float:left;
	margin-left: 5px;
	margin-top: 5px;
	vertical-align: middle;
    text-align: center;
}
.store-box-content {
	padding-top: 60px;
}
.store-box-num {
	font-weight: 900;
	font-size:45px;
	color: #c00000;
}
.store-box-text {
	line-height: 0px;
	font-size: 16px;
}
.chart-text {
	margin-top: 0px;
    text-align: center;
    font-size: 14px;
    font-weight: 700;
}
.rebate-box {
	width: 48%;
    border: 1px solid #999;
    float: left;
    box-shadow: 2px 0px 2px 0px #999;
    height: 180px;
    background-color: #fafafa;
}
.rebate-box > .message-title {
	margin-left: 5px;
	font-size: 13px;
	background-color: #fafafa;
}

.explain-text {
	font-size: 12px;
	font-weight: 400;
}

* {
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;
}
*:before,
*:after {
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;
}

.subject-indexes-wrapper {
	position: fixed;
    right: 0;
    z-index: 999;
    top: 45%;
}
.subject-indexes-wrapper  ul{
	text-align: center;
	margin-right:0px;
}
.subject-indexes-wrapper ul li {
	display: block;
    width: 156px;
    text-align: center;
    height: 30px;
    overflow: hidden;
    background-color: rgba(0, 102, 178, 0.5);
    color: #fff;
    padding: 6px;
    cursor: pointer;
    border: 1px solid #eee;
    box-shadow: 1px -1px 6px #ccc;
}
.subject-indexes-wrapper ul li:hover{
	background-color: #0066b2;
    color: #fff;
}
.subject-indexes-wrapper ul li.active{
    background-color: #0066b2;
    color: #fff;
}
.subject-indexes-wrapper ul li i{
	margin-right:6px;
}


.short-cut-div {
    /*margin-left: 10px;*/
    width: calc(50% - 10px);
    display: inline-block;
    vertical-align:top;
}
@media (max-width: 1280px) {
    .short-cut-div{
        margin-left: 0;
    }
}
.short-cut-div ul {
	font-size: 14px;
	font-weight: 700;
	list-style: none;
	margin-left: 0;
	margin-top: 5px;
}
.short-cut-div ul>li>i {
    color: #007cc8;
}
.short-cut-div ul>li>a {
    margin-left: 3px;
    color: #007cc8;
}
.short-cut-div ul>li {
	margin-top: 15px;
    /*white-space: nowrap;*/
    /*float:left;*/
}
.short-cut-div a:hover {
	text-decoration:underline;
}
.grid-desc {
    margin: 3px 20px 3px 20px;
}
.new-home-grid-div {
   min-height: 80px;
    margin-top: 7px;
}
.grid-content-box {
	max-height: none;
}
.resources-situation tbody td {
	text-align: center;
	height: 20px;
	line-height: 20px;
	font-size: 12px;
}
.resources-situation thead th {
	background-color: rgb(229,229,229);
}
.resources-situation thead th.td-title {
	text-align: center;
	width: 33%;
	height: 20px;
	line-height: 20px;
	font-size: 12px;
}
.resources-situation thead th {
	border-right: 1px solid #dddddd;
	border-bottom: 1px solid #dddddd;
}
.resources-situation tbody tr:hover {
    background-color: #f5f5f5;
}
.resources-situation tbody tr:nth-child(2n+1) {
	background-color: #f9f9f9;
}
.resources-situation tbody td:nth-child(n+2) {
    color: #319dfc;
}
    /* 数据表相关的css */
.new-home-grid {
    width: 100%;
    border-left: 1px solid #dddddd;
    border-top: 1px solid #dddddd;
}
.new-home-grid td{
    width: calc(7%);
    text-align: center;
    line-height: 20px;
    height: 20px;
    font-size: 12px;
    border-left: 1px solid #dddddd;
    border-top: 1px solid #dddddd;
    border-right: 1px solid #dddddd;
    border-bottom: 1px solid #dddddd;
    word-break: keep-all;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
@media (max-width: 1280px) {
    .new-home-grid td{
        max-width: 30px;
    }
}
.new-home-grid div.not-up {
    background-color: rgb(255,0,0);
    font-weight: bold;
    color: #ffffff;
    width: calc(33.3333%);
    text-align: center;
    margin-left: calc(33.3333%);
}
.new-home-grid div.up {
    background-color: rgb(0,176,80);
    font-weight: bold;
    color: #ffffff;
    width: calc(33.3333%);
    text-align: center;
    margin-left: calc(33.3333%);
}
.new-home-grid div.warning {
    background-color: rgb(234,194,130);
    font-weight: bold;
    color: #ffffff;
    width: calc(33.3333%);
    text-align: center;
    margin-left: calc(33.3333%);
}
.new-home-grid div.running {
    background-color: transparent;
    /*font-weight: bold;*/
    color: rgb(51, 51, 51);
    width: calc(33.3333%);
    text-align: center;
    margin-left: calc(33.3333%);
}
.new-home-grid div.normal-text {
    background-color: transparent;
    color: rgb(51, 51, 51);
}
.new-home-grid th{
    width: 200px;
    text-align: center;
    font-size: 12px;
    line-height: 20px;
    height: 20px;
    border-left: 1px solid #dddddd;
    border-top: 1px solid #dddddd;
    border-right: 1px solid #dddddd;
    border-bottom: 1px solid #dddddd;
}
.new-home-grid thead th {
    border-right: 1px solid #dddddd;
    border-bottom: 1px solid #dddddd;
}
.new-home-grid tbody th {
    background-color: rgb(229,229,229);
}
.new-home-grid tbody tr:hover {
    background-color: #f5f5f5;
}
.new-home-grid th.grid-title {
    background-color: rgb(0,124,200);
    /*background-color: rgb(229,229,229);*/
    font-weight: bold;
}
.new-home-grid th.grid-title p {
    font-size: 12px;
    color: #ffffff;
    line-height: 20px;
    height: 20px;
    margin-top: 1px;
    margin-bottom: 1px;
    text-align: left;
    padding-left: 10px;
}
.new-home-grid.quarter-grid td{
    width: calc(21%);
}
.not-up-icon {
    color: rgb(214,85,50);
    margin-right: 5px;
}
.warning-icon {
    color: rgb(234,194,130);
    margin-right: 5px;
}
.right-icon {
    color: rgb(104,164,144);
    margin-right: 5px;
}
.digital-tr td{
    font-weight: 600;
}
.big-content-box {
    max-height: 300px;
    min-height: 300px;
}
.high-content-box {
    min-height: 525px;
    max-height: 525px;
}
@media (max-width: 1024px) {
    .high-content-box {
        max-height: none;
    }
}

.width100 {
    width: 100% !important;
    margin-left: 0 !important;
}