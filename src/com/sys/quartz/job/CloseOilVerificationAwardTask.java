package com.sys.quartz.job;

import java.sql.Date;
import java.util.Calendar;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import com.chevron.awardpolicy.business.OilVerAwardDetBizService;
import com.common.exception.WxPltException;
import com.common.util.DateUtil;

/**
 * 结算上一个月机油核销激励任务
 * <AUTHOR>
 *
 */
public class CloseOilVerificationAwardTask {
	@Autowired
	OilVerAwardDetBizService oilVerAwardDetBizService;
	
	private static Logger log = LoggerFactory.getLogger(CloseOilVerificationAwardTask.class);

	public void execute(){
		log.info("CloseOilVerificationAwardTask job start....");
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(DateUtil.getCurrentDate());
		calendar.add(Calendar.MONTH, -1);
		try {
			oilVerAwardDetBizService.createAwardDetail(Date.valueOf(DateUtil.getDateStr(calendar.getTime(), "yyyy-MM") + "-01"));
		} catch (WxPltException e) {
			e.printStackTrace();
			log.error("CloseOilVerificationAwardTask job break....", e);
		}
		log.info("CloseOilVerificationAwardTask job finish....");
	}

}
