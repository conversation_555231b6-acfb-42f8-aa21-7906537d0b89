select * from PP_MID.dbo.pp_distributor_customer c
left join wx_t_work_shop w on c.customer_id=w.id
where c.customer_type=1

SELECT *
       , (
             SELECT substring((
                           SELECT ',' + d.item_name
                           FROM pp_dict d
                           WHERE d.type_code = 'DistributorCustomer.customerLabel'
                                 AND d.item_code & dc.customer_label > 0
                             FOR XML PATH,TYPE
                    ).value('.', 'varchar(max)'), 2, 100000)
       ) AS "门店标签"
FROM pp_distributor_customer dc


select * from wx_t_work_shop w1 where w1.id in (
SELECT w.id
  FROM [PP_MID].[dbo].[pp_distributor_customer] p left join wx_t_work_shop w on p.customer_id=w.id 
  where distributor_id=673 and w.status<>'3')


INSERT INTO PP_MID.[dbo].[pp_distributor_customer]
           ([distributor_id]
           ,[customer_type]
           ,[customer_id]
           ,[customer_name]
           ,[province_code]
           ,[province_name]
           ,[city_code]
           ,[city_name]
           ,[dist_code]
           ,[dist_name]
           ,[address]
           ,[customer_status]
           ,[dms_key]
           ,[dms_customer_code]
           ,[contact_person]
           ,[contact_person_tel]
           ,[from_source]
           ,[customer_create_time]
           ,[customer_label]
           ,[dsr_name]
           ,[business_property]
           ,[ext_property1]
           ,[ext_property2]
           ,[ext_property3]
           ,[ext_property4]
           ,[ext_property5]
           ,[delete_flag]
           ,[create_time]
           ,[update_time])
select pe.distributor_id, 1 customer_type, w.id [customer_id],
w.work_shop_name [customer_name], r3.region_code [province_code], r3.region_name [province_name],
r2.region_code [city_code], r2.region_name [city_name], r1.region_code [dist_code],
r1.region_name [dist_name], w.work_shop_address [address], w.status [customer_status],
w.dms_key [dms_key], dw.[Customer_Code] [dms_customer_code], w.[contact_person] [contact_person], 
w.[contact_person_tel] [contact_person_tel], w.[from_source] [from_source],
w.create_time [customer_create_time], 
w.from_source & 2 +
+ (case when exists (select 1 from wx_t_workshop_employee we where we.version_no=1 and we.workshop_id=w.id) then 1024 else 0 end/*B2B店*/)
			 + (case when w.shop_recruitment=1 then 2048 else 0 end/*店招店*/) + (case when w.join_location_plan=1 then 4096 else 0 end/*定位店*/)
			  + (case when w.ext_flag&1024>0 then 8192 else 0 end/*参与德乐400MGX陈列活动*/)
			  + (case when w.ext_flag&16>0 then 16384 else 0 end/*导航店*/) [customer_label],
(select u.ch_name from wx_task_sub s LEFT JOIN wx_task_main m ON s.task_main_id = m.task_main_id
left join wx_t_user u on u.user_id=m.excute_user_id where s.org_id=w.id and m.tmb_type_code IN ('TT_2_CLZX', 'TT_2_SD')) [dsr_name],
w.business_weight|w.from_source [business_property], 0 [delete_flag], getdate() [create_time],
getdate() [update_time] from wx_t_work_shop w 
left join wx_t_workshop_partner wp on w.id=wp.workshop_id
left join wx_t_partner_o2o_enterprise pe on pe.partner_id=wp.partner_id
left join dw_dms_workshop dw on dw.[Distributor_Code] + '/' + dw.[Customer_Code]=w.dms_key
		    left join wx_t_region r1 on w.region_id=r1.id
		    left join wx_t_region r2 on r1.parent_id=r2.id
		    left join wx_t_region r3 on r2.parent_id=r3.id
where not exists (select 1 from PP_MID.dbo.pp_distributor_customer c
where c.customer_type=1 and c.customer_id=w.id)
and w.status='3' and w.delete_flag=0
and wp.partner_id!=9 and pe.distributor_id is not null
and w.from_source&3>0
and w.update_time<getdate()