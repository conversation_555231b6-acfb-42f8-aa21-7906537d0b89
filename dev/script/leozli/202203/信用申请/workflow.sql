/*==============================================================*/
/* Module: 流程引擎                                                 */
/*==============================================================*/

/*==============================================================*/
/* Table: wx_t_workflow_step                                    */
/*==============================================================*/
create table wx_t_workflow_step (
	step_id                          bigint                            not null identity,
	workflow_code                    nvarchar(64)                               not null,
	step_code                        nvarchar(64)                               not null,
	step_name                        nvarchar(64)                               not null,
	history_step_name                nvarchar(64)                                   null,
	step_icon                        nvarchar(256)                                  null,
	list_operate_name                nvarchar(64)                                   null,
	accept_alias                     nvarchar(64)                                   null,
	operation_permission_weight      int                                        not null,
	biz_permission_weight            bigint                                     not null,
	reject_to_step                   nvarchar(64)                                   null,
	reject_notify_type               int                                        not null,
	reject_email_cc                  nvarchar(256)                                  null,
	reject_notify_template           nvarchar(256)                                  null,
	todo_notify_type                 int                                        not null,
	todo_email_cc                    nvarchar(256)                                  null,
	todo_notify_template             nvarchar(256)                                  null,
	pre_recall_notify_type           int                                        not null,
	pre_recall_email_cc              nvarchar(256)                                  null,
	pre_recall_email_temp            nvarchar(256)                                  null,
	finish_rate_type                 tinyint                                    not null,
	finish_rate                      decimal(20,6)                                  null,
	ext_property1                    nvarchar(256)                                  null,
	ext_property2                    nvarchar(256)                                  null,
	ext_property3                    nvarchar(256)                                  null,
	ext_property4                    nvarchar(256)                                  null,
	ext_property5                    nvarchar(256)                                  null,
	ext_property6                    nvarchar(256)                                  null,
	ext_property7                    nvarchar(256)                                  null,
	ext_property8                    nvarchar(256)                                  null,
	ext_property9                    nvarchar(256)                                  null,
	sort_numb                        decimal(20,10)                                 null,
	reject_sms_notify_template       nvarchar(256)                                  null,
	todo_sms_notify_template         nvarchar(256)                                  null,
	pre_recall_sms_temp              nvarchar(256)                                  null,
	property_view_weight1            bigint                                     not null,
	property_edit_weight1            bigint                                     not null,
	property_required_weight1        bigint                                     not null,
	property_view_weight2            bigint                                     not null,
	property_edit_weight2            bigint                                     not null,
	property_required_weight2        bigint                                     not null,
	property_view_weight3            bigint                                     not null,
	property_edit_weight3            bigint                                     not null,
	property_required_weight3        bigint                                     not null,
	remark                           nvarchar(1024)                                 null,
	predefined_flag                  tinyint                                    not null,
	delete_flag                      bigint                                     not null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	primary key (step_id)
);
ALTER TABLE wx_t_workflow_step ADD CONSTRAINT uk_workflow_step UNIQUE (workflow_code,step_code,delete_flag);

/*==============================================================*/
/* Table: wx_t_workflow                                         */
/*==============================================================*/
create table wx_t_workflow (
	workflow_code                    nvarchar(64)                               not null,
	workflow_name                    nvarchar(64)                               not null,
	handle_bean                      nvarchar(64)                                   null,
	page_themes                      nvarchar(64)                                   null,
	workflow_version                 int                                        not null,
	end_notify                       int                                        not null,
	end_notify_template              nvarchar(256)                                  null,
	end_email_cc                     nvarchar(256)                                  null,
	def_todo_email_temp              nvarchar(256)                                  null,
	def_reject_notify_template       nvarchar(256)                                  null,
	sms_notify_template              nvarchar(256)                                  null,
	def_sms_notify_temp              nvarchar(255)                                  null,
	flow_status                      int                                        not null,
	property_view_weight1            bigint                                     not null,
	property_edit_weight1            bigint                                     not null,
	property_required_weight1        bigint                                     not null,
	property_view_weight2            bigint                                     not null,
	property_edit_weight2            bigint                                     not null,
	property_required_weight2        bigint                                     not null,
	property_view_weight3            bigint                                     not null,
	property_edit_weight3            bigint                                     not null,
	property_required_weight3        bigint                                     not null,
	delete_flag                      tinyint                                    not null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	primary key (workflow_code)
);

/*==============================================================*/
/* Table: wx_t_wor_ins_exe_list                                 */
/*==============================================================*/
create table wx_t_wor_ins_exe_list (
	executor                         bigint                                     not null,
	step_instance_id                 bigint                                     not null,
	actual_executor                  bigint                                         null,
	execute_status                   int                                        not null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	primary key (executor, step_instance_id)
);

/*==============================================================*/
/* Table: wx_t_workflow_instance                                */
/*==============================================================*/
create table wx_t_workflow_instance (
	flow_instance_id                 bigint                            not null identity,
	workflow_code                    nvarchar(64)                               not null,
	form_key                         nvarchar(64)                               not null,
	flow_request_no                  nvarchar(64)                                   null,
	current_step                     nvarchar(64)                               not null,
	next_step_no                     int                                        not null,
	form_desc                        nvarchar(512)                                  null,
	form_summary                     nvarchar(4000)                                 null,
	request_user                     bigint                                     not null,
	step_version_no                  bigint                                     not null,
	instance_ext_property1           nvarchar(256)                                  null,
	instance_ext_property2           nvarchar(256)                                  null,
	instance_ext_property3           nvarchar(256)                                  null,
	instance_ext_property4           nvarchar(256)                                  null,
	instance_ext_property5           nvarchar(256)                                  null,
	instance_ext_property6           nvarchar(256)                                  null,
	instance_ext_property7           nvarchar(256)                                  null,
	instance_ext_property8           nvarchar(256)                                  null,
	instance_ext_property9           nvarchar(256)                                  null,
	apply_time                       datetime                                       null,
	status                           int                                        not null,
	close_time                       date                                           null,
	workflow_version                 int                                        not null,
	version_no                       bigint                                     not null,
	delete_flag                      tinyint                                    not null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	instance_apply_type              nvarchar(64)                                   null,
	instance_apply_owner             nvarchar(256)                                  null,
	instance_apply_owner_type        nvarchar(64)                                   null,
	instance_apply_owner_key         nvarchar(64)                                   null,
	primary key (flow_instance_id)
);
ALTER TABLE wx_t_workflow_instance ADD CONSTRAINT uk_workflow_instance UNIQUE (workflow_code,form_key);

/*==============================================================*/
/* Table: wx_t_workflow_pre_exe_group                           */
/*==============================================================*/
create table wx_t_workflow_pre_exe_group (
	id                               bigint                            not null identity,
	group_name                       nvarchar(64)                               not null,
	condition_sql                    nvarchar(4000)                             not null,
	sort_numb                        decimal(20,10)                                 null,
	group_desc                       nvarchar(1024)                                 null,
	remark                           nvarchar(1024)                                 null,
	enable_flag                      tinyint                                    not null,
	delete_flag                      tinyint                                    not null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	primary key (id)
);
ALTER TABLE wx_t_workflow_pre_exe_group ADD CONSTRAINT uk_workflow_pre_exe_group UNIQUE (group_name);

/*==============================================================*/
/* Table: wx_t_workflow_exe_list                                */
/*==============================================================*/
create table wx_t_workflow_exe_list (
	source_type                      nvarchar(32)                               not null,
	source_key                       nvarchar(64)                               not null,
	step_id                          bigint                                     not null,
	remark                           nvarchar(1024)                                 null,
	primary key (source_type, source_key, step_id)
);

/*==============================================================*/
/* Table: wx_t_workflow_step_instance                           */
/*==============================================================*/
create table wx_t_workflow_step_instance (
	step_instance_id                 bigint                            not null identity,
	workflow_instance_id             bigint                                     not null,
	step_id                          bigint                                     not null,
	step_no                          int                                        not null,
	version_no                       bigint                                     not null,
	todo_numb                        int                                        not null,
	enable_flag                      tinyint                                    not null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	primary key (step_instance_id)
);
ALTER TABLE wx_t_workflow_step_instance ADD CONSTRAINT uk_workflow_step_instance UNIQUE (workflow_instance_id,step_no,version_no);

/*==============================================================*/
/* Table: wx_t_wor_property_permission                          */
/*==============================================================*/
create table wx_t_wor_property_permission (
	id                               bigint                            not null identity,
	workflow_code                    nvarchar(64)                               not null,
	property_name                    nvarchar(256)                              not null,
	permission_name                  nvarchar(256)                                  null,
	weight_bit_index                 smallint                                   not null,
	sort_numb                        decimal(20,10)                                 null,
	remark                           nvarchar(1024)                                 null,
	primary key (id)
);
ALTER TABLE wx_t_wor_property_permission ADD CONSTRAINT uk_wor_property_permission UNIQUE (workflow_code,weight_bit_index);

/*==============================================================*/
/* Table: wx_t_workflow_ext_config                              */
/*==============================================================*/
create table wx_t_workflow_ext_config (
	property_name                    nvarchar(256)                              not null,
	workflow_code                    nvarchar(64)                               not null,
	property_title                   nvarchar(256)                              not null,
	enable_flag                      tinyint                                    not null,
	primary key (property_name, workflow_code)
);

/*==============================================================*/
/* Table: wx_t_workflow_step_history                            */
/*==============================================================*/
create table wx_t_workflow_step_history (
	history_id                       bigint                            not null identity,
	step_instance_id                 bigint                                     not null,
	executor                         bigint                                     not null,
	actual_executor                  bigint                                         null,
	approve_status                   int                                        not null,
	approve_remark                   nvarchar(1024)                                 null,
	execute_time                     datetime                                       null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	primary key (history_id)
);
CREATE INDEX ix_wor_step_his_exe ON wx_t_workflow_step_history (executor);

/*==============================================================*/
/* Table: wx_t_workflow_delegate                                */
/*==============================================================*/
create table wx_t_workflow_delegate (
	id                               bigint                            not null identity,
	menu_id                          bigint                                     not null,
	delegate_user                    bigint                                     not null,
	authorize_user                   bigint                                     not null,
	start_date                       date                                           null,
	end_date                         date                                           null,
	enable_flag                      tinyint                                    not null,
	remark                           nvarchar(1024)                                 null,
	delete_flag                      tinyint                                    not null,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	update_user_id                   bigint                                         null,
	update_time                      datetime                                       null,
	primary key (id)
);
CREATE INDEX ix_wor_del_del_user ON wx_t_workflow_delegate (delegate_user);

/*==============================================================*/
/* Table: wx_t_workflow_biz_permission                          */
/*==============================================================*/
create table wx_t_workflow_biz_permission (
	id                               bigint                            not null identity,
	workflow_code                    nvarchar(64)                               not null,
	permission_name                  nvarchar(256)                              not null,
	weight_bit_index                 smallint                                   not null,
	sort_numb                        decimal(20,10)                                 null,
	remark                           nvarchar(1024)                                 null,
	primary key (id)
);
ALTER TABLE wx_t_workflow_biz_permission ADD CONSTRAINT uk_workflow_biz_permission UNIQUE (workflow_code,weight_bit_index);

/*==============================================================*/
/* Foreign Key(only index): ix_workflow_step_workflow           */
/*==============================================================*/
CREATE INDEX ix_workflow_step_workflow ON wx_t_workflow_step (workflow_code);

/*==============================================================*/
/* Foreign Key(only index): ix_wo_in_ex_li_wo_st_in             */
/*==============================================================*/
CREATE INDEX ix_wo_in_ex_li_wo_st_in ON wx_t_wor_ins_exe_list (step_instance_id);

/*==============================================================*/
/* Foreign Key(only index): ix_workflow_instance_workflow       */
/*==============================================================*/
CREATE INDEX ix_workflow_instance_workflow ON wx_t_workflow_instance (workflow_code);

/*==============================================================*/
/* Foreign Key(only index): ix_wor_exe_list_wor_step            */
/*==============================================================*/
CREATE INDEX ix_wor_exe_list_wor_step ON wx_t_workflow_exe_list (step_id);

/*==============================================================*/
/* Foreign Key(only index): ix_wor_step_ins_wor_ins             */
/*==============================================================*/
CREATE INDEX ix_wor_step_ins_wor_ins ON wx_t_workflow_step_instance (workflow_instance_id);

/*==============================================================*/
/* Foreign Key(only index): ix_wor_step_ins_wor_step            */
/*==============================================================*/
CREATE INDEX ix_wor_step_ins_wor_step ON wx_t_workflow_step_instance (step_id);

/*==============================================================*/
/* Foreign Key(only index): ix_wor_pro_per_wor                  */
/*==============================================================*/
CREATE INDEX ix_wor_pro_per_wor ON wx_t_wor_property_permission (workflow_code);

/*==============================================================*/
/* Foreign Key(only index): ix_wor_ext_con_wor                  */
/*==============================================================*/
CREATE INDEX ix_wor_ext_con_wor ON wx_t_workflow_ext_config (workflow_code);

/*==============================================================*/
/* Foreign Key(only index): ix_wor_step_his_wor_step_ins        */
/*==============================================================*/
CREATE INDEX ix_wor_step_his_wor_step_ins ON wx_t_workflow_step_history (step_instance_id);

/*==============================================================*/
/* Foreign Key(only index): ix_wor_biz_per_wor                  */
/*==============================================================*/
CREATE INDEX ix_wor_biz_per_wor ON wx_t_workflow_biz_permission (workflow_code);
