package com.sys.quartz.business;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import com.sys.quartz.dao.ScheduleJobMapper;
import com.sys.quartz.model.ScheduleJob;
import com.sys.quartz.service.impl.JobTaskService;
/**
 * 
 * @Copyright: 本内容仅限于德勤公司内部使用，禁止转发. 
 * @Author: bo.liu  2016-7-13 下午1:10:31 
 * @Version: $Id$
 * @Desc: <p></p>
 */
@Service
public class ScanTaskQuartzService{
	public final Logger log = Logger.getLogger(this.getClass());
	@Resource
	ScheduleJobMapper scheduleJobMapper;
	@Resource 
	JobTaskService mjobService;
	public static boolean isStop = false;
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public synchronized void doHandleScanTask() {
		log.debug("------------------------------by ljc 扫描定时任务是否有效开始执行---------------------------");
		//0.获取过期定时任务列表
		List<ScheduleJob> scheduleJobLst = scheduleJobMapper.getExpiredQuartzTask();
		Map<String,Object> reqMap = new HashMap<String,Object>();
		try
		{	
			//测试用例01 获取计划中的job
			/*List<ScheduleJob> planJobs = mjobService.getAllPlanJob();//计划中的，说明是潜在运行的
			for(ScheduleJob planJob: planJobs)
			{
				System.out.println("----111planJob---by bo.liu size:"+planJobs.size()+"----job.id:"+planJob.getJobName());
			}*/
			
			
			//1.容器中删除对应的job
			//List<Long> jobIdsLst =  new ArrayList<Long>();
			//此变量用于测试添加指定job //ScheduleJob deletedScheduleJob = null;
			for(ScheduleJob scheduleJob: scheduleJobLst)
			{
				//deletedScheduleJob = scheduleJob;
				mjobService.deleteJob(scheduleJob);
			}
			
			//2.更改后台数据库对应job的状态为停止0
			reqMap.put("jobLst",scheduleJobLst);
			reqMap.put("jobStatus", ScheduleJob.STATUS_NOT_RUNNING);
			scheduleJobMapper.updateExpiredJob(reqMap);
			
			//测试用例02
			/*List<ScheduleJob> planJobs2 = mjobService.getAllPlanJob();
			//此变量用于测试恢复指定job 
			ScheduleJob scheduleJob = null;
			for(ScheduleJob planJob2: planJobs2)
			{
				if(planJob2.getJobName().equals("1468209985629124783"))
				{
					scheduleJob = planJob2;
					System.out.println("--job yes");
				}//用于恢复指定job  
				
				System.out.println("----222planJob2---by bo.liu size:"+planJobs2.size()+"----job.id:"+planJob2.getJobName());
			}
			
			//测试恢复一个job
			if(isStop)
			{
				mjobService.resumeJob(scheduleJob);
				System.out.println("----resume");
			}
			
			//测试暂停一个job
			if(!isStop)
			{
				mjobService.pauseJob(scheduleJob);
				System.out.println("---stop");
				isStop = true;
			}
		
			//测试添加一个job
			mjobService.addJob(deletedScheduleJob);
			List<ScheduleJob> planJobs3 = mjobService.getAllPlanJob();//计划中的，说明是潜在运行的
			for(ScheduleJob planJob3: planJobs3)
			{
				System.out.println("----333planJob---by bo.liu size:"+planJobs3.size()+"----job.id:"+planJob3.getJobName());
			}*/
			
		}catch(Exception ex)
		{
			TransactionAspectSupport.currentTransactionStatus()
			.setRollbackOnly();
			ex.printStackTrace();
		}
		log.debug("------------------------------by ljc 扫描定时任务是否有效执行结束---------------------------");
	}
}
