package com.chevron.b2b.dao;

import com.chevron.b2b.model.ScheduleItem;
import com.chevron.b2b.model.ScheduleItemExample;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

/**
 * 计划事项-wx_t_schedule_item数据库操作对象
 * <AUTHOR>
 * @version 1.0 2018-12-18 16:56
 */
public interface ScheduleItemMapper {

	/**
	 * 根据主键更新计划事项对象(非空属性)
	 * @param record 新计划事项对象
	 * @return 数据库操作影响记录条数
	 */
    int updateByPrimaryKeySelective(ScheduleItem record);

    /**
     * 根据主键删除计划事项对象
     * @param id 删除计划事项对象主键
     * @return 数据库操作影响记录条数
     */
    int deleteByPrimaryKey(Long id);

	/**
	 * 根据主键查询计划事项对象
	 * @param id 计划事项对象主键值
	 * @return 计划事项对象结果
	 */
    ScheduleItem selectByPrimaryKey(Long id);

 	/**
	 * 插入计划事项对象(非空属性)
	 * @param record 被插入计划事项对象条件
	 * @return 数据库操作影响记录条数
	 */
    int insertSelective(ScheduleItem record);

	/**
	 * 更新计划事项对象
	 * @param record 被修改计划事项对象
	 * @param example 过滤计划事项对象条件
	 * @return 数据库操作影响记录条数
	 */
    int updateByExampleSelective(@Param("record") ScheduleItem record, @Param("example") ScheduleItemExample example);

	/**
	 * 删除计划事项对象
	 * @param example 被删除计划事项对象条件
	 * @return 数据库操作影响记录条数
	 */
    int deleteByExample(ScheduleItemExample example);
    
 	/**
	 * 查询满足条件的计划事项对象数量
	 * @param example 过滤计划事项对象条件
	 * @return 满足条件计划事项对象数量
	 */
    int countByExample(ScheduleItemExample example);

	/**
	 * 单表查询计划事项对象
	 * @param example 过滤计划事项对象条件
	 * @return 满足条件计划事项对象集合
	 */
    List<ScheduleItem> selectByExample(ScheduleItemExample example);

	/**
	 * 列表查询计划事项对象
	 * @param params 查询条件
	 * @return 满足条件计划事项对象集合
	 */
    List<ScheduleItem> queryByParams(Map<String, Object> params);

    /**
     * 批量插入计划事项对象
     * @param records 插入计划事项集合
     * @return 数据库操作影响记录条数
     */
	int insertBatch(@Param("records") List<ScheduleItem> records);
}
