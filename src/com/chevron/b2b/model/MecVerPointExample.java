package com.chevron.b2b.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 技师核销积分单表查询条件
 * <AUTHOR>
 * @version 1.0 2018-12-07 15:51
 */
public class MecVerPointExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public MecVerPointExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andAwardPolicyIdIsNull() {
            addCriterion("award_policy_id is null");
            return (Criteria) this;
        }

        public Criteria andAwardPolicyIdIsNotNull() {
            addCriterion("award_policy_id is not null");
            return (Criteria) this;
        }

        public Criteria andAwardPolicyIdEqualTo(Long value) {
            addCriterion("award_policy_id =", value, "awardPolicyId");
            return (Criteria) this;
        }

        public Criteria andAwardPolicyIdNotEqualTo(Long value) {
            addCriterion("award_policy_id <>", value, "awardPolicyId");
            return (Criteria) this;
        }

        public Criteria andAwardPolicyIdGreaterThan(Long value) {
            addCriterion("award_policy_id >", value, "awardPolicyId");
            return (Criteria) this;
        }

        public Criteria andAwardPolicyIdGreaterThanOrEqualTo(Long value) {
            addCriterion("award_policy_id >=", value, "awardPolicyId");
            return (Criteria) this;
        }

        public Criteria andAwardPolicyIdLessThan(Long value) {
            addCriterion("award_policy_id <", value, "awardPolicyId");
            return (Criteria) this;
        }

        public Criteria andAwardPolicyIdLessThanOrEqualTo(Long value) {
            addCriterion("award_policy_id <=", value, "awardPolicyId");
            return (Criteria) this;
        }

        public Criteria andAwardPolicyIdIn(List<Long> values) {
            addCriterion("award_policy_id in", values, "awardPolicyId");
            return (Criteria) this;
        }

        public Criteria andAwardPolicyIdNotIn(List<Long> values) {
            addCriterion("award_policy_id not in", values, "awardPolicyId");
            return (Criteria) this;
        }

        public Criteria andAwardPolicyIdBetween(Long value1, Long value2) {
            addCriterion("award_policy_id between", value1, value2, "awardPolicyId");
            return (Criteria) this;
        }

        public Criteria andAwardPolicyIdNotBetween(Long value1, Long value2) {
            addCriterion("award_policy_id not between", value1, value2, "awardPolicyId");
            return (Criteria) this;
        }

        public Criteria andQrcodeIsNull() {
            addCriterion("qrcode is null");
            return (Criteria) this;
        }

        public Criteria andQrcodeIsNotNull() {
            addCriterion("qrcode is not null");
            return (Criteria) this;
        }

        public Criteria andQrcodeEqualTo(String value) {
            addCriterion("qrcode =", value, "qrcode");
            return (Criteria) this;
        }

        public Criteria andQrcodeNotEqualTo(String value) {
            addCriterion("qrcode <>", value, "qrcode");
            return (Criteria) this;
        }

        public Criteria andQrcodeGreaterThan(String value) {
            addCriterion("qrcode >", value, "qrcode");
            return (Criteria) this;
        }

        public Criteria andQrcodeGreaterThanOrEqualTo(String value) {
            addCriterion("qrcode >=", value, "qrcode");
            return (Criteria) this;
        }

        public Criteria andQrcodeLessThan(String value) {
            addCriterion("qrcode <", value, "qrcode");
            return (Criteria) this;
        }

        public Criteria andQrcodeLessThanOrEqualTo(String value) {
            addCriterion("qrcode <=", value, "qrcode");
            return (Criteria) this;
        }

        public Criteria andQrcodeIn(List<String> values) {
            addCriterion("qrcode in", values, "qrcode");
            return (Criteria) this;
        }

        public Criteria andQrcodeNotIn(List<String> values) {
            addCriterion("qrcode not in", values, "qrcode");
            return (Criteria) this;
        }

        public Criteria andQrcodeBetween(String value1, String value2) {
            addCriterion("qrcode between", value1, value2, "qrcode");
            return (Criteria) this;
        }

        public Criteria andQrcodeNotBetween(String value1, String value2) {
            addCriterion("qrcode not between", value1, value2, "qrcode");
            return (Criteria) this;
        }

        public Criteria andDistributorIdIsNull() {
            addCriterion("distributor_id is null");
            return (Criteria) this;
        }

        public Criteria andDistributorIdIsNotNull() {
            addCriterion("distributor_id is not null");
            return (Criteria) this;
        }

        public Criteria andDistributorIdEqualTo(Long value) {
            addCriterion("distributor_id =", value, "distributorId");
            return (Criteria) this;
        }

        public Criteria andDistributorIdNotEqualTo(Long value) {
            addCriterion("distributor_id <>", value, "distributorId");
            return (Criteria) this;
        }

        public Criteria andDistributorIdGreaterThan(Long value) {
            addCriterion("distributor_id >", value, "distributorId");
            return (Criteria) this;
        }

        public Criteria andDistributorIdGreaterThanOrEqualTo(Long value) {
            addCriterion("distributor_id >=", value, "distributorId");
            return (Criteria) this;
        }

        public Criteria andDistributorIdLessThan(Long value) {
            addCriterion("distributor_id <", value, "distributorId");
            return (Criteria) this;
        }

        public Criteria andDistributorIdLessThanOrEqualTo(Long value) {
            addCriterion("distributor_id <=", value, "distributorId");
            return (Criteria) this;
        }

        public Criteria andDistributorIdIn(List<Long> values) {
            addCriterion("distributor_id in", values, "distributorId");
            return (Criteria) this;
        }

        public Criteria andDistributorIdNotIn(List<Long> values) {
            addCriterion("distributor_id not in", values, "distributorId");
            return (Criteria) this;
        }

        public Criteria andDistributorIdBetween(Long value1, Long value2) {
            addCriterion("distributor_id between", value1, value2, "distributorId");
            return (Criteria) this;
        }

        public Criteria andDistributorIdNotBetween(Long value1, Long value2) {
            addCriterion("distributor_id not between", value1, value2, "distributorId");
            return (Criteria) this;
        }

        public Criteria andWorkshopIdIsNull() {
            addCriterion("workshop_id is null");
            return (Criteria) this;
        }

        public Criteria andWorkshopIdIsNotNull() {
            addCriterion("workshop_id is not null");
            return (Criteria) this;
        }

        public Criteria andWorkshopIdEqualTo(Long value) {
            addCriterion("workshop_id =", value, "workshopId");
            return (Criteria) this;
        }

        public Criteria andWorkshopIdNotEqualTo(Long value) {
            addCriterion("workshop_id <>", value, "workshopId");
            return (Criteria) this;
        }

        public Criteria andWorkshopIdGreaterThan(Long value) {
            addCriterion("workshop_id >", value, "workshopId");
            return (Criteria) this;
        }

        public Criteria andWorkshopIdGreaterThanOrEqualTo(Long value) {
            addCriterion("workshop_id >=", value, "workshopId");
            return (Criteria) this;
        }

        public Criteria andWorkshopIdLessThan(Long value) {
            addCriterion("workshop_id <", value, "workshopId");
            return (Criteria) this;
        }

        public Criteria andWorkshopIdLessThanOrEqualTo(Long value) {
            addCriterion("workshop_id <=", value, "workshopId");
            return (Criteria) this;
        }

        public Criteria andWorkshopIdIn(List<Long> values) {
            addCriterion("workshop_id in", values, "workshopId");
            return (Criteria) this;
        }

        public Criteria andWorkshopIdNotIn(List<Long> values) {
            addCriterion("workshop_id not in", values, "workshopId");
            return (Criteria) this;
        }

        public Criteria andWorkshopIdBetween(Long value1, Long value2) {
            addCriterion("workshop_id between", value1, value2, "workshopId");
            return (Criteria) this;
        }

        public Criteria andWorkshopIdNotBetween(Long value1, Long value2) {
            addCriterion("workshop_id not between", value1, value2, "workshopId");
            return (Criteria) this;
        }

        public Criteria andMechanicCodeIsNull() {
            addCriterion("mechanic_code is null");
            return (Criteria) this;
        }

        public Criteria andMechanicCodeIsNotNull() {
            addCriterion("mechanic_code is not null");
            return (Criteria) this;
        }

        public Criteria andMechanicCodeEqualTo(String value) {
            addCriterion("mechanic_code =", value, "mechanicCode");
            return (Criteria) this;
        }

        public Criteria andMechanicCodeNotEqualTo(String value) {
            addCriterion("mechanic_code <>", value, "mechanicCode");
            return (Criteria) this;
        }

        public Criteria andMechanicCodeGreaterThan(String value) {
            addCriterion("mechanic_code >", value, "mechanicCode");
            return (Criteria) this;
        }

        public Criteria andMechanicCodeGreaterThanOrEqualTo(String value) {
            addCriterion("mechanic_code >=", value, "mechanicCode");
            return (Criteria) this;
        }

        public Criteria andMechanicCodeLessThan(String value) {
            addCriterion("mechanic_code <", value, "mechanicCode");
            return (Criteria) this;
        }

        public Criteria andMechanicCodeLessThanOrEqualTo(String value) {
            addCriterion("mechanic_code <=", value, "mechanicCode");
            return (Criteria) this;
        }

        public Criteria andMechanicCodeIn(List<String> values) {
            addCriterion("mechanic_code in", values, "mechanicCode");
            return (Criteria) this;
        }

        public Criteria andMechanicCodeNotIn(List<String> values) {
            addCriterion("mechanic_code not in", values, "mechanicCode");
            return (Criteria) this;
        }

        public Criteria andMechanicCodeBetween(String value1, String value2) {
            addCriterion("mechanic_code between", value1, value2, "mechanicCode");
            return (Criteria) this;
        }

        public Criteria andMechanicCodeNotBetween(String value1, String value2) {
            addCriterion("mechanic_code not between", value1, value2, "mechanicCode");
            return (Criteria) this;
        }

        public Criteria andSalesChannelIsNull() {
            addCriterion("sales_channel is null");
            return (Criteria) this;
        }

        public Criteria andSalesChannelIsNotNull() {
            addCriterion("sales_channel is not null");
            return (Criteria) this;
        }

        public Criteria andSalesChannelEqualTo(String value) {
            addCriterion("sales_channel =", value, "salesChannel");
            return (Criteria) this;
        }

        public Criteria andSalesChannelNotEqualTo(String value) {
            addCriterion("sales_channel <>", value, "salesChannel");
            return (Criteria) this;
        }

        public Criteria andSalesChannelGreaterThan(String value) {
            addCriterion("sales_channel >", value, "salesChannel");
            return (Criteria) this;
        }

        public Criteria andSalesChannelGreaterThanOrEqualTo(String value) {
            addCriterion("sales_channel >=", value, "salesChannel");
            return (Criteria) this;
        }

        public Criteria andSalesChannelLessThan(String value) {
            addCriterion("sales_channel <", value, "salesChannel");
            return (Criteria) this;
        }

        public Criteria andSalesChannelLessThanOrEqualTo(String value) {
            addCriterion("sales_channel <=", value, "salesChannel");
            return (Criteria) this;
        }

        public Criteria andSalesChannelIn(List<String> values) {
            addCriterion("sales_channel in", values, "salesChannel");
            return (Criteria) this;
        }

        public Criteria andSalesChannelNotIn(List<String> values) {
            addCriterion("sales_channel not in", values, "salesChannel");
            return (Criteria) this;
        }

        public Criteria andSalesChannelBetween(String value1, String value2) {
            addCriterion("sales_channel between", value1, value2, "salesChannel");
            return (Criteria) this;
        }

        public Criteria andSalesChannelNotBetween(String value1, String value2) {
            addCriterion("sales_channel not between", value1, value2, "salesChannel");
            return (Criteria) this;
        }

        public Criteria andSkuIsNull() {
            addCriterion("sku is null");
            return (Criteria) this;
        }

        public Criteria andSkuIsNotNull() {
            addCriterion("sku is not null");
            return (Criteria) this;
        }

        public Criteria andSkuEqualTo(String value) {
            addCriterion("sku =", value, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuNotEqualTo(String value) {
            addCriterion("sku <>", value, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuGreaterThan(String value) {
            addCriterion("sku >", value, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuGreaterThanOrEqualTo(String value) {
            addCriterion("sku >=", value, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuLessThan(String value) {
            addCriterion("sku <", value, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuLessThanOrEqualTo(String value) {
            addCriterion("sku <=", value, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuIn(List<String> values) {
            addCriterion("sku in", values, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuNotIn(List<String> values) {
            addCriterion("sku not in", values, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuBetween(String value1, String value2) {
            addCriterion("sku between", value1, value2, "sku");
            return (Criteria) this;
        }

        public Criteria andSkuNotBetween(String value1, String value2) {
            addCriterion("sku not between", value1, value2, "sku");
            return (Criteria) this;
        }

        public Criteria andLiterIsNull() {
            addCriterion("liter is null");
            return (Criteria) this;
        }

        public Criteria andLiterIsNotNull() {
            addCriterion("liter is not null");
            return (Criteria) this;
        }

        public Criteria andLiterEqualTo(Double value) {
            addCriterion("liter =", value, "liter");
            return (Criteria) this;
        }

        public Criteria andLiterNotEqualTo(Double value) {
            addCriterion("liter <>", value, "liter");
            return (Criteria) this;
        }

        public Criteria andLiterGreaterThan(Double value) {
            addCriterion("liter >", value, "liter");
            return (Criteria) this;
        }

        public Criteria andLiterGreaterThanOrEqualTo(Double value) {
            addCriterion("liter >=", value, "liter");
            return (Criteria) this;
        }

        public Criteria andLiterLessThan(Double value) {
            addCriterion("liter <", value, "liter");
            return (Criteria) this;
        }

        public Criteria andLiterLessThanOrEqualTo(Double value) {
            addCriterion("liter <=", value, "liter");
            return (Criteria) this;
        }

        public Criteria andLiterIn(List<Double> values) {
            addCriterion("liter in", values, "liter");
            return (Criteria) this;
        }

        public Criteria andLiterNotIn(List<Double> values) {
            addCriterion("liter not in", values, "liter");
            return (Criteria) this;
        }

        public Criteria andLiterBetween(Double value1, Double value2) {
            addCriterion("liter between", value1, value2, "liter");
            return (Criteria) this;
        }

        public Criteria andLiterNotBetween(Double value1, Double value2) {
            addCriterion("liter not between", value1, value2, "liter");
            return (Criteria) this;
        }

        public Criteria andAwardPointIsNull() {
            addCriterion("award_point is null");
            return (Criteria) this;
        }

        public Criteria andAwardPointIsNotNull() {
            addCriterion("award_point is not null");
            return (Criteria) this;
        }

        public Criteria andAwardPointEqualTo(Double value) {
            addCriterion("award_point =", value, "awardPoint");
            return (Criteria) this;
        }

        public Criteria andAwardPointNotEqualTo(Double value) {
            addCriterion("award_point <>", value, "awardPoint");
            return (Criteria) this;
        }

        public Criteria andAwardPointGreaterThan(Double value) {
            addCriterion("award_point >", value, "awardPoint");
            return (Criteria) this;
        }

        public Criteria andAwardPointGreaterThanOrEqualTo(Double value) {
            addCriterion("award_point >=", value, "awardPoint");
            return (Criteria) this;
        }

        public Criteria andAwardPointLessThan(Double value) {
            addCriterion("award_point <", value, "awardPoint");
            return (Criteria) this;
        }

        public Criteria andAwardPointLessThanOrEqualTo(Double value) {
            addCriterion("award_point <=", value, "awardPoint");
            return (Criteria) this;
        }

        public Criteria andAwardPointIn(List<Double> values) {
            addCriterion("award_point in", values, "awardPoint");
            return (Criteria) this;
        }

        public Criteria andAwardPointNotIn(List<Double> values) {
            addCriterion("award_point not in", values, "awardPoint");
            return (Criteria) this;
        }

        public Criteria andAwardPointBetween(Double value1, Double value2) {
            addCriterion("award_point between", value1, value2, "awardPoint");
            return (Criteria) this;
        }

        public Criteria andAwardPointNotBetween(Double value1, Double value2) {
            addCriterion("award_point not between", value1, value2, "awardPoint");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andUnfreezeTimeIsNull() {
            addCriterion("unfreeze_time is null");
            return (Criteria) this;
        }

        public Criteria andUnfreezeTimeIsNotNull() {
            addCriterion("unfreeze_time is not null");
            return (Criteria) this;
        }

        public Criteria andUnfreezeTimeEqualTo(Date value) {
            addCriterion("unfreeze_time =", value, "unfreezeTime");
            return (Criteria) this;
        }

        public Criteria andUnfreezeTimeNotEqualTo(Date value) {
            addCriterion("unfreeze_time <>", value, "unfreezeTime");
            return (Criteria) this;
        }

        public Criteria andUnfreezeTimeGreaterThan(Date value) {
            addCriterion("unfreeze_time >", value, "unfreezeTime");
            return (Criteria) this;
        }

        public Criteria andUnfreezeTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("unfreeze_time >=", value, "unfreezeTime");
            return (Criteria) this;
        }

        public Criteria andUnfreezeTimeLessThan(Date value) {
            addCriterion("unfreeze_time <", value, "unfreezeTime");
            return (Criteria) this;
        }

        public Criteria andUnfreezeTimeLessThanOrEqualTo(Date value) {
            addCriterion("unfreeze_time <=", value, "unfreezeTime");
            return (Criteria) this;
        }

        public Criteria andUnfreezeTimeIn(List<Date> values) {
            addCriterion("unfreeze_time in", values, "unfreezeTime");
            return (Criteria) this;
        }

        public Criteria andUnfreezeTimeNotIn(List<Date> values) {
            addCriterion("unfreeze_time not in", values, "unfreezeTime");
            return (Criteria) this;
        }

        public Criteria andUnfreezeTimeBetween(Date value1, Date value2) {
            addCriterion("unfreeze_time between", value1, value2, "unfreezeTime");
            return (Criteria) this;
        }

        public Criteria andUnfreezeTimeNotBetween(Date value1, Date value2) {
            addCriterion("unfreeze_time not between", value1, value2, "unfreezeTime");
            return (Criteria) this;
        }

        public Criteria andUnfreezeUserIsNull() {
            addCriterion("unfreeze_user is null");
            return (Criteria) this;
        }

        public Criteria andUnfreezeUserIsNotNull() {
            addCriterion("unfreeze_user is not null");
            return (Criteria) this;
        }

        public Criteria andUnfreezeUserEqualTo(Long value) {
            addCriterion("unfreeze_user =", value, "unfreezeUser");
            return (Criteria) this;
        }

        public Criteria andUnfreezeUserNotEqualTo(Long value) {
            addCriterion("unfreeze_user <>", value, "unfreezeUser");
            return (Criteria) this;
        }

        public Criteria andUnfreezeUserGreaterThan(Long value) {
            addCriterion("unfreeze_user >", value, "unfreezeUser");
            return (Criteria) this;
        }

        public Criteria andUnfreezeUserGreaterThanOrEqualTo(Long value) {
            addCriterion("unfreeze_user >=", value, "unfreezeUser");
            return (Criteria) this;
        }

        public Criteria andUnfreezeUserLessThan(Long value) {
            addCriterion("unfreeze_user <", value, "unfreezeUser");
            return (Criteria) this;
        }

        public Criteria andUnfreezeUserLessThanOrEqualTo(Long value) {
            addCriterion("unfreeze_user <=", value, "unfreezeUser");
            return (Criteria) this;
        }

        public Criteria andUnfreezeUserIn(List<Long> values) {
            addCriterion("unfreeze_user in", values, "unfreezeUser");
            return (Criteria) this;
        }

        public Criteria andUnfreezeUserNotIn(List<Long> values) {
            addCriterion("unfreeze_user not in", values, "unfreezeUser");
            return (Criteria) this;
        }

        public Criteria andUnfreezeUserBetween(Long value1, Long value2) {
            addCriterion("unfreeze_user between", value1, value2, "unfreezeUser");
            return (Criteria) this;
        }

        public Criteria andUnfreezeUserNotBetween(Long value1, Long value2) {
            addCriterion("unfreeze_user not between", value1, value2, "unfreezeUser");
            return (Criteria) this;
        }

        public Criteria andBatchNoIsNull() {
            addCriterion("batch_no is null");
            return (Criteria) this;
        }

        public Criteria andBatchNoIsNotNull() {
            addCriterion("batch_no is not null");
            return (Criteria) this;
        }

        public Criteria andBatchNoEqualTo(String value) {
            addCriterion("batch_no =", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoNotEqualTo(String value) {
            addCriterion("batch_no <>", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoGreaterThan(String value) {
            addCriterion("batch_no >", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoGreaterThanOrEqualTo(String value) {
            addCriterion("batch_no >=", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoLessThan(String value) {
            addCriterion("batch_no <", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoLessThanOrEqualTo(String value) {
            addCriterion("batch_no <=", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoIn(List<String> values) {
            addCriterion("batch_no in", values, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoNotIn(List<String> values) {
            addCriterion("batch_no not in", values, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoBetween(String value1, String value2) {
            addCriterion("batch_no between", value1, value2, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoNotBetween(String value1, String value2) {
            addCriterion("batch_no not between", value1, value2, "batchNo");
            return (Criteria) this;
        }

        public Criteria andExtAttr1IsNull() {
            addCriterion("ext_attr1 is null");
            return (Criteria) this;
        }

        public Criteria andExtAttr1IsNotNull() {
            addCriterion("ext_attr1 is not null");
            return (Criteria) this;
        }

        public Criteria andExtAttr1EqualTo(String value) {
            addCriterion("ext_attr1 =", value, "extAttr1");
            return (Criteria) this;
        }

        public Criteria andExtAttr1NotEqualTo(String value) {
            addCriterion("ext_attr1 <>", value, "extAttr1");
            return (Criteria) this;
        }

        public Criteria andExtAttr1GreaterThan(String value) {
            addCriterion("ext_attr1 >", value, "extAttr1");
            return (Criteria) this;
        }

        public Criteria andExtAttr1GreaterThanOrEqualTo(String value) {
            addCriterion("ext_attr1 >=", value, "extAttr1");
            return (Criteria) this;
        }

        public Criteria andExtAttr1LessThan(String value) {
            addCriterion("ext_attr1 <", value, "extAttr1");
            return (Criteria) this;
        }

        public Criteria andExtAttr1LessThanOrEqualTo(String value) {
            addCriterion("ext_attr1 <=", value, "extAttr1");
            return (Criteria) this;
        }

        public Criteria andExtAttr1In(List<String> values) {
            addCriterion("ext_attr1 in", values, "extAttr1");
            return (Criteria) this;
        }

        public Criteria andExtAttr1NotIn(List<String> values) {
            addCriterion("ext_attr1 not in", values, "extAttr1");
            return (Criteria) this;
        }

        public Criteria andExtAttr1Between(String value1, String value2) {
            addCriterion("ext_attr1 between", value1, value2, "extAttr1");
            return (Criteria) this;
        }

        public Criteria andExtAttr1NotBetween(String value1, String value2) {
            addCriterion("ext_attr1 not between", value1, value2, "extAttr1");
            return (Criteria) this;
        }

        public Criteria andExtAttr2IsNull() {
            addCriterion("ext_attr2 is null");
            return (Criteria) this;
        }

        public Criteria andExtAttr2IsNotNull() {
            addCriterion("ext_attr2 is not null");
            return (Criteria) this;
        }

        public Criteria andExtAttr2EqualTo(String value) {
            addCriterion("ext_attr2 =", value, "extAttr2");
            return (Criteria) this;
        }

        public Criteria andExtAttr2NotEqualTo(String value) {
            addCriterion("ext_attr2 <>", value, "extAttr2");
            return (Criteria) this;
        }

        public Criteria andExtAttr2GreaterThan(String value) {
            addCriterion("ext_attr2 >", value, "extAttr2");
            return (Criteria) this;
        }

        public Criteria andExtAttr2GreaterThanOrEqualTo(String value) {
            addCriterion("ext_attr2 >=", value, "extAttr2");
            return (Criteria) this;
        }

        public Criteria andExtAttr2LessThan(String value) {
            addCriterion("ext_attr2 <", value, "extAttr2");
            return (Criteria) this;
        }

        public Criteria andExtAttr2LessThanOrEqualTo(String value) {
            addCriterion("ext_attr2 <=", value, "extAttr2");
            return (Criteria) this;
        }

        public Criteria andExtAttr2In(List<String> values) {
            addCriterion("ext_attr2 in", values, "extAttr2");
            return (Criteria) this;
        }

        public Criteria andExtAttr2NotIn(List<String> values) {
            addCriterion("ext_attr2 not in", values, "extAttr2");
            return (Criteria) this;
        }

        public Criteria andExtAttr2Between(String value1, String value2) {
            addCriterion("ext_attr2 between", value1, value2, "extAttr2");
            return (Criteria) this;
        }

        public Criteria andExtAttr2NotBetween(String value1, String value2) {
            addCriterion("ext_attr2 not between", value1, value2, "extAttr2");
            return (Criteria) this;
        }

        public Criteria andExtAttr3IsNull() {
            addCriterion("ext_attr3 is null");
            return (Criteria) this;
        }

        public Criteria andExtAttr3IsNotNull() {
            addCriterion("ext_attr3 is not null");
            return (Criteria) this;
        }

        public Criteria andExtAttr3EqualTo(String value) {
            addCriterion("ext_attr3 =", value, "extAttr3");
            return (Criteria) this;
        }

        public Criteria andExtAttr3NotEqualTo(String value) {
            addCriterion("ext_attr3 <>", value, "extAttr3");
            return (Criteria) this;
        }

        public Criteria andExtAttr3GreaterThan(String value) {
            addCriterion("ext_attr3 >", value, "extAttr3");
            return (Criteria) this;
        }

        public Criteria andExtAttr3GreaterThanOrEqualTo(String value) {
            addCriterion("ext_attr3 >=", value, "extAttr3");
            return (Criteria) this;
        }

        public Criteria andExtAttr3LessThan(String value) {
            addCriterion("ext_attr3 <", value, "extAttr3");
            return (Criteria) this;
        }

        public Criteria andExtAttr3LessThanOrEqualTo(String value) {
            addCriterion("ext_attr3 <=", value, "extAttr3");
            return (Criteria) this;
        }

        public Criteria andExtAttr3In(List<String> values) {
            addCriterion("ext_attr3 in", values, "extAttr3");
            return (Criteria) this;
        }

        public Criteria andExtAttr3NotIn(List<String> values) {
            addCriterion("ext_attr3 not in", values, "extAttr3");
            return (Criteria) this;
        }

        public Criteria andExtAttr3Between(String value1, String value2) {
            addCriterion("ext_attr3 between", value1, value2, "extAttr3");
            return (Criteria) this;
        }

        public Criteria andExtAttr3NotBetween(String value1, String value2) {
            addCriterion("ext_attr3 not between", value1, value2, "extAttr3");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIsNull() {
            addCriterion("create_user_id is null");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIsNotNull() {
            addCriterion("create_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdEqualTo(Long value) {
            addCriterion("create_user_id =", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotEqualTo(Long value) {
            addCriterion("create_user_id <>", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdGreaterThan(Long value) {
            addCriterion("create_user_id >", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdGreaterThanOrEqualTo(Long value) {
            addCriterion("create_user_id >=", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLessThan(Long value) {
            addCriterion("create_user_id <", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLessThanOrEqualTo(Long value) {
            addCriterion("create_user_id <=", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIn(List<Long> values) {
            addCriterion("create_user_id in", values, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotIn(List<Long> values) {
            addCriterion("create_user_id not in", values, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdBetween(Long value1, Long value2) {
            addCriterion("create_user_id between", value1, value2, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotBetween(Long value1, Long value2) {
            addCriterion("create_user_id not between", value1, value2, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdIsNull() {
            addCriterion("update_user_id is null");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdIsNotNull() {
            addCriterion("update_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdEqualTo(Long value) {
            addCriterion("update_user_id =", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdNotEqualTo(Long value) {
            addCriterion("update_user_id <>", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdGreaterThan(Long value) {
            addCriterion("update_user_id >", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdGreaterThanOrEqualTo(Long value) {
            addCriterion("update_user_id >=", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdLessThan(Long value) {
            addCriterion("update_user_id <", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdLessThanOrEqualTo(Long value) {
            addCriterion("update_user_id <=", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdIn(List<Long> values) {
            addCriterion("update_user_id in", values, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdNotIn(List<Long> values) {
            addCriterion("update_user_id not in", values, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdBetween(Long value1, Long value2) {
            addCriterion("update_user_id between", value1, value2, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdNotBetween(Long value1, Long value2) {
            addCriterion("update_user_id not between", value1, value2, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}
