package com.chevron.pms.service.impl;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.chevron.pms.model.TaskParams;
import com.chevron.pms.service.PmsTaskService;
import com.chevron.task.dao.WxTaskMainMapper;
import com.common.constants.Constants;
import com.common.exception.WxPltException;
import com.common.util.ContextUtil;
import com.common.util.MessageResourceUtil;
import com.sys.auth.model.WxTUser;

@Service
public class PmsTaskServiceImpl implements PmsTaskService {

    private static Logger log = LoggerFactory.getLogger(PmsTaskServiceImpl.class);
    
	@Resource
	private WxTaskMainMapper wxTaskMainMapper;
   
	@Override
	public Map<String, Object> queryTaskForPage(TaskParams params) {
		Map<String,Object> resultMap = new HashMap<String,Object>(5);
		try {
			if(params.getWorkshopId() == null){
				throw new WxPltException("queryTaskForPage接口只能用于门店任务查询，请传入门店ID");
			}
			//组装登录用户信息参数
			WxTUser user = ContextUtil.getCurUser();
			params.setLoginUserType(WxTUser.USER_MODEL_CHEVRON.equals(user.getUserModel()) ? 1l : 0l);
			params.setLoginOrg(user.getOrgId());
			params.setLoginOrgType(user.getType());
			
			resultMap.put(Constants.RESULT_LST_KEY, wxTaskMainMapper.selTasksByWorkshop(params));
			resultMap.put(Constants.RESULT_TOTAL_KEY, params.getTotalCount());
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (Exception e) {
			e.printStackTrace();
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			//add by bo.liu 180111
			resultMap.put(Constants.RESULT_CODE_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error(e.getMessage(), e);
		}
		return resultMap;
	}

	@Override
	public Map<String, Object> queryTaskForPageByFleet(TaskParams params) {
		Map<String,Object> resultMap = new HashMap<String,Object>(5);
		try {
			if(params.getFleetId() == null){
				throw new WxPltException("queryTaskForPageByFleet接口只能用车队任务查询，请传入车队ID");
			}
			//组装登录用户信息参数
			WxTUser user = ContextUtil.getCurUser();
			params.setLoginUserType(WxTUser.USER_MODEL_CHEVRON.equals(user.getUserModel()) ? 1l : 0l);
			params.setLoginOrg(user.getOrgId());
			params.setLoginOrgType(user.getType());
			
			resultMap.put(Constants.RESULT_LST_KEY, wxTaskMainMapper.selTasksByFleet(params));
			resultMap.put(Constants.RESULT_TOTAL_KEY, params.getTotalCount());
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (Exception e) {
			e.printStackTrace();
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			//add by bo.liu 180111
			resultMap.put(Constants.RESULT_CODE_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error(e.getMessage(), e);
		}
		return resultMap;
	}

	@Override
	public Map<String, Object> queryTaskForPageByMachinery(TaskParams params) {
		Map<String,Object> resultMap = new HashMap<String,Object>(5);
		try {
			if(params.getMachineryId() == null){
				throw new WxPltException("queryTaskForPageByMachinery接口只能用于工程机械任务查询，请传入工程机械ID");
			}
			//组装登录用户信息参数
			WxTUser user = ContextUtil.getCurUser();
			params.setLoginUserType(WxTUser.USER_MODEL_CHEVRON.equals(user.getUserModel()) ? 1l : 0l);
			params.setLoginOrg(user.getOrgId());
			params.setLoginOrgType(user.getType());
			
			resultMap.put(Constants.RESULT_LST_KEY, wxTaskMainMapper.selTasksByMachinery(params));
			resultMap.put(Constants.RESULT_TOTAL_KEY, params.getTotalCount());
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (Exception e) {
			e.printStackTrace();
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			//add by bo.liu 180111
			resultMap.put(Constants.RESULT_CODE_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error(e.getMessage(), e);
		}
		return resultMap;
	}

}
