<!DOCTYPE html>
<html>
<head>
    <meta content="text/html; charset=utf-8" http-equiv="Content-Type">
    <meta charset="utf-8" http-equiv="content-type">
    <title>VisionIM首页</title>
    <meta name="Keywords" content="Vision,VisionIM">
	<meta name="Description" content="Vision,VisionIM">
	
    
	<link href="../common/css/common.css?v=20180703" rel="stylesheet">
	<link href="ui.css" rel="stylesheet">
	<script language="javascript"  src="../common/js/jquery-1.9.1.min.js"></script>
	<script language="javascript"  src="../common/js/common.js"></script>
	<script language="javascript"  src="ui.js"></script>
	<style type="text/css">
div.ui-tree-grid .ui-grid-locked .ui-grid-table tr td.ui-grid-part-last-td {
    border-right: none;
}	
	</style>

<script type="text/javascript">
var isByPack = true, startYear = 2018, endYear = 2018, startMonth = 1, endMonth = 12, headerChildren = [{
	title: '', 
	property: 'type', 
	fixedWidth: true,
	width: 100, 
	tdCls: 'col-title',
	formatter: function(value, record, index, recordId){
		if(value == 'qntq'){
			return '去年同期';
		}else if(value == 'actual'){
			return '实际销量';
		}else if(value == 'forecast'){
			return '预测';
		}
		return '';
	},
	propertyProcessor: function(record, propertyName){
		if(record.partnerName){
			return record.type;
		}
		return '';
	} 
}];
var currentYear = startYear, currentMonth = startMonth;
for(var i = 0; i < 6; i++){
	if(currentMonth == 13){
		currentMonth = 1;
		currentYear += 1;
	}
	headerChildren.push({
		title: currentYear + '年' + currentMonth + '月', 
		property: 'forecast' + currentMonth, 
		fixedWidth: true,
		width: 100, 
		summary: true,
		m: currentMonth, 
		formatter: function(value, record, index, recordId){
			if(isByPack){
				return common.formatNumber(value, 0);
			}
			return common.formatNumber(parseFloat(value), 6);
		},
		propertyProcessor: function(record, propertyName){
			var value;
			if(record.partnerName){
				value = record[record.type + this.m];
			}else{
				value = record[propertyName];
			}
			if(isByPack && record.pack){
				return Math.round(value/record.pack);
			}else{
				return value;
			}
		} 
	});
	currentMonth++;
}
headerChildren.push({
	title: '', 
	property: 'forecast1', 
	fixedWidth: true,
	width: 100, 
	tdCls: 'col-title',
	propertyProcessor: function(record, propertyName){
		if(record.partnerName){
			if(record.type == 'qntq'){
				return '去年同期';
			}else if(record.type == 'actual'){
				return '近12月平均';
			}else if(record.type == 'forecast'){
				return '<a href="javascript: void(0);" onclick="">预测</a>';
			}
		}
		return '';
	} 
});
for(var i = 0; i < 6; i++){
	if(currentMonth == 13){
		currentMonth = 1;
		currentYear += 1;
	}
	headerChildren.push({
		title: currentYear + '年' + currentMonth + '月', 
		property: 'forecast' + currentMonth, 
		fixedWidth: true,
		summary: true,
		width: 100, 
		m: currentMonth, 
		formatter: function(value, record, index, recordId){
			if(!record.partnerName || record.type == 'forecast'){
				return '<input type="text" class="control-text" style="width: 76px;" value="' + parseFloat(value) + '" onchange=""/>';
			}
			return common.formatNumber(parseFloat(value), 6);
		},
		propertyProcessor: function(record, propertyName){
			var value;
			if(record.partnerName){
				if(record.type == 'actual'){
					value = record.avg12
				}
				value = record[record.type + this.m];
			}else{
				value = record[propertyName];
			}
			if(isByPack && record.pack){
				return Math.round(value/record.pack);
			}else{
				return value;
			}
		} 
	});
	currentMonth++;
}
var headers = [{
	title: 'SKU', 
	property: 'sku', 
	rowMergeable: true, 
	width: 130, 
	propertyProcessor: function(record, propertyName){
		if(record.partnerName){
			return record.partnerName;
		}
		return record[propertyName];
	} 
},{
	title: '产品名称', 
	property: 'productName', 
	rowMergeable: true, 
	colMergeable: true, 
	endLock: true,
	width: 200, 
	propertyProcessor: function(record, propertyName){
		if(record.partnerName){
			return '<merge/>';
		}
		return record[propertyName];
	} 
},{
	title: '包装单位', 
	property: 'pack', 
	rowMergeable: true, 
	colMergeable: true, 
	fixedWidth: true,
	width: 80, 
	propertyProcessor: function(record, propertyName){
		if(record.partnerName){
			return '<merge/>';
		}
		return record[propertyName];
	} 
},{
	title: 'ABC', 
	property: 'gradeAbcText', 
	rowMergeable: true, 
	colMergeable: true, 
	fixedWidth: true,
	width: 60, 
	propertyProcessor: function(record, propertyName){
		if(record.partnerName){
			return '<merge/>';
		}
		return record[propertyName];
	} 
},{
	title: '总量', 
	property: 'total', 
	valueProperty: 'sku',
	endLock: false, 
	rowMergeable: true, 
	fixedWidth: true,
	summary: true,
	width: 80, 
	formatter: function(value, record, index, recordId){
		if(isByPack){
			return common.formatNumber(value, 0);
		}
		return common.formatNumber(parseFloat(value), 6);
	},
	propertyProcessor: function(record, propertyName){
		if(propertyName == 'sku'){
			if(record.partnerName){
				return record.partnerName;
			}
			return record.sku;
		}
		var total = 0.0;
		for(var i = 1; i < 13; i++){
			if(!isNaN(record['forecast' + i])){
				total += record['forecast' + i];
			}
		}
		if(isByPack && record.pack){
			return Math.round(total/record.pack);
		}
		return total;
	} 
}];
var data = [{
	sku: '570167NJB',
	productName: 'AAM差速齿轮油',
	pack: 12,
	gradeAbcText: 'A',
	forecast1: 500,
	forecast2: 500,
	forecast3: 500,
	forecast4: 500,
	forecast5: 500,
	forecast6: 500,
	forecast7: 500,
	forecast8: 500,
	forecast9: 500,
	forecast10: 500,
	forecast11: 500,
	forecast12: 500,
	expand: false,
	children: [{
		partnerName: '昆明轱辘轮胎有限公司',
		type: 'qntq',
		pack: 12,
		forecast1: 500,
		forecast2: 500,
		forecast3: 500,
		forecast4: 500,
		forecast5: 500,
		forecast6: 500,
		forecast7: 500,
		forecast8: 500,
		forecast9: 500,
		forecast10: 500,
		forecast11: 500,
		forecast12: 500,
		avg12: 500,
		qntq1: 300,
		qntq2: 300,
		qntq3: 300,
		qntq4: 300,
		qntq5: 300,
		qntq6: 300,
		qntq7: 300,
		qntq8: 300,
		qntq9: 300,
		qntq10: 300,
		qntq11: 300,
		qntq12: 300
	},{
		partnerName: '昆明轱辘轮胎有限公司',
		type: 'actual',
		pack: 12,
		forecast1: 500,
		forecast2: 500,
		forecast3: 500,
		forecast4: 500,
		forecast5: 500,
		forecast6: 500,
		forecast7: 500,
		forecast8: 500,
		forecast9: 500,
		forecast10: 500,
		forecast11: 500,
		forecast12: 500,
		avg12: 500,
		qntq1: 300,
		qntq2: 300,
		qntq3: 300,
		qntq4: 300,
		qntq5: 300,
		qntq6: 300,
		qntq7: 300,
		qntq8: 300,
		qntq9: 300,
		qntq10: 300,
		qntq11: 300,
		qntq12: 300
	},{
		partnerName: '昆明轱辘轮胎有限公司',
		type: 'forecast',
		pack: 12,
		forecast1: 500,
		forecast2: 500,
		forecast3: 500,
		forecast4: 500,
		forecast5: 500,
		forecast6: 500,
		forecast7: 500,
		forecast8: 500,
		forecast9: 500,
		forecast10: 500,
		forecast11: 500,
		forecast12: 500,
		avg12: 500,
		qntq1: 300,
		qntq2: 300,
		qntq3: 300,
		qntq4: 300,
		qntq5: 300,
		qntq6: 300,
		qntq7: 300,
		qntq8: 300,
		qntq9: 300,
		qntq10: 300,
		qntq11: 300,
		qntq12: 300
	}]
}];
$(document).ready(function(){
	
	var grid = UI.TreeGrid.init('#grid', {
		headers: headers,
		summary: true,
		sumRender: 'header',
		summaryTitle: '产品总预测量',
		cellPaddingY: 5,
		yOverflow: 'auto',
		data: data
	});
	$('.ui-layout').each(function(){
		UI.Layout.init(this, {
			minWidth: 850,
			minHeight: 300
		});
	});
	$(window).resize(function(){
		var $grid = $('#grid').addClass('hide'), $parent = $grid.parent();
		setTimeout(function(){
			var width = UI.getInnerWidth($parent);
			$grid.removeClass('hide');
			grid.resize(width, -1);
		}, 1);
	});
	$(window).resize();
});
</script>

</head>
<body class="gray-bg">
	<div class="content-wrapper ui-layout">
    	<div class="content-panel header-panel ui-layout-north">
    		<div class="header-title">编辑消息</div>
    		<div class="header-btns">
					<button type="button" class="btn-submit" onclick="publish();">发布</button>
					<button type="button" class="btn-submit" onclick="saveTemp();">暂存</button>
					<button type="button" class="btn-back" onclick="window.location = '${ctx }sys/push/mainMessagePage.jsp?cacheParams=true&toItem=${data.id}'">返回</button>
    		</div>
    	</div>
		<div class="content-panel ui-layout-center">
			<div id="grid"></div>
		</div>
	</div>
</body>
</html>