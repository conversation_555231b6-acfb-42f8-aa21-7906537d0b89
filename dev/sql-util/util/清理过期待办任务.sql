select --count(1)
t_sub.* --into wx_task_sub2022_1
--delete t_sub
FROM wx_task_sub t_sub
INNER JOIN wx_task_main tt_main ON tt_main.task_main_id = t_sub.task_main_id
where t_sub.task_status=1 and tt_main.create_time<'2022-01-01'

select --count(1)
tt_main.* into wx_task_main2022_1
--delete tt_main
FROM wx_task_main tt_main --ON tt_main.task_main_id = t_sub.task_main_id
where --t_sub.task_status=1 and tt_main.create_time<'2022-01-01'
not exists (select 1 from wx_task_sub t_sub where tt_main.task_main_id = t_sub.task_main_id)