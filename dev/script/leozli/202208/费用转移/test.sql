delete cso1 from dw_access_control_customer_org_sales cso1 where from_source='BI'
insert into dw_access_control_customer_org_sales ([distributor_id]
      ,[customer_name_cn]
      ,[org_hier_id]
      ,[region]
      ,[sales_cai]
      ,[sales_name_cn]
      ,[customer_category]
      ,[customer_type]
      ,[create_time]
      ,[update_time]
      ,[del_flag]
      ,[control_type]
      ,[control_type_name]
      ,[product_channel]
      ,[channel_weight]
      ,[from_source])
 select a.* from (
	  SELECT cs.[distributor_id]
      ,cs.[customer_name_cn]
      ,cs.[org_hier_id]
      ,cs.[region]
      ,cs.[sales_cai]
      ,cs.[sales_name_cn]
      ,cs.[customer_category]
      ,cs.[customer_type]
      ,cs.[create_time]
      ,cs.[update_time]
      ,cs.[del_flag]
      ,cs.[control_type]
      ,cs.[control_type_name]
      ,cs.[product_channel]
	  ,case when so.bu='Indirect' then (case when cs.[customer_category]='Consumer' 
			then 1 when cs.[customer_category]='Commercial' then 2 else 0 end) 
			when so.sales_channel='OEM' then 4 
			when so.sales_channel='Industrial' then 8
			else null end channel_weight
	  ,'BI' from_source
  FROM [PP_MID].[dbo].[syn_dw_to_pp_access_control_customer_org_sales] cs
  left join [PP_MID].[dbo].[syn_dw_to_pp_dim_sales_org_hier] so on cs.org_hier_id=so.org_hier_id
 ) a
  where not exists (select 1 from dw_access_control_customer_org_sales dac1 where dac1.from_source='PP' and a.region=dac1.region 
	and a.distributor_id=dac1.distributor_id and (a.channel_weight is null or a.channel_weight & dac1.channel_weight>0))

delete cso1 from dw_customer_org_sales cso1 where from_source='BI'
insert into dw_customer_org_sales ([distributor_id]
      ,[customer_name_cn]
      ,[org_hier_id]
      ,[region]
      ,[product_channel]
      ,[sales_cai]
      ,[sales_name_cn]
      ,[supervisor_cai]
      ,[supervisor_name_cn]
      ,[effective_from_date]
      ,[create_time]
      ,[update_time]
      ,[del_flag]
      ,[customer_type]
      ,[from_source]
	  ,channel_weight)
 select a.[distributor_id]
      ,a.[customer_name_cn]
      ,a.[org_hier_id]
      ,a.[region]
      ,a.[product_channel]
      ,a.[sales_cai]
      ,a.[sales_name_cn]
      ,a.[supervisor_cai]
      ,a.[supervisor_name_cn]
      ,a.[effective_from_date]
      ,a.[create_time]
      ,a.[update_time]
      ,a.[del_flag]
      ,a.[customer_type]
      ,'BI' [from_source],
	  case when so.bu='Indirect' then (case when a.product_channel='Consumer' 
			then 1 when a.product_channel='Commercial' then 2 else 0 end) 
			when so.sales_channel='OEM' then 4 
			when so.sales_channel='Industrial' then 8
			else null end channel_weight 
	from PP_MID.dbo.syn_dw_to_pp_customer_org_sales a
	left join [PP_MID].[dbo].[syn_dw_to_pp_dim_sales_org_hier] so on a.org_hier_id=so.org_hier_id
  where not exists (select 1 from dw_customer_org_sales dac1 where dac1.from_source='PP' and a.region=dac1.region 
	and a.distributor_id=dac1.distributor_id and a.product_channel=dac1.product_channel)

	
--select * from wx_t_user where login_name='baiyingjie'
--select top 100 * from wx_t_budget2021_log order by id desc;
--EXEC [dbo].[pr_syn_distributor_rel]

select a.distributor_id,a.customer_name_cn customer_name,a.sales_cai new_sales_cai,a.sales_name_cn new_sales_name, 
		 cos1.sales_cai old_sales_cai, cos1.sales_name_cn old_sales_name
			from PP_MID.dbo.syn_dw_to_pp_customer_org_sales a
			left join [PP_MID].[dbo].[syn_dw_to_pp_dim_sales_org_hier] so on a.org_hier_id=so.org_hier_id
			left join dw_region_sales_channel_rel rr1 on rr1.region_name=a.region
			left join dw_customer_org_sales cos1 on cos1.distributor_id=a.distributor_id and cos1.product_channel=a.product_channel
			left join dw_region_sales_channel_rel rr2 on rr2.region_name=cos1.region
		  where rr1.bu='Indirect' and rr2.bu='Indirect' and cos1.from_source='BI' and a.sales_cai!=cos1.sales_cai

/****** Script for SelectTopNRows command from SSMS  ******/
SELECT TOP (1000) [distributor_id]
      ,[customer_name_cn]
      ,[org_hier_id]
      ,[region]
      ,[product_channel]
      ,[sales_cai]
      ,[sales_name_cn]
      ,[supervisor_cai]
      ,[supervisor_name_cn]
      ,[effective_from_date]
      ,[create_time]
      ,[update_time]
      ,[del_flag]
      ,[customer_type]
      ,[has_current_year_operation_target]
	  --update c set c.sales_cai='AWSZ', c.sales_name_cn='郑爱武'
	  --update c set c.sales_cai='YIBN', c.sales_name_cn='白英杰'
  FROM [PP_MID].[dbo].[syn_dw_to_pp_customer_org_sales] c where customer_name_cn in ('重庆纽动商贸有限公司','')		  
	