CREATE TABLE [dbo].[wx_t_db2b_activity](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[partner_id] [bigint] NOT NULL,
	[activity_name] [nvarchar](256) NOT NULL,
	[activity_level] [int] NULL,
	[start_date] [date] NULL,
	[end_date] [date] NULL,
	[enable_flag] [tinyint] NOT NULL,
	[delete_flag] [tinyint] NOT NULL,
	[create_user_id] [bigint] NULL,
	[create_time] [datetime] NULL,
	[update_user_id] [bigint] NULL,
	[update_time] [datetime] NULL,
	[message_send_type] [int] NULL,
	[message_content] [varchar](4000) NULL,
	[custom_send_time] [datetime] NULL,
	[publish] [int] NULL,
	[type] [int] NULL,
PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[wx_t_db2b_activity] ADD  DEFAULT ((0)) FOR [message_send_type]
GO

ALTER TABLE [dbo].[wx_t_db2b_activity] ADD  DEFAULT ((0)) FOR [publish]
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'0:自定时间发送 1：立即发送' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'wx_t_db2b_activity', @level2type=N'COLUMN',@level2name=N'message_send_type'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'消息内容' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'wx_t_db2b_activity', @level2type=N'COLUMN',@level2name=N'message_content'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'自定义消息发送时间' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'wx_t_db2b_activity', @level2type=N'COLUMN',@level2name=N'custom_send_time'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'0：未发布 1：已发布' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'wx_t_db2b_activity', @level2type=N'COLUMN',@level2name=N'publish'
GO

CREATE TABLE [dbo].[wx_t_db2b_coupon](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[type] [int] NULL,
	[coupon_code] [nvarchar](64) NOT NULL,
	[name] [nvarchar](256) NULL,
	[start_time] [datetime] NULL,
	[end_time] [datetime] NULL,
	[config] [nvarchar](4000) NULL,
	[quantity] [int] NULL,
	[remark] [nvarchar](4000) NULL,
	[create_user_id] [bigint] NULL,
	[create_time] [datetime] NULL,
	[update_user_id] [bigint] NULL,
	[update_time] [datetime] NULL,
	[delete_flag] [int] NOT NULL,
PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[wx_t_db2b_coupon] ADD  DEFAULT ((0)) FOR [delete_flag]
GO


CREATE TABLE [dbo].[wx_t_db2b_promotion](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[activity_id] [bigint] NOT NULL,
	[promotion_type] [int] NOT NULL,
	[config] [nvarchar](4000) NOT NULL,
	[delete_flag] [tinyint] NOT NULL,
	[create_user_id] [bigint] NULL,
	[create_time] [datetime] NULL,
	[update_user_id] [bigint] NULL,
	[update_time] [datetime] NULL,
PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

CREATE TABLE [dbo].[wx_t_db2b_promotion_product](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[product_id] [bigint] NOT NULL,
	[promotion_id] [bigint] NOT NULL,
PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO