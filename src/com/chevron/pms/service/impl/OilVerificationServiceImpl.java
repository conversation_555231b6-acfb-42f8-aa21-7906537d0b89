package com.chevron.pms.service.impl;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import com.chevron.pms.business.OilVerificationBizService;
import com.chevron.pms.business.VerificationRuleBizService;
import com.chevron.pms.business.WorkshopDistributionRuleBizService;
import com.chevron.pms.dao.MechanicQrcodeMapper;
import com.chevron.pms.dao.OilVerificationMapper;
import com.chevron.pms.dao.VerificationBatchMapper;
import com.chevron.pms.model.ChevronVerificationParams;
import com.chevron.pms.model.MechanicQrcode;
import com.chevron.pms.model.OilVerification;
import com.chevron.pms.model.OilVerificationParams;
import com.chevron.pms.model.OilVerificationSpParams;
import com.chevron.pms.model.VerificationBatch;
import com.chevron.pms.model.VerificationDetail;
import com.chevron.pms.model.VerificationDetailUser;
import com.chevron.pms.model.VerificationRule;
import com.chevron.pms.model.WorkshopDistributionRule;
import com.chevron.pms.model.WorkshopOilVerificationSummary;
import com.chevron.pms.service.OilVerificationService;
import com.common.constants.Constants;
import com.common.exception.WxPltException;
import com.common.util.ContextUtil;
import com.common.util.DateUtil;
import com.common.util.MessageResourceUtil;
import com.common.util.StringUtils;
import com.sys.auth.model.WxTUser;

@Service
public class OilVerificationServiceImpl implements OilVerificationService {
	private static Logger log = LoggerFactory.getLogger(OilVerificationServiceImpl.class);
	public final static String SUCCESS_CODE = "success";
	public final static String FAIL_CODE = "fail";
	public final static String ERROR_CODE = "error";

	public final static String RESULT_CODE_KEY = "code";
	public final static String RESULT_LST_KEY = "resultLst";
	public final static String RESULT_ERROR_MSG_KEY = "errorMsg";
	public final static String RESULT_CURRENT_ORDER_KEY = "currentOrder";

	public final static String RESULT_ISERROR = "isError";
	@Autowired
	private OilVerificationBizService oilVerificationBizService;
	
	@Autowired
	private VerificationRuleBizService verificationRuleBizService;
	
	@Autowired
	private VerificationBatchMapper verificationBatchMapper;
	
	@Resource
	private OilVerificationMapper oilVerificationMapper;
	
	@Autowired
	private WorkshopDistributionRuleBizService workshopDistributionBizService;
	
	@Resource
	private MechanicQrcodeMapper mechanicQrcodeMapper;
	
	@Override
	public Map<String, Object> getVerificationInfoByWorkshop(Long workshopId, Date dateFrom, Date dateTo) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		try {
			WorkshopOilVerificationSummary summary = oilVerificationBizService.getOilVerificationSummary(workshopId, dateFrom, dateTo);
			
			//添加审批核销数据 add by lizhentao 20161019
			Date newDateFrom = (dateFrom == null) ? DateUtil.getMinimumDate() : DateUtil.getDateToZero(dateFrom);
			Date newDateTo = (dateTo == null) ? DateUtil.getMaximumDate() : DateUtil.getDateToMax(dateTo);
			
			Map<String, Object> paramMap = new HashMap<String, Object>(1);
			paramMap.put("workshopId", workshopId);
			paramMap.put("dateFrom", newDateFrom);
			paramMap.put("dateTo", newDateTo);
			int approvedCapacity = 0;
			double approvedAmount = 0;
			double approvedOwnerAmount = 0;
			double approvedMechanicAmount = 0;
			
			int submittedCapacity = 0;
			double submittedAmount = 0;
			double submittedOwnerAmount = 0;
			double submittedMechanicAmount = 0;
			
			List<VerificationBatch> list = verificationBatchMapper.queryVerificationBatch(paramMap);
			if(list != null && !list.isEmpty()){
				for(VerificationBatch batch : list){
					if(VerificationBatch.STATUS_APPROVED.equals(batch.getStatus())){
						approvedAmount += batch.getAmount();
						approvedCapacity += batch.getTotalCapacity();
						approvedOwnerAmount += batch.getOwnerAmount();
						approvedMechanicAmount += batch.getMechanicAmount();
					}else if(VerificationBatch.STATUS_SUBMIT.equals(batch.getStatus())){
						submittedAmount += batch.getAmount();
						submittedCapacity += batch.getTotalCapacity();
						submittedOwnerAmount += batch.getOwnerAmount();
						submittedMechanicAmount += batch.getMechanicAmount();
					}
				}
			}
			resultMap.put("approvedAmount", approvedAmount);
			resultMap.put("approvedCapacity", approvedCapacity);
			resultMap.put("approvedOwnerAmount", approvedOwnerAmount);
			resultMap.put("approvedMechanicAmount", approvedMechanicAmount);
			resultMap.put("submittedAmount", submittedAmount);
			resultMap.put("submittedCapacity", submittedCapacity);
			resultMap.put("submittedOwnerAmount", submittedOwnerAmount);
			resultMap.put("submittedMechanicAmount", submittedMechanicAmount);
			resultMap.put("verifyAmout", summary.getVerifyAmout());
			
			//添加实时发红包核销数据
			paramMap = new HashMap<String, Object>();
			paramMap.put("workshopId", workshopId);
			paramMap.put("dateFrom", newDateFrom);
			paramMap.put("dateTo", newDateTo);

			List<WorkshopOilVerificationSummary> realtimeList = oilVerificationBizService.queryOilVerificationSummaryListWithAwardRealtime(paramMap);
			if(realtimeList != null && realtimeList.size() > 0){
				resultMap.put("realtimeSummary", realtimeList.get(0));
			}
			
			resultMap.put("code", Constants.RESULT_SUCCESS);
			resultMap.put("mechanicScannedCapacity", summary.getMechanicScannedCapacity());
			resultMap.put("verifiedCapacity", summary.getCanVerifyCapacity());
			resultMap.put("closedCapacity", summary.getClosedCapacity());
			resultMap.put("summary", summary);
		} catch (Exception e) {
			log.error("getVerificationInfoByWorkshop exception", e);
			resultMap.put("code", Constants.RESULT_ERROR);
			resultMap.put("codeMsg", MessageResourceUtil.getMessage("system.unexpected_exception"));
		}
		return resultMap;
	}

	@Override
	public Map<String, Object> queryUnClosedVerificationListByMechanic(Long workshopId, String mechanicCode, Date dateFrom,
			Date dateTo) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		Date newDateFrom = (dateFrom == null) ? DateUtil.getMinimumDate() : DateUtil.getDateToZero(dateFrom);
		Date newDateTo = (dateTo == null) ? DateUtil.getMaximumDate() : DateUtil.getDateToMax(dateTo);
		
		Map<String, Object> paramMap = new HashMap<String, Object>();
		try {
			if (workshopId == null){
				Long partnerId = ContextUtil.getCurUser().getOrgId();
				paramMap.put("partnerId", partnerId);
			} else {
				paramMap.put("workshopId", String.valueOf(workshopId));
			}
			
			if (!StringUtils.isNull(mechanicCode) && !"-1".equals(mechanicCode)){
				paramMap.put("mechanicCode", mechanicCode);
			}
			paramMap.put("dateFrom", newDateFrom);
			paramMap.put("dateTo", newDateTo);
			paramMap.put("status", "0");
			List<OilVerification> verificationList = oilVerificationBizService.queryOilVerificationList(paramMap);
			resultMap.put("code", "success");
			resultMap.put("data", verificationList);
		} catch (Exception e) {
			log.error("queryUnClosedVerificationListByMechanic exception", e);
			resultMap.put("code", "error");
			resultMap.put("codeMsg", MessageResourceUtil.getMessage("system.unexpected_exception"));
		}
		return resultMap;
	}

	@Override
	public Map<String, Object> queryVerificationSummaryList(Long partnerId, Long workshopId, String workshopName, Date dateFrom,
			Date dateTo) {
		Date newDateFrom = (dateFrom == null) ? DateUtil.getMinimumDate() : DateUtil.getDateToZero(dateFrom);
		Date newDateTo = (dateTo == null) ? DateUtil.getMaximumDate() : DateUtil.getDateToMax(dateTo);
		
		Map<String, Object> resultMap = new HashMap<String, Object>();
		
		try {
			Long newPartnerId;
			WxTUser user = ContextUtil.getCurUser();
			if(user.getmUserTypes() == 1){
				newPartnerId = partnerId;
			}else{
				newPartnerId = user.getOrgId();
			}
			
			Map<String, Object> paramMap = new HashMap<String, Object>();
			paramMap.put("workshopId", workshopId == null ? null : String.valueOf(workshopId));
			paramMap.put("partnerId", newPartnerId);
			paramMap.put("dateFrom", newDateFrom);
			paramMap.put("dateTo", newDateTo);
			if (!StringUtils.isNull(workshopName)){
				paramMap.put("workshopName", "%" + workshopName + "%");
			}

			List<WorkshopOilVerificationSummary> summaryList = oilVerificationBizService.queryOilVerificationSummaryList(paramMap);
//			if (summaryList != null && !summaryList.isEmpty()){
//				Iterator<WorkshopOilVerificationSummary> iter = summaryList.iterator();
//				while (iter.hasNext()){
//					WorkshopOilVerificationSummary summary = iter.next();
//					if (summary.getDistributionRule() != null && summary.getMechanicScannedCapacity() - summary.getClosedCapacity() == 0){
//						iter.remove();
//					}
//				}
//			}
			resultMap.put("code", "success");
			resultMap.put("data", summaryList);
		} catch (Exception e) {
			log.error("queryVerificationSummaryList exception", e);
			resultMap.put("code", "error");
			resultMap.put("codeMsg", MessageResourceUtil.getMessage("system.unexpected_exception"));
		}
		
		return resultMap;
	}

	@Override
	public Map<String, Object> verifyWorkshops(Long[] workshopIds, Date dateFrom, Date dateTo) {
		Date newDateFrom = (dateFrom == null) ? DateUtil.getMinimumDate() : DateUtil.getDateToZero(dateFrom);
		Date newDateTo = (dateTo == null) ? DateUtil.getMaximumDate() : DateUtil.getDateToMax(dateTo);
		
		Map<String, Object> resultMap = new HashMap<String, Object>();
		
		try {
			if (workshopIds == null || workshopIds.length == 0){
				throw new WxPltException(MessageResourceUtil.getMessage("system.no_data_save"));
			}
			
			for (Long workshopId : workshopIds) {
				oilVerificationBizService.verifyWorkshop(workshopId, newDateFrom, newDateTo);
			}
			
			resultMap.put("code", "success");
		} catch (WxPltException e) {
			log.error("verifyWorkshops exception", e);
			resultMap.put("code", "error");
			resultMap.put("codeMsg", e.getExpMsg());
		} catch (Exception e) {
			log.error("verifyWorkshops exception", e);
			resultMap.put("code", "error");
			resultMap.put("codeMsg", MessageResourceUtil.getMessage("system.unexpected_exception"));
		}
		
		return resultMap;
	}
	
	@Override
	public Map<String, Object> verifyWorkshopsRegardlessUser(Long[] workshopIds, Date dateFrom, Date dateTo) {
		Date newDateFrom = (dateFrom == null) ? DateUtil.getMinimumDate() : DateUtil.getDateToZero(dateFrom);
		Date newDateTo = (dateTo == null) ? DateUtil.getMaximumDate() : DateUtil.getDateToMax(dateTo);
		
		Map<String, Object> resultMap = new HashMap<String, Object>();
		try {
			if (workshopIds == null || workshopIds.length == 0){
				throw new WxPltException(MessageResourceUtil.getMessage("system.no_data_save"));
			}
			
			for (Long workshopId : workshopIds) {
				oilVerificationBizService.verifyWorkshopRegardlessUser(workshopId, newDateFrom, newDateTo);
			}
			
			resultMap.put("code", "success");
		} catch (WxPltException e) {
			log.error("verifyWorkshopsRegardlessUser exception", e);
			resultMap.put("code", "error");
			resultMap.put("codeMsg", e.getExpMsg());
		} catch (Exception e) {
			log.error("verifyWorkshopsRegardlessUser exception", e);
			resultMap.put("code", "error");
			resultMap.put("codeMsg", MessageResourceUtil.getMessage("system.unexpected_exception"));
		}
		
		return resultMap;
	}

	@Override
	public Map<String, Object> saveVerificationRule(VerificationRule rule) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
	
		try {
			verificationRuleBizService.saveVerificationRule(rule);
			
			resultMap.put("code", "success");
		} catch (WxPltException e) {
			log.error("saveVerificationRule exception", e);
			resultMap.put("code", "error");
			resultMap.put("codeMsg", e.getExpMsg());
		} catch (Exception e) {
			log.error("saveVerificationRule exception", e);
			resultMap.put("code", "error");
			resultMap.put("codeMsg", MessageResourceUtil.getMessage("system.unexpected_exception"));
		}
		
		return resultMap;
	}

	@Override
	public Map<String, Object> queryVerificationRule(Long partnerId, Long workshopId, Date dateFrom, Date dateTo) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		
		try {
			List<VerificationRule> ruleList = verificationRuleBizService.queryVerificationRule(partnerId, workshopId, dateFrom, dateTo);
			
			resultMap.put("code", "success");
			resultMap.put("data", ruleList);
		} catch (Exception e) {
			log.error("queryVerificationRule exception", e);
			resultMap.put("code", "error");
			resultMap.put("codeMsg", MessageResourceUtil.getMessage("system.unexpected_exception"));
		}
		
		return resultMap;
	}

	@Override
	public Map<String, Object> getMaxRewardPerOrder() {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		try {
			String reward = (String) Constants.getSystemPropertyByCodeType("oil.verification.order.maxreward");
			
			resultMap.put("code", "success");
			resultMap.put("maxReward", reward);
		} catch (Exception e) {
			log.error("getMaxRewardPerOrder exception", e);
			resultMap.put("code", "error");
			resultMap.put("codeMsg", MessageResourceUtil.getMessage("system.unexpected_exception"));
		}
		
		return resultMap;
	}
	@Override
	public Map<String, Object> getMaxRewardPerLiter() {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		try {
			String reward = (String) Constants.getSystemPropertyByCodeType(Constants.VERIFICATION_MAX_REWARD_PER_LITER);
			
			resultMap.put("code", "success");
			resultMap.put("maxReward", reward);
		} catch (Exception e) {
			e.printStackTrace();
			log.error("getMaxRewardPerLiter exception", e);
			resultMap.put("code", "error");
		}
		
		return resultMap;
	}

	@Override
	public Map<String, Object> queryUserScannedValidVerificationList(Long workshopId, Date dateFrom, Date dateTo) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		
		try {
			Date newDateFrom = (dateFrom == null) ? DateUtil.getMinimumDate() : DateUtil.getDateToZero(dateFrom);
			Date newDateTo = (dateTo == null) ? DateUtil.getMaximumDate() : DateUtil.getDateToMax(dateTo);
			
			Map<String, Object> paramMap = new HashMap<String, Object>();
			if (workshopId == null){
				throw new WxPltException(MessageResourceUtil.getMessage("system.illegal_parameter"));
			} 
			paramMap.put("workshopId", String.valueOf(workshopId));
			
			paramMap.put("dateFrom", newDateFrom);
			paramMap.put("dateTo", newDateTo);
			paramMap.put("userValid", "Y");
			List<OilVerification> verificationList = oilVerificationBizService.queryOilVerificationList(paramMap);
			resultMap.put("code", "success");
			resultMap.put("data", verificationList);
		} catch (WxPltException e){
			log.error("queryUserScannedValidVerificationList exception", e);
			resultMap.put("code", "error");
			resultMap.put("codeMsg", e.getExpMsg());
		} catch (Exception e) {
			log.error("queryUserScannedValidVerificationList exception", e);
			resultMap.put("code", "error");
			resultMap.put("codeMsg", MessageResourceUtil.getMessage("system.unexpected_exception"));
		}
		return resultMap;
	}

	@Override
	public Map<String, Object> queryUserScannedInvalidVerificationList(Long workshopId, Date dateFrom, Date dateTo) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		
		try {
			Date newDateFrom = (dateFrom == null) ? DateUtil.getMinimumDate() : DateUtil.getDateToZero(dateFrom);
			Date newDateTo = (dateTo == null) ? DateUtil.getMaximumDate() : DateUtil.getDateToMax(dateTo);
			
			Map<String, Object> paramMap = new HashMap<String, Object>();
			if (workshopId == null){
				throw new WxPltException(MessageResourceUtil.getMessage("system.illegal_parameter"));
			} 
			paramMap.put("workshopId", String.valueOf(workshopId));
			
			paramMap.put("dateFrom", newDateFrom);
			paramMap.put("dateTo", newDateTo);
			paramMap.put("userValid", "N");
			List<OilVerification> verificationList = oilVerificationBizService.queryOilVerificationList(paramMap);
			resultMap.put("code", "success");
			resultMap.put("data", verificationList);
		} catch (WxPltException e){
			log.error("queryUserScannedInvalidVerificationList exception", e);
			resultMap.put("code", "error");
			resultMap.put("codeMsg", e.getExpMsg());
		} catch (Exception e) {
			log.error("queryUserScannedInvalidVerificationList exception", e);
			resultMap.put("code", "error");
			resultMap.put("codeMsg", MessageResourceUtil.getMessage("system.unexpected_exception"));
		}
		return resultMap;
	}

	@Override
	public Map<String, Object> queryClosedVerificationList(Long workshopId, Date dateFrom, Date dateTo) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		
		try {
			Date newDateFrom = (dateFrom == null) ? DateUtil.getMinimumDate() : DateUtil.getDateToZero(dateFrom);
			Date newDateTo = (dateTo == null) ? DateUtil.getMaximumDate() : DateUtil.getDateToMax(dateTo);
			
			Map<String, Object> paramMap = new HashMap<String, Object>();
			if (workshopId == null){
				throw new WxPltException(MessageResourceUtil.getMessage("system.illegal_parameter"));
			} 
			paramMap.put("workshopId", String.valueOf(workshopId));
			
			paramMap.put("dateFrom", newDateFrom);
			paramMap.put("dateTo", newDateTo);
			paramMap.put("closed", "Y");
			List<OilVerification> verificationList = oilVerificationBizService.queryOilVerificationList(paramMap);
			resultMap.put("code", "success");
			resultMap.put("data", verificationList);
		} catch (WxPltException e){
			log.error("queryClosedVerificationList exception", e);
			resultMap.put("code", "error");
			resultMap.put("codeMsg", e.getExpMsg());
		} catch (Exception e) {
			log.error("queryClosedVerificationList exception", e);
			resultMap.put("code", "error");
			resultMap.put("codeMsg", MessageResourceUtil.getMessage("system.unexpected_exception"));
		}
		return resultMap;
	}

	@Override
	public Map<String, Object> queryVerificationBatch(Long partnerId, Long workshopId, String workshopName, String status, Date dateFrom,
			Date dateTo, String operation) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		try {
			Date newDateFrom = (dateFrom == null) ? DateUtil.getMinimumDate() : DateUtil.getDateToZero(dateFrom);
			Date newDateTo = (dateTo == null) ? DateUtil.getMaximumDate() : DateUtil.getDateToMax(dateTo);
			
			Map<String, Object> paramMap = new HashMap<String, Object>();
			paramMap.put("workshopId", workshopId);
			paramMap.put("partnerId", partnerId);
			paramMap.put("dateFrom", newDateFrom);
			paramMap.put("dateTo", newDateTo);
			
			if (!StringUtils.isNull(workshopName)){
				paramMap.put("workshopName", "%" + workshopName + "%");
			}
			
			if (!StringUtils.isNull(status)){
				String[] statusArray = status.split(",");
				paramMap.put("status", statusArray);
			}
			List<VerificationBatch> batchList = oilVerificationBizService.queryVerificationBatch(paramMap, operation);
			resultMap.put("code", "success");
			resultMap.put("data", batchList);
		} catch (Exception e) {
			log.error("queryUserScannedValidVerificationList exception", e);
			resultMap.put("code", "error");
			resultMap.put("codeMsg", MessageResourceUtil.getMessage("system.unexpected_exception"));
		}
		return resultMap;
	}

	@Override
	public Map<String, Object> confirmVerification(Long[] batchIds) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		
		try {
			
			oilVerificationBizService.confirmVerification(batchIds);
			resultMap.put("code", "success");
		} catch (Exception e) {
			log.error("queryUserScannedValidVerificationList exception", e);
			resultMap.put("code", "error");
			resultMap.put("codeMsg", MessageResourceUtil.getMessage("system.unexpected_exception"));
		}
		return resultMap;
	}

	@Override
	public Map<String, Object> queryVerificationDetail(Long batchId) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		try {
			
			List<VerificationDetail> detailList = oilVerificationBizService.queryVerificationDetail(batchId);
			resultMap.put("code", "success");
			resultMap.put("data", detailList);
		} catch (Exception e) {
			log.error("queryVerificationDetail exception", e);
			resultMap.put("code", "error");
			resultMap.put("codeMsg", MessageResourceUtil.getMessage("system.unexpected_exception"));
		}
		return resultMap;
	}
	
	@Override
	public Map<String, Object> queryVerificationDetailUser(Long batchId) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		try {
			
			List<VerificationDetailUser> detailUserList = oilVerificationBizService.queryVerificationDetailUser(batchId);
			resultMap.put("code", "success");
			resultMap.put("data", detailUserList);
		} catch (Exception e) {
			log.error("queryUserScannedValidVerificationList exception", e);
			resultMap.put("code", "error");
			resultMap.put("codeMsg", MessageResourceUtil.getMessage("system.unexpected_exception"));
		}
		return resultMap;
	}

	@Override
	public Map<String, Object> queryVerificationBatchHistoryForChevron(Long partnerId, Long workshopId, String workshopName, Date dateFrom,
			Date dateTo) {
		return queryVerificationBatch(partnerId, workshopId, workshopName, VerificationBatch.STATUS_APPROVED, dateFrom, dateTo, "ch");
	}

	@Override
	public Map<String, Object> saveWorkshopDistributionRule(WorkshopDistributionRule rule) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		
		try {
			workshopDistributionBizService.saveDistributionRule(rule);
			
			resultMap.put("code", "success");
		} catch (WxPltException e) {
			log.error("saveWorkshopDistributionRule exception", e);
			resultMap.put("code", "error");
			resultMap.put("codeMsg", e.getExpMsg());
		} catch (Exception e) {
			log.error("saveWorkshopDistributionRule exception", e);
			resultMap.put("code", "error");
			resultMap.put("codeMsg", MessageResourceUtil.getMessage("system.unexpected_exception"));
		}
		
		return resultMap;
	}

	@Override
	public Map<String, Object> queryWorkshopDistributionRule(Long workshopId) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		
		try {
			WorkshopDistributionRule rule = workshopDistributionBizService.queryDistributionRuleByWorkshopId(workshopId);
			
			resultMap.put("code", "success");
			resultMap.put("distributionRule", rule);
		} catch (Exception e) {
			log.error("queryWorkshopDistributionRule exception", e);
			resultMap.put("code", "error");
			resultMap.put("codeMsg", MessageResourceUtil.getMessage("system.unexpected_exception"));
		}
		
		return resultMap;
	}

	@Override
	@Transactional(rollbackFor={WxPltException.class}, readOnly=false, propagation=Propagation.REQUIRES_NEW)
	public Map<String, Object> verifyWorkshopsByIndividualMechanicQrcode(Long[] workshopIds, Date dateFrom,
			Date dateTo) {
		Date newDateFrom = (dateFrom == null) ? DateUtil.getMinimumDate() : DateUtil.getDateToZero(dateFrom);
		Date newDateTo = (dateTo == null) ? DateUtil.getMaximumDate() : DateUtil.getDateToMax(dateTo);
		
		Map<String, Object> resultMap = new HashMap<String, Object>();
		try {
			if (workshopIds == null || workshopIds.length == 0){
				throw new WxPltException(MessageResourceUtil.getMessage("system.no_data_save"));
			}
			
			for (Long workshopId : workshopIds) {
				oilVerificationBizService.verifyWorkshopByIndividualMechanicQrcode(workshopId, newDateFrom, newDateTo);
			}
			
			resultMap.put("code", "success");
		} catch (WxPltException e) {
			log.error("verifyWorkshopsByIndividualMechanicQrcode exception", e);
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			resultMap.put("code", "error");
			resultMap.put("codeMsg", e.getExpMsg());
		} catch (Exception e) {
			log.error("verifyWorkshopsByIndividualMechanicQrcode exception", e);
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			resultMap.put("code", "error");
			resultMap.put("codeMsg", MessageResourceUtil.getMessage("system.unexpected_exception"));
		}
		
		return resultMap;
	}

	@Override
	public Map<String, Object> queryVerificationListWithAwardRealtime(Long workshopId, String mechanicCode, Date dateFrom, Date dateTo) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		Date newDateFrom = (dateFrom == null) ? DateUtil.getMinimumDate() : DateUtil.getDateToZero(dateFrom);
		Date newDateTo = (dateTo == null) ? DateUtil.getMaximumDate() : DateUtil.getDateToMax(dateTo);
		
		
		Map<String, Object> paramMap = new HashMap<String, Object>();
		try {
			if (workshopId == null){
				Long partnerId = ContextUtil.getCurUser().getOrgId();
				paramMap.put("partnerId", partnerId);
			} else {
				paramMap.put("workshopId", String.valueOf(workshopId));
			}
			
			if (!StringUtils.isNull(mechanicCode) && !"-1".equals(mechanicCode)){
				paramMap.put("mechanicCode", mechanicCode);
			}
			paramMap.put("dateFrom", newDateFrom);
			paramMap.put("dateTo", newDateTo);
			List<OilVerification> verificationList = oilVerificationBizService.queryOilVerificationListWithAwardRealtime(paramMap);
			resultMap.put("code", "success");
			resultMap.put("data", verificationList);
		} catch (Exception e) {
			log.error("queryVerificationListWithAwardRealtime exception", e);
			resultMap.put("code", "error");
			resultMap.put("codeMsg", MessageResourceUtil.getMessage("system.unexpected_exception"));
		}
		return resultMap;
	}

	@Override
	public Map<String, Object> queryVerificationSummaryListWithAwardRealtime(Long partnerId, Long workshopId, String workshopName,
			Date dateFrom, Date dateTo) {
		Date newDateFrom = (dateFrom == null) ? DateUtil.getMinimumDate() : DateUtil.getDateToZero(dateFrom);
		Date newDateTo = (dateTo == null) ? DateUtil.getMaximumDate() : DateUtil.getDateToMax(dateTo);
		
		Map<String, Object> resultMap = new HashMap<String, Object>();
		
		try {
			WxTUser currentUser = ContextUtil.getCurUser();
			Long newPartnerId = (partnerId == null && currentUser.getmUserTypes() != 1L) ? currentUser.getOrgId() : partnerId;
			
			Map<String, Object> paramMap = new HashMap<String, Object>();
			paramMap.put("workshopId", workshopId == null ? null : String.valueOf(workshopId));
			paramMap.put("partnerId", newPartnerId);
			paramMap.put("dateFrom", newDateFrom);
			paramMap.put("dateTo", newDateTo);
			if (!StringUtils.isNull(workshopName)){
				paramMap.put("workshopName", "%" + workshopName + "%");
			}

			List<WorkshopOilVerificationSummary> summaryList = oilVerificationBizService.queryOilVerificationSummaryListWithAwardRealtime(paramMap);
			
			resultMap.put("code", "success");
			resultMap.put("data", summaryList);
		} catch (Exception e) {
			log.error("queryVerificationSummaryListWithAwardRealtime exception", e);
			resultMap.put("code", "error");
			resultMap.put("codeMsg", MessageResourceUtil.getMessage("system.unexpected_exception"));
		}
		
		return resultMap;
	}

	@Override
	public Map<String, Object> queryMechanicVerificationInfo(String mechanicCode, Date dateFrom, Date dateTo) {
		Date newDateFrom = (dateFrom == null) ? DateUtil.getMinimumDate() : DateUtil.getDateToZero(dateFrom);
		Date newDateTo = (dateTo == null) ? DateUtil.getMaximumDate() : DateUtil.getDateToMax(dateTo);
		
		Map<String, Object> resultMap = new HashMap<String, Object>();
		
		try {
			if (StringUtils.isNull(mechanicCode)){
				throw new WxPltException(MessageResourceUtil.getMessage("system.illegal_parameter"));
			}
			Map<String, Object> dataMap = oilVerificationBizService.queryVerificationInfoByMechanic(mechanicCode, newDateFrom, newDateTo);
			
			resultMap.put("code", "success");
			resultMap.put("data", dataMap);
		} catch(WxPltException e) {
			log.error("queryMechanicVerificationInfo exception", e);
			resultMap.put("code", "error");
			resultMap.put("codeMsg", e.getMessage());
		} catch (Exception e) {
			log.error("queryMechanicVerificationInfo exception", e);
			resultMap.put("code", "error");
			resultMap.put("codeMsg", MessageResourceUtil.getMessage("system.unexpected_exception"));
		}
		
		return resultMap;
	}

	@Override
	public Map<String, Object> saveRules(WorkshopDistributionRule[] rules) {
		Map<String, Object> map = new HashMap<String, Object>(5);
		try {
			for(WorkshopDistributionRule rule : rules){
				workshopDistributionBizService.saveDistributionRule(rule);
			}
			map.put("code", "success");
		} catch (Exception e) {
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			log.error("queryMechanicVerificationInfo exception", e);
			map.put("code", "error");
			map.put("codeMsg", MessageResourceUtil.getMessage("system.unexpected_exception"));
		}
		return map;
	}

	@Override
	public Map<String, Object> queryUnsetRuleVerificationData(
			OilVerificationParams params) {
		Map<String, Object> map = new HashMap<String, Object>(2);
		try {
			params.setPaging(false);
			map.put("data", oilVerificationMapper.queryUnsetRuleVerificationData(params));
			map.put("success", true);
		} catch (Exception e) {
			e.printStackTrace();
			map.put("success", false);
			map.put("errorMsg", MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error("error",e);
		}
		return map;
	}

	@Override
	public Map<String, Object> queryVerification(
			ChevronVerificationParams chevronVerification) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		try {
			resultMap.put(RESULT_LST_KEY, verificationBatchMapper.queryVerification(chevronVerification));
			resultMap.put("total", chevronVerification.getTotalCount());
			resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
			resultMap.put(RESULT_ISERROR, false);
		} catch (Exception e) {
			e.printStackTrace();
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ISERROR, true);
			resultMap.put(RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error(ERROR_CODE,e);
		}
		return resultMap;
	}

	@Override
	public Map<String, Object> queryVerificationBatchHistoryForChevron(ChevronVerificationParams chevronVerificationParams) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		try {
			resultMap.put(RESULT_LST_KEY, verificationBatchMapper.queryChevronVerificationHistory(chevronVerificationParams));
			resultMap.put("total", chevronVerificationParams.getTotalCount());
			chevronVerificationParams.closeOrder();
			List<VerificationBatch> sumList = verificationBatchMapper.queryChevronVerificationHistorySum(chevronVerificationParams);
			VerificationBatch sum = null;
			if(!sumList.isEmpty()){
				sum = sumList.get(0);
				sum.setId(null);
				for(int i = 1; i < sumList.size(); i++){
					VerificationBatch item = sumList.get(i);
					sum.setTotalCapacity(sum.getTotalCapacity() + item.getTotalCapacity());
					sum.setAmount(sum.getAmount() + item.getAmount());
					sum.setMechanicAmount(sum.getMechanicAmount() + item.getMechanicAmount());
					sum.setOwnerAmount(sum.getOwnerAmount() + item.getOwnerAmount());
				}
			}
			if(sum == null){
				sum = new VerificationBatch();
			}
			resultMap.put("summary", sum);

			resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
			resultMap.put(RESULT_ISERROR, false);
		} catch (Exception e) {
			e.printStackTrace();
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ISERROR, true);
			resultMap.put(RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error(ERROR_CODE,e);
		}
		return resultMap;
	}
	
	@Override
	public Map<String, Object> queryVerificationBatchHistoryForSP(OilVerificationSpParams params) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		try {
			List<VerificationBatch> temp = verificationBatchMapper.queryVerificationBatchForSp(params);
			resultMap.put(RESULT_LST_KEY, temp);
			resultMap.put("total", params.getTotalCount());
			params.closeOrder();
			List<VerificationBatch> sumList = verificationBatchMapper.queryVerificationBatchForSpSum(params);
			VerificationBatch sum = null;
			if(!sumList.isEmpty()){
				sum = sumList.get(0);
				sum.setId(null);
				for(int i = 1; i < sumList.size(); i++){
					VerificationBatch item = sumList.get(i);
					sum.setTotalCapacity(sum.getTotalCapacity() + item.getTotalCapacity());
					sum.setAmount(sum.getAmount() + item.getAmount());
					sum.setMechanicAmount(sum.getMechanicAmount() + item.getMechanicAmount());
					sum.setOwnerAmount(sum.getOwnerAmount() + item.getOwnerAmount());
				}
			}
			if(sum == null){
				sum = new VerificationBatch();
			}
			resultMap.put("summary", sum);
			resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
		} catch (Exception e) {
			e.printStackTrace();
			resultMap.put(RESULT_LST_KEY, null);
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error(ERROR_CODE,e);
		}
		return resultMap;
	}

	@Override
	public Map<String, Object> queryReportByWorkshopForMonth(Long workshopId) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		try {
			Map<String, Object> params = new HashMap<String, Object>();
			params.put("workshopId", workshopId);
			String today = DateUtil.getCurrentDate("yyyy-MM-dd");
			Calendar calendar = Calendar.getInstance();
			calendar.setTime(new SimpleDateFormat("yyyy-MM-dd").parse(today));
			calendar.set(Calendar.DAY_OF_MONTH, 1);
			calendar.add(Calendar.MONTH, 1);
			params.put("dateTo", calendar.getTime());
			calendar.add(Calendar.MONTH, -12);
			params.put("dateFrom", calendar.getTime());
			params.put("orderBy", "report_date desc");
			resultMap.put(RESULT_LST_KEY, oilVerificationMapper.queryReportForMonth(params));
			resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
		} catch (Exception e) {
			e.printStackTrace();
			resultMap.put(RESULT_LST_KEY, null);
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error(ERROR_CODE,e);
		}
		return resultMap;
	}

	@Override
	public Map<String, Object> queryReportByWorkshopForMonthDetail(
			Long workshopId, String reportDate) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		try {
			Map<String, Object> params = new HashMap<String, Object>();
			Date dateFrom = new SimpleDateFormat("yyyy-MM-dd").parse(reportDate + "-01");
			params.put("dateFrom", dateFrom);
			Calendar calendar = Calendar.getInstance();
			calendar.setTime(dateFrom);
			calendar.add(Calendar.MONTH, 1);
			params.put("dateTo", calendar.getTime());
			params.put("workshopId", workshopId);
			resultMap.put(RESULT_LST_KEY, oilVerificationBizService.queryOilVerificationList(params));
			resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
		} catch (Exception e) {
			e.printStackTrace();
			resultMap.put(RESULT_LST_KEY, null);
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error(ERROR_CODE,e);
		}
		return resultMap;
	}

	@Override
	public Map<String, Object> queryVerificationSum(OilVerificationParams params) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		try {
			params.closeOrder();
			MechanicQrcode sum = mechanicQrcodeMapper.queryVerificationSum(params);
			if(sum == null){
				resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
				resultMap.put(RESULT_ERROR_MSG_KEY, "技师不存在");
				return resultMap;
			}
			resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
			resultMap.put("totalCapacity", sum.getCapacity());
			resultMap.put("redPacketCapacity", sum.getRedPacketCapacity());
			resultMap.put("partnerName", sum.getPartnerName());
			resultMap.put("workshopName", sum.getWorkshopName());
			resultMap.put("mechanicName", sum.getMechanicName());
			resultMap.put("workshopId", sum.getWorkshopId());
			resultMap.put("employeeType", sum.getEmployeeType());
		} catch (Exception e) {
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error("OilVerificationServiceImpl.queryVerificationSum查询失败。" + e.getMessage(),e);
		}
		return resultMap;
	}

	@Override
	public Map<String, Object> queryMechanicVerificationSum(
			OilVerificationParams params) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		try {
			params.closeOrder();
			resultMap.put(RESULT_LST_KEY, mechanicQrcodeMapper.queryMechanicVerificationSum(params));
			resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
		} catch (Exception e) {
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error("OilVerificationServiceImpl.queryMechanicVerificationSum查询失败。" + e.getMessage(),e);
		}
		return resultMap;
	}

	@Override
	public Map<String, Object> queryMechanicVerificationDetail(
			OilVerificationParams params) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		try {
			params.closeOrder();
			resultMap.put(RESULT_LST_KEY, mechanicQrcodeMapper.queryMechanicVerificationDetail(params));
			resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
		} catch (Exception e) {
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error("OilVerificationServiceImpl.queryMechanicVerificationDetail查询失败。" + e.getMessage(),e);
		}
		return resultMap;
	}
}
