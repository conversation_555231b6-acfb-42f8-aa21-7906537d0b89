package com.sys.auth.service.impl;

import java.text.SimpleDateFormat;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import javax.annotation.Resource;

import net.sf.json.JSONObject;

import org.apache.commons.codec.digest.DigestUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import com.chevron.b2b.service.ScheduleInstanceService;
import com.chevron.disb2b.dao.Db2bDistSettingMapper;
import com.chevron.disb2b.model.Db2bDistSetting;
import com.chevron.disb2b.model.Db2bDistSettingExample;
import com.chevron.dwz.business.BaiduDwzBizService;
import com.chevron.master.business.WorkshopMasterBizService;
import com.chevron.master.model.WorkshopMaster;
import com.chevron.o2oorder.Constant.HttpClientUtils;
import com.chevron.o2oorder.service.O2oOrderService;
import com.chevron.plc.model.LogisticsStockVo;
import com.chevron.plc.model.LogisticsTrackInfo;
import com.chevron.plc.service.AppStockService;
import com.chevron.pms.business.WorkshopEmployeeBizService;
import com.chevron.pms.dao.OemDeliveryMapper;
import com.chevron.pms.dao.OemDeliveryProductMapper;
import com.chevron.pms.dao.OemProductPackagingCodeMapper;
import com.chevron.pms.dao.WorkshopEmployeeMapper;
import com.chevron.pms.model.MechanicQrcode;
import com.chevron.pms.model.OemDelivery;
import com.chevron.pms.model.OemDeliveryProduct;
import com.chevron.pms.model.OemDeliveryProductExample;
import com.chevron.pms.model.OemProductPackagingCode;
import com.chevron.pms.model.UserQrcode;
import com.chevron.pms.model.WorkshopEmployee;
import com.chevron.pms.model.WorkshopPointsRecordView;
import com.chevron.pms.service.MechanicQrcodeService;
import com.chevron.pms.service.QrCodeService;
import com.chevron.pms.service.RegionService;
import com.chevron.pms.service.WxTWorkshopPointsRecordService;
import com.chevron.scanorder.model.ReqSyncUserInfo;
import com.chevron.scanorder.model.ResponseWorkshopInfo;
import com.chevron.scanorder.service.ScanCodeOrderService;
import com.chevron.scanorder.service.impl.ScanCodeOrderServiceImpl;
import com.common.constants.Constants;
import com.common.exception.WxPltException;
import com.common.util.CommonUtil;
import com.common.util.ContextUtil;
import com.common.util.DateUtil;
import com.common.util.HttpSender;
import com.common.util.JsonResponse;
import com.common.util.JsonUtil;
import com.common.util.MessageResourceUtil;
import com.common.util.ResponseMap;
import com.common.util.ResponseStatus;
import com.common.util.WeixinMessageUtil;
import com.sys.auth.service.AnonRpcService;
import com.sys.dic.model.DicItemVo;
import com.sys.dic.service.DicService;
import com.sys.log.model.DeviceLocationLog;
import com.sys.log.service.DeviceLocationLogService;
import com.sys.utils.business.EtoBizService;

@Service
public class AnonRpcServiceImpl implements AnonRpcService {
	private static Logger log = LoggerFactory.getLogger(AnonRpcServiceImpl.class);
	public static final String O2O_SERVICE_API_URL_TYPE = "o2o_service.api.url";
	public static String O2O_SERVICE_API_URL_VALUE = "http://wxportalstg.techronworks.cn:8024";
	public static final String O2O_SERVICE_API_REGISTERMECHANIC_TYPE = "o2o_service.api.registerMechanic";
	public static String O2O_SERVICE_API_REGISTERMECHANIC_VALUE = "/api/common/registeroffline";
	public static final String O2O_SERVICE_API_REGISTERMECHANIC_KEY_TYPE = "o2o_service.api.registerMechanic.key";
	public static String O2O_SERVICE_API_REGISTERMECHANIC_KEY_VALUE = "123456";
	
	public static final String O2O_SERVICE_API_UPDATE_MECHANIC_TYPE = "o2o_service.api.updateRegisterMechanic";
	public static final String O2O_SERVICE_API_UPDATE_MECHANIC_VALUE = "/api/common/regupdateoffline";
	public static final String O2O_SERVICE_API_REGISTERMECHANIC_DEPARTMENT_TYPE = "o2o_service.api.registerMechanic.deparment";
	public static String O2O_SERVICE_API_REGISTERMECHANIC_DEPARTMENT_VALUE = "d017";
	@Autowired
	private DeviceLocationLogService deviceLocationLogService;
	
	@Autowired
	private MechanicQrcodeService mechanicQrcodeService;
	
	@Autowired
	private AppStockService appStockService;
	
	@Autowired
	private QrCodeService qrCodeService;
	
	@Autowired
	private OemProductPackagingCodeMapper oemProductPackagingCodeMapper;
	
	@Autowired
	private OemDeliveryMapper oemDeliveryMapper;
	
	@Autowired
	private OemDeliveryProductMapper oemDeliveryProductMapper;
	
	@Autowired
	DicService dicService;
	
	@Autowired
	WorkshopMasterBizService workshopMasterBizService;

	@Resource
	WorkshopEmployeeBizService workshopBizService;
	
	@Resource
	private WorkshopEmployeeMapper workshopEmployeeMapper;
	
	@Resource
	ScanCodeOrderService scanCodeOrderService;
	
	@Resource
	WxTWorkshopPointsRecordService workshopPointsRecordService;
	
	@Autowired
	private ScheduleInstanceService scheduleInstanceService;

	
	@Resource
	UserService userService;
	
	@Resource
	O2oOrderService o2oOrderService;
	
    @Resource
    private Db2bDistSettingMapper db2bDistSettingMapper;
    
    @Autowired
    private RegionService regionService;
    
    @Autowired
    private EtoBizService etoBizService;
	
	@Autowired
	private BaiduDwzBizService baiduDwzBizService;
	
	@Override
	public Map<String, Object> recordScannerLocation(
			DeviceLocationLog deviceLocationLog) {
		deviceLocationLog.setDeviceType(DeviceLocationLog.DEVICE_TYPE_SCANNER);
		if(deviceLocationLog.getDeviceName() == null){
			deviceLocationLog.setDeviceName("扫码枪" + deviceLocationLog.getDeviceCode());
		}
		deviceLocationLog.setCreateTime(new Date());
		return deviceLocationLogService.save(deviceLocationLog);
	}

	@Override
	public Map<String, Object> getMechanicQrcodeByTraceNo(String traceNo) {
		return mechanicQrcodeService.getMechanicQrcodeByTraceNo(traceNo);
	}

//	@Override
//	public Map<String, Object> getSaleDailyReportByYear(Long partnerId,
//			Date reportDate) {
//		return partnerResponsibleService.getSaleDailyReportByYear(partnerId, reportDate);
//	}
//
//	@Override
//	public Map<String, Object> getSaleDailyReport(String mobile) {
//		return partnerResponsibleService.getSaleDailyReport(mobile);
//	}
	
	protected void buildLogisticsInfo(String code,String type, Map<String, Object> resultMap) throws WxPltException {
		String qrCode = "";
		String logisticsCode="";
		if (type.equals("qrcode") && (code.startsWith("https://") || code.startsWith("http://"))) {
			//瓶盖码
			String capCode = baiduDwzBizService.queryLongUrl(code);
			capCode = capCode.split("=")[1].split(",")[0];
			Map<String, Object> capByQrcode = qrCodeService.getCapByQrcode(capCode);
			qrCode = (String) capByQrcode.get("qr_code");
			logisticsCode = qrCodeService.getLogisticsCode((Long) capByQrcode.get("track_code_id"));
			
		}else if (type.equals("qrcode")) {
			qrCode = code;
			logisticsCode = qrCodeService.getLogisticsCodeByQrcode(code);
		} else {
			if(code.length() == 12 && code.startsWith("8")) {
				//瓶盖外码
				Map<String, Object> qrInfo = qrCodeService.getQrInfoByCapCode(code);
				if (qrInfo == null) {
					WeixinMessageUtil.weixinAlarmMessagePush("瓶盖码物流追踪", "瓶盖码异常", "瓶盖码异常", code, "瓶盖码异常，瓶盖码未绑定");
					throw new WxPltException("瓶盖外码异常");
				}
				qrCode = (String) qrInfo.get("qrCode");
				logisticsCode = (String) qrInfo.get("logisticsCode");
			}else {
				logisticsCode = code;
				// 获取用户扫码信息
				Map<String, Object> qrCodeMap = qrCodeService.getQrcode(logisticsCode);
				qrCode = (String) qrCodeMap.get("qrcode");
			}
		}
		List<LogisticsTrackInfo> logisticsInfoList = new ArrayList<LogisticsTrackInfo>();
		//查询从中商过来的物流吗是否包含这个码
		List<OemProductPackagingCode> packagingList  = oemProductPackagingCodeMapper.queryProductPackingCodeByCode(logisticsCode);
		if(packagingList != null && !packagingList.isEmpty()){
			String boxCode = packagingList.get(0).getCode2();

			LogisticsTrackInfo info = new LogisticsTrackInfo();
			StringBuilder sb = new StringBuilder();
			sb.append("产品激活");
			info.setMessage(sb.toString());
			info.setMsgDate(new SimpleDateFormat("yyyy-MM-dd").format(packagingList.get(0).getScantime()));
			info.setMsgTime(new SimpleDateFormat("HH:mm").format(packagingList.get(0).getScantime()));
			info.setType("oemProductPackageCode");
			
			logisticsInfoList.add(info);
		}
		OemDelivery delivery = oemDeliveryMapper.queryDeliveryByCode(logisticsCode);
		if(delivery != null){
			String recvName = delivery.getRecvName().substring(1, delivery.getRecvName().length() -1);

			OemDeliveryProductExample oemDeliveryProductExample = new OemDeliveryProductExample();
			oemDeliveryProductExample.createCriteria().andCodeEqualTo(logisticsCode);
			List<OemDeliveryProduct> oemDeliveryProductList = oemDeliveryProductMapper.selectByExample(oemDeliveryProductExample);
			if(oemDeliveryProductList != null && oemDeliveryProductList.size() > 0){
				OemDeliveryProduct oemDeliveryProduct = oemDeliveryProductList.get(0);
				
				LogisticsTrackInfo info = new LogisticsTrackInfo();
				StringBuilder sb = new StringBuilder();
				sb.append("产品出库到：");
				sb.append(recvName);
				info.setMessage(sb.toString());
				info.setMsgDate(new SimpleDateFormat("yyyy-MM-dd").format(oemDeliveryProduct.getScantime()));
				info.setMsgTime(new SimpleDateFormat("HH:mm").format(oemDeliveryProduct.getScantime()));
				info.setType("oemDelivery");
				
				logisticsInfoList.add(info);
			}else{
				if(packagingList != null && packagingList.size() > 0){
					String boxCode = packagingList.get(0).getCode2();
					if(!logisticsCode.equals(boxCode)){
						oemDeliveryProductExample.clear();
						oemDeliveryProductExample.createCriteria().andCodeEqualTo(boxCode);
						oemDeliveryProductList = oemDeliveryProductMapper.selectByExample(oemDeliveryProductExample);
						if(oemDeliveryProductList != null && oemDeliveryProductList.size() > 0){
							OemDeliveryProduct oemDeliveryProduct = oemDeliveryProductList.get(0);
							
							LogisticsTrackInfo info = new LogisticsTrackInfo();
							StringBuilder sb = new StringBuilder();
							sb.append("产品出库到：");
							sb.append(recvName);
							info.setMessage(sb.toString());
							info.setMsgDate(new SimpleDateFormat("yyyy-MM-dd").format(oemDeliveryProduct.getScantime()));
							info.setMsgTime(new SimpleDateFormat("HH:mm").format(oemDeliveryProduct.getScantime()));
							info.setType("oemDelivery");
							
							logisticsInfoList.add(info);
						}
					}
				}
				
			}
			
		}
		if((logisticsCode!=null)&&(!logisticsCode.equals(""))) {
			//获取出入库信息
			Map<String,Object> stockMap = appStockService.getStockDetailByCode(logisticsCode);
			if(stockMap.get(Constants.RESULT_CODE_KEY).equals(Constants.SUCCESS_CODE)) {
				List<LogisticsStockVo> stockList = (List<LogisticsStockVo>)stockMap.get("detailList");
				for(LogisticsStockVo vo:stockList) {
					LogisticsTrackInfo info = new LogisticsTrackInfo();
					StringBuilder sb = new StringBuilder();
					sb.append(vo.getPartnerName());
					if (vo.getType().equals("out_stock")) {
						sb.append("出库");
					}else if(vo.getType().equals("in_stock")) {
						sb.append("入库");
					}
					sb.append("该瓶机油，操作员：");
					if(vo.getScanPersonName()!=null) {
						sb.append(vo.getScanPersonName());
					}
					info.setMessage(sb.toString());
					info.setMsgDate(new SimpleDateFormat("yyyy-MM-dd").format(vo.getCreateDate()));
					info.setMsgTime(new SimpleDateFormat("HH:mm").format(vo.getCreateDate()));
					info.setType("inventory");
					
					logisticsInfoList.add(info);
				}
				//加入出入库物流信息
				if(stockList.size()>=1) {
					resultMap.put("storage", stockList.get(0).getPartnerName());
				}
				if(stockList.size()>=2) {
					resultMap.put("partner", stockList.get(1).getPartnerName());
				}if(stockList.size()>=4) {
					resultMap.put("workshop", stockList.get(3).getPartnerName());
				}
			} 
		//获取核销信息
//		if(logisticsInfoList.size()>=4) {
			Map<String,Object> mechanicMap = getMechanicQrcodeByTraceNo(logisticsCode);
			if(Constants.SUCCESS_CODE.equals(mechanicMap.get(Constants.RESULT_CODE_KEY))) {
				MechanicQrcode mq = (MechanicQrcode)mechanicMap.get(Constants.RESULT_DATA_CODE);
				if(mq!=null) {
					LogisticsTrackInfo info = new LogisticsTrackInfo();
					StringBuilder sb = new StringBuilder("【").append(mq.getPartnerName()).append("】【");
					sb.append(mq.getWorkshopName());
					sb.append("】【");
					sb.append(mq.getMechanicName());
					sb.append("】核销了该瓶机油");
					info.setMessage(sb.toString());
					info.setMsgDate(new SimpleDateFormat("yyyy-MM-dd").format(mq.getCreationTime()));
					info.setMsgTime(new SimpleDateFormat("HH:mm").format(mq.getCreationTime()));
					info.setType("verify");
					logisticsInfoList.add(info);
				}
			}
		}
		
//		//test qrCode
//		qrCode="008c6667d93b48edbd14edf11290";
		Map<String,Object> openidMap = qrCodeService.getUserQrcodeByQrcode(qrCode);
		UserQrcode uq = (UserQrcode)openidMap.get("userQrcode");
		
		if(uq!=null) {
			//处理返回数据
			String customerName=etoBizService.getWechatNickName(uq.getUserWechatOpenid());
			LogisticsTrackInfo info = new LogisticsTrackInfo();
			StringBuilder sb = new StringBuilder();
			sb.append("顾客【");
			if((customerName!=null)&&(!customerName.equals("null"))) {
				sb.append(customerName);
			}
			sb.append("】在手机上扫了该码进行验真");
			
			info.setMessage(sb.toString());
			info.setMsgDate(new SimpleDateFormat("yyyy-MM-dd").format(uq.getCreationTime()));
			info.setMsgTime(new SimpleDateFormat("HH:mm").format(uq.getCreationTime()));
			info.setType("check");
			logisticsInfoList.add(info);
//			sb.append("10");
//			sb.append("积分");
		}
		resultMap.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		resultMap.put(Constants.RESULT_DATA_CODE, logisticsInfoList);
		
	}
	
	@Override
	public ResponseMap getLogisticsInfo(String code,String type) {
		ResponseMap resultMap = new ResponseMap();
		log.info("getLogisticsInfo: " + code + "," + type);

		try {
			buildLogisticsInfo(code, type, resultMap);
			log.info("getLogisticsInfo success." );
		} catch (WxPltException e) {
			resultMap.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
			resultMap.setErrorMsg(e.getMessage());
		} catch (Exception e) {
			resultMap.handleException(e, this, ContextUtil.getCurUserId(), 
					"com.sys.auth.service.impl.AnonRpcServiceImpl.getLogisticsInfo", code + "," + type);
		}
		return resultMap;
	}
	
//	public static void main(String[] args) {
//		Map<String, String> postData = new TreeMap<String, String>();
//		postData.put("openid", "oQlflwbjBcdmKzJiv0RPF4H9d_CU");
//		StringBuilder tokenStr = null;
//		for(String k : postData.keySet()){
//			if(tokenStr == null) {
//				tokenStr = new StringBuilder();
//			}else {
//				tokenStr.append("&");
//			}
//			tokenStr.append(k).append("=").append(postData.get(k));
//		}
//		log.info("tokenStr: {}", tokenStr.toString());
//		String sign = DigestUtils.md5Hex(tokenStr.toString());
//		postData.put("sign", sign);
//		log.info("getO2ORegisterPostData params: " + JSONObject.fromObject(postData).toString());
//		Map<String, String> httpHeader = new HashMap<String, String>();
//		httpHeader.put("Content-Type", "application/x-www-form-urlencoded;charset=utf-8");
//		String responseContent = HttpSender.post("https://scrmbackend.techronworks.cn/dwaiter/api/user/findFansOne/chevron_shopping", postData, null, httpHeader);
//		log.info("responseContent: " + responseContent);
//		JSONObject responseJsonObj = JSONObject.fromObject(responseContent);
//		System.out.println(responseJsonObj.getJSONObject("data"));
////		if (responseContent == null){
////			throw new WxPltException(MessageResourceUtil.getMessage("system.mobile.unexpected_exception"));
////		}
//
//	}

	@Override
	public Map<String, Object> getWeixinSdkSignature(String pageUrl) {
		Map<String,Object> resultMap = new HashMap<String, Object>();
		
		try {
			String wxSdkSignatureUrl = (String) Constants.getSystemPropertyByCodeType(Constants.WX_SDK_SIGNATURE_URL);
			Map<String, Object> postData = new HashMap<String, Object>();

			// 返回结果
			postData.put("spid", "-1");
			postData.put("pageUrl", pageUrl);
			String responseContent = HttpSender.postJSON(wxSdkSignatureUrl + "/common/getJsapiSignature", postData,
					null);

			// 处理返回数据
			JSONObject data = null;
			JSONObject responseJsonObj = JSONObject.fromObject(responseContent);
			if (responseJsonObj.get("code").equals("1")) {
				data = responseJsonObj.getJSONObject("data");
			}

			resultMap.put("code", "success");
			resultMap.put("data", data);
		} catch (Exception e) {
			resultMap.put("code", "error");
			e.printStackTrace();
		}
		return resultMap;
	}

	/*
	@Override
	public Map<String, Object> getUserInfoByQrCode(String qrCode) {
		List<LogisticsTrackInfo> infoList  = new ArrayList<LogisticsTrackInfo>();
		Map<String,Object> resultMap = new HashMap<String,Object>();
		Map<String,Object> openidMap = qrCodeService.getUserQrcodeByQrcode(qrCode);
		UserQrcode uq = (UserQrcode)openidMap.get("userQrcode");
		
		if(uq!=null) {
			
//			调用微信服务号的接口
//			String openId = uq.getUserWechatOpenid();
			
//			//用现有的openid
			String openId="oAPZ2wNqsWg3pc65b6MeA-YsNNqc";
			
			String wxUserInfoUrl = (String) Constants.getSystemPropertyByCodeType(Constants.WX_USERINFO_URL);
			String o2oKey = (String) Constants.getSystemPropertyByCodeType(Constants.O2O_SERVICE_REGISTER_MECHANIC_KEY);
			Map<String, String> httpHeader = new HashMap<String, String>();
			Map<String,String> postData = new HashMap<String,String>();
			
			postData.put("openid", openId);
			postData.put("token", DigestUtils.md5Hex(openId+o2oKey));
			httpHeader.put("Content-Type", "application/x-www-form-urlencoded;charset=utf-8");
			String responseContent = HttpSender.postJSON(wxUserInfoUrl, postData, httpHeader);
			
			//处理返回数据
			String customerName="";
			String customerPhone="";
			JSONObject responseJsonObj = JSONObject.fromObject(responseContent);
			if(responseJsonObj.getJSONObject("userinfo")!=null) {
				JSONObject userInfoJson = responseJsonObj.getJSONObject("userinfo");
				customerName=userInfoJson.get("nickname").toString();
				customerPhone=userInfoJson.get("phone").toString();
			}
			LogisticsTrackInfo info = new LogisticsTrackInfo();
			StringBuilder sb = new StringBuilder();
			sb.append("顾客");
			if((customerName!=null)&&(!customerName.equals("null"))) {
				sb.append(customerName);
			}
			sb.append("在手机上扫了该码进行验真");
			if((customerPhone!=null)&&(!customerPhone.equals("null")&&(!customerPhone.equals("")))) {
				sb.append(" 电话："+customerPhone);
			}
			info.setMessage(sb.toString());
			info.setMsgDate(new SimpleDateFormat("yyyy-MM-dd").format(uq.getCreationTime()));
			info.setMsgTime(new SimpleDateFormat("HH:mm").format(uq.getCreationTime()));
			info.setType("check");
			infoList.add(info);
		}
		resultMap.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		resultMap.put(Constants.RESULT_DATA_CODE, infoList);
		return resultMap;
	}
	*/
	

	@Override
	public Map<String, Object> getWorkshopListForInsertMerchanic(
			String workshopName) {
		log.info("getWorkshopListForInsertMerchanic workshopName:{}",workshopName);
		Map<String,Object> resultMap = new HashMap<String,Object>();
		try
		{
			Map<String,Object> reqMap = new HashMap<String,Object>();
			reqMap.put("workshopName", workshopName);
			Map<String,String> apiConfigMap = doGetApiConfigInfo();
			//门店业务线
			String workshoptBusinessType = (String)(apiConfigMap.get(ScanCodeOrderServiceImpl.WORKSHOP_BUSINESS_TYPE)==null?
					ScanCodeOrderServiceImpl.WORKSHOP_BUSINESS_TYPE_VALUE:apiConfigMap.get(ScanCodeOrderServiceImpl.WORKSHOP_BUSINESS_TYPE));
			Long workshopPartnerId = null;//ScanCodeOrderServiceImpl.WORKSHOP_PARTNER_ID_VAlUE;
			String partnerIdStr = (String)apiConfigMap.get(ScanCodeOrderServiceImpl.WORKSHOP_PARTNER_ID_TYPE);
			if(null!=partnerIdStr)
			{
				workshopPartnerId = Long.parseLong(partnerIdStr);
			}
			int channelWeight = Constants.getChannelWeight(workshoptBusinessType);
//			reqMap.put("businessLineType", workshoptBusinessType);
			reqMap.put("workshopName", workshopName);
			reqMap.put("partnerId", workshopPartnerId);
			reqMap.put("amStatus", new Integer[] {1, 3});
			reqMap.put("businessWeight", channelWeight & 3);
			reqMap.put("customerType", WorkshopMaster.CUSTOMER_TYPE1);
			List<WorkshopMaster> lst = workshopMasterBizService.querySimpleByParams(reqMap);
			List<ResponseWorkshopInfo> list = new ArrayList<ResponseWorkshopInfo>(lst.size());
			for(WorkshopMaster workshopMaster : lst) {
				list.add(new ResponseWorkshopInfo(workshopMaster));
			}
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
			resultMap.put(Constants.RESULT_LST_KEY, lst);
		}catch (Exception e) {
			e.printStackTrace();
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			resultMap.put(Constants.SYSTEM_UNEXPECTED_EXCEPTION, e.getLocalizedMessage());
			return resultMap;
		}
		return resultMap;
	}
	
	/**
	 * 录入技师及同步到积分商城
	 */
	@Override
	@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
	public Map<String, Object> insertMerchanicAndSyncToPointPlatform(String partnerName,Long partnerId,
			String workshopName, Long workshopId, String merchanicName,
			String merchanicPhone) {
		log.info("insertMerchanicAndSyncToPointPlatform partnerName:{}, partnerId:{}, workshopName:{},workshopId:{},merchanicName:{},merchanicPhone:{}"
				,partnerName,partnerId,workshopName,workshopId,merchanicName,merchanicPhone);
		Map<String,Object> resultMap = new HashMap<String,Object>();
		try
		{
			//获取api配置
			Map<String,String> apiConfigMap  = doGetApiConfigInfo();
			apiConfigMap.put("workshopName", workshopName);
			apiConfigMap.put("workshopId", ""+workshopId);
			apiConfigMap.put("merchanicName", merchanicName);
			apiConfigMap.put("merchanicPhone", merchanicPhone);
			apiConfigMap.put("partnerId", ""+partnerId);
			apiConfigMap.put("partnerName",partnerName);
			
			//构造employee
			WorkshopEmployee employee = doCreateEmployee(apiConfigMap);
			
			//根据手机号判断PP平台是否存在
			WorkshopEmployee existEmployee= workshopBizService.checkEmployeeExist(employee);
			/*//直接显示删除PP平台的数据，然后再同步
			Map<String,Object> reqMap = new HashMap<String,Object>();
			reqMap.put("workshopId", workshopId);
			reqMap.put("merchanicPhone", merchanicPhone);
			workshopEmployeeMapper.deleteWorkshopEmployee(reqMap);*/
			
			//注册技师到企业号
			//employee = doRegisterMerchanic(apiConfigMap,employee);
			if(null==existEmployee)
			{
				employee = doRegisterMerchanic(apiConfigMap,employee);
			}else 
			{
				employee = doUpdateMerchanic(apiConfigMap,existEmployee,employee);
			}
			
			//同步技师到积分平台
			doSyncUserInfoToPointsPlatform(employee);
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		}catch (Exception e) {
			e.printStackTrace();
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly(); 
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			resultMap.put(Constants.SYSTEM_UNEXPECTED_EXCEPTION, e.getLocalizedMessage());
			return resultMap;
		}
		return resultMap;
	}
	
	private void doSyncUserInfoToPointsPlatform(WorkshopEmployee employee)throws Exception {
		log.info("doSyncUserInfoToPointsPlatform employee:{}",employee.toString());
		//根据workshopid  获取  门店信息
		Map<String, Object> params = new HashMap<String, Object>(5);
		params.put("id", employee.getWorkshopId());
		WorkshopMaster workshop = workshopMasterBizService.querySimpleByParams(params).get(0);
		//构造同步积分的数据
		ReqSyncUserInfo reqSyncUser =  new ReqSyncUserInfo();
		reqSyncUser.setAddress(workshop.getWorkshopAddress());
		reqSyncUser.setProvince(workshop.getProvinceName());
		reqSyncUser.setCity(workshop.getCityName());
		reqSyncUser.setRural(workshop.getRegionName());
		reqSyncUser.setRegistrationtime(DateUtil.getDateStr(new Date()));
		reqSyncUser.setShopname(employee.getWorkshopName());
		reqSyncUser.setShopno(""+employee.getWorkshopId());
		reqSyncUser.setName(employee.getName());
		reqSyncUser.setMobile(employee.getMobile());
		reqSyncUser.setUid(employee.getCode());
		log.info("doSyncUserInfoToPointsPlatform reqSyncUser:{}",JsonUtil.writeValue(reqSyncUser));
		scanCodeOrderService.doSyncInfo(reqSyncUser, ScanCodeOrderServiceImpl.method_userinfosync);
	}

	private WorkshopEmployee doUpdateMerchanic(
			Map<String, String> apiConfigMap, WorkshopEmployee existEmployee,
			WorkshopEmployee employee)throws Exception {
		existEmployee.setWorkshopId(employee.getWorkshopId());
		existEmployee.setWorkshopName(employee.getWorkshopName());
		existEmployee.setWechatAccount(employee.getWechatAccount());
		existEmployee.setName(employee.getName());
		existEmployee.setEmployeeType(employee.getEmployeeType());
		existEmployee.setUpdateTime(DateUtil.getCurrentDate());
		existEmployee.setUpdator(employee.getCreator());
		existEmployee.setOldPartnerId(existEmployee.getPartnerId());
		existEmployee.setPartnerId(employee.getPartnerId());
		existEmployee.setPartnerName(employee.getPartnerName());
		existEmployee.setCreatorName(employee.getCreatorName());
		existEmployee.setCreator(employee.getCreator());
		log.info("doUpdateMerchanic existEmployee:{}",JsonUtil.writeValue(existEmployee));
		workshopEmployeeMapper.updateByPrimaryKeySelective(existEmployee);
	
		String host = apiConfigMap.get(O2O_SERVICE_API_URL_TYPE)==null?O2O_SERVICE_API_URL_VALUE:apiConfigMap.get(O2O_SERVICE_API_URL_TYPE);
		String method = apiConfigMap.get(O2O_SERVICE_API_UPDATE_MECHANIC_TYPE)==null?O2O_SERVICE_API_UPDATE_MECHANIC_VALUE:apiConfigMap.get(O2O_SERVICE_API_UPDATE_MECHANIC_TYPE);
		String o2oUpdateRegisterMechanicUrl = host+method;
		Map<String, String> postData = getO2ORegisterPostData(apiConfigMap,existEmployee);
		Map<String, String> httpHeader = new HashMap<String, String>();
		httpHeader.put("Content-Type", "application/x-www-form-urlencoded;charset=utf-8");
		String responseContent = HttpSender.post(o2oUpdateRegisterMechanicUrl, postData, null, httpHeader);
		log.info("responseContent: " + responseContent);
		if (responseContent == null){
			throw new WxPltException(MessageResourceUtil.getMessage("system.mobile.unexpected_exception"));
		}
		JSONObject responseJsonObj = JSONObject.fromObject(responseContent);
		if (responseJsonObj.getInt("code") == 0){
			throw new WxPltException(responseJsonObj.getString("message"));
		}
		return existEmployee;
	}

	private WorkshopEmployee doRegisterMerchanic(Map<String,String> apiMap, WorkshopEmployee employee)throws Exception {
		employee.setCode(CommonUtil.generateCode("M"));
		log.info("doRegisterMerchanic employee:{}",JsonUtil.writeValue(employee));
		workshopEmployeeMapper.insertSelective(employee);
		
		String host = apiMap.get(O2O_SERVICE_API_URL_TYPE)==null?O2O_SERVICE_API_URL_VALUE:apiMap.get(O2O_SERVICE_API_URL_TYPE);
		String method = apiMap.get(O2O_SERVICE_API_REGISTERMECHANIC_TYPE)==null?O2O_SERVICE_API_REGISTERMECHANIC_VALUE:apiMap.get(O2O_SERVICE_API_REGISTERMECHANIC_TYPE);
		String o2oRegisterMechanicUrl = host + method;
		
		Map<String, String> postData = getO2ORegisterPostData(apiMap,employee);
		Map<String, String> httpHeader = new HashMap<String, String>();
		httpHeader.put("Content-Type", "application/x-www-form-urlencoded;charset=utf-8");
		String responseContent = HttpSender.post(o2oRegisterMechanicUrl, postData, null, httpHeader);
		log.info("responseContent: " + responseContent);
		if (responseContent == null){
			throw new WxPltException(MessageResourceUtil.getMessage("system.mobile.unexpected_exception"));
		}
		JSONObject responseJsonObj = JSONObject.fromObject(responseContent);
		if (responseJsonObj.getInt("code") == 0){
			throw new WxPltException(responseJsonObj.getString("message"));
		}
		return employee;
	}
	


	private WorkshopEmployee doCreateEmployee(Map<String, String> apiConfigMap)throws Exception {
		WorkshopEmployee employee = new WorkshopEmployee();
		employee.setMobile(apiConfigMap.get("merchanicPhone"));
		employee.setWechatAccount("");
		employee.setCreationTime(new Date());
		employee.setCreator(Constants.ADMINISTRATOR_ID);
		employee.setCreatorName(Constants.ADMINISTRATOR_NAME);
		employee.setEmployeeType(WorkshopEmployeeBizService.EMPLOYEE_TYPE_OWNER);
		employee.setName(apiConfigMap.get("merchanicName"));
		//TODO 如果后续其他合伙人也可以，，，那么这个地方应该从前端传递合伙人id过来  add by bo.liu 180905
		Long workshopPartnerId = Long.parseLong(apiConfigMap.get("partnerId")); //ScanCodeOrderServiceImpl.WORKSHOP_PARTNER_ID_VAlUE;
		String partnerIdStr = apiConfigMap.get(ScanCodeOrderServiceImpl.WORKSHOP_PARTNER_ID_TYPE);
		if(null!=partnerIdStr)
		{
			workshopPartnerId = Long.parseLong(partnerIdStr);
		}
		
		String partnerName = apiConfigMap.get("partnerName");//ScanCodeOrderServiceImpl.WORKSHOP_PARTNER_NAME_VAlUE;
		String tmppartnerName = (String)apiConfigMap.get(ScanCodeOrderServiceImpl.WORKSHOP_PARTNER_NAME_TYPE);
		if(null!=tmppartnerName)
		{
			partnerName = tmppartnerName;
		}
		employee.setPartnerId(workshopPartnerId);
		employee.setPartnerName(partnerName);
		employee.setWorkshopId(Long.parseLong(apiConfigMap.get("workshopId")));
		employee.setWorkshopName(apiConfigMap.get("workshopName"));
		log.info("doCreateEmployee employee:{}",JsonUtil.writeValue(employee));
		return employee;
	}

	private Map<String, String> getO2ORegisterPostData(Map<String,String> apiMap, WorkshopEmployee employee)throws Exception {//TODO
//		String key = apiMap.get(O2O_SERVICE_API_REGISTERMECHANIC_KEY_TYPE)==null?O2O_SERVICE_API_REGISTERMECHANIC_KEY_VALUE:apiMap.get(O2O_SERVICE_API_REGISTERMECHANIC_KEY_TYPE);
		String departmentId = apiMap.get(O2O_SERVICE_API_REGISTERMECHANIC_DEPARTMENT_TYPE)==null?O2O_SERVICE_API_REGISTERMECHANIC_DEPARTMENT_VALUE:apiMap.get(O2O_SERVICE_API_REGISTERMECHANIC_DEPARTMENT_TYPE);
//		log.info("getO2ORegisterPostData key: " + key);
		String userid = employee.getCode();
		String userNmae = employee.getName();
		String userMobile = employee.getMobile();
		String wechatId = employee.getWechatAccount();
		String workshopId = ""+employee.getWorkshopId();
		String workshopName = employee.getWorkshopName();
		String partnerName = employee.getPartnerName();
		String partnerId = "Delo"+employee.getPartnerId();
		String creator = employee.getCreatorName();
		String employeeType = employee.getEmployeeType();
		String oldPartnerId = "Delo"+employee.getOldPartnerId();
		String departmetnId = departmentId;// 配置
//		StringBuilder tokenStr = new StringBuilder();
//		tokenStr.append(userid).append(userNmae).append(userMobile).append(wechatId).append(workshopId)
//				.append(workshopName).append(partnerName).append(partnerId).append(creator).append(employeeType).append(departmetnId);
//		if(employee.getOldPartnerId() != null){
//			tokenStr.append(oldPartnerId);
//		}
//		tokenStr.append(key);
//		log.info("tokenStr: {}", tokenStr.toString());
//		String token = DigestUtils.md5Hex(tokenStr.toString());
		Map<String, String> postData = new TreeMap<String, String>();
		postData.put("userid", userid);
		postData.put("name", userNmae); 
		postData.put("department", departmetnId);
		postData.put("position", employeeType);
//		postData.put("cellphone", userMobile);
		postData.put("mobile", userMobile);
//		postData.put("wechatid", wechatId);
		postData.put("weixinid", wechatId);
		postData.put("wsid", workshopId);
		postData.put("wsname", workshopName);
		postData.put("sp", partnerName);
		postData.put("bd", creator);
		postData.put("spid", partnerId);
		if(employee.getOldPartnerId() != null){
			postData.put("oldspid", oldPartnerId);
		}
//		postData.put("token", token);
		StringBuilder tokenStr = null;
		for(String k : postData.keySet()){
			if(tokenStr == null) {
				tokenStr = new StringBuilder();
			}else {
				tokenStr.append("&");
			}
			tokenStr.append(k).append("=").append(postData.get(k));
		}
		log.info("tokenStr: {}", tokenStr.toString());
		String sign = DigestUtils.md5Hex(tokenStr.toString());
		postData.put("sign", sign);
		log.info("getO2ORegisterPostData params: " + JSONObject.fromObject(postData).toString());
		return postData;
	}

	/**
	 * 扫码枪扫码生成积分的接口配置  / 注册技师的接口配置   企业号
	 * <AUTHOR> 2018-8-29 下午2:51:07
	 * @return
	 */
	private Map<String, String> doGetApiConfigInfo() {
		//获取api基本配置信息：
		Map<String, String> returnApiConfigMap = new HashMap<String,String>();
		
		//积分平台api
		Map<String, Object> scanGanApiConfigInfo =  dicService.getDicItemByDicTypeCode("scanorder.syncpoint.api");
		List<DicItemVo> scanGanApiConfigInfoLst = (ArrayList)scanGanApiConfigInfo.get("data");
		for(DicItemVo dicItem : scanGanApiConfigInfoLst){
			returnApiConfigMap.put(dicItem.getDicItemCode(),  dicItem.getDicItemName());
		}
		
		//O2O技师api
		Map<String, Object> o2oApiConfigInfo =  dicService.getDicItemByDicTypeCode("o2o_service.api");
		List<DicItemVo> o2oApiConfigInfoLst = (ArrayList)o2oApiConfigInfo.get("data");
		for(DicItemVo dicItem : o2oApiConfigInfoLst){
			returnApiConfigMap.put(dicItem.getDicItemCode(),  dicItem.getDicItemName());
		}
		return returnApiConfigMap;
	}
	
	@Override
	public Map<String, Object> synchroWorkshopPointsRecord(
			WorkshopPointsRecordView[] recordList) {
		Map<String, Object> returnApiConfigMap = new HashMap<String,Object>();
		ArrayList<WorkshopPointsRecordView> arrayList = new ArrayList<WorkshopPointsRecordView>(Arrays.asList(recordList));
		returnApiConfigMap = workshopPointsRecordService.synchroWorkshopPointsRecord(arrayList);
		return returnApiConfigMap;
	}

	@Override
	public JsonResponse getScheduleShareInfo(Long id, String clientId) {
		return scheduleInstanceService.getScheduleShareInfo(id, clientId);
	}

	@Override
	public JsonResponse getScheduleShareDetail(Long id, String clientId) {
		return scheduleInstanceService.getScheduleShareDetail(id, clientId);
	}
	
	public JsonResponse getUserInfoByCode(String code) {
		 String openId = HttpClientUtils.getOpenId(code);
	     JsonResponse result= userService.boundCheck(openId);
	     result.put("openId", openId);
		return result;
	}
	
	public JsonResponse getWexinSignature() {
		return o2oOrderService.getWexinSignature();
	}
	
	public JsonResponse getLoginB2B(String code,String openIdUrl,String appId,String openId) {
		//根据appId 查询 secret
		if(!openId.isEmpty()) {
			JsonResponse result= userService.b2bBoundCheck(openId);
			result.put("openId", openId);
			return result;
		}else {
			String secret = "";
	    	Db2bDistSettingExample example = new Db2bDistSettingExample();
	    	example.createCriteria().andAppIdEqualTo(appId);
	    	 List<Db2bDistSetting> setList = db2bDistSettingMapper.selectByExample(example);
	    	 if(setList==null || setList.isEmpty() || setList.size()<=0) {
	    	 }else {
	    		 secret = setList.get(0).getAppSecret();
	    	 }
			 openId = HttpClientUtils.getB2bOpenId(code,openIdUrl,appId,secret);
		     JsonResponse result= userService.b2bBoundCheck(openId);
		     result.put("openId", openId);
		     return result;
		}
	}




	@Override
	public Map<String, Object> getProvinces() {
		return regionService.findRegionInfo(-1l);
	}

	@Override
	public Map<String, Object> getCitiesByProvince(Long provinceId) {
		return regionService.findCities(provinceId);
	}

	@Override
	public Map<String, Object> getRegionsByCity(Long cityId) {
		return regionService.findDistricts(cityId);
	}
}
