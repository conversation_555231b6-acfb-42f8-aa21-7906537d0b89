package com.chevron.elites2.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;

import com.chevron.elites2.base.BaseResp;
import com.chevron.elites2.dao.*;
import com.chevron.elites2.dto.request.CustomerParams;
import com.chevron.elites2.model.DwCustomerRegionSalesSupervisorRel;
import com.chevron.elites2.model.DwPpCustomerCompanyFundQuarter;
import com.chevron.elites2.model.WxTRebateApply2;
import com.chevron.elites2.model.excel.RebateDetailExcel;
import com.chevron.elites2.model.excel.RebateSalesExcel;
import com.chevron.elites2.model.fund.RebateFund;
import com.chevron.elites2.service.RebateApply2Service;
import com.chevron.elites2.service.RebateAudit2Service;
import com.chevron.elites2.service.RebateExport2Service;
import com.chevron.elites2.service.RebateFund2Service;
import com.chevron.elites2.util.RebateUtil;
import com.chevron.exportdata.ExportCol;
import com.chevron.exportdata.ExportColAlign;
import com.chevron.exportdata.IExportCol;
import com.chevron.exportdata.IPropertyHelper;
import com.chevron.exportdata.PoiWriteExcel;
import com.chevron.exportdata.PoiWriteExcel.ExportSheet;
import com.common.constants.Constants;
import com.common.exception.WxPltException;
import com.common.util.CommonUtil;
import com.common.util.ContextUtil;
import com.common.util.DateUtil;
import com.common.util.FileUtil;
import com.common.util.MessageResourceUtil;
import com.sys.auth.model.WxTUser;
import com.sys.dic.dao.WxSystemConfigMapper;
import com.sys.dic.model.DicItemVo;
import com.sys.dic.model.WxSystemConfig;
import com.sys.utils.business.ReportViewBizService;
import com.sys.utils.model.AsynProcessStatus;
import freemarker.template.Configuration;
import freemarker.template.Template;

import org.apache.ibatis.annotations.Param;
import org.apache.tools.zip.ZipEntry;
import org.apache.tools.zip.ZipOutputStream;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.WebApplicationContext;

import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.util.*;

@Controller
@RequestMapping(value = "/elites2/export")
public class RebateExport2Controller {

    public static final Logger log = LoggerFactory.getLogger(RebateExport2Controller.class);

    @Autowired
    private RebateExport2Service rebateExport2Service;

    @Autowired
    private RebateApply2Service rebateApply2Service;

    @Autowired
    private RebateAudit2Service rebateAudit2Service;

    @Autowired
    private WxTRebateApply2Mapper rebateApplyDao;

    @Autowired
    private WXTRebateAudit2Mapper wxtRebateAudit2Mapper;

    @Autowired
    private WXTRebateRelated2Mapper wxtRebateRelated2Mapper;

    @Autowired
    private DwPpCustomerFundQuarterNewMapper dwPpCustomerFundQuarterNewMapper;

    @Autowired
    private DwCustomerRegionSalesSupervisorRelV2Mapper dwCustomerRegionSalesSupervisorRelV2Mapper;

    @Autowired
    private DwPpCustomerCompanyFundQuarterMapper dwPpCustomerCompanyFundQuarterMapper;

    @Autowired
    private RebateFund2Service rebateFund2Service;

    @Autowired
    private WxSystemConfigMapper wxSystemConfigMapper;
    
    @Autowired
    private ReportViewBizService reportViewBizService;

    @ResponseBody
    @RequestMapping(value = "/partnerList.do", produces = "application/json")
    public Map<String, Object> partnerList(CustomerParams params, HttpServletRequest request) {
        Map<String, Object> resultMap = new HashMap<String, Object>(5);
        try {
            log.debug("rebate page start, param:" + params.toString());
            WxTUser user = ContextUtil.getCurUser();
            params.setCai(user != null ? user.getCai() : null);

            BaseResp rebateRoleCode = rebateApply2Service.getRebateRoleCode(null);
            String permissionCode = (String) rebateRoleCode.getData();
            params.setPermissionCode(permissionCode);
            resultMap.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);

            List<String> regionList = null;
            if (StrUtil.equalsIgnoreCase(permissionCode, "partner")) {
                resultMap.put(Constants.RESULT_LST_KEY, new ArrayList<DwCustomerRegionSalesSupervisorRel>());
                resultMap.put(Constants.RESULT_TOTAL_KEY, 0);
            } else {
                if (StrUtil.equalsIgnoreCase(permissionCode, "flsrAudit")) {//FLSR
                } else if (StrUtil.equalsAnyIgnoreCase(permissionCode, "asmAudit", "flsrAudit,asmAudit", "areaAudit")) {//ASM
                } else if (StrUtil.equalsIgnoreCase(permissionCode, "channelAudit")) {//CM
                } else if (StrUtil.equalsIgnoreCase(permissionCode, "bsNorth")) {//BS
                    regionList = CollUtil.newArrayList("C&I-North", "C&I-NE&NW", "CDM-North", "CDM-NE&NW");
                } else if (StrUtil.equalsIgnoreCase(permissionCode, "bsSouth")) {//BS
                    regionList = CollUtil.newArrayList("C&I-South", "CDM-South");
                } else if (StrUtil.equalsIgnoreCase(permissionCode, "bsSW")) {//BS
                    regionList = CollUtil.newArrayList("C&I-SW", "CDM-SW");
                } else if (StrUtil.equalsAnyIgnoreCase(permissionCode, "bsmAudit", "sdoAudit", "abmAudit") || user.getUserId() == 1L) {
                }
                params.setRegionList(regionList);
                List<DwCustomerRegionSalesSupervisorRel> list = dwCustomerRegionSalesSupervisorRelV2Mapper.getCustomerListPage(params);
                Map<String, Object> paramMap = new HashMap<String, Object>(3);
                paramMap.put("salesChannel", params.getSalesChannel());
            	List<Map<String, Object>> allData = reportViewBizService.loadData("elitefund", "FundReimbursementExport2019q3q4", paramMap);
            	Set<Long> existsPartner = new HashSet<Long>(allData.size());
            	for(Map<String, Object> data : allData) {
            		existsPartner.add(((Number)data.get("org_id")).longValue());
            	}
                for(DwCustomerRegionSalesSupervisorRel rel : list) {
                	if(existsPartner.contains(rel.getDistributorId())) {
                		rel.setFlag(1);
                	}
                }
                resultMap.put(Constants.RESULT_LST_KEY, list);
                resultMap.put(Constants.RESULT_TOTAL_KEY, params.getTotalCount());
            }
        } catch (Exception e) {
            resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
            resultMap.put(Constants.RESULT_ERROR_MSG_KEY,
                    MessageResourceUtil.getMessage("system.unexpected_exception"));
            log.error("queryForPage fail. " + e.getMessage(), e);
        }
        return resultMap;
    }

//    @RequestMapping(value = "/partnerFileExport.do")
//    public String partnerFileExport(@RequestParam("orgId") Long orgId, @RequestParam("times") Integer times, HttpServletRequest request, HttpServletResponse response) {
//        try {
//            BaseResp resp = generateExportTemlateFiles(orgId, times);
//            if (!StrUtil.equals("0000", resp.getCode()))
//                throw new Exception(resp.getMessage());
//
//            File file = (File) resp.getData();
//            InputStream inputStream = new FileInputStream(file);
//
//            FileUtil.download(response, inputStream, file.getName());
//        } catch (Exception e) {
//            log.error("partnerFileExport exception:", e);
//            request.setAttribute("errorMsg", "导出文件异常: " + e.getMessage());
//            return "forward:/common/jsp/downloadError.jsp";
//        }
//        return null;
//    }

    @RequestMapping(value = "/partnerFileExport.do")
    public String partnerFileExport(@RequestParam("orgId") Long orgId, @RequestParam("salesChannel") String salesChannel, 
    		HttpServletRequest request, HttpServletResponse response) {
		log.info("partnerFileExport start.");
        try {
        	salesChannel = URLDecoder.decode(salesChannel, "UTF-8");
        	Map<String, Object> params = new HashMap<String, Object>(3);
        	params.put("orgId", orgId);
        	params.put("salesChannel", salesChannel);
        	List<Map<String, Object>> allData = reportViewBizService.loadData("elitefund", "FundReimbursementExport2019q3q4", params);
			if("CDM".equals(salesChannel)) {
				salesChannel = "Consumer";
			}else {
				salesChannel = "Commerical&Industrial";
			}
            ServletContext context = ContextLoader.getCurrentWebApplicationContext().getServletContext();
            Configuration configuration = new Configuration();
            configuration.setDefaultEncoding("UTF-8");
            configuration.setServletContextForTemplateLoading(context, "/WEB-INF/templates");
            
        	Map<String, Map<String, Object>> companyMap = new HashMap<String, Map<String,Object>>();
        	Map<String, Template> templateMap = new HashMap<String, Template>(8);
        	String SignDate = DateUtil.getCurrentDate("yyyy年MM月dd日");
        	String customerName = null;
        	for(Map<String, Object> item : allData) {
        		String companyCode = (String)item.get("company_code");
        		String key = companyCode + "/" + item.get("sales_channel");
        		//初始化模板
        		if(!templateMap.containsKey(companyCode)) {
        			templateMap.put(companyCode, configuration.getTemplate("elitefund/elite_fund_Tax_Invoicing_Rebate_" + companyCode + ".ftl"));
        		}
        		if(companyMap.containsKey(key)) {
        			Map<String, Object> total = companyMap.get(key);
        			total.put("ReimbursementTotal", ((Number)item.get("fund_value")).doubleValue() + (Double)total.get("ReimbursementTotal"));
        			if("Q1".equals(item.get("quarter"))) {
        				total.put("ReimbursementQ1", ((Number)item.get("fund_value_rated")).doubleValue() + (Double)total.get("ReimbursementQ1"));
        				total.put("ReimbursementQ1Vat", ((Number)item.get("fund_value")).doubleValue() + (Double)total.get("ReimbursementQ1Vat"));
        			}else {
        				total.put("ReimbursementQ234", ((Number)item.get("fund_value_rated")).doubleValue() + (Double)total.get("ReimbursementQ234"));
        				total.put("ReimbursementQ234Vat", ((Number)item.get("fund_value")).doubleValue() + (Double)total.get("ReimbursementQ234Vat"));
        			}
        		}else {
        			customerName = (String)item.get("customer_name_cn");
        			item.put("SignDate", SignDate);
        			item.put("ReimbursementTotal", ((Number)item.get("fund_value")).doubleValue());
        			if("4400".equals(companyCode)) {
        				item.put("company_name", "");
        			}
    				item.put("sales_channel", salesChannel);
        			if("Q1".equals(item.get("quarter"))) {
	        			item.put("ReimbursementQ1", ((Number)item.get("fund_value_rated")).doubleValue());
	        			item.put("ReimbursementQ1Vat", ((Number)item.get("fund_value")).doubleValue());
	        			item.put("ReimbursementQ234", 0.0);
	        			item.put("ReimbursementQ234Vat", 0.0);
        			}else {
	        			item.put("ReimbursementQ1", 0.0);
	        			item.put("ReimbursementQ1Vat", 0.0);
	        			item.put("ReimbursementQ234", ((Number)item.get("fund_value_rated")).doubleValue());
	        			item.put("ReimbursementQ234Vat", ((Number)item.get("fund_value")).doubleValue());
        			}
        			companyMap.put(key, item);
        		}
        	}
        	if(companyMap.isEmpty()) {
        		throw new WxPltException("经销商无待兑现金额");
        	}
        	if(companyMap.size() == 1) {
        		Map<String, Object> companyData = companyMap.values().iterator().next();
        		formatDisctributorDocData(companyData);
        		CommonUtil.setExportResponseHeader(request, response, customerName + "_" + buildDistributorDocFileName(companyData), "doc");
        		templateMap.get(companyData.get("company_code")).process(companyData, new OutputStreamWriter(response.getOutputStream(), "UTF-8"));
        		response.getOutputStream().close();
        	}else {
        		CommonUtil.setExportResponseHeader(request, response, customerName + "_" + salesChannel, "zip");
    			ZipOutputStream zipOutputStream = new ZipOutputStream(response.getOutputStream());
    			zipOutputStream.setEncoding("GBK");
    			for(Map<String, Object> companyData : companyMap.values()){
            		formatDisctributorDocData(companyData);
					zipOutputStream.putNextEntry(new ZipEntry(buildDistributorDocFileName(companyData) + ".doc"));
					templateMap.get(companyData.get("company_code")).process(companyData, new OutputStreamWriter(zipOutputStream, "UTF-8"));
    			}
    			zipOutputStream.flush();
    			zipOutputStream.close();
        	}
    		log.info("partnerFileExport success.");
        } catch (Exception e) {
			log.error(e.getMessage(), e);
			request.setAttribute("errorMsg", "导出失败：" + e.getMessage());
			return "forward:/common/jsp/downloadError.jsp";
        }
        return null;
    }
    
    protected void formatDisctributorDocData(Map<String, Object> companyData) {
    	companyData.put("ReimbursementQ1", CommonUtil.formatAmount((Double)companyData.get("ReimbursementQ1")));
    	companyData.put("ReimbursementQ1Vat", CommonUtil.formatAmount((Double)companyData.get("ReimbursementQ1Vat")));
    	companyData.put("ReimbursementQ234", CommonUtil.formatAmount((Double)companyData.get("ReimbursementQ234")));
    	companyData.put("ReimbursementQ234Vat", CommonUtil.formatAmount((Double)companyData.get("ReimbursementQ234Vat")));
    	companyData.put("ReimbursementTotal", CommonUtil.formatAmount((Double)companyData.get("ReimbursementTotal")));
    }
    
    protected String buildDistributorDocFileName(Map<String, Object> companyData) {
    	return ("4400".equals(companyData.get("company_code")) ? "北京总公司" : companyData.get("company_name")) + "_" + 
        		companyData.get("sales_channel") + "_" + companyData.get("company_code");
    }

    private File generateFinaceTemplateFile(String companyCode, Dict dataMap, String filePath) {
        ServletContext context = ContextLoader.getCurrentWebApplicationContext().getServletContext();
        String fileName = (StrUtil.equals(companyCode, "4400") ? "北京总公司" : dataMap.get("SubCompanyName")) + "_" + StrUtil.nullToEmpty((String) dataMap.get("SalesChannel")) + "_" + companyCode;

        Configuration configuration = new Configuration();
        configuration.setDefaultEncoding("UTF-8");
        configuration.setServletContextForTemplateLoading(context, "/WEB-INF/templates");
        Template template = null;
        try {
            template = configuration.getTemplate("Rebate_Fund_CompanyCode.xml");
        } catch (IOException e1) {
            log.error(e1.getMessage(), e1);
            throw new RuntimeException(e1);
        }
        File file = new File(filePath + File.separator + fileName + ".doc");
        OutputStream outputStream = null;
        try {
            outputStream = new FileOutputStream(file);
            template.process(dataMap, new OutputStreamWriter(outputStream, "UTF-8"));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException("生成Doc失败", e);
        } finally {
            IoUtil.close(outputStream);
        }
        return file;
    }

    private BaseResp generateExportTemlateFiles(Long orgId, Integer times) {
        String fileBasePath = (String) Constants.getSystemPropertyByCodeType(Constants.report_file_path);
        // 组装文件路径
        String rebateFileRootStr = "elites2";
        String rebateFileRootPath = fileBasePath + File.separator + rebateFileRootStr + File.separator;
        cn.hutool.core.io.FileUtil.mkdir(new File(rebateFileRootPath));

        List<DwCustomerRegionSalesSupervisorRel> customerList = dwCustomerRegionSalesSupervisorRelV2Mapper.getCustomerInfoById(orgId);
        String customerName = CollUtil.isNotEmpty(customerList) ? customerList.get(0).getCustomerNameCn() : "";

        String channel = null;
        WxTUser user = ContextUtil.getCurUser();
        ArrayList<String> configs = CollUtil.newArrayList("elites2.bsm_cai", "elites2.sdo_cai", "elites2.cdm_bu_cai", "elites2.cio_bu_cai");
        Map<String, WxSystemConfig> configItems = wxSystemConfigMapper.getConfigs(configs);
        boolean isContainsValue = false;
        for (String configKey : configItems.keySet()) {
            if (StrUtil.equalsIgnoreCase(user.getCai(), configItems.get(configKey).getValue())) {
                isContainsValue = true;
                break;
            }
        }
        Map<String, File> files = new HashMap<String, File>();
        String filePathDir = null, dirName = null;
        if (isContainsValue) {
            dirName = "RebateFund_" + orgId;
            Map<String, File> filesCDM = generateExportTemplateByChannel("CDM", orgId, customerName, times, dirName);
            if (CollUtil.isNotEmpty(filesCDM)) {
                for (String cc : filesCDM.keySet()) {
                    files.put("CDM_" + cc, filesCDM.get(cc));
                }
            }

            Map<String, File> filesCIO = generateExportTemplateByChannel("C&I", orgId, customerName, times, dirName);
            if (CollUtil.isNotEmpty(filesCIO)) {
                for (String cc : filesCIO.keySet()) {
                    files.put("CDM_" + cc, filesCIO.get(cc));
                }
            }
        } else {
            channel = "C&I";//(user != null ? user.getSalesChannel() : "");
            dirName = "RebateFund_" + (StrUtil.equals("C&I", channel) ? "CIO" : channel) + "_" + orgId;
            files = generateExportTemplateByChannel(channel, orgId, customerName, times, null);
            if (files == null) return new BaseResp("0002", "该公司在当前渠道下没有关联的下单分公司！", null);

        }
        filePathDir = rebateFileRootPath + dirName + File.separator;


        String generateFileName = rebateFileRootPath + (customerName) + "-" + DateUtil.getCurrentDate("yyyyMMddHHmmss");
        if (files != null && files.size() > 0) {
            if (files.size() > 1) {
                File file = ZipUtil.zip(filePathDir, generateFileName + ".zip");
                cn.hutool.core.io.FileUtil.clean(filePathDir);
                cn.hutool.core.io.FileUtil.del(filePathDir);
                return new BaseResp("0000", "", file);
            } else {
                File file = RebateUtil.getFirstValueOfMap(files);
                File newFile = new File(generateFileName + ".doc");
                file.renameTo(newFile);
                return new BaseResp("0000", "", newFile);
            }
        }
        return new BaseResp();
    }


    private Map<String, File> generateExportTemplateByChannel(String channel, Long orgId, String customerName, Integer times, String dirName) {
        Map<String, File> result = new HashMap<String, File>();
        Dict dataMap = Dict.create().set("CustomerName", customerName);
        String fileBasePath = (String) Constants.getSystemPropertyByCodeType(Constants.report_file_path);
        if (dirName == null) dirName = "RebateFund_" + (StrUtil.equals("C&I", channel) ? "CIO" : channel) + "_" + orgId;
        String workPath = fileBasePath + "elites2" + File.separator + dirName + File.separator;
        cn.hutool.core.io.FileUtil.mkdir(new File(workPath));
        RebateSalesExcel excelRowQ1 = null, excelRowQ234 = null;
        List<DwPpCustomerCompanyFundQuarter> companyList = dwPpCustomerCompanyFundQuarterMapper.getCompanyCodeList(orgId, channel);
        if (CollUtil.isEmpty(companyList)) return null;

        RebateFund fund = rebateFund2Service.getFundForOrg(orgId, 2, null, times);

        Map<String, DwPpCustomerCompanyFundQuarter> companyFundListQ1 = dwPpCustomerCompanyFundQuarterMapper.getFundDetailListByOrgId(orgId, channel,
                CollUtil.newArrayList("Q1"));
        Map<String, DwPpCustomerCompanyFundQuarter> companyFundListQ234 = dwPpCustomerCompanyFundQuarterMapper.getFundDetailListByOrgId(orgId, channel,
                CollUtil.newArrayList("Q2", "Q3", "Q4"));

        String companyCode = null;
        Map<String, DicItemVo> officeList = rebateExport2Service.getCompanyCodeList();
        for (DwPpCustomerCompanyFundQuarter dwPpCustomerCompanyFundQuarter : companyList) {
            companyCode = dwPpCustomerCompanyFundQuarter.getCompanyCode();
            excelRowQ1 = rebateExport2Service.getCompanyFundItem(orgId, companyCode, channel, "Q1", fund, companyFundListQ1.get(companyCode));
            excelRowQ234 = rebateExport2Service.getCompanyFundItem(orgId, companyCode, channel, "Q234", fund, companyFundListQ234.get(companyCode));
            dataMap.set("ReimbursementTotal", (new BigDecimal(StrUtil.replace(excelRowQ1.getTotalFundEarned(), ",", "")))
                    .add(new BigDecimal(StrUtil.replace(excelRowQ234.getTotalFundEarned(), ",", ""))));
            dataMap.set("ReimbursementQ1", excelRowQ1.getTotalFundEarnedWithoutTax());
            dataMap.set("ReimbursementQ1Vat", excelRowQ1.getTotalFundEarned());
            dataMap.set("ReimbursementQ234", excelRowQ234.getTotalFundEarnedWithoutTax());
            dataMap.set("ReimbursementQ234Vat", excelRowQ234.getTotalFundEarned());
            dataMap.set("SubCompanyName", StrUtil.equals(companyCode, "4400") ? "" : officeList.get(companyCode).getDicItemName());
            dataMap.set("SalesChannel", channel);
            dataMap.set("Address", officeList.get(companyCode).getDicItemDesc());
            dataMap.set("SignDate", cn.hutool.core.date.DateUtil.format(new Date(), "yyyy年MM月dd日"));
            result.put(companyCode, generateFinaceTemplateFile(companyCode, dataMap, workPath));
        }

        return result;
    }

    @ResponseBody
    @RequestMapping(value = "/exportFinalExcel.do", method = RequestMethod.POST, produces = "application/json")
    public void exportFinalExcel(@RequestParam("auditId") Long auditId, HttpServletResponse response) {
        try {
            log.debug("rebate audit detail excel export start, param:", auditId);
            File file = rebateExport2Service.exportFinalExcel(auditId);
            InputStream inputStream = new FileInputStream(file);
            FileUtil.download(response, inputStream, file.getName());
            log.debug("rebate audit detail excel export end");
        } catch (Exception e) {
            log.error("rebate audit detail excel export exception:", e);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/exportDetailExcel.do", method = RequestMethod.POST, produces = "application/json")
    public void exportDetailExcel(@RequestParam("auditId") Long auditId, HttpServletResponse response) {
        try {
            log.debug("rebate audit detail excel export start, param:", auditId);
            File file = rebateExport2Service.exportDetailExcel(auditId);
            InputStream inputStream = new FileInputStream(file);
            FileUtil.download(response, inputStream, file.getName());
            log.debug("rebate audit detail excel export end");
        } catch (Exception e) {
            log.error("rebate audit detail excel export exception:", e);
        }
    }

    /**
     * 导出详情(40多列）
     *
     * @param response
     */
    @ResponseBody
    @RequestMapping(value = "/exportAllDetailExcel.do", method = RequestMethod.GET, produces = "application/json")
    public void exportAllDetailExcel(HttpServletResponse response) {
        try {
            log.debug("exportAllDetailExcel start");
            WxTUser user = ContextUtil.getCurUser();
            Long start = System.currentTimeMillis();

            //List<Long> relateList = new ArrayList<Long>();
            /*List<WXTRebateAudit> auditList = wxtRebateAudit2Mapper.getSummaryAuditList();
            for (WXTRebateAudit audit : auditList) {
                List<WXTRebateRelated> relatedList = wxtRebateRelated2Mapper.getListByAuditId(audit.getId());
                for (WXTRebateRelated related : relatedList) {
                    relateList.add(related.getRebateId());
                }
            }*/
            /*List<WXTRebateRelated> relatedList = wxtRebateRelated2Mapper.listAll();
            for (WXTRebateRelated related : relatedList) {
                relateList.add(related.getRebateId());
            }*/
            BaseResp res = rebateAudit2Service.caculateFund(null, "");

            BaseResp rebateRoleCode = rebateApply2Service.getRebateRoleCode(null);
            String roleCode = (String) rebateRoleCode.getData();
            Map<Long, DwCustomerRegionSalesSupervisorRel> orgList = rebateExport2Service.getManagedOrgList(roleCode, user, user.getSalesChannel(), 1);
            List<Long> orgIdList = new ArrayList<Long>();
            if (CollUtil.isNotEmpty(orgList)) {
                for (Long orgId : orgList.keySet()) {
                    orgIdList.add(orgId);
                }
                //orgIdList.add(284L);
            }
            File file = rebateExport2Service.exportAllDetailExcel(orgIdList);
            InputStream inputStream = new FileInputStream(file);
            FileUtil.download(response, inputStream, file.getName());
            log.debug("导出详情共耗时" + (System.currentTimeMillis() - start) + "ms");
            log.debug("exportAllDetailExcel end");
        } catch (Exception e) {
            log.error("exportAllDetailExcel exception:", e);
        }
    }

    /**
     * 导出详情(40多列）
     *
     * @param request
     * @param response
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/exportAllDetailExcelNew.do")
    public Map<String, Object> exportAllDetailExcelNew(HttpServletRequest request, HttpServletResponse response) {
        Map<String, Object> map = new HashMap<String, Object>(5);
        try {
            final WxTUser user = ContextUtil.getCurUser();
            final AsynProcessStatus processStatus = new AsynProcessStatus("com.chevron.elites2.controller.RebateExport2Controller.download", user.getUserId());
            processStatus.setAttrs(new HashMap<String, Object>(2));
            processStatus.save();
            new Thread() {
                @Override
                public void run() {
                    try {
                        Long start = System.currentTimeMillis();
                        processStatus.setStatus(AsynProcessStatus.STATUS_PROCESSING);
                        processStatus.setMessage("处理数据中...");
                        processStatus.save();

                        //WebApplicationContext webApplicationContext = ContextLoader.getCurrentWebApplicationContext();
                        //ServletContext servletContext = webApplicationContext.getServletContext();

                        //BaseResp res = rebateAudit2Service.caculateFund(null, "");
                        List<WxTRebateApply2> rebateList;
                        rebateList = rebateApplyDao.getRebateApplyByRelated();
                        int cnt = 1;
                        for (WxTRebateApply2 rebateApply : rebateList) {
                            processStatus.setMessage("加载 " + rebateApply.getOrganizationName() + " 数据 (" + cnt + "/" + rebateList.size() + ") ...");
                            processStatus.save();
                            rebateAudit2Service.caculateOneFund(rebateApply, "");
                            cnt++;
                        }

                        BaseResp rebateRoleCode = rebateApply2Service.getRebateRoleCode(null, user);
                        String roleCode = (String) rebateRoleCode.getData();
                        Map<Long, DwCustomerRegionSalesSupervisorRel> orgList = null;
                        orgList = rebateExport2Service.getManagedOrgList(roleCode, user, user.getSalesChannel(), 1);
                       /* DwCustomerRegionSalesSupervisorRel org = new DwCustomerRegionSalesSupervisorRel();
                        org.setCustomerNameCn("");
                        orgList.put(1069L, org);*/

                        RebateDetailExcel vo = null;
                        int no = 1;
                        List<RebateDetailExcel> lstExportData = new ArrayList<RebateDetailExcel>();
                        for (Long orgId : orgList.keySet()) {
                            processStatus.setMessage("处理 " + orgList.get(orgId).getCustomerNameCn() + " 导出数据 (" + no + "/" + orgList.size() + ") ...");
                            processStatus.save();
                            vo = rebateExport2Service.exportOneOrgDetailExcel(orgId);
                            if (vo != null) {
                                lstExportData.add(vo);
                            }
                            no++;
                        }
                        processStatus.setMessage("写入数据...");
                        processStatus.save();
                        File file = rebateExport2Service.exportDetailExcelFile(lstExportData);
                        processStatus.getAttrs().put("filePath", file.getAbsolutePath());
                        processStatus.getAttrs().put("fileName", file.getName());
                        processStatus.setStatus(AsynProcessStatus.STATUS_SUCCESS);
                        processStatus.save();
                        log.debug("处理完成 exportAllDetailExcelNew：" + (System.currentTimeMillis() - start) + "ms");
                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
                        processStatus.setStatus(AsynProcessStatus.STATUS_ERROR);
                        processStatus.setMessage("导出失败。" + MessageResourceUtil.getMessage("system.unexpected_exception"));
                        processStatus.save();
                    }
                }
            }.start();
            map.put(AsynProcessStatus.RESULT_KEY, processStatus);
            map.put(Constants.RESULT_CODE_KEY, Constants.RESULT_SUCCESS);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            map.put(Constants.RESULT_CODE_KEY, Constants.RESULT_ERROR);
            map.put(Constants.RESULT_ERROR_MSG_KEY, "导出失败。" + MessageResourceUtil.getMessage("system.unexpected_exception"));
        }
        return map;
    }
    
    @RequestMapping(value = "/exportRefundSummary.do")
    public String exportRefundSummary(HttpServletRequest request, HttpServletResponse response) {
		log.info("exportRefundSummary start.");
        return this.exportRefundSummary2019(request, response);
    }

    private String exportRefundSummary2019(HttpServletRequest request, HttpServletResponse response){
        try {
            List<Map<String, Object>> exportData = reportViewBizService.loadData("elitefund", "RefundSummaryExport2019", null);
            WebApplicationContext webApplicationContext = ContextLoader.getCurrentWebApplicationContext();
            ServletContext servletContext = webApplicationContext.getServletContext();
            Configuration configuration = new Configuration();
            configuration.setDefaultEncoding("UTF-8");
            configuration.setServletContextForTemplateLoading(servletContext, "/WEB-INF/templates");
            Template sheet1 = configuration.getTemplate("Rebate_Refund_Summary.ftl");
            Map<String, Object> rootMap = new HashMap<String, Object>(5);
            rootMap.put("dataLists", exportData);
//            rootMap.put("total", total);
            rootMap.put("dataLength", exportData.size());
            response.reset();
            response.setContentType("application/vnd.ms-excel;charset=GBK");
            response.addHeader("Content-Disposition", "attachment; filename=\"Refund Summary.xls\"");
            sheet1.process(rootMap, new OutputStreamWriter(response.getOutputStream(), "UTF-8"));
            log.info("exportRefundSummary success.");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            request.setAttribute("errorMsg", "导出失败：" + e.getMessage());
            return "forward:/common/jsp/downloadError.jsp";
        }
        return null;
    }
    
    private static String[] fundReimbursement2019Sum = new String[] {"fund_value", "fund_value_rated"};
    
    @RequestMapping(value = "/exportFundReimbursement.do")
    public String exportFundReimbursement(HttpServletRequest request, HttpServletResponse response) {
		log.info("exportFundReimbursement start.");
        return this.exportFundReimbursement2019(request, response);
    }

    private String exportFundReimbursement2019(HttpServletRequest request, HttpServletResponse response){
        log.info("exportFundReimbursement start.");
        try {
            List<Map<String, Object>> allData = reportViewBizService.loadData("elitefund", "FundReimbursementExport2019q3q4", null);
            List<Map<String, Object>> cioExportData = new ArrayList<Map<String,Object>>(200);
            List<Map<String, Object>> cdmExportData = new ArrayList<Map<String,Object>>(200);
            List<Map<String, Object>> sumExportData = new ArrayList<Map<String,Object>>(200);
            Map<String, Object> currentData = null;
            for(Map<String, Object> item : allData) {
                if(currentData == null || !currentData.get("org_id").equals(item.get("org_id")) || !currentData.get("quarter").equals(item.get("quarter"))) {
                    currentData = new HashMap<String, Object>(item);
                    sumExportData.add(currentData);
                }else {
                    //合计
                    for(String p : fundReimbursement2019Sum) {
                        currentData.put(p, ((Number)currentData.get(p)).doubleValue() + ((Number)item.get(p)).doubleValue());
                    }
                }
                //拆分
                if("CDM".equals(item.get("sales_channel"))) {
                    cdmExportData.add(item);
                }else {
                    cioExportData.add(item);
                }
            }
            response.reset();
            response.setContentType("application/vnd.ms-excel;charset=GBK");
            response.addHeader("Content-Disposition", "attachment; filename=\"Fund Reimbursement.xlsx\"");
            PoiWriteExcel.exportLargeData(response.getOutputStream(), true, new ExportSheet[] {buildFundReimbursementSummarySheet(sumExportData),
                    buildFundReimbursementSheet(cdmExportData, "Consumer"), buildFundReimbursementSheet(cioExportData, "Commerical & Industrial")});
            log.info("exportFundReimbursement success.");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            request.setAttribute("errorMsg", "导出失败：" + e.getMessage());
            return "forward:/common/jsp/downloadError.jsp";
        }
        return null;
    }

    protected ExportSheet buildFundReimbursementSummarySheet(List<Map<String, Object>> exportData) {
    	List<ExportCol> exportCols = new ArrayList<ExportCol>();
    	ExportCol col = new ExportCol("customer_name_cn", "客户名称(中文)");
		col.setPropertyHelper(IPropertyHelper.mapHelper);
		col.setWidth(40);
		exportCols.add(col);
		col = new ExportCol("customer_name_en", "客户名称(英文)");
		col.setPropertyHelper(IPropertyHelper.mapHelper);
		col.setWidth(40);
		exportCols.add(col);
		col = new ExportCol("sales_name", "销售");
		col.setPropertyHelper(IPropertyHelper.mapHelper);
//		col.setWidth(40);
		exportCols.add(col);
		col = new ExportCol("product_name", "产品名称");
		col.setPropertyHelper(IPropertyHelper.mapHelper);
//		col.setWidth(40);
		exportCols.add(col);
		col = new ExportCol("quarter", "季度");
		col.setPropertyHelper(IPropertyHelper.mapHelper);
		col.setWidth(10);
		exportCols.add(col);
		col = new ExportCol("fund_value_rated", "销售折扣折让金额(元/不含增值税)");
		col.setPropertyHelper(IPropertyHelper.mapHelper);
		col.setWidth(30);
		col.setDataType(IExportCol.DATA_TYPE_NUMBER);
		col.setAlign(ExportColAlign.RIGHT);
		col.setDataFormat("#,##0.0#");
		exportCols.add(col);
		col = new ExportCol("rate", "税率");
		col.setPropertyHelper(IPropertyHelper.mapHelper);
		col.setWidth(10);
		exportCols.add(col);
		col = new ExportCol("fund_value", "价税合计");
		col.setPropertyHelper(IPropertyHelper.mapHelper);
		col.setDataType(IExportCol.DATA_TYPE_NUMBER);
//		col.setWidth(40);
		col.setAlign(ExportColAlign.RIGHT);
		col.setDataFormat("#,##0.0#");
		exportCols.add(col);

    	return new ExportSheet("Fund Reimbursement Summary", exportCols, exportData);
    }
    
    protected ExportSheet buildFundReimbursementSheet(List<Map<String, Object>> exportData, String sheetName) {
    	List<ExportCol> exportCols = new ArrayList<ExportCol>();
    	ExportCol col = new ExportCol("customer_name_cn", "客户名称(中文)");
		col.setPropertyHelper(IPropertyHelper.mapHelper);
		col.setWidth(40);
		exportCols.add(col);
		col = new ExportCol("customer_name_en", "客户名称(英文)");
		col.setPropertyHelper(IPropertyHelper.mapHelper);
		col.setWidth(40);
		exportCols.add(col);
		col = new ExportCol("sales_name", "销售");
		col.setPropertyHelper(IPropertyHelper.mapHelper);
//		col.setWidth(40);
		exportCols.add(col);
		col = new ExportCol("product_name", "产品名称");
		col.setPropertyHelper(IPropertyHelper.mapHelper);
//		col.setWidth(40);
		exportCols.add(col);
		col = new ExportCol("quarter", "季度");
		col.setPropertyHelper(IPropertyHelper.mapHelper);
		col.setWidth(10);
		exportCols.add(col);
		col = new ExportCol("company_name", "办公室");
		col.setPropertyHelper(IPropertyHelper.mapHelper);
//		col.setWidth(40);
		exportCols.add(col);
		col = new ExportCol("company_code", "办公室编码");
		col.setPropertyHelper(IPropertyHelper.mapHelper);
//		col.setWidth(40);
		exportCols.add(col);
		col = new ExportCol("proportion", "占比%");
		col.setPropertyHelper(IPropertyHelper.mapHelper);
		col.setWidth(10);
		exportCols.add(col);
		col = new ExportCol("fund_value_rated", "销售折扣折让金额(元/不含增值税)");
		col.setPropertyHelper(IPropertyHelper.mapHelper);
		col.setWidth(30);
		col.setDataType(IExportCol.DATA_TYPE_NUMBER);
		col.setAlign(ExportColAlign.RIGHT);
		col.setDataFormat("#,##0.0#");
		exportCols.add(col);
		col = new ExportCol("rate", "税率");
		col.setPropertyHelper(IPropertyHelper.mapHelper);
		col.setWidth(10);
		exportCols.add(col);
		col = new ExportCol("fund_value", "价税合计");
		col.setPropertyHelper(IPropertyHelper.mapHelper);
		col.setDataType(IExportCol.DATA_TYPE_NUMBER);
		col.setAlign(ExportColAlign.RIGHT);
		col.setDataFormat("#,##0.0#");
//		col.setWidth(40);
		exportCols.add(col);
    	
    	return new ExportSheet(sheetName, exportCols, exportData);
    }

    /**
     * 导出汇总数据
     *
     * @param response
     */
    @ResponseBody
    @RequestMapping(value = "/exportSalesMailExcel.do", method = RequestMethod.POST, produces = "application/json")
    public void exportSalesMailExcel(HttpServletResponse response) {
        try {
            WxTUser user = ContextUtil.getCurUser();

            //List<Long> relateList = new ArrayList<Long>();
            /*List<WXTRebateAudit> auditList = wxtRebateAudit2Mapper.getSummaryAuditList();
            for (WXTRebateAudit audit : auditList) {
                List<WXTRebateRelated> relatedList = wxtRebateRelated2Mapper.getListByAuditId(audit.getId());
                for (WXTRebateRelated related : relatedList) {
                    relateList.add(related.getRebateId());
                }
            }*/
            /*List<WXTRebateRelated> relatedList = wxtRebateRelated2Mapper.listAll();
            for (WXTRebateRelated related : relatedList) {
                relateList.add(related.getRebateId());
            }*/
            BaseResp res = rebateAudit2Service.caculateFund(null, "");

            List<Long> orgIdList = new ArrayList<Long>();
            //orgIdList.add(275L);
            BaseResp rebateRoleCode = rebateApply2Service.getRebateRoleCode(null);
            String roleCode = (String) rebateRoleCode.getData();
            Map<Long, DwCustomerRegionSalesSupervisorRel> orgList = rebateExport2Service.getManagedOrgList(roleCode, user, user.getSalesChannel(), 1);
            if (CollUtil.isNotEmpty(orgList)) {
                for (Long orgId : orgList.keySet()) {
                    orgIdList.add(orgId);
                }
            }
            File file = rebateExport2Service.exportSalesMailExcel(orgIdList);
            InputStream inputStream = new FileInputStream(file);
            FileUtil.download(response, inputStream, file.getName());
        } catch (Exception e) {
            log.error("exportSalesMailExcel exception:", e);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/exportSalesMailExcelNew.do")
    public Map<String, Object> exportSalesMailExcelNew(HttpServletRequest request, HttpServletResponse response) {
        Map<String, Object> map = new HashMap<String, Object>(5);
        try {
            final WxTUser user = ContextUtil.getCurUser();
            Long userId = user.getUserId();
            final AsynProcessStatus processStatus = new AsynProcessStatus("com.chevron.elites2.controller.RebateExport2Controller.download", userId);
            processStatus.setAttrs(new HashMap<String, Object>(2));
            processStatus.save();
            new Thread() {

                @Override
                public void run() {
                    try {
                        Long start = System.currentTimeMillis();
                        processStatus.setStatus(AsynProcessStatus.STATUS_PROCESSING);
                        processStatus.setMessage("处理数据中......");
                        processStatus.save();

                        //BaseResp res = rebateAudit2Service.caculateFund(null, "");
                        List<WxTRebateApply2> rebateList;
                        rebateList = rebateApplyDao.getRebateApplyByRelated();
                        int cnt = 1;
                        for (WxTRebateApply2 rebateApply : rebateList) {
                            processStatus.setMessage("加载 " + rebateApply.getOrganizationName() + " 数据 (" + cnt + "/" + rebateList.size() + ") ...");
                            processStatus.save();
                            rebateAudit2Service.caculateOneFund(rebateApply, "");
                            cnt++;
                        }

                        BaseResp rebateRoleCode = rebateApply2Service.getRebateRoleCode(null, user);
                        String roleCode = (String) rebateRoleCode.getData();
                        Map<Long, DwCustomerRegionSalesSupervisorRel> orgList = rebateExport2Service.getManagedOrgList(roleCode, user, user.getSalesChannel(), 1);
                        /*orgList.clear();
                        DwCustomerRegionSalesSupervisorRel org = new DwCustomerRegionSalesSupervisorRel();
                        org.setCustomerNameCn("");
                        orgList.put(1069L, org);*/
                        List<Long> orgIdList = new ArrayList<Long>();
                        if (CollUtil.isNotEmpty(orgList)) {
                            for (Long orgId : orgList.keySet()) {
                                orgIdList.add(orgId);
                            }
                        }

                        processStatus.setMessage("写入数据中......");
                        processStatus.save();
                        File file = rebateExport2Service.exportSalesMailExcel(orgIdList);

                        processStatus.getAttrs().put("filePath", file.getAbsolutePath());
                        processStatus.getAttrs().put("fileName", file.getName());
                        processStatus.setStatus(AsynProcessStatus.STATUS_SUCCESS);
                        processStatus.save();
                        log.debug("处理完成 exportSalesMailExcelNew：" + (System.currentTimeMillis() - start) + "ms");
                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
                        processStatus.setStatus(AsynProcessStatus.STATUS_ERROR);
                        processStatus.setMessage("导出失败。" + MessageResourceUtil.getMessage("system.unexpected_exception"));
                        processStatus.save();
                    }
                }
            }.start();
            map.put(AsynProcessStatus.RESULT_KEY, processStatus);
            map.put(Constants.RESULT_CODE_KEY, Constants.RESULT_SUCCESS);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            map.put(Constants.RESULT_CODE_KEY, Constants.RESULT_ERROR);
            map.put(Constants.RESULT_ERROR_MSG_KEY, "导出失败。" + MessageResourceUtil.getMessage("system.unexpected_exception"));
        }
        return map;
    }


    @RequestMapping(value = "/download-help.do")
    public String downloadHelpDoc(@RequestParam("file") String filename, HttpServletRequest request, HttpServletResponse response) {
        try {
            String filePath = request.getServletContext().getRealPath("elites2/help/" + filename);
            File file = new File(filePath);
            if (!file.exists()) {
                throw new Exception("文件不存在！");
            }

            InputStream inputStream = new FileInputStream(file);
            Map<String, String> files = new HashMap<String, String>(3);
            //files.put("doc1.pptx", URLEncoder.encode("如何在金税开具红字增值税专用发票信息表", "UTF-8"));
            //files.put("doc2.doc", URLEncoder.encode("开具红字发票说明", "UTF-8"));
            files.put("doc1.pptx", "如何在金税开具红字增值税专用发票信息表.pptx");
            files.put("doc2.doc", "开具红字发票说明.doc");
            files.put("doc3.pdf", "返利红票和月末开票.pdf");
            FileUtil.download(response, inputStream, files.get(filename));
        } catch (Exception e) {
            log.error("downloadHelpDoc exception:", e);
            request.setAttribute("errorMsg", "下载文件异常: " + e.getMessage());
            return "forward:/common/jsp/downloadError.jsp";
        }
        return null;
    }
}
