SET IDENTITY_INSERT [dbo].[wx_t_sop_support_adjust_apply] ON 

INSERT [dbo].[wx_t_sop_support_adjust_apply] ([id], [req_no], [req_user_id], [req_time], [form_status], [approval_completion_time], [channel], [year], [quarter], [signage], [seminar], [oil_change_car], [equipment_tools], [other_amount], [create_user_id], [create_time], [update_user_id], [update_time]) VALUES (2, N'202103050001', 131747, CAST(N'2021-03-05T11:12:55.177' AS DateTime), 50, NULL, N'Consumer', N'2020', N'2', CAST(4000.000000 AS Decimal(20, 6)), CAST(0.000000 AS Decimal(20, 6)), CAST(80000.000000 AS Decimal(20, 6)), CAST(67450.000000 AS Decimal(20, 6)), CAST(0.000000 AS Decimal(20, 6)), 131747, CAST(N'2021-01-05T11:12:57.830' AS DateTime), NULL, NULL)
INSERT [dbo].[wx_t_sop_support_adjust_apply] ([id], [req_no], [req_user_id], [req_time], [form_status], [approval_completion_time], [channel], [year], [quarter], [signage], [seminar], [oil_change_car], [equipment_tools], [other_amount], [create_user_id], [create_time], [update_user_id], [update_time]) VALUES (3, N'202103050002', 131747, CAST(N'2021-03-05T11:12:55.177' AS DateTime), 50, NULL, N'Commercial', N'2020', N'4', CAST(4000.000000 AS Decimal(20, 6)), CAST(0.000000 AS Decimal(20, 6)), CAST(80000.000000 AS Decimal(20, 6)), CAST(67450.000000 AS Decimal(20, 6)), CAST(0.000000 AS Decimal(20, 6)), 131747, CAST(N'2021-01-05T11:12:57.830' AS DateTime), NULL, NULL)
INSERT [dbo].[wx_t_sop_support_adjust_apply] ([id], [req_no], [req_user_id], [req_time], [form_status], [approval_completion_time], [channel], [year], [quarter], [signage], [seminar], [oil_change_car], [equipment_tools], [other_amount], [create_user_id], [create_time], [update_user_id], [update_time]) VALUES (4, N'202103050003', 131747, CAST(N'2021-03-05T11:12:55.177' AS DateTime), 50, NULL, N'Consumer', N'2020', N'4', CAST(4000.000000 AS Decimal(20, 6)), CAST(0.000000 AS Decimal(20, 6)), CAST(80000.000000 AS Decimal(20, 6)), CAST(67450.000000 AS Decimal(20, 6)), CAST(0.000000 AS Decimal(20, 6)), 131747, CAST(N'2021-01-05T11:12:57.830' AS DateTime), NULL, NULL)
SET IDENTITY_INSERT [dbo].[wx_t_sop_support_adjust_apply] OFF

--update s set s.status=0 from wx_t_sop_support_summary s --where 