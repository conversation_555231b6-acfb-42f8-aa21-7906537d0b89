package com.chevron.cdm_mkt.constant;

public enum AuditStatusEnum {

    CREATE("-1", "提交"),
    WAIT("0", "待审核"),
    PASS("1", "审核通过"),
    REJECT("2", "审核驳回"),
    REVOKE("3", "撤回"),
    CANCEL("9", "终止关闭");

    private String code;
    private String name;

    AuditStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(String code) {
        for (AuditStatusEnum type : AuditStatusEnum.values()) {
            if (type.getCode().equals(code)) {
                return type.getName();
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
