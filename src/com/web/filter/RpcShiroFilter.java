package com.web.filter;

import java.io.IOException;
import java.io.PrintWriter;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import javax.servlet.*;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import javax.servlet.http.HttpSession;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.common.wrapper.RpcRequestWrapper;
import com.sys.auth.service.WxPermissionService;
import org.apache.shiro.subject.Subject;
import org.apache.shiro.web.filter.authc.FormAuthenticationFilter;
import org.apache.shiro.web.filter.authz.AuthorizationFilter;
import org.apache.shiro.web.servlet.ShiroHttpServletRequest;
import org.apache.shiro.web.util.WebUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.common.util.ContextUtil;
import com.common.util.StringUtils;
import com.sys.auth.model.WxTUser;
import com.sys.auth.service.UserServiceI;

public class RpcShiroFilter extends AuthorizationFilter {

	private static Logger log = LoggerFactory.getLogger(RpcShiroFilter.class);
	
	@Autowired
	private UserServiceI userService;
	@Autowired
	RedisTemplate<Serializable, Serializable> redisTemplate;
	@Autowired
	private WxPermissionService wxPermissionService;
	
	@Override
	protected void executeChain(ServletRequest request, ServletResponse response, FilterChain chain) throws Exception {
		super.executeChain(request, response, chain);
	}

	@Override
	public void doFilterInternal(ServletRequest request, ServletResponse response, FilterChain chain) throws ServletException, IOException {
		ShiroHttpServletRequest shiroHttpServletRequest = (ShiroHttpServletRequest) request;
		RpcRequestWrapper wrapper = new RpcRequestWrapper(shiroHttpServletRequest);
		super.doFilterInternal(wrapper, response, chain);
	}

	@Override
	protected boolean isAccessAllowed(ServletRequest arg0,
			ServletResponse arg1, Object arg2) throws Exception {
		HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder
				.getRequestAttributes()).getRequest();
		RpcRequestWrapper wrapper = (RpcRequestWrapper) arg0;
		//判断token是否合法
		if(rpcIsLogin(wrapper)){
			return checkPermissionFactory(wrapper);
		}else{//不合法的rpc请求
//			String loginUrl = super.getLoginUrl();
//			if(!loginUrl.contains(ContextUtil.APP_TOKEN_KEYWORD)){
//				loginUrl = loginUrl + "?"+FormAuthenticationFilter.DEFAULT_USERNAME_PARAM+"=&" + ContextUtil.APP_TOKEN_KEYWORD + "=";
//			}
			super.setLoginUrl("/login.do");
			return false;
		}

	}

	private boolean checkRPCPermission(HttpServletRequest var1, WxTUser user) throws IOException {
		boolean isPermitted = false;
		RpcRequestWrapper wrapper = new RpcRequestWrapper(var1);
		JSONObject bodyJson = wrapper.getBodyJson();
		String method = bodyJson.getString("method");
		if (null == bodyJson || org.apache.commons.lang3.StringUtils.isBlank(method)) {
			isPermitted = false;
		} else {
			isPermitted = user.getPermissionKeyList().contains(method);
			List<String> permissionKeyList = getAllPermissionKeyList();
			if(Boolean.FALSE.equals(isPermitted) && !permissionKeyList.contains(method)) {
				isPermitted = true;
			}
		}

		return isPermitted;
	}


	private boolean checkRestfulPermission(HttpServletRequest var1, WxTUser user) {
		boolean isPermitted = false;
		String servletPath = var1.getServletPath();
		isPermitted = user.getPermissionKeyList().contains(servletPath);

		List<String> permissionKeyList = getAllPermissionKeyList();
		if(Boolean.FALSE.equals(isPermitted) && !permissionKeyList.contains(servletPath)) {
			isPermitted = true;
		}

		return isPermitted;
	}

	private boolean checkPermissionFactory(HttpServletRequest var1) throws IOException {
		boolean isPermitted = true;
		WxTUser user = ContextUtil.getCurUser();
		log.info("user id is: " +  (user == null ? "null user" : user.getUserId()));
		String servletPath = var1.getServletPath();
		log.info("path is: " + servletPath);
		servletPath = servletPath.toLowerCase().replace(".do", "");
		if (servletPath.endsWith("rpc")) {
			isPermitted = checkRPCPermission(var1, user);
		} else {
			isPermitted = checkRestfulPermission(var1, user);
		}
		log.info("isPermitted: " + isPermitted);
		return isPermitted;
	}

	@Override
	protected void redirectToLogin(ServletRequest request,
			ServletResponse response) throws IOException {
		String loginUrl = getLoginUrl();
		HttpServletRequest httpServletRequest = (HttpServletRequest) request;
		HttpServletResponse httpServletResponse = (HttpServletResponse) response;

		try {
			if("XMLHttpRequest".equalsIgnoreCase(httpServletRequest.getHeader("X-Requested-With"))){
				log.info("write logout as response content for ajax request....");
				httpServletResponse.setCharacterEncoding("UTF-8");
	            PrintWriter out = httpServletResponse.getWriter();
	            out.print("logout");
	            out.flush();
	            out.close();
			}
			else{
//				httpServletRequest.getRequestDispatcher(this.getLoginUrl())
//				.forward(httpServletRequest, httpServletResponse);
                WebUtils.issueRedirect(request, response, loginUrl);
			}
		} catch (Exception e) {
			log.error("redirectToLogin with servlet exception", e);
		    WebUtils.issueRedirect(request, response, loginUrl);

		}
	}
	
	private boolean rpcIsLogin(HttpServletRequest request){
		String token = request.getParameter(ContextUtil.APP_TOKEN_KEYWORD);
		String sToken = request.getParameter(ContextUtil.SSO_TOKEN);
		boolean isLogin = false;
        if(!StringUtils.isNull(token)){
        	//根据token中获取用户对象
        	WxTUser user =  ContextUtil.createTokenUser(token);
        	log.info("tokenUser: {}", user);
        	if(user != null){
        		isLogin =true;
        	}
        } else if (!StringUtils.isNull(sToken)){
        	String tokenSource = ContextUtil.decryptSubSystemToken(sToken);
        	if (!StringUtils.isNull(tokenSource) && tokenSource.contains("-")){
				String loginName = tokenSource.split("-")[0];
				
				WxTUser tUser = ContextUtil.getCurUser();
				if (tUser != null){
					isLogin = true;
				} else {
					tUser = userService.findUserByLoginName(loginName);
					if (tUser != null){
						tUser.setPassword(null);
						tUser.setSalt(null);
						ContextUtil.putSSOTokenUser(sToken, tUser);
						isLogin = true;
					}
				}
				
        	}
        } else{
        	isLogin = FilterHelper.isSessionValid(request.getSession());
        }
        return isLogin;
	}
	
	@Override
	protected boolean onAccessDenied(ServletRequest request, ServletResponse response) throws IOException {
		String token = request.getParameter(ContextUtil.APP_TOKEN_KEYWORD);
		if (!StringUtils.isNull(token)){
			log.info("app token invalid....");
			FilterHelper.responseRpcTokenInvalid(request, response);

			return false;
		}
//		Subject subject = getSubject(request, response);
//		log.info("subject logout....");
//		subject.logout();
		return super.onAccessDenied(request, response);
	}

	/**
	 * query all permission keys
	 *
	 * @return List<String>
	 */
	private List<String> getAllPermissionKeyList() {
		List<String> resultList = JSON.parseArray((String) redisTemplate.opsForValue().get("permissionKey"), String.class);
		if(CollectionUtils.isEmpty(resultList)) {
			List<String> permissionKeyList = wxPermissionService.selectAllPermissionKey();
			redisTemplate.opsForValue().set("permissionKey", JSON.toJSONString(permissionKeyList));
			return permissionKeyList;
		}

		return resultList;
	}
	
}
