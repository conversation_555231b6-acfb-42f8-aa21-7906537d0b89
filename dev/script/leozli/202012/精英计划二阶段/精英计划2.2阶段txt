--1. 精英计划2阶段.sql
--2. 精英计划2阶段-update.sql
--3. 修复数据.sql
--4. 精英计划2阶段-view_mid_customer_elite_fund.sql
--4.1 线下精英计划.sql 《Copy of 线下基金报销汇总.xlsx》
--4.2 《精英计划34季度处罚.xlsx》 
--4.3 3、4季度基金.sql
--4.4 vicky授权 Buisness_Support_Region_Manager
--4.5 QIOY汇总审核权限

--5. /sys/workflow/workflowPreExeGroupPage.jsp
--经销商的Buisness Support区域经理
exists (select 1 from dw_customer_region_sales_supervisor_rel xx_001_crss left join dw_region_sales_channel_rel xx_001_rsc 
        on xx_001_rsc.region_name=xx_001_crss.region_name left join wx_t_partner_responsible_main xx_001_prm 
        on xx_001_prm.region_name= xx_001_crss.region_name where xx_001_prm.fun_flag='bs_region'and xx_001_crss.distributor_id=$distributorId$ and xx_001_prm.user_id=$field.userId$)
        
 --6. 导出 FundReimbursementExport2020  
select di1.dic_item_desc company_address, di1.dic_item_name company_name, t.* from ( 
select f1.distributor_id as org_id,isnull(crss.customer_name_cn,ecc1.customer_name_cn) customer_name_cn,ecc1.customer_name_en, crss.sales_name, crss.sales_channel_name,
'润滑油产品' product_name, f1.sales_channel, 'Q1-Q4' quarter,'13%' rate,
case when isnull(#applyPhase#, 'q1q2')='q1q2' then isnull
                      ((SELECT h.company_code
                        FROM      dbo.dw_customer_history4593 AS h
                        WHERE   (h.distributor_id = f1.distributor_id)), company_code) else ecc1.company_code end company_code,
'100%' proportion,
f1.fund_value fund_value, 
f1.fund_value/1.13 fund_value_rated,
isnull(efc.ext_flag,1) ext_flag,
isnull(sope.sop_amount, 0) sop_spark_amount,
row_number()over(partition by f1.distributor_id,f1.sales_channel order by f1.distributor_id desc) as idx 
from (
	 	SELECT t1.distributor_id,sum(isnull(final_amount,0)) as fund_value ,'CDM' sales_channel,channel_weight,'Consumer' as channel_type
		FROM wx_t_v2_elite_fund_form t1 WHERE t1.apply_year = '2020' and channel_weight & 1 = 1 and t1.form_status >= #startFormStatus# and t1.apply_phase = isnull(#applyPhase#, 'q1q2') and t1.delete_flag = 0 and (#bu# is null or t1.bu = #bu#)
		GROUP BY t1.Distributor_id,channel_weight
		union all
		SELECT t1.distributor_id,sum(isnull(final_amount,0)) as fund_value ,'C&I' sales_channel,channel_weight,'Commerical' as channel_type
		FROM wx_t_v2_elite_fund_form t1 WHERE t1.apply_year = '2020' and channel_weight & 2 = 2 and channel_weight & 8 != 8 and t1.form_status >= #startFormStatus# and t1.apply_phase = isnull(#applyPhase#, 'q1q2') and t1.delete_flag = 0 and (#bu# is null or t1.bu = #bu#)
		GROUP BY t1.distributor_id,channel_weight
		union all
		SELECT t1.distributor_id,sum(isnull(final_amount,0)) as fund_value ,'Industrial' sales_channel,max(channel_weight),'Commerical' as channel_type
		FROM wx_t_v2_elite_fund_form t1 WHERE t1.apply_year = '2020' and channel_weight & 8 = 8 and t1.form_status >= #startFormStatus# and t1.apply_phase = isnull(#applyPhase#, 'q1q2') and t1.delete_flag = 0 and (#bu# is null or t1.bu = #bu#)
		GROUP BY t1.distributor_id
		) f1
left join view_customer_region_sales_channel crss on f1.distributor_id = crss.distributor_id
left join wx_t_elite_fund2020_config efc on f1.distributor_id=efc.distributor_id
left join (select pe.distributor_id,sum(isnull(signage, 0) + isnull(seminar, 0) + isnull(oil_change_car, 0) + isnull(equipment_tools,0)) sop_amount from wx_t_sop_support_summary sss 
left join wx_t_partner_o2o_enterprise pe on pe.partner_id=sss.partner_id
where sss.year=2020
group by pe.distributor_id) sope on sope.distributor_id=f1.distributor_id
left join (select distributor_id,customer_name_cn,customer_name_en,company_code 
from view_mid_customer_elite_fund group by distributor_id,customer_name_cn,customer_name_en,company_code) ecc1 on ecc1.distributor_id=f1.distributor_id
where f1.fund_value > 0 and (f1.channel_weight & crss.channel_weight > 0 or crss.distributor_id is null) and (#distributorId# is null or crss.distributor_id=#distributorId#)
and (#channelType# is null or f1.channel_type=#channelType#)
and (#permissionWeight# is null or #executor# is null or exists (select 1 from wx_t_partner_responsible_main xx_prm002 where crss.region_name=xx_prm002.region_name and xx_prm002.fun_flag='bs_region' and xx_prm002.user_id = #executor#))
and (#permissionWeight# is null or #executor# is not null or exists (select 1 from wx_t_partner_responsible_main xx_prm002 where crss.region_name=xx_prm002.region_name and xx_prm002.fun_flag='bs_region' and xx_prm002.user_id = #value.loginUserId#))
) t 
left join wx_t_dic_item di1 on di1.dic_type_code='elites.office.detail' and di1.dic_item_code=t.company_code collate Chinese_PRC_CI_AS
where idx = 1