select * --into wx_t_store202120221116
--delete s
from wx_t_store2021 s where id=363

/****** Script for SelectTopNRows command from SSMS  ******/
SELECT TOP (1000) [id]
      ,[distributor_id]
      ,[planning_customer_name]
      ,[sap_code]
      ,[customer_name]
      ,[region_name]
      ,[channel]
      ,[partner_function]
      ,[ship_to_code]
      ,[partner_name_with_type]
      ,[payment_term]
      ,[sales_org]
      ,[price_level]
      ,[purpose]
      ,[date_from]
      ,[date_to]
      ,[quota_type]
      ,[quota]
      ,[creation_time]
      ,[created_by]
      ,[update_time]
      ,[updated_by]
      ,[quota_ratio]
      ,[is_quota_ratio]
      ,[quota_ratio_sales_org]
      ,[quota_ratio_sales_code]
      ,[dist_id]
      ,[address]
      ,[contact_person]
      ,[contact_person_tel]
      ,[store_id]
	  --into mid_partner_sale_config20221116
	  --delete c
  FROM [PP_MID].[dbo].[mid_partner_sale_config] c where ship_to_code='8476148' and sales_org=2023