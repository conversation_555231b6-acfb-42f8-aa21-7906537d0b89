="union all select (select pe.distributor_id from wx_t_organization o left join wx_t_partner_o2o_enterprise pe on pe.partner_id=o.id where o.organization_name='"&A2&"' and o.status=1) distributor_id"

select distinct o.organization_name, u.user_id, u.login_name, u.ch_name, u.mobile_tel from wx_t_user u 
left join wx_t_organization o on o.id=u.org_id
left join wx_t_partner_o2o_enterprise pe on pe.partner_id=o.id
where u.status=1 and  exists (select 1 from wx_t_userrole ur left join wx_t_role r on ur.role_id=r.role_id where ur.user_id=u.user_id and r.ch_role_name in ('Service_Partner_Manager','CDM_DIST','Caltex_Dealer'))
and pe.distributor_id in ()

union all select (select count(1) from wx_t_user u 
left join wx_t_organization o on o.id=u.org_id
left join wx_t_partner_o2o_enterprise pe on pe.partner_id=o.id
where  exists (select 1 from wx_t_userrole ur left join wx_t_role r on ur.role_id=r.role_id where ur.user_id=u.user_id and r.ch_role_name in ('Service_Partner_Manager','CDM_DIST','Caltex_Dealer'))
and pe.distributor_id=)

="union all select (select count(1) from wx_t_user u "&
"left join wx_t_organization o on o.id=u.org_id "&
"left join wx_t_partner_o2o_enterprise pe on pe.partner_id=o.id "&
"where  u.status=1 and exists (select 1 from wx_t_userrole ur left join wx_t_role r on ur.role_id=r.role_id where ur.user_id=u.user_id and r.ch_role_name in ('Service_Partner_Manager','CDM_DIST','Caltex_Dealer')) "&
"and pe.distributor_id="&D2&")"