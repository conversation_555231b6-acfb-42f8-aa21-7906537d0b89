select * from wx_t_workflow_exe_list el
left join wx_t_workflow_step ws on el.step_id=ws.step_id
left join wx_t_workflow_pre_exe_group eg on eg.id=el.source_key and el.source_type='GROUP'
where ws.workflow_code='CDM_APPLY' and eg.group_name='申请FLSR'

update ws set ws.step_name='市场部专员审核' from wx_t_workflow_exe_list el left join wx_t_workflow_step ws on el.step_id=ws.step_id left join wx_t_workflow_pre_exe_group eg on eg.id=el.source_key and el.source_type='GROUP' LEFT JOIN wx_t_user u on u.user_id=el.source_key and el.source_type='USER' where ws.workflow_code='CIO_MKT_RESOURCE' and ws.step_code='MKT_APPROVE'
