package com.chevron.cdm_mkt.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.text.StrSpliter;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import com.chevron.mktsignageapply.dao.MktSignageApplyMapper;
import com.chevron.mktsignageapply.model.MktSignageApply;
import com.chevron.report.model.PartnerOverallPerformanceField;
import com.chevron.report.model.PartnerOverallPerformanceParam;
import com.chevron.report.model.PartnerOverallPerformanceVo;
import net.sf.json.JSONArray;

import com.chevron.cdm_mkt.constant.CdmMktConstant;
import com.chevron.cdm_mkt.constant.MktApplyType;
import com.chevron.cdm_mkt.dao.PartnerRegionMapper;
import com.chevron.cdm_mkt.dao.ViewBudgetByAsmMapper;
import com.chevron.cdm_mkt.dao.ViewDwCdmMktSellInMapper;
import com.chevron.cdm_mkt.dao.WXTMktApplyMapper;
import com.chevron.cdm_mkt.model.MktFund;
import com.chevron.cdm_mkt.model.ViewMktApply;
import com.chevron.cdm_mkt.model.vo.MktFundVo;
import com.chevron.cdm_mkt.service.MktApplyService;
import com.chevron.cdm_mkt.service.MktFieldService;
import com.chevron.cdm_mkt.service.MktFundService;
import com.chevron.cdm_mkt.util.MktUtil;
import com.chevron.mktcdm.dao.MktCdmApplyMapper;
import com.chevron.mktcdm.model.MktCdmApply;
import com.chevron.mktci.dao.MktCiApplyMapper;
import com.chevron.mktci.model.MktCiApply;
import com.chevron.promotev2.dao.V2PromoteActivityMapper;
import com.chevron.promotev2.model.V2PromoteActivity;
import com.chevron.report.business.BiProcedureBizService;
import com.common.base.BaseResp;
import com.common.exception.WxPltException;
import com.common.util.DateUtil;
import com.sys.dic.model.DicItemVo;
import com.sys.dic.service.DicService;

import org.apache.commons.lang3.StringUtils;
import org.drools.compiler.lang.dsl.DSLMapParser.variable_definition_return;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class MktFundServiceImpl extends MktBaseServiceImpl implements MktFundService {

    @Autowired
    private PartnerRegionMapper partnerRegionMapper;

    @Autowired
    private WXTMktApplyMapper wxtMktApplyMapper;


    @Autowired
    private ViewDwCdmMktSellInMapper viewDwCdmMktSellInMapper;

    @Autowired
    private ViewBudgetByAsmMapper viewBudgetByAsmMapper;

    @Autowired
    @Qualifier("mktApplyServiceImpl")
    private MktApplyService mktApplyService;

    @Autowired
    private MktFieldService mktFieldService;

    @Autowired
    private DicService dicService;
    
    @Autowired
    private BiProcedureBizService biProcedureBizService;
    
    @Autowired
    private MktCiApplyMapper mktCiApplyMapper;
    
    @Autowired
    private V2PromoteActivityMapper v2PromoteActivityMapper;
    
    @Autowired
    private MktCdmApplyMapper mktCdmApplyMapper;

    @Autowired
    private MktSignageApplyMapper mktSignageApplyMapper;
    
    public static final String CONSUMER = "Consumer";
    public static final String COMMERCIAL = "Commercial";
    public static final String OLD_CONSUMER = "OldConsumer";

    public static final String CONSUMER_2021 = "Consumer2021";
    public static final String COMMERCIAL_2021= "Commercial2021";
    
    public static final String CAR_AD = "CAR_AD";
    public static final String OUTSIDE_AD = "OUTSIDE_AD";
    public static final String STORE_SIGN = "STORE_SIGN";



    @Override
    public BaseResp getFundDetail(String mktType, Long partnerId, Long mktId) {
        MktFund mktFund = new MktFund();
        ViewMktApply viewMktApply = mktApplyService.getViewMktApply(mktId, "");
        boolean viewInverstmentInfo = false;
        if (viewMktApply != null) {
            if (!StrUtil.equals(mktType, MktApplyType.STORE_OTHERS.getCode())) {
                if (StrUtil.startWith(mktType, "STORE")) {
                    mktType = MktApplyType.STORE_FRONT_OFFER.getCode();
                    viewInverstmentInfo = true;
                } else {
                    mktType = MktApplyType.SEMINAR_REIMBURSE.getCode();
                }
            }
        }
        int year = Integer.parseInt(DateUtil.getYear(viewMktApply.getCreateTime()));

        String region = getPartnerRegionDetail(partnerId, viewMktApply.getChannel());
        mktFund.setRegionName(region);

        BigDecimal totalFund = getTotalFund(mktType, partnerId, viewMktApply.getChannel(), year);
        mktFund.setTotalFund(totalFund);

        BigDecimal usedFund = getUsedFund(mktType, partnerId, viewMktApply.getChannel(), year);
        mktFund.setUsedFund(usedFund);

        BigDecimal remainFund = MktUtil.getNullAsZero(totalFund).subtract(MktUtil.getNullAsZero(usedFund));
        mktFund.setRemainFund(remainFund);

        BigDecimal totalMargin = BigDecimal.ZERO;
        BigDecimal returnYears = BigDecimal.ZERO;
        if (viewInverstmentInfo) {
            totalMargin = getTotalMargin(partnerId, mktId);
            returnYears = getReturnYears(partnerId, mktId, totalMargin);
        	//使用新的接口获取总毛利和回报期
        	
        }
        mktFund.setTotalMargin(totalMargin);
        mktFund.setReturnYears(returnYears);

        BaseResp baseResp = new BaseResp();
        baseResp.setData(mktFund);
        return baseResp;
    }

    /**
     * @param mktType
     * @param partnerId
     * @param salesChannel
     * @return
     * @deprecated
     */
    public BigDecimal getTotalFund0(String mktType, Long partnerId, String salesChannel) {
        BigDecimal totalFund = BigDecimal.ZERO;

        //通过经销商区域查询累计金额
        //TODO:
        String regionNameDetail = getPartnerRegionDetail(partnerId, salesChannel);

        //通过字典查询累计金额
        String dicCode = null;
        if (StrUtil.equals(salesChannel, CdmMktConstant.SALES_CHANNEL)) {
            if (StrUtil.startWith(mktType, "STORE")) {
                if (StrUtil.equals(mktType, MktApplyType.STORE_OTHERS.getCode())) {
                    dicCode = "cdm.mkt.signage.others.fund";
                } else {
                    dicCode = "cdm.mkt.signage.fund";
                }
            } else if (StrUtil.startWith(mktType, "SEMINAR")) {
                dicCode = "cdm.mkt.seminar.fund";
            }
        } else if (StrUtil.equals(salesChannel, CdmMktConstant.SALES_CHANNEL_CIO)) {
            if (StrUtil.startWith(mktType, "STORE")) {
                dicCode = "cio.mkt.signage.fund";
            } else if (StrUtil.startWith(mktType, "SEMINAR")) {
                dicCode = "cio.mkt.seminar.fund";
            }
        }
        Map<String, Object> dicList = dicService.getDicItemByDicTypeCode(dicCode);
        List<DicItemVo> itemLst = (ArrayList<DicItemVo>) dicList.get("data");

        for (DicItemVo item : itemLst) {
            if (StrUtil.containsIgnoreCase(item.getDicItemCode(), regionNameDetail)) {
                totalFund = new BigDecimal(StrUtil.blankToDefault(item.getDicItemName(), "0.0"));
                break;
            }
        }
        return totalFund;
    }

    /**
     * 通过经销商区域查询累计金额
     *
     * @param mktType
     * @param region
     * @param salesChannel
     * @return
     */
    @Override
    public BigDecimal getTotalFund(String mktType, String region, String salesChannel, int year) {
        BigDecimal totalFund = BigDecimal.ZERO;
        String expenseCode = null;
        List<String> regions = new ArrayList<String>(2);

        //String region = getPartnerRegionDetail(partnerId, salesChannel);
        String regionName = getRegionName(salesChannel, region);
        if (StrUtil.equals(salesChannel, CdmMktConstant.SALES_CHANNEL)) {
            if (StrUtil.equalsAny(regionName, "South-West", "South", "West")) {
                regions = CollUtil.newArrayList(salesChannel + "-South", salesChannel + "-West");
            } else {
                regions = CollUtil.newArrayList(salesChannel + "-" + region);
            }
            if (StrUtil.startWith(mktType, "STORE")) {
                if (StrUtil.equals(mktType, MktApplyType.STORE_OTHERS.getCode())) {
                    expenseCode = "Others";
                } else {
                    expenseCode = "Signage";
                }
            } else if (StrUtil.startWith(mktType, "SEMINAR")) {
                expenseCode = "Seminars";
            }
        } else if (StrUtil.equals(salesChannel, CdmMktConstant.SALES_CHANNEL_CIO)) {
            if (StrUtil.containsAnyIgnoreCase(regionName, "北区")) {
                regions = CollUtil.newArrayList(salesChannel + "-North", salesChannel + "-NE&NW");
            } else if (StrUtil.containsAnyIgnoreCase(regionName, "南区")) {
                regions = CollUtil.newArrayList(salesChannel + "-South", salesChannel + "-SW");
            } else {
                regions = CollUtil.newArrayList(salesChannel + "-" + regionName);
            }
            if (StrUtil.startWith(mktType, "STORE")) {
                expenseCode = "CIO_signage";
            }
        }
        Map<String, Object> params = new HashMap<String, Object>(3);
        params.put("expenseCode", expenseCode);
        params.put("regions", regions);
        params.put("year", year);
        totalFund = viewBudgetByAsmMapper.getTotalBudgetByParams(params);
        return MktUtil.getNullAsZero(totalFund);
    }

    @Override
    public BigDecimal getUsedFund(String mktType, Long partnerId, String salesChannel, int year) {
        String region = getPartnerRegionDetail(partnerId, salesChannel);
        BigDecimal usedFund = getUsedFund(salesChannel, region, mktType, year);
        return MktUtil.getNullAsZero(usedFund);
    }

    @Override
    public BigDecimal getTotalFund(String mktType, Long partnerId, String salesChannel, int year) {
        String region = getPartnerRegionDetail(partnerId, salesChannel);
        BigDecimal totalFund = getTotalFund(mktType, region, salesChannel, year);
        return MktUtil.getNullAsZero(totalFund);
    }

    /**
     * @param salesChannel
     * @param regionName
     * @param mktType
     * @return
     */
    @Override
    public BigDecimal getUsedFund(String salesChannel, String regionName, String mktType, int year) {
        BigDecimal usedFund = BigDecimal.ZERO;
        BigDecimal usedFundOffline = BigDecimal.ZERO;
        List<String> mktTypes = new ArrayList<String>(2);
        regionName = getRegionName(salesChannel, regionName);
        if (StrUtil.equals(salesChannel, CdmMktConstant.SALES_CHANNEL)) {
            if (StrUtil.startWith(mktType, "STORE")) {
                if (StrUtil.equals(mktType, MktApplyType.STORE_OTHERS.getCode())) {//其它
                    mktTypes = CollUtil.newArrayList(MktApplyType.STORE_OTHERS.getCode());
                    if (StrUtil.containsAnyIgnoreCase(regionName, "South", "West")) {
                        BigDecimal usedFund1 = wxtMktApplyMapper.getUsedFundTotal(salesChannel, "South", mktTypes, year);
                        BigDecimal usedFund2 = wxtMktApplyMapper.getUsedFundTotal(salesChannel, "West", mktTypes, year);
                        usedFund = MktUtil.getNullAsZero(usedFund1).add(MktUtil.getNullAsZero(usedFund2));
                    } else {
                        usedFund = wxtMktApplyMapper.getUsedFundTotal(salesChannel, regionName, mktTypes, year);
                    }
                } else {
                    //店招
                    mktTypes = CollUtil.newArrayList(MktApplyType.STORE_FRONT.getCode(), MktApplyType.STORE_IN_STORE.getCode());//, MktApplyType.STORE_FRONT_REIMBURSE.getCode()
                    if (StrUtil.containsAnyIgnoreCase(regionName, "South", "West")) {
                        BigDecimal usedFund1 = wxtMktApplyMapper.getUsedFundTotal(salesChannel, "South", mktTypes, year);
                        BigDecimal usedFund2 = wxtMktApplyMapper.getUsedFundTotal(salesChannel, "West", mktTypes, year);
                        BigDecimal usedFundOffline1 = wxtMktApplyMapper.getUsedFundTotalOffline(salesChannel, "South", year);
                        BigDecimal usedFundOffline2 = wxtMktApplyMapper.getUsedFundTotalOffline(salesChannel, "West", year);
                        usedFund = MktUtil.getNullAsZero(usedFund1).add(MktUtil.getNullAsZero(usedFund2));
                        usedFundOffline = MktUtil.getNullAsZero(usedFundOffline1).add(MktUtil.getNullAsZero(usedFundOffline2));
                    } else {
                        usedFund = wxtMktApplyMapper.getUsedFundTotal(salesChannel, regionName, mktTypes, year);
                        usedFundOffline = wxtMktApplyMapper.getUsedFundTotalOffline(salesChannel, regionName, year);
                    }
                    usedFund = MktUtil.getNullAsZero(usedFund).add(usedFundOffline);
                }
            } else {
                //研讨会
                if (StrUtil.containsAnyIgnoreCase(regionName, "South", "West")) {
                    BigDecimal usedFund1 = wxtMktApplyMapper.getSeminarUsedFundTotal(salesChannel, "South", year);
                    BigDecimal usedFund2 = wxtMktApplyMapper.getSeminarUsedFundTotal(salesChannel, "West", year);
                    usedFund = MktUtil.getNullAsZero(usedFund1).add(MktUtil.getNullAsZero(usedFund2));
                } else {
                    usedFund = wxtMktApplyMapper.getSeminarUsedFundTotal(salesChannel, regionName, year);
                }
            }
        } else if (StrUtil.equals(salesChannel, CdmMktConstant.SALES_CHANNEL_CIO)) {
            if (StrUtil.startWith(mktType, "STORE")) {
                //店招
                mktTypes = CollUtil.newArrayList(MktApplyType.STORE_FRONT.getCode());
                if (StrUtil.equals(regionName, "北区")) {
                    BigDecimal usedFund1 = wxtMktApplyMapper.getUsedFundTotal(salesChannel, "NE&NW", mktTypes, year);
                    BigDecimal usedFund2 = wxtMktApplyMapper.getUsedFundTotal(salesChannel, "North", mktTypes, year);
                    usedFund = MktUtil.getNullAsZero(usedFund1).add(MktUtil.getNullAsZero(usedFund2));
                } else if (StrUtil.equals(regionName, "南区")) {
                    BigDecimal usedFund1 = wxtMktApplyMapper.getUsedFundTotal(salesChannel, "South", mktTypes, year);
                    BigDecimal usedFund2 = wxtMktApplyMapper.getUsedFundTotal(salesChannel, "SW", mktTypes, year);
                    usedFund = MktUtil.getNullAsZero(usedFund1).add(MktUtil.getNullAsZero(usedFund2));
                } else {
                    usedFund = wxtMktApplyMapper.getUsedFundTotal(salesChannel, regionName, mktTypes, year);
                }
            }
        }
        return usedFund;
    }
    
//    @Override
//    public BigDecimal getApplyingFund(String salesChannel, String regionName, String mktType) {
//        BigDecimal applyingFund = BigDecimal.ZERO;
//        List<String> mktTypes = new ArrayList<String>(2);
//        regionName = getRegionName(salesChannel, regionName);
//        if (StrUtil.equals(salesChannel, CdmMktConstant.SALES_CHANNEL)) {
//            if (StrUtil.startWith(mktType, "STORE")) {
//                if (StrUtil.equals(mktType, MktApplyType.STORE_OTHERS.getCode())) {//其它
//                    mktTypes = CollUtil.newArrayList(MktApplyType.STORE_OTHERS.getCode());
//                    if (StrUtil.containsAnyIgnoreCase(regionName, "South", "West")) {
//                        BigDecimal usedFund1 = wxtMktApplyMapper.getApplyingFundTotal(salesChannel, "South", mktTypes);
//                        BigDecimal usedFund2 = wxtMktApplyMapper.getApplyingFundTotal(salesChannel, "West", mktTypes);
//                        applyingFund = MktUtil.getNullAsZero(usedFund1).add(MktUtil.getNullAsZero(usedFund2));
//                    } else {
//                    	applyingFund = wxtMktApplyMapper.getApplyingFundTotal(salesChannel, regionName, mktTypes);
//                    }
//                } else {
//                    //店招
//                    mktTypes = CollUtil.newArrayList(MktApplyType.STORE_FRONT.getCode(), MktApplyType.STORE_IN_STORE.getCode());//, MktApplyType.STORE_FRONT_REIMBURSE.getCode()
//                    if (StrUtil.containsAnyIgnoreCase(regionName, "South", "West")) {
//                        BigDecimal usedFund1 = wxtMktApplyMapper.getApplyingFundTotal(salesChannel, "South", mktTypes);
//                        BigDecimal usedFund2 = wxtMktApplyMapper.getApplyingFundTotal(salesChannel, "West", mktTypes);
//                        applyingFund = MktUtil.getNullAsZero(usedFund1).add(MktUtil.getNullAsZero(usedFund2));
//                    } else {
//                    	applyingFund = wxtMktApplyMapper.getApplyingFundTotal(salesChannel, regionName, mktTypes);
//                    }
//                }
//            } else {
//                //研讨会
//                mktTypes = CollUtil.newArrayList(MktApplyType.SEMINAR.getCode());
//                if (StrUtil.containsAnyIgnoreCase(regionName, "South", "West")) {
//                    BigDecimal applyingFund1 = wxtMktApplyMapper.getApplyingFundTotal(salesChannel, "South", mktTypes);
//                    BigDecimal applyingFund2 = wxtMktApplyMapper.getApplyingFundTotal(salesChannel, "West", mktTypes);
//                    applyingFund = MktUtil.getNullAsZero(applyingFund1).add(MktUtil.getNullAsZero(applyingFund2));
//                } else {
//                	applyingFund = wxtMktApplyMapper.getApplyingFundTotal(salesChannel, regionName, mktTypes);
//                }
//            }
//        } else if (StrUtil.equals(salesChannel, CdmMktConstant.SALES_CHANNEL_CIO)) {
//            if (StrUtil.startWith(mktType, "STORE")) {
//                //店招
//                mktTypes = CollUtil.newArrayList(MktApplyType.STORE_FRONT.getCode());
//                if (StrUtil.equals(regionName, "北区")) {
//                    BigDecimal applyingFund1 = wxtMktApplyMapper.getApplyingFundTotal(salesChannel, "NE&NW", mktTypes);
//                    BigDecimal applyingFund2 = wxtMktApplyMapper.getApplyingFundTotal(salesChannel, "North", mktTypes);
//                    applyingFund = MktUtil.getNullAsZero(applyingFund1).add(MktUtil.getNullAsZero(applyingFund2));
//                } else if (StrUtil.equals(regionName, "南区")) {
//                    BigDecimal applyingFund1 = wxtMktApplyMapper.getApplyingFundTotal(salesChannel, "South", mktTypes);
//                    BigDecimal applyingFund2 = wxtMktApplyMapper.getApplyingFundTotal(salesChannel, "SW", mktTypes);
//                    applyingFund = MktUtil.getNullAsZero(applyingFund1).add(MktUtil.getNullAsZero(applyingFund2));
//                } else {
//                	applyingFund = wxtMktApplyMapper.getApplyingFundTotal(salesChannel, regionName, mktTypes);
//                }
//            }
//        }
//        return applyingFund;
//    }

    protected MktFundVo getMktFund(String salesChannel, String region, int year) {
        MktFundVo fundVo = new MktFundVo();
        fundVo.setSalesChannel(salesChannel);
        fundVo.setRegionName(getRegionName(salesChannel, region));
        fundVo.setRegions(getRegions(salesChannel, region));
        BigDecimal totalFund = BigDecimal.ZERO;
        BigDecimal usedFund = BigDecimal.ZERO;

        if (StrUtil.equals(salesChannel, CdmMktConstant.SALES_CHANNEL)) {
            //店招
            totalFund = getTotalFund(MktApplyType.STORE_FRONT.getCode(), region, salesChannel, year);
            usedFund = getUsedFund(salesChannel, region, MktApplyType.STORE_FRONT.getCode(), year);
            fundVo.setSignageTotalFund(totalFund);
            fundVo.setSignageUsedFund(usedFund);
            fundVo.setSignageProgramId(viewBudgetByAsmMapper.getProgramId(Arrays.asList("Signage", "Seminars", "Others"), year));

            //研讨会
            totalFund = getTotalFund(MktApplyType.SEMINAR.getCode(), region, salesChannel, year);
            usedFund = getUsedFund(salesChannel, region, MktApplyType.SEMINAR.getCode(), year);
            fundVo.setSeminarTotalFund(totalFund);
            fundVo.setSeminarUsedFund(usedFund);
            fundVo.setSeminarProgramId(fundVo.getSignageProgramId());

            //其它
            totalFund = getTotalFund(MktApplyType.STORE_OTHERS.getCode(), region, salesChannel, year);
            usedFund = getUsedFund(salesChannel, region, MktApplyType.STORE_OTHERS.getCode(), year);
            fundVo.setOthersTotalFund(totalFund);
            fundVo.setOthersUsedFund(usedFund);
            fundVo.setOthersProgramId(fundVo.getSignageProgramId());

        } else if (StrUtil.equals(salesChannel, CdmMktConstant.SALES_CHANNEL_CIO)) {
            //店招
            totalFund = getTotalFund(MktApplyType.STORE_FRONT.getCode(), region, salesChannel, year);
            usedFund = getUsedFund(salesChannel, region, MktApplyType.STORE_FRONT.getCode(), year);
            fundVo.setSignageTotalFund(totalFund);
            fundVo.setSignageUsedFund(usedFund);
            fundVo.setSignageProgramId(viewBudgetByAsmMapper.getProgramId(Arrays.asList("CIO_signage"), year));
        }

        return fundVo;
    }

    @Override
    public List<MktFundVo> getFundList(String salesChannel, String region, int year) {
        List<MktFundVo> list = new ArrayList<MktFundVo>(3);
        if (StrUtil.equals(salesChannel, CdmMktConstant.SALES_CHANNEL)) {
            if (StrUtil.isBlank(region)) { //All
                list.add(getMktFund(salesChannel, "South-West", year));
                list.add(getMktFund(salesChannel, "East", year));
                list.add(getMktFund(salesChannel, "North", year));
            } else {
                list.add(getMktFund(salesChannel, region, year));
            }
        } else if (StrUtil.equals(salesChannel, CdmMktConstant.SALES_CHANNEL_CIO)) {
            if (StrUtil.isBlank(region)) {//All
                //list.add(getMktFund(salesChannel, "北区"));//北区
                //list.add(getMktFund(salesChannel, "南区"));//南区
                list.add(getMktFund(salesChannel, "South", year));
                list.add(getMktFund(salesChannel, "SW", year));
                list.add(getMktFund(salesChannel, "North", year));
                list.add(getMktFund(salesChannel, "NE&NW", year));
            } else if (StrUtil.isNotBlank(region) && region.contains(",")) {
                String[] regionNames = StrSpliter.splitToArray(region, ",", -1, true, true);
                if (ArrayUtil.isNotEmpty(regionNames)) {
                    for (int i = 0; i < regionNames.length; i++) {
                        String[] regionParts = StrSpliter.splitToArray(regionNames[i], "-", -1, true, true);
                        list.add(getMktFund(salesChannel, (String) ArrayUtil.get(regionParts, 1), year));
                    }
                }
            } else {
                list.add(getMktFund(salesChannel, region, year));
            }
        }
        return list;
    }


    /**
     * 回报年限
     *
     * @param partnerId
     * @param mktId
     * @return
     */
    @Override
    public BigDecimal getReturnYears(Long partnerId, Long mktId, BigDecimal totalMargin) {
        BigDecimal returnYears = BigDecimal.ZERO;
        String moneyCost = "0";
        ViewMktApply viewMktApply = getViewMktApply(mktId, "");
        if (viewMktApply != null) {
            Map<String, String> fields = mktFieldService.getFieldValuesByIds(viewMktApply.getAuditApplyIds());
            moneyCost = StrUtil.blankToDefault(MapUtil.getStr(fields, "moneyCost"), "0");
        }
        /*if (viewMktApply.getId2() != null) {
            moneyCost = mktFieldService.getFieldValue(viewMktApply.getId2(), "moneyCost");
        }*/
        if (totalMargin.compareTo(BigDecimal.ZERO) > 0 && StrUtil.isNotBlank(moneyCost)) {
            returnYears = new BigDecimal(moneyCost).divide(totalMargin, 2, BigDecimal.ROUND_HALF_UP);
        }
        return returnYears;
    }

    @Override
    public BigDecimal getTotalMargin(Long partnerId, Long mktId) {
        ViewMktApply viewMktApply = getViewMktApply(mktId, "");
        Map<String, String> fields = mktFieldService.getFieldValuesByIds(viewMktApply.getApplyIds());
        String contractYears = StrUtil.blankToDefault(MapUtil.getStr(fields, "contractYear"), "0");

        Map<String, Date> dateRange = DateUtil.getLastQuarter(viewMktApply.getCreateTime());//创建记录时的日期起算总毛利
        Date startDate = dateRange.get("start");
        Date endDate = dateRange.get("end");
        //List<Map<String, Object>> categoryLst = viewDwCdmMktSellInMapper.getMonthlyMarginCategoryList(partnerId, startDate, endDate);
        BigDecimal sumVolume = viewDwCdmMktSellInMapper.getTotalVolumeByOrgQuarter(viewMktApply.getChannel(), partnerId, startDate, endDate);
        BigDecimal sumMargin = viewDwCdmMktSellInMapper.getTotalMarginByOrgQuarter(viewMktApply.getChannel(), partnerId, startDate, endDate);
        //BigDecimal quarterMargin = CollUtil.isNotEmpty(categoryLst) ? sumMargin.divide(new BigDecimal(categoryLst.size()), 2, BigDecimal.ROUND_HALF_UP) : BigDecimal.ZERO;
        //BigDecimal avgMargin = MktUtil.getNullAsZero(quarterMargin).divide(new BigDecimal("3.0"), 2, BigDecimal.ROUND_HALF_UP);//产品平均毛利
        BigDecimal avgMargin = BigDecimal.ZERO;
        if (sumVolume != null && sumVolume.compareTo(BigDecimal.ZERO) > 0) {
            avgMargin = MktUtil.getNullAsZero(sumMargin).divide(sumVolume, 6, BigDecimal.ROUND_HALF_UP);//:毛利和/销量和
        }
        BigDecimal totalVolume = getTotalTargetVolume(fields);
        //平均毛利（即:毛利和/销量和）*预计总销量（即小包装+大包装之和）*12*合同年限
        BigDecimal totalMargin = MktUtil.getNullAsZero(avgMargin).multiply(new BigDecimal("12.0")).multiply(new BigDecimal(contractYears))
                .multiply(totalVolume);
        return totalMargin;
    }


    protected BigDecimal getTotalTargetVolume(Map<String, String> fields) {
        String[] packTypes = new String[]{"PackFullySyn", "PackBlendSyn", "PackFormula", "PackClassicExtra", "PackOthers"};
        BigDecimal total = BigDecimal.ZERO;
        if (CollUtil.isNotEmpty(fields)) {
            for (String key : fields.keySet()) {
                for (int i = 0; i < packTypes.length; i++) {
                    if (StrUtil.equalsIgnoreCase(packTypes[i], key.substring(5))) {
                        BigDecimal val = new BigDecimal(StrUtil.nullToDefault(MapUtil.getStr(fields, key), "0.0"));
                        total = total.add(MktUtil.getNullAsZero(val));
                    }
                }
            }
        }
        return total;
    }

    public String getPartnerRegionDetail(Long partnerId, String salesChannel) {
        String regionName = partnerRegionMapper.getPartnerRegion(partnerId, salesChannel);
        String[] regionNameArr = StrSpliter.splitToArray(regionName, "-", -1, true, true);
        return StrUtil.nullToDefault((String) ArrayUtil.get(regionNameArr, 1), "");
    }

    private String getRegionName(String salesChannel, String region) {
        String regionName = null;
        if (StrUtil.equals(salesChannel, CdmMktConstant.SALES_CHANNEL)) {
            if (StrUtil.containsAnyIgnoreCase(region, "South", "West")) {
                regionName = "South-West";
            } else {
                regionName = region;
            }
        } else if (StrUtil.equals(salesChannel, CdmMktConstant.SALES_CHANNEL_CIO)) {
            /*if (StrUtil.containsIgnoreCase(region, "North") && StrUtil.containsIgnoreCase(region, "NE&NW")) {
                regionName = "北区";
            } else if (StrUtil.containsIgnoreCase(region, "South") && StrUtil.containsIgnoreCase(region, "SW")) {
                regionName = "南区";
            } */
            /*if (region!=null && region.contains(",")) {
                String[] regionNames = StrSpliter.splitToArray(region, ",", -1, true, true);
                regionName = "";
            } else {*/
            regionName = region;
            //}
        }
        return regionName;
    }

    private String getRegions(String salesChannel, String region) {
        String regionName = null;
        if (StrUtil.equals(salesChannel, CdmMktConstant.SALES_CHANNEL)) {
            if (StrUtil.containsIgnoreCase(region, "South") && StrUtil.containsIgnoreCase(region, "West")) {
                regionName = "CDM-South,CDM-West";
            } else {
                regionName = "CDM-" + region;
            }
        } else if (StrUtil.equals(salesChannel, CdmMktConstant.SALES_CHANNEL_CIO)) {
            if (StrUtil.containsIgnoreCase(region, "North") && StrUtil.containsIgnoreCase(region, "NE&NW")) {
                regionName = "C&I-North,C&I-NE&NW";
            } else if (StrUtil.containsIgnoreCase(region, "South") && StrUtil.containsIgnoreCase(region, "SW")) {
                regionName = "C&I-South,C&I-SW";
            } else {
                regionName = "C&I-" + region;
            }
        }
        return regionName;
    }

    private String getMktTypeName(String salesChannel, String mktType) {
        String mktTypeName = null;
        if (StrUtil.equals(salesChannel, CdmMktConstant.SALES_CHANNEL)) {
            if (StrUtil.startWith(mktType, "STORE")) {
                if (StrUtil.equals(mktType, MktApplyType.STORE_OTHERS.getCode())) {//其它
                    mktTypeName = "其它";
                } else {
                    mktTypeName = "店招";
                }
            } else {//研讨会
                mktTypeName = "研讨会";
            }
        } else if (StrUtil.equals(salesChannel, CdmMktConstant.SALES_CHANNEL_CIO)) {
            if (StrUtil.startWith(mktType, "STORE")) {
                mktTypeName = "店招";
            } else {
                mktTypeName = "其它";
            }
        }
        return mktTypeName;
    }

	@Override
	public String getBudgetExpenseCode(String mktType, String salesChannel) throws WxPltException {
        if (StrUtil.equals(salesChannel, CdmMktConstant.SALES_CHANNEL)) {
            if (StrUtil.startWith(mktType, "STORE")) {
                if (StrUtil.equals(mktType, MktApplyType.STORE_OTHERS.getCode())) {
                    return "Others";
                } else {
                	return "Signage";
                }
            } else if (StrUtil.startWith(mktType, "SEMINAR")) {
            	return "Seminars";
            }
        } else if (StrUtil.equals(salesChannel, CdmMktConstant.SALES_CHANNEL_CIO)) {
            if (StrUtil.startWith(mktType, "STORE")) {
            	return "CIO_signage";
            }
        }
        throw new WxPltException("不支持" + mktType + "申请的预算");
	}
	
	
	 @Override
		public BaseResp getNewFundDetail(String mktType, Long partnerId, Long mktId,String salesChannel) {
		        MktFund mktFund = new MktFund();
		        boolean viewInverstmentInfo = false;
		        if(OLD_CONSUMER.equals(salesChannel)) {
		            if (!StrUtil.equals(mktType, MktApplyType.STORE_OTHERS.getCode())) {
		                if (StrUtil.startWith(mktType, "STORE")) {
		                    viewInverstmentInfo = true;
		                }
		           }
		        }else if(COMMERCIAL.equals(salesChannel)) {
		        	if(CAR_AD.equals(mktType) || OUTSIDE_AD.equals(mktType) || STORE_SIGN.equals(mktType)) {
		        		 viewInverstmentInfo = true;
		        	}
		        }else if(CONSUMER.equals(salesChannel)) {
		        	viewInverstmentInfo = true;
		        }else if(COMMERCIAL_2021.equals(salesChannel) || CONSUMER_2021.equals(salesChannel)) {
                    if(CAR_AD.equals(mktType) || OUTSIDE_AD.equals(mktType) || STORE_SIGN.equals(mktType)) {
                        viewInverstmentInfo = true;
                    }
                }
		        BigDecimal totalMargin = BigDecimal.ZERO;
		        BigDecimal returnYears = BigDecimal.ZERO;
		        if (viewInverstmentInfo) {
		        	//使用新的接口获取总毛利和回报期
		        	totalMargin = getNewTotalMargin(partnerId,mktId,salesChannel);
		        	returnYears = getNewReturnYears(partnerId,mktId,totalMargin,salesChannel);
		        }
		        mktFund.setTotalMargin(totalMargin);
		        mktFund.setReturnYears(returnYears);

		        BaseResp baseResp = new BaseResp();
		        baseResp.setData(mktFund);
		        return baseResp;
		    }
		 
		 public BigDecimal getNewTotalMargin(Long partnerId,Long mktId,String salesChannel) {
			    Date submitTimeDate = null;
			    BigDecimal totalVolume = BigDecimal.ZERO;
			    String contractYears = "0";
//			    String salesChannel = salesChannel;
			    //老金富力
			    if(OLD_CONSUMER.equals(salesChannel)) {
			       ViewMktApply viewMktApply = getViewMktApply(mktId, "");
		           Map<String, String> fields = mktFieldService.getFieldValuesByIds(viewMktApply.getApplyIds());
		            contractYears = StrUtil.blankToDefault(MapUtil.getStr(fields, "contractYear"), "0");
		            submitTimeDate = viewMktApply.getCreateTime();
		           totalVolume = getTotalTargetVolumes(fields);
                    salesChannel = CONSUMER;
			    }else if(COMMERCIAL.equals(salesChannel)){
			    	//德乐
			    	MktCiApply mktCiApply = mktCiApplyMapper.selectByPrimaryKey(mktId);
			    	submitTimeDate = mktCiApply.getReqTime();
			    	//获取合同年限
			    	 JSONObject storeJson = new JSONObject(mktCiApply.getStoreInfo());
			    	 JSONArray productJson=JSONArray.fromObject(mktCiApply.getProductInfo().toString());
			    	 if(storeJson.has("storeCooperationyear")&& storeJson.getInt("storeCooperationyear")!=0) {
			    		 contractYears = String.valueOf(storeJson.getInt("storeCooperationyear"));
				    }
			    	 totalVolume = getTotalTargetVolumn(partnerId,productJson,submitTimeDate);
			    }else if(CONSUMER.equals(salesChannel)) {
			    	//新金富力
			    	MktCdmApply mktCdmApply = mktCdmApplyMapper.selectByPrimaryKey(mktId);
			    	submitTimeDate = mktCdmApply.getReqTime();
			    	//获取合同年线
			    	 JSONObject storeJson = new JSONObject(mktCdmApply.getApplyBaseInfo());
			    	 JSONArray productJson=JSONArray.fromObject(mktCdmApply.getSalesInfo().toString());
			    	 if(storeJson.has("storeCooperationyear") && storeJson.getInt("storeCooperationyear")!=0) {
				    	    contractYears = String.valueOf(storeJson.getInt("storeCooperationyear"));
				    	 }
			    	 totalVolume = getTotalTargetCdmVolumn(productJson);
			    }else if(CONSUMER_2021.equals(salesChannel) || COMMERCIAL_2021.equals(salesChannel)) {
                    //2021新店招
                    MktSignageApply mktSignageApply = mktSignageApplyMapper.selectByPrimaryKey(mktId);
                    submitTimeDate = mktSignageApply.getReqTime();
                    //获取合同年限
                    JSONObject storeJson = new JSONObject(mktSignageApply.getStoreInfo());
                    JSONArray productJson=JSONArray.fromObject(mktSignageApply.getProductInfo().toString());
                    if(storeJson.has("storeCooperationyear")&& storeJson.getInt("storeCooperationyear")!=0) {
                        contractYears = String.valueOf(storeJson.getInt("storeCooperationyear"));
                    }
                    totalVolume = getTotalTargetSignageVolumn(salesChannel,productJson);
                }
			    //根据distributorId 获取partnerId;
			    Long distributorId = v2PromoteActivityMapper.getPartnerIdByDistributorId(partnerId);
			    
		        BigDecimal avgMargin;
		        if(CONSUMER_2021.equals(salesChannel) || COMMERCIAL_2021.equals(salesChannel)){
                    avgMargin = avgMargin2021(salesChannel,distributorId,submitTimeDate);
                }else {
                    avgMargin = avgMargin(salesChannel,distributorId,submitTimeDate);
                }
		        //平均毛利（即:毛利和/销量和）*预计总销量（即小包装+大包装之和）*12*合同年限
		        BigDecimal totalMargin = MktUtil.getNullAsZero(avgMargin).multiply(new BigDecimal(12)).multiply(new BigDecimal(contractYears))
		                .multiply(totalVolume);
		        return totalMargin;
		 }

    private BigDecimal avgMargin2021(String salesChannel, Long partnerId, Date submitTimeDate) {
        BigDecimal avgMargin = BigDecimal.ZERO;

        PartnerOverallPerformanceParam param = new PartnerOverallPerformanceParam();
        param.setPartnerId(partnerId.toString());
        if(CONSUMER_2021.equals(salesChannel)){
            param.setProductChannel("Consumer");
        }else if(COMMERCIAL_2021.equals(salesChannel)){
            param.setProductChannel("Commercial");
        }
        List<PartnerOverallPerformanceVo> partnerOverallPerformanceVos = biProcedureBizService.queryPartnerOverallPerformance(param
                , PartnerOverallPerformanceField.PARTNER_ID
                , PartnerOverallPerformanceField.ROLLING_12_MONTH_AVG_SELL_IN_MARGIN_RMB
                , PartnerOverallPerformanceField.ROLLING_12_MONTH_SELL_IN);

        if(!CollectionUtils.isEmpty(partnerOverallPerformanceVos)){
            PartnerOverallPerformanceVo partnerOverallPerformanceVo = partnerOverallPerformanceVos.get(0);
//            BigDecimal actLitersBig = new BigDecimal(partnerOverallPerformanceVo.getRolling12MonthSellIn());
            BigDecimal actMarginRmbBig = new BigDecimal(partnerOverallPerformanceVo.getRolling12MonthAvgSellInMarginRmb());
//            if (actLitersBig != null && actLitersBig.compareTo(BigDecimal.ZERO) > 0) {
//                avgMargin =  MktUtil.getNullAsZero(actMarginRmbBig).divide(actLitersBig, 6, BigDecimal.ROUND_HALF_UP);//:毛利和/销量和
//            }
            avgMargin =  MktUtil.getNullAsZero(actMarginRmbBig).divide(BigDecimal.ONE, 6, BigDecimal.ROUND_HALF_UP);
        }

        return avgMargin;
    }

    public BigDecimal getNewReturnYears(Long partnerId,Long mktId,BigDecimal totalMargin,String salesChannel) {
			   BigDecimal returnYears = BigDecimal.ZERO;
		        String moneyCost = "0";
//		        String salesChannel = salesChannel;
		        if(OLD_CONSUMER.equals(salesChannel)) {
		        	//老流程金富力
		        ViewMktApply viewMktApply = getViewMktApply(mktId, "");
		        if (viewMktApply != null) {
		            Map<String, String> fields = mktFieldService.getFieldValuesByIds(viewMktApply.getAuditApplyIds());
		            moneyCost = StrUtil.blankToDefault(MapUtil.getStr(fields, "moneyCost"), "0");
		        }
		        }else if(COMMERCIAL.equals(salesChannel)) {
		        	//新流程德乐
		        	MktCiApply mktCiApply = mktCiApplyMapper.selectByPrimaryKey(mktId);
			    	JSONObject storeSignJson = new JSONObject(mktCiApply.getStoreSignInfo());
			    	moneyCost = (String) storeSignJson.get("signboardQuote");
		        }else if(CONSUMER.equals(salesChannel)) {
		        	//新流程金富力
		        	MktCdmApply mktCdmApply = mktCdmApplyMapper.selectByPrimaryKey(mktId);
		        	JSONObject storeSignJson = new JSONObject(mktCdmApply.getApplyBaseInfo());
			    	moneyCost = storeSignJson.get("signboardQuote")==null?"0":String.valueOf(storeSignJson.get("signboardQuote"));//自定義字段
		        }else if(CONSUMER_2021.equals(salesChannel) || COMMERCIAL_2021.equals(salesChannel)) {
                    //2021新店招
                    MktSignageApply mktSignageApply = mktSignageApplyMapper.selectByPrimaryKey(mktId);
                    JSONObject storeSignJson = new JSONObject(mktSignageApply.getStoreSignInfo());
                    moneyCost = (String) storeSignJson.get("signboardQuote");
                }

		        if (totalMargin.compareTo(BigDecimal.ZERO) > 0 && StrUtil.isNotBlank(moneyCost)) {
		            returnYears = new BigDecimal(moneyCost).divide(totalMargin, 2, BigDecimal.ROUND_HALF_UP);
		        }
		        return returnYears;
		 }
		 
		 @SuppressWarnings("unlikely-arg-type")
		public BigDecimal avgMargin(String salseChannel,Long distributorId, Date submitDate) {
		       Calendar submitCalendar = Calendar.getInstance();
		       submitCalendar.setTime(submitDate);
		       int endYear = submitCalendar.get(Calendar.YEAR);
		       int endMonth = submitCalendar.get(Calendar.MONTH)+1;
		       int startYear = 0;
		       int startMonth = 0;
		       if(endMonth<12) {
		    	   startYear = endYear -1;
		    	   startMonth = endMonth+1;
		       }else{
		    	   startYear = endYear;
		    	   startMonth = 1;
		       }
		       Double actLiters = 0D;
		       Double actMarginRmb = 0D;
		       if(startYear == endYear) {
		       List<Map<String, Object>> startList = biProcedureBizService.querySellInByProduct(startYear, salseChannel, distributorId);
		       for (Map<String, Object> map : startList) {
		            for (String s : map.keySet()) {
		            	if(s.equals("act_liters")) {
		            		actLiters += Double.valueOf((String) map.get(s));
		            	}
		            	if(s.equals("act_margin_rmb")) {
		            		actMarginRmb += Double.valueOf((String) map.get(s));
		            	}
		            }
		        }
		       }else {
		    	   List<Map<String, Object>> startList = biProcedureBizService.querySellInByProduct(startYear, salseChannel, distributorId);
		    	     for (Map<String, Object> map : startList) {
		    	       if(startMonth <= Integer.valueOf(map.get("month").toString())) {
		 	            for (String s : map.keySet()) {
		 	            	if(s.equals("act_liters")) {
		 	            		actLiters += Double.valueOf((String) map.get(s));
		 	            	}
		 	            	if(s.equals("act_margin_rmb")) {
		 	            		actMarginRmb += Double.valueOf((String) map.get(s));
		 	            	}
		 	            }
		    	    	 }
		 	        }
		    	   List<Map<String, Object>> endList = biProcedureBizService.querySellInByProduct(endYear, salseChannel, distributorId);
		    	    for (Map<String, Object> map : endList) {
			    	       if(Integer.valueOf(map.get("month").toString())<=endMonth) {
			 	            for (String s : map.keySet()) {
			 	            	if(s.equals("act_liters")) {
			 	            		actLiters += Double.valueOf((String) map.get(s));
			 	            	}
			 	            	if(s.equals("act_margin_rmb")) {
			 	            		actMarginRmb += Double.valueOf((String) map.get(s));
			 	            	}
			 	            }
			    	    	 }
			 	        }
		       }
		       BigDecimal actLitersBig=new BigDecimal(actLiters);
		       BigDecimal actMarginRmbBig=new BigDecimal(actMarginRmb);
		       BigDecimal avgMargin = BigDecimal.ZERO;
		       if (actLitersBig != null && actLitersBig.compareTo(BigDecimal.ZERO) > 0) {
		    	   avgMargin =  MktUtil.getNullAsZero(actMarginRmbBig).divide(actLitersBig, 6, BigDecimal.ROUND_HALF_UP);//:毛利和/销量和
		        }
		       return avgMargin; 
		 }
		 
		 public BigDecimal getTotalTargetVolumn(Long partnerId,JSONArray productJson,Date submitTimeDate) {
//			 Double targetVolumeLitre = 0D;
//			 Double targetVolumeBucket = 0D;
//			 BigDecimal targetVolumn = BigDecimal.ZERO;
//			 BigDecimal sizeBigDecimal = BigDecimal.ZERO;
//			 Double totalRise = 0D;
//			 Calendar submitCalendar = Calendar.getInstance();
//		     submitCalendar.setTime(submitTimeDate);
//		     int endYear = submitCalendar.get(Calendar.YEAR);
//		     int endMonth = submitCalendar.get(Calendar.MONTH)+1;
//		     int startYear = 0;
//		     int startMonth = 0;
//		     if(endMonth<12) {
//		    	   startYear = endYear -1;
//		    	   startMonth = endMonth+1;
//		       }else{
//		    	   startYear = endYear;
//		    	   startMonth = 1;
//		       }
//		     String startTime = null;
//		     String endTime = null;
//		     if(startMonth<10) {
//		    	 startTime = startYear +"-0"+startMonth+"-01";
//		    	 endTime = endYear + "-0" + startMonth+"-01";
//		     }else {
//		    	 startTime = startYear +"-"+startMonth+"-01"; 
//		    	 endTime = endYear + "-" + startMonth+"-01";
//		     }
//			 if(productJson.size()>0) {
//			    sizeBigDecimal = new BigDecimal(productJson.size());
//			 }
//			 List<String> productList = new ArrayList<String>();
//			 
//			 Map<String, Object> map = new HashMap<String, Object>();
//		     map.put("distributorId", partnerId);
//		     map.put("startDate", startTime);
//		     map.put("endDate", endTime);
//		     List<V2PromoteActivity> promoteActivity1 =  v2PromoteActivityMapper.getProductCode(map);
//		     V2PromoteActivity promoteActivity2 =  v2PromoteActivityMapper.getProductLitersAndMargins(map);
//		     int flagNum = 0;
//		     String sapCodeLiString ="";
//		     if(promoteActivity1.size()>0) {
//		     for(V2PromoteActivity tt: promoteActivity1) {
//		    	 sapCodeLiString = tt.getProductCode() + "," + sapCodeLiString;
//			  }
//		     }
		     
//		     if(promoteActivity1.size()>0) {
//			 for(int i=0;i<productJson.size();i++){
//				 //3、把里面的对象转化为JSONObject
//				  net.sf.json.JSONObject product = productJson.getJSONObject(i); 
////				  targetVolumeLitre +=Double.valueOf((String) product.get("targetVolumeLitre"));
////				  targetVolumeBucket +=Double.valueOf((String) product.get("targetVolumeBucket"));
////				  String sku = product.getString("sku");
////				  productList.add(sku);
//				  if(!product.getString("targetVolumeLitre").isEmpty()) {
//				     totalRise = totalRise+product.getDouble("targetVolumeLitre");
//				  }
//				     if(!sapCodeLiString.contains(sku)) {
//				    	 flagNum++;
//				     }
//			 }
//		     }
//		     BigDecimal targetVolumeLitreBigAvg=BigDecimal.ZERO;
//		     BigDecimal targetVolumeBucketBigAvg=BigDecimal.ZERO;
//		     if(flagNum>0) {
//		    	 targetVolumeLitreBigAvg = new BigDecimal(promoteActivity2.getSumProductLiters()*flagNum);
//		    	 targetVolumeBucketBigAvg = new BigDecimal(promoteActivity2.getSumProductMargins()*flagNum);
//		     }
//		     //获取产品平均毛利
//		     map.put("productSapCode", productList);
//		     V2PromoteActivity promoteActivity =  v2PromoteActivityMapper.getProductLitersAndMargins(map);
//		     BigDecimal targetVolumeLitreBig=new BigDecimal(promoteActivity.getSumProductLiters());
//		     BigDecimal targetVolumeBucketBig=new BigDecimal(promoteActivity.getSumProductMargins());
//		     targetVolumeLitreBig = targetVolumeLitreBig.add(targetVolumeLitreBigAvg);
//		     targetVolumeBucketBig = targetVolumeBucketBig.add(targetVolumeBucketBigAvg);
//		     BigDecimal totalRiseBig=new BigDecimal(totalRise);
//			 if(targetVolumeLitreBig.compareTo(BigDecimal.ZERO) > 0) {
//			    targetVolumn =  (MktUtil.getNullAsZero(targetVolumeBucketBig).divide(targetVolumeLitreBig, 6, BigDecimal.ROUND_HALF_UP)).multiply(totalRiseBig);
//			 }
//			 
			 Double totalRise = 0D;
			 for(int i=0;i<productJson.size();i++){
				 //3、把里面的对象转化为JSONObject
				  net.sf.json.JSONObject product = productJson.getJSONObject(i); 
				  if(!product.getString("targetVolumeLitre").isEmpty()) {
				     totalRise = totalRise+product.getDouble("targetVolumeLitre");
				  }
			 }
			  BigDecimal targetVolumeBucketBig=new BigDecimal(totalRise);
			return targetVolumeBucketBig;
		 }
		public BigDecimal getTotalTargetVolumes(Map<String, String> fields) {
		        String[] packTypes = new String[]{"PackFullySyn", "PackBlendSyn", "PackFormula", "PackClassicExtra", "PackOthers"};
		        BigDecimal total = BigDecimal.ZERO;
		        if (CollUtil.isNotEmpty(fields)) {
		            for (String key : fields.keySet()) {
		                for (int i = 0; i < packTypes.length; i++) {
		                    if (StrUtil.equalsIgnoreCase(packTypes[i], key.substring(5))) {
		                        BigDecimal val = new BigDecimal(StrUtil.nullToDefault(MapUtil.getStr(fields, key), "0.0"));
		                        total = total.add(MktUtil.getNullAsZero(val));
		                    }
		                }
		            }
		        }
		        return total;
		    }
		public BigDecimal getTotalTargetCdmVolumn(JSONArray storeMap) {
			 String[] packTypes = new String[]{"FullySyn", "BlendSyn", "Formula", "ClassicExtra", "Others"};
		        BigDecimal total = BigDecimal.ZERO;
		        if (CollUtil.isNotEmpty(storeMap)) {
		        	 for(int i=0;i<storeMap.size();i++){
		   			  net.sf.json.JSONObject product = storeMap.getJSONObject(i); 
		   			   String key = product.getString("category");
		                for (int j = 0; j < packTypes.length; j++) {
		                    if (StrUtil.equalsIgnoreCase(packTypes[j], key)) {
		                    	BigDecimal val =BigDecimal.ZERO;
		                    	if(StringUtils.isNotEmpty(product.getString("estimatedSmallPack"))) {
		                    		val = new BigDecimal(product.getString("estimatedSmallPack"));
		                    	}
		                    	BigDecimal val1 =BigDecimal.ZERO;
		                    	if(StringUtils.isNotEmpty(product.getString("estimatedLargePack"))) {
		                    		val1 = new BigDecimal(product.getString("estimatedLargePack"));
		                    	}
		                        total = total.add(MktUtil.getNullAsZero(val)).add(MktUtil.getNullAsZero(val1));
		                    }
		                }
		            }
		        }
		        return total;
		}

    public BigDecimal getTotalTargetSignageVolumn(String salesChannel, JSONArray productMap) {
        String[] packTypes = new String[]{"Total"};
        BigDecimal total = BigDecimal.ZERO;
        if (CollUtil.isNotEmpty(productMap)) {
            for(int i=0;i<productMap.size();i++){
                net.sf.json.JSONObject product = productMap.getJSONObject(i);
                if(product.get("category") != null) {
                    String key = product.getString("category");
                    for (int j = 0; j < packTypes.length; j++) {
                        if (StrUtil.equalsIgnoreCase(packTypes[j], key)) {
//                            if(CONSUMER_2021.equals(salesChannel)){
//                                BigDecimal val =BigDecimal.ZERO;
//                                if(StringUtils.isNotEmpty(product.getString("estimatedSmallPack"))) {
//                                    val = new BigDecimal(product.getString("estimatedSmallPack"));
//                                }
//                                BigDecimal val1 =BigDecimal.ZERO;
//                                if(StringUtils.isNotEmpty(product.getString("estimatedSmallPack"))) {
//                                    val1 = new BigDecimal(product.getString("estimatedLargePack"));
//                                }
//                                total = total.add(MktUtil.getNullAsZero(val)).add(MktUtil.getNullAsZero(val1));
//                            }else if(COMMERCIAL_2021.equals(salesChannel)){
//                            }
                            BigDecimal val =BigDecimal.ZERO;
                            if(product.get("estimatedPack") != null && StringUtils.isNotEmpty(product.getString("estimatedPack"))) {
                                val = new BigDecimal(product.getString("estimatedPack"));
                            }
                            total = total.add(MktUtil.getNullAsZero(val));
                        }
                    }
                }
            }
        }
        return total;
    }
}
