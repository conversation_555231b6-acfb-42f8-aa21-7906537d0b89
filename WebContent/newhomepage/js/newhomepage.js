$(function() {
	if(isMenuPermitted == false){
		$(".dmsURL").hide();
	}
	newhomepage_init.init();
});

var newhomepage_init = (function($) {
	
	var marketIviBar = null,
	
	iconNotUpClass = "fa fa-times-circle not-up-icon";
	iconRightClass = "fa fa-check-circle-o right-icon";
	iconWarmingClass= "fa fa-exclamation-circle warning-icon";
	
	rightTextClass = "up";
	notUpTextClass = "not-up";
	runningTextClass = "running";
	
	rightText = "已达标";
	notUpText = "未达标";
	runningText = "进行中";
	
	workshopTarget = 8;
		
	req = {}, 
	util = {},
	action = {},
	loader = {};
	
	req = {
			getSysMessage: function(callback) {
				var maxLength = 5;
				$.post(common.ctx + "messagepush/personaldata.do",{
					start:0,
					limit:maxLength,
					pageIndex:0,
					field:"createTime",
					direction:"DESC"
				}, function(data){
					$.isFunction(callback) && callback(data);
				});
			},
			getPartnerRebateInfo: function(year, month, callback) {
				var params = {
						year: year, 
						month: month,
						t: new Date().getTime()
						};
				$.get(common.ctx + "newhomepage/rebateDataInfo.do", params, function(data){
					if(data.code == '0000'){
						$.isFunction(callback) && callback(data.data);
					} else {
						common.alertMes(data.message, 'error');
						return;
					}
				});
			},
			getCdmPoints: function(callback) {
				var params = {
						salesChannel: 'Consumer',
						dealerId: orgId,
						yearList: [lastYear, currentYear]
				};
				$.ajax({
					dataType: 'json',
					type: 'post',
					contentType: 'application/json',
					url: common.ctx + "wxtpointvaluedetailvo/getPointSummeryByDealerId.do",
					data: JSON.stringify(params),
					success : function (data) {
						if(data.code == 'success'){
							$.isFunction(callback) && callback(data.data);
						} else {
							common.alertMes("系统异常", 'error');
							return;
						}
	                },
				});
			},
			getMonthlySellInfo: function(callback) {
				$.get(common.ctx + "newhomepage/monthlySellInfo.do?partnerId="+orgId+"&productChannel=Consumer&year=" + currentYear + "&t=" + new Date().getTime(), function(data){
					if(data.code == '0000'){
						$.isFunction(callback) && callback(data.data);
					} else {
						common.alertMes(data.message, 'error');
						return;
					}
				});
			},
			getProductMonthlySellInfo: function(callback) {
				$.get(common.ctx + "newhomepage/productMonthSellInfo.do?t=" + new Date().getTime(), function(data){
					if(data.code == '0000'){
						$.isFunction(callback) && callback(data.data);
					} else {
						common.alertMes(data.message, 'error');
						return;
					}
				});
			},
			getQuarterlySellInfo: function(callback) {
				$.get(common.ctx + "newhomepage/quarterlySellInfo.do?partnerId="+orgId+"&productChannel=Consumer&year=" + currentYear + "&t=" + new Date().getTime(), function(data){
					if(data.code == '0000'){
						$.isFunction(callback) && callback(data.data);
					} else {
						common.alertMes(data.message, 'error');
						return;
					}
				});
			},
			getWorkshopDetail: function(callback) {
				$.get(common.ctx + "newhomepage/storeFrontCount.do?year=" + currentYear + "&t=" + new Date().getTime(), function(data){
					if(data.code == '0000'){
						$.isFunction(callback) && callback(data.data);
					} else {
						common.alertMes(data.message, 'error');
						return;
					}
				});
			},
			getSeminarCount: function(callback) {
				$.get(common.ctx + "newhomepage/seminarCount.do?year=" + currentYear + "&t=" + new Date().getTime(), function(data){
					if(data.code == '0000'){
						$.isFunction(callback) && callback(data.data);
					} else {
						common.alertMes(data.message, 'error');
						return;
					}
				});
			},
			getTotalTrainingInfo: function(callback) {
				$.get(common.ctx + "newhomepage/queryTotalTrainingInfo.do?year=" + currentYear + "&t=" + new Date().getTime(), function(data){
					if(data.code == '0000'){
						$.isFunction(callback) && callback(data.data);
					} else {
						common.alertMes(data.message, 'error');
						return;
					}
				});
			},
			getSupportOrderPhone: function(callback) {
				$.get(common.ctx + "newhomepage/supportOrderPhone.do?t=" + new Date().getTime(), function(data){
					if(data.code == '0000'){
						$.isFunction(callback) && callback(data.data);
					} else {
						common.alertMes(data.message, 'error');
						return;
					}
				});
			},
			getSellThrouthInfo: function(callback) {
				$.get(common.ctx + "newhomepage/sellThrouthInfo.do?year=" + currentYear + "&t=" + new Date().getTime(), function(data){
					if(data.code == '0000'){
						$.isFunction(callback) && callback(data.data);
					} else {
						common.alertMes(data.message, 'error');
						return;
					}
				});
			},
			getLocalSalesResult: function(callback) {
				$.get(common.ctx + "newhomepage/getLocalSalesResult.do?year=" + currentYear + "&t=" + new Date().getTime(), function(data){
					if(data.code == '0000'){
						$.isFunction(callback) && callback(data.data);
					} else {
						common.alertMes(data.message, 'error');
						return;
					}
				});
			},
			getOrgIviFund: function(callback) {
				$.get(common.ctx + "newhomepage/getOrgIviFund.do?year=" + currentYear + "&t=" + new Date().getTime(), function(data){
					if(data.code == '0000'){
						$.isFunction(callback) && callback(data.data);
					} else {
						common.alertMes(data.message, 'error');
						return;
					}
				});
			},	
			getDmsCount: function(callback,id){
				LoadMask.show();
				$.get(common.ctx + "newhomepage/getDMSInfoByPartnerId.do?id="+id + "&t=" + new Date().getTime(), function(data){
					LoadMask.hide();
					if(data != 0){
						$.isFunction(callback) && callback(data);
					} /*else {
						common.alertMes(data.message, 'error');
						return 0;
					}*/
				});
			}
	},
	util = {
			openMenu: function(url,newUrl) {
				if(top.openMenuTab){
					top.openMenuTab(url,newUrl);
				}else{
					window.open(url);
				}
			},
			openUrl: function(url, menuId, menuTitle) {
				if(top.openMenu){
					top.openMenu(url, menuId, menuTitle);
				}else{
					window.open(url);
				}
			},
			openPage: function(url, type) {
				window.location.href = url + "?type=" + type;
			},
			toThousands: function(n) {
				return parseFloat(n).toLocaleString() 
			}
	},
	loader = {
		    loadMarketIviInfo: function(){
		    	req.getOrgIviFund(function(data) {
		    		if(data.length > 1){
			    		var rootNode = {item: data[0], value: data[0].quarter};
			    		for(var i = 1; i < data.length; i++){
			    			UI.buildBinaryTree({
								item: data[i],
								value: data[i].quarter
							}, rootNode);
			    		}
			    		data = [];
						UI.traverseBinaryTree(rootNode, function(node){
							data.push(node.item);
						});
		    		}
		    		var ivifundData = [];
		    		var iviFundSum = 0;
		    		var mktFundData = [];
		    		var mktFundSum = 0;
		    		
		    		for(var i = 0; i < 4; i++) {
		    			
		    			if(data[i] != null && data[i].cdmIviFund != null) {
		    				ivifundData[i] = data[i].cdmIviFund.toFixed(0);
			    			iviFundSum += parseFloat(data[i].cdmIviFund.toFixed(0));
		    			} else {
		    				ivifundData[i] = 0;
			    			iviFundSum += 0;
		    			}
		    			
		    			if(data[i] != null && data[i].cdmMarketingFund != null) {
		    				mktFundData[i] = data[i].cdmMarketingFund.toFixed(0);
			    			mktFundSum += parseFloat(data[i].cdmMarketingFund.toFixed(0));
		    			} else {
		    				mktFundData[i] = 0;
			    			mktFundSum += 0;
		    			}
		    		}
		    		ivifundData[4] = iviFundSum;
		    		mktFundData[4] = mktFundSum;
		    		
		    		var marketIviBarOption =  {
		                      tooltip : {
		                          trigger: 'axis',
		                          axisPointer : {            // 坐标轴指示器，坐标轴触发有效
		                              type : 'shadow'        // 默认为直线，可选为：'line' | 'shadow'
		                          }
		                      },
		                      legend: {
		                          data:['营销基金(元)','IVI奖励(元)']
		                      },
		                      toolbox: {
		                          show : true,
		                          orient: 'vertical',
		                          x: 'right',
		                          y: 'center',
		                          feature : {
		                              mark : {show: false},
		                              dataView : {show: false, readOnly: false},
		                              magicType : {show: false, type: ['line', 'bar', 'stack', 'tiled']},
		                              restore : {show: false},
		                              saveAsImage : {show: false}
		                          }
		                      },
		                      calculable : true,
		                      xAxis : [
		                          {
		                              type : 'category',
		                              data : ['第一季度','第二季度','第三季度','第四季度','截至当季累计'],
									  axisLabel:{
		                              	interval:0
										  // rotate:-30
									  },
		                              splitLine:{
		                                  show: true
		                              }
		                          }
		                      ],
		                      yAxis : [
		                          {
		                              type : 'value',
		                              splitLine:{
		                                  show: false
		                              }
		                          }
		                      ],
		                      series : [
		                          {
		                              name: '营销基金(元)',
		                              type: 'bar',
		                              data: mktFundData,
		                              itemStyle: {
		                                  normal: {
		                                      color: '#4472C4'  //圈圈的颜色
		                                  }
		                              },
		                              label: {
		                                  normal: {
		                                      show: true,
		                                      position: 'top',
		                                      textStyle: {
		                                          color: 'rgb(55,55,55)',
		                                          fontSize: 8
		                                      }
		                                  }
		                              },
		                              barWidth: 20,
		                              barGap: '50%',
		                          },
		                          {
		                              name:'IVI奖励(元)',
		                              type:'bar',
		                              data: ivifundData,
		                              itemStyle : {
		                                  normal : {
		                                      color:'#ED7D31'  //圈圈的颜色
		                                  }
		                              },
		                              label: {
		                                  normal: {
		                                      show: true,
		                                      position: 'top',
		                                      textStyle: {
		                                          color: 'rgb(55,55,55)',
		                                          fontSize: 8
		                                      }
		                                  }
		                              },
		                              barWidth: 20
		                          }
		                      ]
		                  };
		                marketIviBar = echarts.init($('#marketing-ivi-bar')[0]);
		                $(window).resize(marketIviBar.resize);
		                marketIviBar.setOption(marketIviBarOption);
		    	});
            },
			loadMontlySales: function() {
				req.getMonthlySellInfo(function(data){
					for(var i in data) {
						var month = data[i].month;
						var sellIn = data[i].sellIn;
						$("#actual-m" + month).html(util.toThousands(sellIn));
                        $("#actual-m" + month).attr("title",(util.toThousands(sellIn)));
                    }
				});
			},
			loadSellThrouth: function() {
				req.getSellThrouthInfo(function(data) {
					//季度数据初始化
					var sellThrouthQuarterList = [];
					for(var q = 1; q <= 4; q++) {
						sellThrouthQuarterList.push({
							quarter: q,
							sellIn: 0,
							sellThrouth: 0,
							workshopCount: 0,
							targetBaseLine: 0,
							sellThrouthFlag: true,
						});
					}

					var snMonthTarget = 0;
					for(var i in data) {
						var month = data[i].month;
						var sellThrouth = data[i].sellThrouth;
						var sellIn = data[i].sellIn;
						var workshopCount = data[i].workshopCount;
						var targetBaseLine = data[i].targetBaseLine;
						//每月目标
						
						snMonthTarget += targetBaseLine;
						//生成季度数据
						var quarterIndex = parseInt((month + 2) / 3) - 1;
						sellThrouthQuarterList[quarterIndex].sellIn += sellIn;
						sellThrouthQuarterList[quarterIndex].sellThrouth += sellThrouth;
						sellThrouthQuarterList[quarterIndex].workshopCount += workshopCount;
						sellThrouthQuarterList[quarterIndex].targetBaseLine += targetBaseLine;
						//月目标是否达标
						var $productMonthSellInSpan = $('<span></span>');
						var $productMonthSellInIcon = $('<i aria-hidden="true"></i>');

						// var monthSellThrouthFlag = false;
						// if()
						// if(sellThrouth / (targetBaseLine * 0.5) >= 1) {
						// 	$productMonthSellInIcon.addClass(iconRightClass);
						// } else if (sellThrouth / (targetBaseLine * 0.5) < 1 && sellThrouth / (targetBaseLine * 0.5) > 0.75) {
						// 	$productMonthSellInIcon.addClass(iconWarmingClass);
						// 	if(month < currentMonth) {
						// 		sellThrouthQuarterList[quarterIndex].sellThrouthFlag = false;
						// 	}
						// } else {
						// 	$productMonthSellInIcon.addClass(iconNotUpClass);
						// 	if(month < currentMonth) {
						// 		sellThrouthQuarterList[quarterIndex].sellThrouthFlag = false;
						// 	}
						// }

						$productMonthSellInSpan.append($productMonthSellInIcon);
						$productMonthSellInSpan.append(util.toThousands(sellThrouth));

						$("#p-actual-m" + month).empty();
						$("#p-actual-m" + month).append($productMonthSellInSpan);

						
					}
					for(var i in data) {
						var month = data[i].month;
						var sellThrouth = data[i].sellThrouth;
						//现在“每月SN级以上小包装产品出货扫码目标”的达标计算变成了月份了
                        var mTarget = $("#p-target-m" + month);
                        if (month >= currentMonth && nowYear == currentYear) {
                            mTarget.addClass(runningTextClass);
                            mTarget.html(runningText);
                        } else {
                            var actualTarget = snMonthTarget/28;
                            if (sellThrouth / actualTarget >= 1) {
                                mTarget.addClass(rightTextClass);
                                mTarget.html(rightText);
                            } else if (sellThrouth / actualTarget < 1) {
                                mTarget.addClass(notUpTextClass);
                                mTarget.html(notUpText);
                            }
                        }
					}
					$("#sn-month-target").html(util.toThousands((snMonthTarget/28).toFixed(0)) + "&nbsp;&nbsp;升");

					//季度数据加载
					for(var qi in sellThrouthQuarterList) {
						var quarter = sellThrouthQuarterList[qi].quarter;
						// var sellThrouth = sellThrouthQuarterList[qi].sellThrouth;
						// var sellIn = sellThrouthQuarterList[qi].sellIn;
						var workshopCount = sellThrouthQuarterList[qi].workshopCount;
						// var targetBaseLine = sellThrouthQuarterList[qi].targetBaseLine;
						// var sellThrouthFlag = sellThrouthQuarterList[qi].sellThrouthFlag;
						// //扫码季度达标
						// $pTarget = $("#p-target-q" + quarter);
						// if(!sellThrouthFlag) {
						// 	$pTarget.addClass(notUpTextClass);
						// 	$pTarget.html(notUpText);
						// } else {
						// 	if(quarter >= currentQuarter) {
						// 		$pTarget.addClass(runningTextClass);
						// 		$pTarget.html(runningText);
						// 	} else {
						// 		if(sellThrouth / (targetBaseLine * 0.5) >= 1) {
						// 			$pTarget.addClass(rightTextClass);
						// 			$pTarget.html(rightText);
						// 		} else if(sellThrouth / (targetBaseLine * 0.5) < 1) {
						// 			$pTarget.addClass(notUpTextClass);
						// 			$pTarget.html(notUpText);
						// 		}
						// 	}
						// }

						//新维修门店
						var $storeCountSpan = $('<span></span>');
						var $storeCountIcon = $('<i aria-hidden="true"></i>');
						if(workshopCount >= workshopTarget) {
							$storeCountIcon.addClass(iconRightClass);
						} else if(workshopCount < workshopTarget && workshopCount >= 6) {
							$storeCountIcon.addClass(iconWarmingClass);
						} else {
							$storeCountIcon.addClass(iconNotUpClass);
						}

						$storeCountSpan.append($storeCountIcon);
						$storeCountSpan.append(util.toThousands(workshopCount));

						$("#s-actual-q" + quarter).empty();
						$("#s-actual-q" + quarter).append($storeCountSpan);
						//门店达标
						$sTraget = $("#s-target-q" + quarter);
						if(quarter >= currentQuarter && nowYear == currentYear) {
							$sTraget.addClass(runningTextClass);
							$sTraget.html(runningText);
						} else {
							if(workshopCount >= workshopTarget) {
								$sTraget.addClass(rightTextClass);
								$sTraget.html(rightText);
							} else {
								$sTraget.addClass(notUpTextClass);
								$sTraget.html(notUpText);
							}
						}

					}
				});
			},
			loadQuarterlySales: function() {
				req.getQuarterlySellInfo(function(data) {
					var totalSalesTraget = 0;
					for(var i in data) {
						var quarter = data[i].quarter;
						var targetBaseLine = data[i].targetBaseline;
						var sellIn = data[i].sellIn;
						var monthAvgTarget = (targetBaseLine / 3).toFixed(0);
						//总目标销量
						totalSalesTraget += targetBaseLine;
						//进货量基数标准
						$("#target-q" + quarter).html(util.toThousands(targetBaseLine));
						//月平均进货目标=季度进货量基数标准/3
						for(var j = 0; j < 3; j++) {
							month = 3 * quarter - j;
							$("#target-m" + month).html(util.toThousands(monthAvgTarget));
							$("#target-m" + month).attr("title",util.toThousands(monthAvgTarget));
						}
						//季度实际完成进货量
						var sellInDiv = $('<div></div>');
						sellInDiv.html(util.toThousands(sellIn));
						if(sellIn / targetBaseLine < 0.8) {
							sellInDiv.addClass("not-up");
						} else if (sellIn / targetBaseLine < 1 && sellIn / targetBaseLine > 0.8) {
							sellInDiv.addClass("warning");
						} else {
							sellInDiv.addClass("up");
						}
						$("#actual-q" + quarter).html(sellInDiv);
                        $("#actual-q" + quarter).attr("title",util.toThousands(sellIn));
						//季度实际完成比例
						var percent = "";
						if(targetBaseLine != 0) {
							percent = (sellIn / targetBaseLine * 100).toFixed(0) + "%";
						}
						$("#percent-q" + quarter).html(percent);
					}
					$("#total-sales-target").html(util.toThousands(totalSalesTraget.toFixed(0)) + "&nbsp;&nbsp;升");
				});
			},
			loadSysMessage: function() {
				req.getSysMessage(function(result) {
					if(result.code == "success") {
						var data = result.resultLst;
						if(data.length==0){
							$('.sys-message .more-btn').hide();
							$("#sys_message").append('<h2 style="color: #bbb; text-align: center;">没有系统公告</h2>');
						} else {
							var len = data.length;
							var h = [];
							for(var i=0;i<len;i++){
								h.push('<div class="content-panel">',
										'<div class="message-title message-content"><span style="cursor: pointer;" onclick="top.common.viewMessage(' + data[i].messageId 
										+ ',\'' + data[i].messageTitle + '\', false)">', i + 1, '. ', 
										common.formatDate(new Date(data[i].createTime), "yyyy-MM-dd"), 
										' ', data[i].messageTitle,
										'</span></div>',
										'</div>');
							}
							$("#sys_message").append(h.join(''));
						}
					}
				})
			},
			loadlastYearRebateInfo: function() {
				req.getPartnerRebateInfo(lastYear, "12", function(data){
					//年
					$("#last-year-str").html(data.year);
					//月
					$("#last-mon-str").html(data.month);
					//返利金额总额
					if(data.totalAmount != null) {
						$("#last-rebate-total-money").html(data.totalAmount);
						$("#last-rebate-sum").html(data.totalAmount);
					} else {
						$("#last-rebate-total-money").html(0);
						$("#last-rebate-sum").html(0);
					}
					//已申请返利金额
					if(data.usedAmount != null) {
						$("#last-rebate-used-money").html(data.usedAmount);
					} else {
						$("#last-rebate-used-money").html(0);
					}
					//可使用返利金额
					if(data.remainAmount != null) {
						$("#last-rebate-could-money").html(data.remainAmount)
					} else {
						$("#last-rebate-could-money").html(0);
					}
				});
			},
			loadRebateInfo: function() {
				req.getPartnerRebateInfo(currentYear, "", function(data){
					//年
					$("#year-str").html(data.year);
					//月
					$("#mon-str").html(data.month);
					//返利金额总额
					if(data.totalAmount != null) {
						$("#rebate-total-money").html(data.totalAmount);
						$("#rebate-sum").html(data.totalAmount);
					} else {
						$("#rebate-total-money").html(0);
						$("#rebate-sum").html(0);
					}
					//已申请返利金额
					if(data.usedAmount != null) {
						$("#rebate-used-money").html(data.usedAmount);
					} else {
						$("#rebate-used-money").html(0);
					}
					//可使用返利金额
					if(data.remainAmount != null) {
						$("#rebate-could-money").html(data.remainAmount)
					} else {
						$("#rebate-could-money").html(0);
					}
					
				});
			},
			loadCdmPoints: function() {
				req.getCdmPoints(function(data) {
					var currentYearDataList = data[currentYear];
					for(var i = 0; i < currentYearDataList.length; i++) {
						if(currentYearDataList[i].pointType == "CDM_STOCK_POINT") {//进货积分
							$("#s-total-point").html(currentYearDataList[i].totalGainPoint);
							$("#s-left-point").html(currentYearDataList[i].totalLeftPoint);
						} else if(currentYearDataList[i].pointType == "CDM_MATERIAL_POINT") {//物料积分
							$("#m-total-point").html(currentYearDataList[i].totalGainPoint);
							$("#m-left-point").html(currentYearDataList[i].totalLeftPoint);
						} else if(currentYearDataList[i].pointType == "CDM_PROMOTION_POINT") {//促销积分
							$("#p-total-point").html(currentYearDataList[i].totalGainPoint);
							$("#p-left-point").html(currentYearDataList[i].totalLeftPoint);
						}
					}
				});
			},
			loadWorkshop: function() {
				req.getWorkshopDetail(function(data) {
					$("#apply-storeFront-num").html(data.applyCount);
					$("#approve-storeFront-num").html(data.approveCount);
				});
			},
        	loadSeminarCount: function() {
				req.getSeminarCount(function(data) {
					$("#apply-seminar-num").html(data.applyCount);
					$("#approve-seminar-num").html(data.approveCount);
				});
			},
		    loadTotalTrainingInfo :function() {
                req.getTotalTrainingInfo(function (data) {
                	var days = 0;
                	if(data && data.days){
                        days = data.days;
					}
                    $("#training-total-num span").html(days);//data.workshopNum
                });
			},
			loadSupportOrderPhone: function() {
				req.getSupportOrderPhone(function(data) {
					$("#support-order-phone").html(data);
				});
			},
			loadLocalGoal: function() {
				req.getLocalSalesResult(function(data) {
					for(var i = 1; i <= 4; i++) {
						$goalSpan = $("#goal-q" + i);
						$goalSpan.empty();
						for(var index = 0; index < 4; index++) {
							if(data[index].quarter == i) {
								if(i < currentQuarter || nowYear > currentYear) {
									
									if(data[index].salesResult == '1') {
										$SpanContent = $('<i class="fa fa-check-circle-o right-icon" aria-hidden="true"></i>');
										$goalSpan.append($SpanContent);
										$goalSpan.append('完成');
									} else {
										$SpanContent = $('<span><i class="fa fa-times-circle not-up-icon" aria-hidden="true"></i></span>');
										$goalSpan.append($SpanContent);
										$goalSpan.append('未达标');
									}
								} else {
									$SpanContent = $('<span></span>');
									$goalSpan.append($SpanContent);
									$goalSpan.append('进行中');
								}
							}
						}
					}
				});
				
			},
			loadDMSCount: function(){
				req.getDmsCount(function(data){
					if(data !=0 ){
						$("#count").show();
						$("#countNum").html(data);
					}else{
						$("#count").hide();
					};	
				},orgId);
			}
	},
	action = {
	};
	
	return {
		init: function() {
			loader.loadDMSCount();
			loader.loadMarketIviInfo();
			loader.loadMontlySales();
			loader.loadSellThrouth();
			loader.loadQuarterlySales();
			loader.loadSysMessage();
//			loader.loadRebateInfo();
//			loader.loadlastYearRebateInfo();
//			loader.loadCdmPoints();
			loader.loadWorkshop();
			loader.loadSeminarCount();
			loader.loadTotalTrainingInfo();
			loader.loadSupportOrderPhone();
			loader.loadLocalGoal();
		},
		openUrl: function(url, menuId, menuTitle) {
			util.openUrl(url, menuId, menuTitle);
		},
		openMenu: function(url,newUrl) {
			util.openMenu(url,newUrl);
		},
		openPage: function(url, type) {
			util.openPage(url, type);
		},
	};
	
}($));