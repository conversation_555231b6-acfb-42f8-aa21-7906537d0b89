SELECT YEAR(os.out_time) [year]
       , MONTH(os.out_time) [month]
       ,ws.partner_id
          ,sum(osp.actual_out_count * CONVERT(FLOAT, tp.capacity)) AS liters
            FROM wx_t_order wto
            LEFT JOIN wx_t_out_stock os ON wto.order_no = os.order_no AND os.order_type = 'workshop'
            LEFT JOIN wx_t_out_stock_product osp ON os.stock_out_no = osp.stock_out_no
            LEFT JOIN wx_t_product tp ON osp.sku = tp.sku
            LEFT JOIN wx_t_work_shop ws ON ws.id = wto.work_shop_id
           WHERE wto.source='OUT_STOCK' and os.status='2'
                    group by YEA<PERSON>(os.out_time),MONTH(os.out_time),ws.partner_id
