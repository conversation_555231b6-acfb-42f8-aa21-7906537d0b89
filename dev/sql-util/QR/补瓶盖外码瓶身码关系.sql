--drop table temp_cap
select code1, p.cap_code, c.internal_code, p.creation_time,c.file_import_time into temp_cap from wx_t_oem_product_packaging_code p
join [PP_ETL].[chv_access_view].[fact_wx_t_return_code] c on p.cap_code=c.[external_code] 
where cap_code is not null and cap_code!='' --and c.[external_code] is null

select cap_code, count(1) from wx_t_oem_product_packaging_code p group by p.cap_code having count(1)>1

select count(1) from wx_t_oem_product_packaging_code p
join [PP_ETL].[chv_access_view].[fact_wx_t_return_code] c on p.cap_code=c.[external_code] 
where cap_code is not null and cap_code!='' --and c.[external_code] is null