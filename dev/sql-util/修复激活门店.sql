insert into wx_t_workshop_status (workshop_id, subtask_id, workshop_with_status, create_time) 
select w.id, s.task_id, 3, s.xg_sj from wx_task_main m
	LEFT JOIN wx_task_sub s ON m.task_main_id = s.task_main_id
	LEFT JOIN wx_t_work_shop w ON s.org_id = w.id
	LEFT JOIN wx_t_organization o ON o.id = m.tenant_id
	LEFT JOIN wx_t_partner_o2o_enterprise pe ON pe.partner_id = o.id
	LEFT JOIN wx_t_user u ON u.user_id = s.exec_user
	LEFT JOIN wx_t_region r1 ON r1.id = w.region_id
	LEFT JOIN wx_t_region r2 ON r1.parent_id = r2.id
	LEFT JOIN wx_t_region r3 ON r2.parent_id = r3.id
WHERE m.tmb_type_code = 'TT_2_SD'
	AND s.task_status IN ('4')
	and s.xg_sj>'2020-04-01' and s.is_pass>0 and w.status='3'
	and not exists (select 1 from wx_t_workshop_status ws1 where ws1.workshop_id=w.id and ws1.workshop_with_status='3')