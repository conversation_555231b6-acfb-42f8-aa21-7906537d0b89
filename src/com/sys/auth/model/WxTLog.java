package com.sys.auth.model;

import java.util.Date;

public class WxTLog {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column wx_t_log.log_id
     *
     * @mbggenerated Mon Jun 22 14:58:10 CST 2015
     */
    private Long logId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column wx_t_log.user_id
     *
     * @mbggenerated Mon Jun 22 14:58:10 CST 2015
     */
    private Long userId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column wx_t_log.session_id
     *
     * @mbggenerated Mon Jun 22 14:58:10 CST 2015
     */
    private String sessionId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column wx_t_log.log_type
     *
     * @mbggenerated Mon Jun 22 14:58:10 CST 2015
     */
    private Integer logType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column wx_t_log.log_descript
     *
     * @mbggenerated Mon Jun 22 14:58:10 CST 2015
     */
    private String logDescript;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column wx_t_log.xg_sj
     *
     * @mbggenerated Mon Jun 22 14:58:10 CST 2015
     */
    private Date xgSj;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column wx_t_log.status
     *
     * @mbggenerated Mon Jun 22 14:58:10 CST 2015
     */
    private Integer status;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column wx_t_log.tenant_id
     *
     * @mbggenerated Mon Jun 22 14:58:10 CST 2015
     */
    private Long tenantId;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column wx_t_log.log_id
     *
     * @return the value of wx_t_log.log_id
     *
     * @mbggenerated Mon Jun 22 14:58:10 CST 2015
     */
    public Long getLogId() {
        return logId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column wx_t_log.log_id
     *
     * @param logId the value for wx_t_log.log_id
     *
     * @mbggenerated Mon Jun 22 14:58:10 CST 2015
     */
    public void setLogId(Long logId) {
        this.logId = logId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column wx_t_log.user_id
     *
     * @return the value of wx_t_log.user_id
     *
     * @mbggenerated Mon Jun 22 14:58:10 CST 2015
     */
    public Long getUserId() {
        return userId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column wx_t_log.user_id
     *
     * @param userId the value for wx_t_log.user_id
     *
     * @mbggenerated Mon Jun 22 14:58:10 CST 2015
     */
    public void setUserId(Long userId) {
        this.userId = userId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column wx_t_log.session_id
     *
     * @return the value of wx_t_log.session_id
     *
     * @mbggenerated Mon Jun 22 14:58:10 CST 2015
     */
    public String getSessionId() {
        return sessionId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column wx_t_log.session_id
     *
     * @param sessionId the value for wx_t_log.session_id
     *
     * @mbggenerated Mon Jun 22 14:58:10 CST 2015
     */
    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column wx_t_log.log_type
     *
     * @return the value of wx_t_log.log_type
     *
     * @mbggenerated Mon Jun 22 14:58:10 CST 2015
     */
    public Integer getLogType() {
        return logType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column wx_t_log.log_type
     *
     * @param logType the value for wx_t_log.log_type
     *
     * @mbggenerated Mon Jun 22 14:58:10 CST 2015
     */
    public void setLogType(Integer logType) {
        this.logType = logType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column wx_t_log.log_descript
     *
     * @return the value of wx_t_log.log_descript
     *
     * @mbggenerated Mon Jun 22 14:58:10 CST 2015
     */
    public String getLogDescript() {
        return logDescript;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column wx_t_log.log_descript
     *
     * @param logDescript the value for wx_t_log.log_descript
     *
     * @mbggenerated Mon Jun 22 14:58:10 CST 2015
     */
    public void setLogDescript(String logDescript) {
        this.logDescript = logDescript;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column wx_t_log.xg_sj
     *
     * @return the value of wx_t_log.xg_sj
     *
     * @mbggenerated Mon Jun 22 14:58:10 CST 2015
     */
    public Date getXgSj() {
        return xgSj;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column wx_t_log.xg_sj
     *
     * @param xgSj the value for wx_t_log.xg_sj
     *
     * @mbggenerated Mon Jun 22 14:58:10 CST 2015
     */
    public void setXgSj(Date xgSj) {
        this.xgSj = xgSj;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column wx_t_log.status
     *
     * @return the value of wx_t_log.status
     *
     * @mbggenerated Mon Jun 22 14:58:10 CST 2015
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column wx_t_log.status
     *
     * @param status the value for wx_t_log.status
     *
     * @mbggenerated Mon Jun 22 14:58:10 CST 2015
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column wx_t_log.tenant_id
     *
     * @return the value of wx_t_log.tenant_id
     *
     * @mbggenerated Mon Jun 22 14:58:10 CST 2015
     */
    public Long getTenantId() {
        return tenantId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column wx_t_log.tenant_id
     *
     * @param tenantId the value for wx_t_log.tenant_id
     *
     * @mbggenerated Mon Jun 22 14:58:10 CST 2015
     */
    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }
    
    private String objName;

	public String getObjName() {
		return objName;
	}

	public void setObjName(String objName) {
		this.objName = objName;
	}
}