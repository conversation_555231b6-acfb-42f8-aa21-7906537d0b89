INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='Service_Partner_Manager'), 'Dsr.kpi', 2048, 1, 'KPI经销商确认-经销商老板确认', 1, getdate(), NULL, NULL);
INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='CDM_DIST'), 'Dsr.kpi', 2048, 1, 'KPI经销商确认-经销商老板确认', 1, getdate(), NULL, NULL);
INSERT INTO wx_t_operation_permission (role_id, module_code, permission_weight, enable_flag, remark, create_user_id, create_time, update_user_id, update_time) VALUES ((select role_id from wx_t_role where ch_role_name='Caltex_Dealer'), 'Dsr.kpi', 2048, 1, 'K<PERSON>经销商确认-经销商老板确认', 1, getdate(), NULL, NULL);

--APP_Dsr_Point_Exchange,APP DSR积分兑换
/*update r set r.user_flag=64
 from wx_t_role r where ch_role_name='APP_Dsr_Point_Exchange'*/

INSERT INTO wx_t_userrole (user_id,grant_userid,role_id,status)
select distinct u.user_id, 1, (select role_id from wx_t_role where ch_role_name='APP_Dsr_Point_Exchange'),1
from wx_t_user u where u.status=1 and u.login_name in ('18292942850',
'15091600534',
'18629147472',
'18033870929',
'15373691354',
'18131142139',
'18533218997',
'13953577393',
'18663898726',
'15668027429',
'13156936206',
'13725758914',
'13592719393',
'13712644457',
'13726392863',
'吕垣文',
'郭志豪',
'15818040620',
'13928698702',
'13922318185',
'13247368639',
'18312666432',
'18688444145',
'13927307282',
'13923620148',
'13556864293',
'13751059508',
'13928189740',
'13924977149',
'13924937037',
'13924936377',
'13924957090',
'13924922059',
'13527237884',
'LXC',
'CYB',
'LWS',
'shb',
'hjy',
'刘宇鸿',
'王元军',
'严振功',
'zhangjianzhong',
'yaobo',
'liyang',
'wangting',
'杨根稳销售经理',
'李俊销售经理',
'minxiong',
'何文',
'18306405155',
'18769754931',
'15168831998',
'15098985881',
'lanfangyou',
'9306',
'晏晓安',
'陈科亘',
'唐双喜',
'欧阳剑锋',
'chen',
'Liuwei',
'heciguang',
'Lanweiqiang',
'H13902643001',
'Zhm',
'Czp',
'wzx',
'18177167943',
'hejie',
'PXY',
'liujianfeng',
'huangyi',
'huangxiaoshen',
'jiadeshi4',
'xuefolong001',
'xuefolong002',
'jiadeshi6',
'taojianfei',
'13985422373',
'dulin',
'lizhiqiang',
'hexianming',
'li321',
'tangbing',
'LXK',
'TB',
'zhaogang',
'liujinli',
'yangyuhua',
'卜周龙',
'陈敏军',
'zhangfu',
'guolong',
'xiaojie',
'吴聪文',
'13017118088zlj',
'18820290472',
'18890448110',
'wangleida',
'13722663586',
'13363610519',
'Yws',
'Lys',
'Tzz',
'QianQuiXue',
'徐金安',
'张拴马',
'张欢',
'陈俊',
'zhangjunyi',
'tanghua',
'刘雷',
'wangyuan',
'mayong',
'王明远',
'陈振操',
'陈炳礼',
'周华滨',
'张人镇',
'WCR',
'QYJ',
'ZL',
'XYJ',
'dengdaoxiang',
'tangyu',
'ffr',
'KANE',
'lym',
'lj',
'17386771602',
'18148437448',
'13550837971',
'13983877543',
'13667656162',
'shenxiaobo',
'18907387622',
'15576696955',
'15869884287',
'18773981788',
'13873945666',
'suntanwei1')
