<%@page import="com.common.util.StringUtils"%>
<%@page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@page import= "com.common.util.ContextUtil"%>
<%@page import= "com.sys.auth.model.WxTUser"%>
<%@page import="com.sys.auth.model.WxTRole"%>
<%@page import="java.util.List"%>
<%@page import="java.util.Calendar"%>
<%@page import="java.net.URLDecoder"%>
<%
    String initPartnerIdStr = request.getParameter("partnerId");
    Long initPartnerId = null;
    String initPartnerName = null;
    if(initPartnerIdStr != null){
        try{
            initPartnerId = Long.valueOf(initPartnerIdStr);
            initPartnerName = request.getParameter("partnerName") == null ? null : "'" + URLDecoder.decode(request.getParameter("partnerName"),"UTF-8") + "'";
        }catch (Exception e){

        }
    }
	int year = 0;
	int currentMonth = 12;
	Calendar cal = Calendar.getInstance();
	int currentYear = cal.get(Calendar.YEAR);
	if(StringUtils.isNotBlank(request.getParameter("year"))){
		year = Integer.parseInt(request.getParameter("year"));
		if(year == currentYear){
			currentMonth = cal.get(Calendar.MONTH) + 1;
		}
	}else{
		year = cal.get(Calendar.YEAR);
		currentMonth = cal.get(Calendar.MONTH) + 1;
	}
	int lastYear = year - 1;

    WxTUser curUser = ContextUtil.getCurUser();
    String userName = curUser.getChName();
    Long userId = curUser.getUserId();
    Long orgId = curUser.getOrgId();

    List<WxTRole> roleList = curUser.getRoleList();

    boolean isDistributor = false;
    String userModel = curUser.getUserModel();
    if(WxTUser.USER_MODEL_SP.equals(userModel)) {
        isDistributor = true;
    }

    boolean isCdmDistributor = false;
    for(WxTRole role : roleList) {
        if(role.getChRoleName().equals("Service_Partner_Admin")
                || role.getChRoleName().equals("Service_Partner_Manager")) {
            isCdmDistributor = true;
            break;
        }
    }

    boolean isCioDistributor = false;
    for(WxTRole role : roleList) {
        if(role.getChRoleName().equals("Caltex_Dealer")) {
            isCioDistributor = true;
            break;
        }
    }
%>
<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>合伙人营销情况分析</title>
    <%@include file="/common/jsp/common.jsp"%>
    <link href="${ctx }newhomepage/css/newhomepage.css?v=${version}1" rel="stylesheet">
    <link href="${ctx }newhomepage/css/SpSellInInfoAnalysis.css?v=${version}" rel="stylesheet">
    <script type="text/javascript" src="${ctx }common/hplus/js/plugins/echarts/echarts.min.js"></script>
    <script type="text/javascript" src="${ctx }newhomepage/js/SpSalesPerfAnalysis.js?v=${version}"></script>
    <script type="text/javascript">
        var orgId = <%=orgId%>;
        var currentYear = <%=year%>;
        var lastYear = <%=lastYear%>;
        var nowYear = <%=currentYear %>;
        var currentMonth = <%=currentMonth %>;
        var currentQuarter = parseInt((currentMonth + 2) / 3);
        var initPartnerName = decodeURIComponent(<%=initPartnerName%>);
        var initPartnerId = <%=initPartnerId%>;
        var currentParams = {
        		partnerId: initPartnerId,
        		partnerName: initPartnerName
        };
    </script>
    <style type="text/css">
        .control-text.control-auto {
            height: 30px;
            width: 180px;
        }
    </style>
</head>
<body class="gray-bg">
<div class="not-top">
    <div  id="detailDiv" class="content-wrapper">
        <!--标题栏-->
        <div class="content-panel header-panel">
            <div class="header-title" id="titleDiv">合伙人营销情况分析</div>
        </div>
        <!--查询条件-->
        <div class="content-panel query-panel">
                         <div class="field-group" style="width:250px;">
                            <label class="search-label">年份：</label>
                            <div class="control-group years-wrapper" id="year_c"><ul></ul></div>
                        </div>
            <div class="field-group" style="width: 280px;">
                <label class="field-label width-auto">经销商：</label>
                <div class="control-group">
                    <input type="hidden" name='partnerId' id="partnerId"/>
                </div>
            </div>
            <div class="query-btns" style="width: auto;">
                <div class="query-btn field-label" style="width: auto;">
                    <button id="search" class="btn-query">查询</button>
                </div>
            </div>
    </div>
    <div class="content-box" style="max-height: none;text-align: center">
        <div style="max-width: 1000px;margin: auto;">
            <div class="grid-content-box high-content-box" style="margin-right: 15px;">
                <div class="content-panel" style="margin-top: 12px;">
                    <div class="content-panel new-home-grid-div" id="totalPerformanceInfoGrid">
                        <table class="ui-grid-table ui-grid new-home-grid">
                            <thead>
                            <tr>
                                <th colspan="13" class="grid-title">
                                    <div><p>总销量目标<!-- &nbsp;&nbsp;&nbsp;&nbsp;<span id="total-sales-target">&nbsp;&nbsp;升</span> --></p></div>
                                </th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr>
                                <th>季度</th><th colspan="3">第一季度</th><th colspan="3">第二季度</th><th colspan="3">第三季度</th><th colspan="3">第四季度</th>
                            </tr>
                            <!-- <tr class="digital-tr">
                                <th>进货量基数目标</th>
                                <td id="target-q1" colspan="3">2.00</td>
                                <td id="target-q2" colspan="3">2.50</td>
                                <td id="target-q3" colspan="3">3.00</td>
                                <td id="target-q4" colspan="3">2.50</td>
                            </tr>
                            <tr class="digital-tr">
                                <th>月平均进货目标</th>
                                <td id="target-m1">1</td>
                                <td id="target-m2">2</td>
                                <td id="target-m3">3</td>
                                <td id="target-m4">4</td>
                                <td id="target-m5">5</td>
                                <td id="target-m6">6</td>
                                <td id="target-m7">7</td>
                                <td id="target-m8">8</td>
                                <td id="target-m9">9</td>
                                <td id="target-m10">10</td>
                                <td id="target-m11">11</td>
                                <td id="target-m12">12</td>
                            </tr> -->
                            <tr class="digital-tr">
                                <th>月平均进货量</th>
                                <td id="actual-m1"></td>
                                <td id="actual-m2"></td>
                                <td id="actual-m3"></td>
                                <td id="actual-m4"></td>
                                <td id="actual-m5"></td>
                                <td id="actual-m6"></td>
                                <td id="actual-m7"></td>
                                <td id="actual-m8"></td>
                                <td id="actual-m9"></td>
                                <td id="actual-m10"></td>
                                <td id="actual-m11"></td>
                                <td id="actual-m12"></td>
                            </tr>
                            <tr class="digital-tr">
                                <th>季度实际完成进货量</th>
                                <!--绿色——<div class="up">2.00</div>-->
                                <!--黄色——<div class="warning">2.00</div>-->
                                <!--红色——<div class="not-up">2.00</div>-->
                                <td colspan="3" id="actual-q1"><div class="normal-text"></div></td>
                                <td colspan="3" id="actual-q2"><div class="normal-text"></div></td>
                                <td colspan="3" id="actual-q3"><div class="normal-text"></div></td>
                                <td colspan="3" id="actual-q4"><div class="normal-text"></div></td>
                            </tr>
                            <!-- <tr class="digital-tr">
                                <th>季度实际完成比例</th>
                                <td colspan="3" id="percent-q1"><div class="normal-text"></div></td>
                                <td colspan="3" id="percent-q2"><div class="normal-text"></div></td>
                                <td colspan="3" id="percent-q3"><div class="normal-text"></div></td>
                                <td colspan="3" id="percent-q4"><div class="normal-text"></div></td>
                            </tr> -->
                            </tbody>
                        </table>
                    </div>
                    <div class="content-panel new-home-grid-div" id="SNScanInfoGrid">
                        <table class="ui-grid-table ui-grid new-home-grid">
                            <thead>
                            <tr>
                                <th colspan="13" class="grid-title">
                                    <div><p>每月SN级以上小包装产品出货扫码目标&nbsp;&nbsp;&nbsp;&nbsp;<span id="sn-month-target">&nbsp;&nbsp;升</span></p></div>
                                </th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr>
                                <th>月份</th><td>1</td><td>2</td><td>3</td><td>4</td><td>5</td><td>6</td><td>7</td><td>8</td><td>9</td><td>10</td><td>11</td><td>12</td>
                            </tr>
                            <tr>
                            <tr class="digital-tr">
                                <th>实际完成</th>
                                <!--没达标的叉——<i class="fa fa-times-circle not-up-icon" aria-hidden="true"></i>-->
                                <!--感叹号——<i class="fa fa-exclamation-circle warning-icon" aria-hidden="true"></i>-->
                                <!--达标——<i class="fa fa-check-circle-o right-icon" aria-hidden="true"></i>-->
                                <td id="p-actual-m1"></td>
                                <td id="p-actual-m2"></td>
                                <td id="p-actual-m3"></td>
                                <td id="p-actual-m4"></td>
                                <td id="p-actual-m5"></td>
                                <td id="p-actual-m6"></td>
                                <td id="p-actual-m7"></td>
                                <td id="p-actual-m8"></td>
                                <td id="p-actual-m9"></td>
                                <td id="p-actual-m10"></td>
                                <td id="p-actual-m11"></td>
                                <td id="p-actual-m12"></td>
                            </tr>
                            <tr>
                                <th>月度是否达标</th>
                                <!--绿色——<div class="up">2.00</div>-->
                                <!--黄色——<div class="warning">2.00</div>-->
                                <!--红色——<div class="not-up">2.00</div>-->
                                <!--进行中——<div class="running">进行中</div>-->
                                <td><div id="p-target-m1" class="width100"></div></td>
                                <td><div id="p-target-m2" class="width100"></div></td>
                                <td><div id="p-target-m3" class="width100"></div></td>
                                <td><div id="p-target-m4" class="width100"></div></td>
                                <td><div id="p-target-m5" class="width100"></div></td>
                                <td><div id="p-target-m6" class="width100"></div></td>
                                <td><div id="p-target-m7" class="width100"></div></td>
                                <td><div id="p-target-m8" class="width100"></div></td>
                                <td><div id="p-target-m9" class="width100"></div></td>
                                <td><div id="p-target-m10" class="width100"></div></td>
                                <td><div id="p-target-m11" class="width100"></div></td>
                                <td><div id="p-target-m12" class="width100"></div></td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="content-panel new-home-grid-div" id="newStoresInfoGrid">
                        <table class="ui-grid-table ui-grid new-home-grid quarter-grid">
                            <thead>
                            <tr>
                                <th colspan="5" class="grid-title">
                                    <div><p>每季度成功开发的新维修门店目标&nbsp;&nbsp;&nbsp;&nbsp;<span>8&nbsp;&nbsp;家</span></p></div>
                                </th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr>
                                <th>季度</th><td>第一季度</td><td>第二季度</td><td>第三季度</td><td>第四季度</td>
                            </tr>
                            <tr>
                            <tr class="digital-tr">
                                <th>实际完成</th>
                                <!--没达标的叉——<i class="fa fa-times-circle not-up-icon" aria-hidden="true"></i>-->
                                <!--感叹号——<i class="fa fa-exclamation-circle warning-icon" aria-hidden="true"></i>-->
                                <!--勾——<i class="fa fa-times-circle right-icon" aria-hidden="true"></i>-->
                                <td id="s-actual-q1"></td>
                                <td id="s-actual-q2"></td>
                                <td id="s-actual-q3"></td>
                                <td id="s-actual-q4"></td>
                            </tr>
                            <tr>
                                <th>季度是否达标</th>
                                <!--绿色——<div class="up">2.00</div>-->
                                <!--黄色——<div class="warning">2.00</div>-->
                                <!--红色——<div class="not-up">2.00</div>-->
                                <!--进行中——<div class="running">进行中</div>-->
                                <td><div id="s-target-q1" class=""></div></td>
                                <td><div id="s-target-q2" class=""></div></td>
                                <td><div id="s-target-q3" class=""></div></td>
                                <td><div id="s-target-q4" class=""></div></td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="content-panel new-home-grid-div" id="marketingInfoGrid">
                        <table class="ui-grid-table ui-grid new-home-grid quarter-grid">
                            <thead>
                            <tr>
                                <th colspan="5" class="grid-title">
                                    <div><p>本地销售/营销目标&nbsp;&nbsp;&nbsp;&nbsp;<span style="display: none">&nbsp;&nbsp;(升)</span></p></div>
                                </th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr>
                                <th>季度</th>
                                <td>第一季度</td>
                                <td>第二季度</td>
                                <td>第三季度</td>
                                <td>第四季度</td>
                            </tr>
                            <tr>
                                <th>实际完成</th>
                                <!--完成——<span><i class="fa fa-check-circle-o right-icon" aria-hidden="true"></i>完成</span>-->
                                <!--未完成——<span><i class="fa fa-times-circle not-up-icon" aria-hidden="true"></i>未完成</span>-->
                                <td><div id="goal-q1"></div></td>
                                <td><div id="goal-q2"></div></td>
                                <td><div id="goal-q3"></div></td>
                                <td><div id="goal-q4"></div></td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="content-panel grid-desc">
                        温馨提示：上述信息每天更新一次，销量数据为信息更新时公司已发货的数字。月中提供信息只供参考，最终计算奖励的销量数据以下一个月的第7个工作日所公布的为准。此外，由于月末5个工作日为工厂发货及财务关账高峰，敬请服务商提前下单，尤其是季度末的最后一个月，以免订单无法发货并如期入账而影响当月（当季）销量统计。
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>