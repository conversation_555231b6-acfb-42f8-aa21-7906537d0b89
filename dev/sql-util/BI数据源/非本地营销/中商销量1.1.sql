select distinct odp1.scantime/*发货时间*/, odp1.product_id/*产品SKU*/, 
opp1.product_batch/*生产批次*/,odp1.code/*箱码/瓶码*/, odp1.codelevel/*1-瓶，2-箱*/,
od1.recv_distributor_id/*收货经销商*/, bp1.pack liters/*升数*/
from wx_t_oem_delivery od1
inner hash join wx_t_oem_delivery_product odp1 on od1.id = odp1.delivery_id
join wx_t_oem_product_packaging_code oppc1 on oppc1.code2=odp1.code and odp1.codelevel=2
left join wx_t_oem_product_packaging opp1 on opp1.id = oppc1.packaging_id
left join [PP_MID].[dbo].[syn_dw_to_pp_product] bp1 on bp1.product_code_SAP=odp1.product_id
where odp1.creation_time>='2022-01-01' and od1.recv_distributor_id>0 and bp1.pack>0
union all 
select distinct odp1.scantime, odp1.product_id, opp1.product_batch,odp1.code, odp1.codelevel,
od1.recv_distributor_id,bp1.unit_size_l
from wx_t_oem_delivery od1
inner hash join wx_t_oem_delivery_product odp1 on od1.id = odp1.delivery_id
join wx_t_oem_product_packaging_code oppc1 on oppc1.code1=odp1.code and odp1.codelevel=1
left join wx_t_oem_product_packaging opp1 on opp1.id = oppc1.packaging_id
left join [PP_MID].[dbo].[syn_dw_to_pp_product] bp1 on bp1.product_code_SAP=odp1.product_id
where odp1.creation_time>='2022-01-01' and od1.recv_distributor_id>0 and bp1.unit_size_l>0
