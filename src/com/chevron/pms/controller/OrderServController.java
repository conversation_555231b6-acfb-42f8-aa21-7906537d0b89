package com.chevron.pms.controller;

import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.common.exception.auth.WxAuthException;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.chevron.cdm_mkt.constant.CdmMktConstant;
import com.chevron.cdm_mkt.dao.WXTMktFileMapper;
import com.chevron.cdm_mkt.model.WXTMktFile;
import com.chevron.exportdata.ExportCol;
import com.chevron.exportdata.ExportExcel;
import com.chevron.exportdata.IExportCol;
import com.chevron.exportdata.IPropertyHelper;
import com.chevron.exportdata.PoiWriteExcel;
import com.chevron.master.business.WorkshopMasterBizService;
import com.chevron.master.model.WorkshopMaster;
import com.chevron.o2oorder.model.OrderO2OPageVo;
import com.chevron.o2oorder.service.O2oOrderService;
import com.chevron.plc.dao.OutStockProductVoMapper;
import com.chevron.pms.dao.OrderLineVoMapper;
import com.chevron.pms.dao.OrderVoMapper;
import com.chevron.pms.dao.PartnerResponsibleVoMapper;
import com.chevron.pms.dao.RegionVoMapper;
import com.chevron.pms.dao.UserChargeRegionMapper;
import com.chevron.pms.model.OrderCondition;
import com.chevron.pms.model.OrderDDBXCondition;
import com.chevron.pms.model.OrderLineVo;
import com.chevron.pms.model.OrderVo;
import com.chevron.pms.model.OrderVoAuthParams;
import com.chevron.pms.model.PartnerResponsibleVo;
import com.chevron.pms.service.OrderDDBXService;
import com.chevron.pms.service.OrderService;
import com.chevron.thirdorder.model.OrderCommonVo;
import com.common.constants.Constants;
import com.common.constants.MessageContants;
import com.common.util.CommonUtil;
import com.common.util.ContextUtil;
import com.common.util.DateUtil;
import com.common.util.DateUtils;
import com.common.util.MessageResourceUtil;
import com.common.util.ResponseMap;
import com.common.util.SendMessageUtil;
import com.common.util.StringUtils;
import com.common.util.ThrowableUtil;
import com.sys.auth.model.WxTUser;
import com.sys.file.dao.WxAttFileMapper;
import com.sys.file.web.FileManager;
import com.sys.utils.model.AsynProcessStatus;
import com.wordnik.swagger.annotations.ApiImplicitParam;
import com.wordnik.swagger.annotations.ApiOperation;
import com.wordnik.swagger.annotations.ApiParam;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
/**
 * 
 * @Author: bo.liu  2016-11-25 下午2:53:17 
 * @Version: $Id$
 * @Desc: <p>采购订单||针对服务包||服务订单 控制类</p>
 */
@Controller
@RequestMapping(value="/orderServ")
public class OrderServController {
	public final static String SUCCESS_CODE = "success";
	public final static String ERROR_CODE = "error";
	
	public final static String RESULT_CODE_KEY = "code";
	public final static String RESULT_LST_KEY = "resultLst";
	public final static String RESULT_ERROR_MSG_KEY = "errorMsg";
	@Resource
	OrderDDBXService orderDDService;
	@Resource
	OrderService orderServiceImpl;
	@Resource
	private OrderVoMapper orderVoMapper;
	@Resource
	PartnerResponsibleVoMapper partnerResponsibleVoMapper;
	@Resource
	UserChargeRegionMapper userChargeRegionMapper;
	
	@Resource
	OrderLineVoMapper orderLineMapper;
	@Autowired
	FileManager fileManager;
	@Resource
	WXTMktFileMapper wXTMktFileMapper;
	@Resource
	WxAttFileMapper wxAttFileMapper;
	@Autowired
	public WorkshopMasterBizService workshopMasterBizService;
	@Resource
	public RegionVoMapper regionVoMapper;
	@Resource
	public O2oOrderService o2oOrderServiceImpl;
	
	@Autowired
	private OutStockProductVoMapper outStockProductVoMapper;
	
	private final static Logger log = Logger.getLogger(OrderServController.class);
	
	@ResponseBody
    @ApiOperation(value="经销商出库订单详情查询接口",  httpMethod="POST", notes="经销商出库订单详情查询接口")
    @ApiImplicitParam(paramType="query", name="appToken", value="token信息", required=true)
	@RequestMapping(value="/distributorout/detail.do")
	public ResponseMap getDistributorOutOrder(@ApiParam(name="id", value="订单ID", required=false) @RequestParam(value="id", required=true) Long id){
		ResponseMap resultMap = new ResponseMap();
		log.info("getDistributorOutOrder: " + id);

		try {
			Map<String, Object> params = new HashMap<String, Object>(3);
			params.put("id", id);
			OrderVo orderVo = orderVoMapper.queryByParams(params).get(0);
			if(OrderVo.ORDER_SOURCE_OUT_STOCK.equals(orderVo.getSource())) {
				//经销商出库
				params = new HashMap<String, Object>(3);
				params.put("orderType", Constants.STOCK_TYPE_WORKSHOP);
				params.put("orderNo", orderVo.getOrderNo());
				resultMap.put("outProductLines", outStockProductVoMapper.queryByParams(params));
			}
			resultMap.setDataResult(orderVo);
			log.info("getDistributorOutOrder success: " + id);
		} catch (Exception e) {
			resultMap.handleException(e, this, ContextUtil.getCurUserId(), 
					"com.chevron.plc.controller.OutStockController.getDistributorOutOrder", id.toString());
		}
		return resultMap;
	}
	/**
	 * 服务包订单查询
	 * <AUTHOR> 2016-11-25 下午3:00:47
	 * @param orderConditions
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value="/queryFWBOrders.do", method = {RequestMethod.POST})
    public Map<String,Object> queryFWBOrders(OrderDDBXCondition orderConditions, HttpServletRequest request)
	{
		Map<String,Object> resultMap = new HashMap<String,Object>();
		try {
			Long currentUserId = ContextUtil.getCurUserId();//123303L;//ContextUtil.getCurUserId();
			List<Long> regionIdLst = userChargeRegionMapper.getRegionsByUserId(currentUserId);
			/*List<String> regionIds =null;
			if(null!=regionIdLst)
			{
				regionIds = new ArrayList<String>();
				for(Long regionId:regionIdLst)
				{
					regionIds.add(""+regionId);
				}
			}*/
			if(null==regionIdLst||regionIdLst.isEmpty())
			{
				regionIdLst = null;
			}
			orderConditions.setRegionIds(regionIdLst);
			resultMap = orderDDService.queryFWBOrders(orderConditions);
			
		} catch (Exception e) {
			e.printStackTrace();
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error(ERROR_CODE,e);
		}
		return resultMap;
	}
	
	/**
	 * 服务订单查询
	 * <AUTHOR> 2016-11-25 下午4:11:12
	 * @param orderConditions
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value="/queryFWOrders.do", method = {RequestMethod.POST})
	public Map<String,Object> queryFWOrders(OrderDDBXCondition orderConditions)
	{
		Map<String,Object> resultMap = new HashMap<String,Object>();
		try {
			Long currentUserId =ContextUtil.getCurUserId();
			List<Long> regionIdLst = userChargeRegionMapper.getRegionsByUserId(currentUserId);
			/*List<String> regionIds =null;
			if(null!=regionIdLst)
			{
				regionIds = new ArrayList<String>();
				for(Long regionId:regionIdLst)
				{
					regionIds.add(""+regionId);
				}
			}*/
			if(null==regionIdLst||regionIdLst.isEmpty())
			{
				regionIdLst = null;
			}
			orderConditions.setRegionIds(regionIdLst);
			resultMap = orderDDService.queryFWOrders(orderConditions);
		} catch (Exception e) {
			e.printStackTrace();
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error(ERROR_CODE,e);
		}
		return resultMap;
	}

	/**
	 * 采购订单查询
	 * <AUTHOR> 2016-11-30 上午10:49:17
	 * @param orderConditions
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value="/queryCGOrders.do", method = {RequestMethod.POST})
	public Map<String,Object> queryCGOrders(OrderCondition orderConditions)
	{
		Map<String,Object> resultMap = new HashMap<String,Object>();
		try {
			resultMap = orderServiceImpl.queryOrdersByConditions(orderConditions);
		} catch (Exception e) {
			e.printStackTrace();
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error(ERROR_CODE,e);
		}
		return resultMap;
	}
	
	@SuppressWarnings({ "unchecked", "unused" })
	@ResponseBody
	@RequestMapping(value="/export.do", method = {RequestMethod.POST})
	public ResponseMap export(OrderCondition orderConditions, final HttpServletRequest request, HttpServletResponse response)
	{
		ResponseMap responseMap = new ResponseMap();
		try {
			
			final Long userId = ContextUtil.getCurUserId();
			final AsynProcessStatus processStatus = new AsynProcessStatus("com.chevron.pms.controller.OrderServController.export", userId);
			processStatus.setAttrs(new HashMap<String, Object>(2));
			processStatus.save();
			
			final List<IExportCol> exportCols = new ArrayList<IExportCol>();
			ExportCol exportCol = new ExportCol("orderNo", "订单编号");
			exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
			exportCol.setWidth(40);
			exportCols.add(exportCol);
			
			exportCol = new ExportCol("orgName", "合伙人");
			exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
			exportCol.setWidth(40);
			exportCols.add(exportCol);
		
			exportCol = new ExportCol("workshopName", "门店名称");
			exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
			exportCol.setWidth(40);
			exportCols.add(exportCol);

			exportCol = new ExportCol("b2bStatus", "支付状态");
			exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
			exportCol.setWidth(30);
			exportCols.add(exportCol);
			
			exportCol = new ExportCol("statusMeaning", "订单状态");
			exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
			exportCol.setWidth(30);
			exportCols.add(exportCol);
			
			exportCol = new ExportCol("receiveUserName", "创建人");
			exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
			exportCol.setWidth(20);
			exportCols.add(exportCol);
		
			exportCol = new ExportCol("createTime", "创建时间");
			exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
			exportCol.setWidth(20);
			exportCols.add(exportCol);

			/*//获取数据
			List<Map<String, Object>> newDataList = new ArrayList<Map<String,Object>>();
			Map<String, Object> dataMap = new HashMap<String, Object>();
			*/
			
			
			processStatus.setStatus(AsynProcessStatus.STATUS_PROCESSING);
			processStatus.setMessage("加载数据中...");
			processStatus.save();
			orderConditions.setPaging(false);
			
			final List<OrderVo> lstOrders  = (List<OrderVo>) orderServiceImpl.queryOrdersByConditions(orderConditions).get(RESULT_LST_KEY);
			
			new Thread(){
				public void run() {
					try {
						//获取数据
						List<Map<String, Object>> newDataList = new ArrayList<Map<String,Object>>();
						Map<String, Object> dataMap = new HashMap<String, Object>();
						
						//创建文件
						String fileName = "销售订单"+DateUtils.getTimestamp(new Date());
						String folderPath = FileManager.fileUploadPath + "fleet/";
						String filePath = folderPath + fileName + ".xlsx";
						File folder = new File(folderPath);
						if(!folder.exists()){
							folder.mkdirs();
						}
						final File file = new File(filePath);
						processStatus.getAttrs().put("filePath", filePath);
						processStatus.getAttrs().put("fileName", file.getName());
						
						//组装数据
						if(CollectionUtil.isNotEmpty(lstOrders)) {
							for (OrderVo orderVo : lstOrders) {
								dataMap = new HashMap<String, Object>();
								dataMap.put("orderNo", orderVo.getOrderNo());
								dataMap.put("orgName", orderVo.getOrgName());
								dataMap.put("workshopName", orderVo.getWorkshopName());
								dataMap.put("statusMeaning", orderVo.getStatusMeaning());
								dataMap.put("receiveUserName", orderVo.getReceiveUserName());
								dataMap.put("createTime", DateUtil.getDateStr(orderVo.getCreateTime(),DateUtil.DEFAULT_PATTERN));
								if(null == orderVo.getB2bStatus()) {
									dataMap.put("b2bStatus", "");
								}else {
									dataMap.put("b2bStatus", orderVo.getB2bStatus()==0?"待付款":"已付款");
								}
								newDataList.add(dataMap);
							}
						}
					
						OutputStream outputStream = new FileOutputStream(file);
						processStatus.setMessage("写入数据中......");
						processStatus.save();
						
						PoiWriteExcel.exportLargeData(newDataList, exportCols, outputStream, "sheet1", true);
						/*CommonUtil.setExportResponseHeader(request, response, "销售订单" + DateUtil.getCurrentDate("yyyyMMdd"), "xlsx");
						PoiWriteExcel.exportLargeData(newDataList, exportCols, response.getOutputStream(), "sheet1", true);*/
						outputStream.flush();
						outputStream.close();
						processStatus.setStatus(AsynProcessStatus.STATUS_SUCCESS);
						processStatus.save();
					} catch (Exception e) {
						log.error(e.getMessage(), e);
						processStatus.setStatus(AsynProcessStatus.STATUS_ERROR);
						processStatus.setMessage("导出失败。" + MessageResourceUtil.getMessage("system.unexpected_exception"));
						processStatus.save();
					}
				};
			}.start();
			responseMap.put(AsynProcessStatus.RESULT_KEY, processStatus);
			responseMap.put(Constants.RESULT_CODE_KEY, Constants.RESULT_SUCCESS);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			request.setAttribute("errorMsg", "Export Failed: " + e.getMessage());
			responseMap.put(Constants.RESULT_CODE_KEY, Constants.RESULT_ERROR);
			responseMap.setErrorMsg(WxAuthException.System_Exception_msg);
			//return "forward:/common/jsp/downloadError.jsp";
		}
		return responseMap;
	}
	
	@ResponseBody
	@RequestMapping(value="/countGetCGOrders.do")
	public Map<String,Object> countGetCGOrders(){
		Map<String,Object> resultMap = new HashMap<String,Object>();
		try {
			OrderVoAuthParams orderCondition = new OrderVoAuthParams();
			WxTUser curUser = ContextUtil.getCurUser();
				
			if(!curUser.getmUserTypes().equals(Long.valueOf("1"))){
				orderCondition.setPartnerId(String.valueOf(curUser.getOrgId()));
			}	
			long count = orderVoMapper.countCGOrdersList(orderCondition);
			resultMap.put("totalRecord", count);
		} catch (Exception e) {
			e.printStackTrace();
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error(ERROR_CODE,e);
		}
		return resultMap;
	}
	
	@ResponseBody
	@RequestMapping(value="/getConfirmOrderFlag.do")
	public Map<String,Object> getConfirmOrderFlag(HttpServletRequest request){
		Map<String,Object> resultMap = new HashMap<String,Object>();
		try{
			//获取当前userid,判断是否有确认订单的权限
			Map<String,Object> reqMap = new HashMap<String,Object>();
			reqMap.put("funFlag", "order_confirm");
			//reqMap.put("organizationName", OrderCommonVo.ORDER_SP[1]);//多个的时候后面考虑用in查询
			List<String> orgNames = new ArrayList<String>();
			orgNames.add(OrderCommonVo.ORDER_SP[1]);
			reqMap.put("organizationNames",orgNames);
			reqMap.put("userId", ContextUtil.getCurUser().getUserId());
			List<PartnerResponsibleVo> lstPartnerResponsibleVo =  partnerResponsibleVoMapper.queryEmailInfoForFWBOrderConfirm(reqMap);
			if(null==lstPartnerResponsibleVo || lstPartnerResponsibleVo.isEmpty())
			{
				resultMap.put("confirmFlag", false);
			}else
			{
				resultMap.put("confirmFlag", true);
			}
			resultMap.put("code", "success");
		} catch (Exception e) {
			resultMap.put("code", "error");
			e.printStackTrace();
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error(ERROR_CODE,e);
		}
		return resultMap;
	}
	
	
	@ResponseBody
	@RequestMapping("/serviceOrderExport.do")
	public Map<String, Object> serviceOrderExport(final OrderDDBXCondition orderConditions,
			final HttpServletRequest request, final HttpServletResponse response) {
		orderConditions.setPaging(false);
		Map<String, Object> map = new HashMap<String, Object>(8);
		String relPath = (String) Constants.getSystemPropertyByCodeType(Constants.report_file_path);
		String fileName = "服务订单-" + DateUtil.getDateStr(null, DateUtil.ACCURACY_PATTERN_DAY);
		final File file = new File(relPath + File.separator + fileName + ".xls");
		try {
//			Long currentUserId = ContextUtil.getCurUserId();
			final Long currentUserId = ContextUtil.getCurUserId();
			orderConditions.setLoginOrg(ContextUtil.getCurUser().getOrgId());
			List<Long> regionIdLst = userChargeRegionMapper.getRegionsByUserId(currentUserId);
			if (null == regionIdLst || regionIdLst.isEmpty()) {
				regionIdLst = null;
			}
			orderConditions.setRegionIds(regionIdLst);
			final AsynProcessStatus processStatus = new AsynProcessStatus(
					"com.chevron.pms.controller.OrderServController.serviceOrderExport",currentUserId);
			processStatus.setAttrs(new HashMap<String, Object>(2));
			processStatus.save();
			final Map<String, Object> resultMap = orderDDService.queryFWOrders(orderConditions);
			map = isMaxLine(orderConditions,resultMap);
			if(map.isEmpty() || "1".equals(map.get("result"))) {
			new Thread() {
				@Override
				public void run() {
					try {
						Long start = System.currentTimeMillis();
						processStatus.setStatus(AsynProcessStatus.STATUS_PROCESSING);
						processStatus.setMessage("加载数据中...");
						processStatus.save();
						processStatus.getAttrs().put("filePath", file.getAbsolutePath());
						processStatus.getAttrs().put("fileName", file.getName());
						String orderIds = orderConditions.getOrderIds();
						List<OrderVo> orderResultList = null;

						if (StringUtils.isNotBlank(orderIds)) {
							List<Long> orderIdList = new ArrayList<Long>();
							String[] orderLiStrings = orderIds.split(",");
							for (String s : orderLiStrings) {
								orderIdList.add(Long.parseLong(s));
							}
							orderResultList = orderVoMapper.selectOrderList(orderIdList);
						} else {
//							Map<String, Object> resultMap = new HashMap<String, Object>();
//							resultMap = orderDDService.queryFWOrders(orderConditions);
							if (!resultMap.isEmpty()) {
								orderResultList = (List<OrderVo>) resultMap.get(RESULT_LST_KEY);
							}
						}
						if (orderResultList != null) {
							int no = 1;
							for (OrderVo vo : orderResultList) {
								processStatus.setMessage(
										"处理 序号" + vo.getId() + " 数据 (" + no + "/" + orderResultList.size() + ") ...");
								processStatus.save();
								// 根据订单编号id 查询对应的详情信息
								Map<String, Object> reqMap = new HashMap<String, Object>();
								reqMap.put("orderId", vo.getId());
								List<OrderLineVo> lstOrderLineVo = orderLineMapper
										.getOrderLinesByConditionForDDBXOrder(reqMap);
								if (lstOrderLineVo == null || lstOrderLineVo.isEmpty()) {
									lstOrderLineVo = new ArrayList<OrderLineVo>();
								}
								vo.setOrderLines(lstOrderLineVo);
								no++;
							}
						}
						processStatus.setMessage("写入数据中......");
						processStatus.save();
						List<ExportCol> headerCols = getHeaderCols();
						List<ExportCol> headerDetailCols = getHeadeDetailCols();
						OutputStream outputStream = null;
						outputStream = new FileOutputStream(file);
						ExportExcel.exportOrderSeverDatas(orderResultList, headerCols, "orderLines", headerDetailCols,
								"订单详情", outputStream, "服务订单", true);
						processStatus.setStatus(AsynProcessStatus.STATUS_SUCCESS);
						processStatus.save();
						log.debug("处理完成 applySignage：" + (System.currentTimeMillis() - start) + "ms");
					} catch (Exception e) {
						log.error(e.getMessage(), e);
						processStatus.setStatus(AsynProcessStatus.STATUS_ERROR);
						processStatus
								.setMessage("导出失败。" + MessageResourceUtil.getMessage("system.unexpected_exception"));
						processStatus.save();
					}
				}
			}.start();
			map.put(AsynProcessStatus.RESULT_KEY, processStatus);
			map.put(Constants.RESULT_CODE_KEY, Constants.RESULT_SUCCESS);
			}
		} catch (Exception e) {
			log.error("applySignage exception:", e);
			map.put(Constants.RESULT_CODE_KEY, Constants.RESULT_ERROR);
			map.put(Constants.RESULT_ERROR_MSG_KEY,
					"导出失败。" + MessageResourceUtil.getMessage("system.unexpected_exception"));
		}
		return map;

	}

	public List<ExportCol> getHeaderCols() throws Exception {
		List<ExportCol> exportCols = new ArrayList<ExportCol>();
		ExportCol col;
		col = new ExportCol("id", "序号", OrderVo.class);
		col.setWidth(30);
		exportCols.add(col);
		col = new ExportCol("orgName", "订单来源", OrderVo.class);
		col.setWidth(30);
		exportCols.add(col);
		col = new ExportCol("orderNo", "服务单号", OrderVo.class);
		col.setWidth(30);
		exportCols.add(col);
		col = new ExportCol("plateNumber", "车牌号", OrderVo.class);
		col.setWidth(30);
		exportCols.add(col);
		col = new ExportCol("billId", "物流发货单号", OrderVo.class);
		col.setWidth(30);
		exportCols.add(col);
		col = new ExportCol("receiveUserName", "收货人", OrderVo.class);
		col.setWidth(30);
		exportCols.add(col);
		col = new ExportCol("receivePhoneNo", "收货电话", OrderVo.class);
		col.setWidth(30);
		exportCols.add(col);
		col = new ExportCol("deliveryTime", "发货时间", OrderVo.class);
		col.setWidth(30);
		exportCols.add(col);
		col = new ExportCol("address", "发货地址", OrderVo.class);
		col.setWidth(30);
		exportCols.add(col);
		col = new ExportCol("createTime", "创建时间", OrderVo.class);
		col.setWidth(30);
		exportCols.add(col);
		return exportCols;
	}

	public List<ExportCol> getHeadeDetailCols() throws Exception {
		ExportCol col;
		// 详情信息
		List<ExportCol> detailCols = new ArrayList<ExportCol>();
		col = new ExportCol("sku", "商品编号", OrderLineVo.class);
		col.setWidth(20);
		detailCols.add(col);
		col = new ExportCol("productName", "商品名称", OrderLineVo.class);
		col.setWidth(50);
		detailCols.add(col);
		col = new ExportCol("amount", "发货数量", OrderLineVo.class);
		col.setWidth(20);
		// col.setDataType("number");
		detailCols.add(col);
		col = new ExportCol("units", "单位", OrderLineVo.class);
		col.setWidth(20);
		detailCols.add(col);
		return detailCols;
	}
	public Map<String,Object> isMaxLine(OrderDDBXCondition orderConditions,Map<String, Object> resultMap){
		Map<String,Object> map = new HashMap<String, Object>();
		List<OrderVo> orderResultList=null;
		if (!resultMap.isEmpty()) {
			orderResultList = (List<OrderVo>) resultMap.get(RESULT_LST_KEY);
		}
		String orderIds = orderConditions.getOrderIds();
		if (StringUtils.isNotBlank(orderIds)) {
			map.put("result", "1");
		}else if(orderResultList!=null && orderResultList.size()>=ExportExcel.SHEET_MAX_ROW) {
			map.put(Constants.RESULT_CODE_KEY, Constants.RESULT_ERROR);
			map.put(Constants.RESULT_ERROR_MSG_KEY,
					"您导出的数据量过大，请使用筛选功能导出您所需要的数据。");
		}
		return map;
		
	}
	
	/**
	 * O2O客服确认订单
	 * @param orderVo
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value="/confirmOrderInfos.do")
	public Map<String, Object> confirmOrderInfos(OrderVo orderVo){
		Map<String, Object> resultMap = new HashMap<String, Object>();
			try {
				//orderVo.setStatus(OrderVo.ORDER_STATUS_3);//客服已确认预约信息
				//判断验证码是否已经存在，存在则不再生成验证码
				OrderVo result = orderVoMapper.getOrderDetailById(orderVo.getId());
				if(!StringUtils.isNotEmpty(result.getVerificateCode())) {
					//生成验证码 代码待写
					int verificateCode = CommonUtil.generateRandomCode6(Constants.RANDOM_CODE6_MODULE_O2O_ORDER);
					orderVo.setVerificateCode(String.valueOf(verificateCode));
				}
				if(OrderVo.ORDER_STATUS_3.equals(orderVo.getStatus())) {
					//1代表验证码有效，空或者0 为无效
					orderVo.setVerificateFlag(1);
				}else if(OrderVo.ORDER_STATUS_2.equals(orderVo.getStatus())) {
					orderVo.setVerificateFlag(0);
				}
				if(StringUtils.isNotEmpty(orderVo.getWorkshopName())) {
				result.setWorkshopName(orderVo.getWorkshopName());
				}
				if(orderVo.getServiceTime()!=null) {
				result.setServiceTime(orderVo.getServiceTime());
				}
				if(StringUtils.isNotEmpty(orderVo.getVerificateCode())) {
					result.setVerificateCode(orderVo.getVerificateCode());
				}
				orderVoMapper.updateOrderInfos(orderVo);
				//获取门店地址
				WorkshopMaster  workShopResult= workshopMasterBizService.getBean(result.getWorkShopId());
				String workShopAddress = "";
				if(workShopResult!=null) {
					workShopAddress = workShopResult.getWorkShopAddress();
				}
				if(OrderVo.ORDER_STATUS_3.equals(orderVo.getStatus())) {
				//订单确认发送短信给客户
				String content=StrUtil.EMPTY;
				String serviceTime = DateUtils.getDateStr(result.getServiceTime());
				if(OrderVo.SHOP_ADDRESS_TYPE.equals(result.getAddressType())) {
					content = CdmMktConstant.SHOP_CONFIRM_SMS.replace("{workshopName}",result.getWorkshopName()).replace("{workShopAddress}", workShopAddress).replace("{serviceTime}", serviceTime).replace("{verificationCode}", result.getVerificateCode());
				}else {
					content = CdmMktConstant.OTHER_CONFIRM_SMS.replace("{workshopName}",result.getWorkshopName()).replace("{workShopAddress}", workShopAddress).replace("{serviceTime}", serviceTime).replace("{verificationCode}", result.getVerificateCode());
				}
				//订单完成向客户发送短信
				SendMessageUtil.sndMessageToPhone(result.getPhoneNo(), content);
				}
				resultMap.put("code", MessageContants.SUCCESS_CODE);
				resultMap.put("codeMsg", MessageContants.SUCCESS_MSG);
			} catch (Exception e) {
				log.error("confirmOrderInfos exception", e);
				resultMap.put("code", MessageContants.EXCEPTION_CODE);
				resultMap.put("codeMsg", ThrowableUtil.getStackTrace(e));
				return resultMap;
		}
		return resultMap;
	}
	
	/**
	 * PP后台获取订单列表
	 * @param orderConditions
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value="/queryOrderList.do")
	public Map<String, Object> queryOrderList(OrderO2OPageVo orderConditions) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
	try {
		List<OrderVo> lstOrders = orderVoMapper.getOrderList(orderConditions);
		resultMap.put("resultLst", lstOrders);
		resultMap.put("totalRecord", orderConditions.getTotalCount());
		resultMap.put("code", MessageContants.SUCCESS_CODE);
		resultMap.put("codeMsg", MessageContants.SUCCESS_MSG);
	} catch (Exception e) {
		log.error("queryOrderList exception", e);
		resultMap.put("code", MessageContants.EXCEPTION_CODE);
		resultMap.put("codeMsg", ThrowableUtil.getStackTrace(e));
		return resultMap;
       }
            return resultMap;
	}
	
	/**
	 * PP后台获取订单详情
	 * @param id
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value="/getOrderDetailById.do")
	public Map<String, Object> getOrderDetailById(Long id) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		try {
			OrderVo result = orderVoMapper.getOrderDetailById(id);
			Map<String, Object> reqMap = new HashMap<String, Object>();
			if(result!=null) {
				reqMap.put("orderId", result.getId());
				List<OrderLineVo> lstOrderLineVo = orderLineMapper
						.getOrderLinesByConditionForDDBXOrder(reqMap);
				if (lstOrderLineVo == null || lstOrderLineVo.isEmpty()) {
					lstOrderLineVo = new ArrayList<OrderLineVo>();
				}
				result.setOrderLines(lstOrderLineVo);
				List<WXTMktFile> wxtMktFiles = wXTMktFileMapper.selectFileList(result.getId());
				if(wxtMktFiles == null || wxtMktFiles.isEmpty()) {
					wxtMktFiles = new ArrayList<WXTMktFile>();
				}
				result.setWxfileList(wxtMktFiles);
				//获取门店信息
				WorkshopMaster  workShopResult= workshopMasterBizService.getBean(result.getWorkShopId());
				if(workShopResult!=null) {
					result.setWorkshopName(workShopResult.getWorkShopName());
					result.setWorkShopAddress(workShopResult.getWorkShopAddress());
					result.setWorkShopTel(workShopResult.getContactPersonTel());
					////获取对应门店的省市，区域
					Long regionId = workShopResult.getRegionId();
					String regionName = o2oOrderServiceImpl.getWorkShopRegionNames(regionId);
					result.setWorkRegionName(regionName);
				}
			}
			resultMap.put("resultLst", result);
			resultMap.put("code", MessageContants.SUCCESS_CODE);
			resultMap.put("codeMsg", MessageContants.SUCCESS_MSG);
		} catch (Exception e) {
			log.error("getOrderDetailById exception", e);
			resultMap.put("code", MessageContants.EXCEPTION_CODE);
			resultMap.put("codeMsg", ThrowableUtil.getStackTrace(e));
			return resultMap;
		}
		return resultMap;
	}
	
	/**
	 * 更新订单信息
	 * @param orderVo
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value="/updateOrderInfos.do")
	public  Map<String, Object> updateOrderInfos(OrderVo orderVo) {
		 Map<String, Object> resultMap = new HashMap<String, Object>();
		try {
			orderVoMapper.updateOrderInfos(orderVo);
			//如果订单状态为已完成则释放验证码
			if(OrderVo.ORDER_STATUS_4.equals(orderVo.getStatus())) {
				String code = orderVo.getVerificateCode();
				if(StringUtils.isNotEmpty(code)) {
				CommonUtil.releaseRandomCode6(Integer.valueOf(code), Constants.RANDOM_CODE6_MODULE_O2O_ORDER);
				//订单状态为已完成则验证码失效
				orderVo.setVerificateFlag(0);
				orderVoMapper.updateOrderCodeById(orderVo);
				}
			}
			resultMap.put("code", MessageContants.SUCCESS_CODE);
			resultMap.put("codeMsg", MessageContants.SUCCESS_MSG);
		} catch (Exception e) {
			resultMap.put("code", MessageContants.EXCEPTION_CODE);
			resultMap.put("codeMsg", ThrowableUtil.getStackTrace(e));
			return resultMap;
		}
		return resultMap;
	}
	
	
}
