package com.common.aop;

import org.apache.log4j.Logger;
import org.aspectj.lang.JoinPoint;

import com.common.constants.ExceptionCodeConstants;
import com.common.exception.WxPltException;

public class AOPHandler {
	Logger logger = Logger.getLogger(AOPHandler.class);

	public void afterThrowing(JoinPoint joinPoint, Exception exception) throws WxPltException {
		logger.error("BeginAOPException:---------------------------------------------------------------------");

		// 拦截的实体类
		Object target = joinPoint.getTarget();
		// 拦截的方法名称
		String methodName = joinPoint.getSignature().getName();
		// 拦截的方法参数
		logger.error("类名：" + target.getClass().getName());
		logger.error("方法名称:" + methodName);
		System.out.println();
		// 代表的是我们自定义的的异常
		if (exception instanceof WxPltException) {
			WxPltException wxException = (WxPltException) exception;
			logger.error("expCode：" + (wxException.getExpCode()));
			logger.error("expMsg：" + (wxException.getExpMsg()));
			logger.error((wxException.getPrintStackTrace()));
			logger.error("EndAOPException1:---------------------------------------------------------------------");
		} else {
			// 通用的异常或错误用AOP处理
			WxPltException ehPtException = new WxPltException(exception).setExpCode(ExceptionCodeConstants.EXCEPTION);
			logger.error(ehPtException.getPrintStackTrace());
			logger.error("EndAOPException2:---------------------------------------------------------------------");
			throw ehPtException;

		}

	}

}
