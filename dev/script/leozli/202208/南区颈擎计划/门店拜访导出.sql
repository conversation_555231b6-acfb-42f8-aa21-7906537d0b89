/taskServ/exportTaskByTypeAndBrand.do?start=0&limit=10&queryType=1&pageIndex=0&field=createTime&taskTypeCode=TT_2_7ne%2F&resourceId=taskNew&direction=DESC&r=0.16020023312913456
/reportview/report/data.do?packageName=dsrkpi&viewName=VisitCustomerChart&month=2022-08&permissionWeight=-1&cai=&appToken=5892342fb969e2a0657b5fa73480ca47690a13ff
参数：
	month：必填
	permissionWeight: 必填登录用户权限
	cai:必填 登录用户CAI
	region
	distributorId
	dsrId
	
select day(s.submit_time) d, count(1) times
	from wx_task_sub s LEFT JOIN wx_task_main m ON s.task_main_id = m.task_main_id
	left join wx_t_partner_o2o_enterprise pe on pe.partner_id=m.tenant_id
	left join dw_customer_org_sales cos1 on cos1.distributor_id=pe.distributor_id and cos1.channel_weight&(case when s.brand&6>0 then 2 else 1 end)>0
	where m.tmb_type_code IN ('TT_2_7ne/','TT_2_7nf/') 
	and s.task_status='4' and pe.partner_id!=9
	and s.submit_time>=#VARCHAR,month#+'-01' and s.submit_time<dateadd(month, 1, #VARCHAR,month#+'-01')
	and (#VARCHAR,region# is null or #VARCHAR,region#='' or cos1.region=#VARCHAR,region#)
and (#BIGINT,distributorId# is null or pe.distributor_id=#BIGINT,distributorId#)
and (#INTEGER,permissionWeight#&1>0 or (#INTEGER,permissionWeight#&16>0 and #BIGINT,distributorId# is not null) or (#INTEGER,permissionWeight#&14>0 and exists (select 1 from dw_sales_role dp_001_sr1 where dp_001_sr1.[sales_cai]=cos1.sales_cai and dp_001_sr1.sales_cai_level like '%_'+#VARCHAR,cai#+'%')))
and (#BIGINT,dsrId# is null or m.excute_user_id=#BIGINT,dsrId#)
	group by day(s.submit_time)