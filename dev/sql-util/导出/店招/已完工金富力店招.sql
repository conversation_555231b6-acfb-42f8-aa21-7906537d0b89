select a.id "申请号", a.organization_name "经销商名称", a.store_name "修理厂名称", 
v.region_name "销售大区", v.sales_name "销售代表", a.cost_money "预算价/申请金额",
a.materiel_money "店招完工价", a.price, a.create_time "申请日期",
case when flow_finish3=1 then a.curr_auditor_role3 + a.curr_auditor3 + (case when a.audit_status3=-1 then '提交'
		when a.audit_status3=0 then '待审核'
		when a.audit_status3=1 then '审核通过'
		when a.audit_status3=2 then '审核驳回'
		when a.audit_status3=3 then '撤回' end)
	when id3 is not null then a.pre_auditor_role3 + a.pre_auditor3 + (case when a.audit_status3=-1 then '提交'
		when a.audit_status3=0 then '待审核'
		when a.audit_status3=1 then '审核通过'
		when a.audit_status3=2 then '审核驳回'
		when a.audit_status3=3 then '撤回' end)
	when id2 is not null then a.pre_auditor_role2 + a.pre_auditor2 + (case when a.audit_status2=-1 then '提交'
		when a.audit_status2=0 then '待审核'
		when a.audit_status2=1 then '审核通过'
		when a.audit_status2=2 then '审核驳回'
		when a.audit_status2=3 then '撤回' end)
	when a.id is not null then a.pre_auditor_role1 + a.pre_auditor1 + (case when a.audit_status1=-1 then '提交'
		when a.audit_status1=0 then '待审核'
		when a.audit_status1=1 then '审核通过'
		when a.audit_status1=2 then '审核驳回'
		when a.audit_status1=3 then '撤回' end) end "当前流程状态",
case when flow_finish3=1 then ''
	when id3 is not null then a.curr_auditor_role3 + a.curr_auditor3 
	when id2 is not null then a.curr_auditor_role2 + a.curr_auditor2 
	when a.id is not null then a.curr_auditor_role1 + a.curr_auditor1 end "下一节点审批人"
 from view_mkt_apply a
left join view_customer_region_sales_channel v on a.partner_id=v.distributor_id and v.channel_weight&2>0
where a.channel='CDM' and a.state=1 and a.mkt_type in ('STORE_FRONT', 'STORE_IN_STORE')
and a.id3 is not null and a.current_step3>1