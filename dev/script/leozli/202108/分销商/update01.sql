
insert into wx_t_dic_type (dic_type_code, dic_type_name, dic_type_desc) values ('Role<PERSON>hannel', '角色渠道配置', '角色渠道配置');
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('RoleChannel', 'CDM', '乘用车', '', '1', 10);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('RoleChannel', 'C&I', '商用油', '', '1', 20);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('RoleChannel', 'Indirect', 'Indirect', '', '1', 30);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('RoleChannel', 'Industrial', '工业油', '', '1', 40);
insert into wx_t_dic_item (dic_type_code, dic_item_code, dic_item_name, dic_item_desc, status, sort_numb) values ('RoleChannel', 'OEM', 'OEM', '', '1', 50);

INSERT INTO [dbo].[wx_t_res_permission_type]            ([permission_type_id]            ,[permission_type_code]            ,[permission_type_desc]            ,[resource_type_code]           ,[creation_time]            ,[created_by]            ,[permission_expression]            ,[sequence_no]            ,[with_user_type]            ,[permission_expression_by_region])      VALUES            (33           ,''            ,'所有门店'           ,'WS_FULL_RETAILER'           ,getdate()            ,1           ,'${cond.excludeTestSp}'            ,1           ,'CHEVRON'            ,'${cond.excludeTestSp}')
INSERT INTO [dbo].[wx_t_res_permission_type]            ([permission_type_id]            ,[permission_type_code]            ,[permission_type_desc]            ,[resource_type_code]           ,[creation_time]            ,[created_by]            ,[permission_expression]            ,[sequence_no]            ,[with_user_type]            ,[permission_expression_by_region])      VALUES            (34           ,''            ,'销售负责合伙人门店'           ,'WS_FULL_RETAILER'           ,getdate()            ,1           ,'exists (select 1 from dw_customer_org_sales dp_001_cos left join dw_sales_role dp_001_sr1 on dp_001_sr1.[sales_cai]=dp_001_cos.sales_cai and dp_001_sr1.del_flag=0 where dp_001_sr1.sales_cai_level like ''%_${value.loginCai}%'' and dp_001_cos.distributor_id=${field.distributorId} ${030.cond.channelWeight})'            ,3           ,'CHEVRON'            ,'(exists(select 1 from [PP_MID].[dbo].[syn_dw_to_pp_dim_sales_org_hier] dp_001_soh1 left join dw_sales_role dp_001_sr1 on dp_001_sr1.[sales_cai]=dp_001_soh1.level_mgr_cai and dp_001_sr1.sales_role=''ASM'' and dp_001_sr1.del_flag=0 where dp_001_soh1.level_name=''Region'' and dp_001_soh1.del_flag=0 and dp_001_sr1.sales_cai_level like ''%${value.loginCai}%'' ${010.cond.regionName.cos}) or exists (select 1 from wx_t_partner_responsible_main dp_001_prm where dp_001_prm.user_id=${value.loginUserId} ${020.cond.regionName.prm}))')
INSERT INTO [dbo].[wx_t_res_permission_type]            ([permission_type_id]            ,[permission_type_code]            ,[permission_type_desc]            ,[resource_type_code]           ,[creation_time]            ,[created_by]            ,[permission_expression]            ,[sequence_no]            ,[with_user_type]            ,[permission_expression_by_region])      VALUES            (35           ,''            ,'合伙人下所有门店'           ,'WS_FULL_RETAILER'           ,getdate()            ,1           ,'1=1'            ,2           ,'SP'            ,'')
INSERT INTO [dbo].[wx_t_res_permission_type]            ([permission_type_id]            ,[permission_type_code]            ,[permission_type_desc]            ,[resource_type_code]           ,[creation_time]            ,[created_by]            ,[permission_expression]            ,[sequence_no]            ,[with_user_type]            ,[permission_expression_by_region])      VALUES            (36           ,''            ,'合伙人下负责区域门店'           ,'WS_FULL_RETAILER'           ,getdate()            ,1           ,'(not exists (select 1 from wx_t_user_charge_region xx_001_ucr where xx_001_ucr.user_id=${value.loginUserId}) or exists (select 1 from wx_t_work_shop xx_001_ws join wx_t_user_charge_region xx_001_ucr on xx_001_ucr.charge_region_id=xx_001_ws.region_id where xx_001_ucr.user_id=${value.loginUserId} and xx_001_ws.id=${field.workshopId}))'            ,5           ,'SP'            ,'')
INSERT INTO [dbo].[wx_t_res_permission_type]            ([permission_type_id]            ,[permission_type_code]            ,[permission_type_desc]            ,[resource_type_code]           ,[creation_time]            ,[created_by]            ,[permission_expression]            ,[sequence_no]            ,[with_user_type]            ,[permission_expression_by_region])      VALUES            (37           ,''            ,'所有合伙人'           ,'SP_FULL_RETAILER'           ,getdate()            ,1           ,'${cond.excludeTestSp}'            ,1           ,'CHEVRON'            ,'${cond.excludeTestSp}')
INSERT INTO [dbo].[wx_t_res_permission_type]            ([permission_type_id]            ,[permission_type_code]            ,[permission_type_desc]            ,[resource_type_code]           ,[creation_time]            ,[created_by]            ,[permission_expression]            ,[sequence_no]            ,[with_user_type]            ,[permission_expression_by_region])      VALUES            (38           ,''            ,'所属合伙人'           ,'SP_FULL_RETAILER'           ,getdate()            ,1           ,'1=1'            ,2           ,'SP'            ,'')
INSERT INTO [dbo].[wx_t_res_permission_type]            ([permission_type_id]            ,[permission_type_code]            ,[permission_type_desc]            ,[resource_type_code]           ,[creation_time]            ,[created_by]            ,[permission_expression]            ,[sequence_no]            ,[with_user_type]            ,[permission_expression_by_region])      VALUES            (39           ,''            ,'销售负责合伙人'           ,'SP_FULL_RETAILER'           ,getdate()            ,1           ,'exists (select 1 from dw_customer_org_sales dp_001_cos left join dw_sales_role dp_001_sr1 on dp_001_sr1.[sales_cai]=dp_001_cos.sales_cai and dp_001_sr1.del_flag=0 where dp_001_sr1.sales_cai_level like ''%_${value.loginCai}%'' and dp_001_cos.distributor_id=${field.distributorId} ${030.cond.channelWeightFull})'            ,3           ,'CHEVRON'            ,'(exists(select 1 from [PP_MID].[dbo].[syn_dw_to_pp_dim_sales_org_hier] dp_001_soh1 left join dw_sales_role dp_001_sr1 on dp_001_sr1.[sales_cai]=dp_001_soh1.level_mgr_cai and dp_001_sr1.sales_role=''ASM'' and dp_001_sr1.del_flag=0 where dp_001_soh1.level_name=''Region'' and dp_001_soh1.[del_flag]=0 and dp_001_sr1.sales_cai_level like ''%${value.loginCai}%'' ${010.cond.regionName.cos}) or exists (select 1 from wx_t_partner_responsible_main dp_001_prm where dp_001_prm.user_id=${value.loginUserId} ${020.cond.regionName.prm} ${cond.permissionConfigFun}))')

