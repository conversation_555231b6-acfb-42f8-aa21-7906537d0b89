package com.chevron.pmp.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sys.auth.dao.WxTDataMapper;
import com.sys.auth.model.TreeNode;
import com.sys.auth.model.WxTData;
import com.sys.auth.model.WxTUser;
import com.sys.auth.service.impl.WxRoleServiceImpl;
import com.chevron.task.service.impl.TaskTypeService;
import com.chevron.pmp.model.StepNode;
import com.chevron.pmp.service.StepService;
import com.common.util.ContextUtil;
import com.common.util.json.JsonGenerator;

@Service
public class StepServiceImpl implements StepService {
	public final static String STEP_ROOT_CODE = "TS";
	@Resource
	private WxTDataMapper wxTDataMapper;
	@Resource
	private WxRoleServiceImpl wxRoleService;

	@Override
	public Map<String, Object> findStepsByTaskTypeForUpdate(String taskTypeCode) {
		Map<String, Object> map = new HashMap<String, Object>();
		try {
			WxTUser user = ContextUtil.getCurUser();
			Long partnerId = null;
			if(!WxTUser.USER_MODEL_CHEVRON.equals(user.getUserModel())){
				partnerId = user.getOrgId();
			}
			map.put("data", wxTDataMapper.selStepsByTaskTypeCodeForUpdate(taskTypeCode, STEP_ROOT_CODE, partnerId));
			map.put("success", true);
		} catch (Exception e) {
			e.printStackTrace();
			map.put("success", false);
		}
		return map;
	}

	@Override
	public Map<String, Object> findStepsByTaskType(String taskTypeCode) {
		Map<String, Object> map = new HashMap<String, Object>();
		try {
			map.put("data", wxTDataMapper.selStepsByTaskTypeCode(taskTypeCode, STEP_ROOT_CODE, "data_order asc"));
			map.put("success", true);
		} catch (Exception e) {
			e.printStackTrace();
			map.put("success", false);
		}
		return map;
	}

	@Override
	public Map<String, Object> deactiveStep(Long stepId) {
		Map<String, Object> map = new HashMap<String, Object>(2);
		try {
			WxTUser user = ContextUtil.getCurUser();
			WxTData data = new WxTData();
			data.setDataId(stepId);
			data.setStatus(0);
			data.setXgSj(new Date());
			data.setXgUser(user.getUserId());
			wxTDataMapper.updateByPrimaryKeySelective(data);
			map.put("success", true);
		} catch (Exception e) {
			e.printStackTrace();
			map.put("success", false);
		}
		return map;
	}

	@Override
	public Map<String, Object> creatNewStep(Long stepRootId, String stepName,
			String taskTypeCode, String order, String dataDescript) {
		Map<String, Object> map = new HashMap<String, Object>(3);
		try {
			//查询任务步骤根节点
			if(stepRootId == null){
				WxTData stepRoot = wxTDataMapper.selectByCode(STEP_ROOT_CODE);
				if(stepRoot == null){
					stepRoot = new WxTData();
					stepRoot.setDataCode(STEP_ROOT_CODE);
					stepRoot.setDataDescript("任务步骤数据根节点");
					stepRoot.setDataName("任务步骤");
					stepRoot.setStatus(1);
					wxTDataMapper.insertSelective(stepRoot);
				}
				stepRootId = stepRoot.getDataId();
			}
			return wxRoleService.creatNewData1(stepRootId, stepName, STEP_ROOT_CODE + "_",  "_" + taskTypeCode, order, dataDescript);
		} catch (Exception e) {
			e.printStackTrace();
			map.put("code", "systemerror");
		}
		return map;
	}

	@SuppressWarnings("unchecked")
	@Override
	public Map<String, Object> findStepTree() {
		Map<String, Object> map = new HashMap<String, Object>(2);
		try {
			WxTUser user = ContextUtil.getCurUser();
			Long userType = WxTUser.USER_MODEL_CHEVRON.equals(user.getUserModel()) ? 1l : 0l;
			List nodeList = wxTDataMapper.selTaskTypeNodes(TaskTypeService.TASK_TYPE_ROOT_CODE, user.getOrgId(), userType, "data_order asc");
			List<StepNode> stepNodes = wxTDataMapper.selStepNodes(TaskTypeService.TASK_TYPE_ROOT_CODE, STEP_ROOT_CODE, 
					user.getOrgId(), userType, "data_order asc");
			nodeList.addAll(stepNodes);
			List<TreeNode> nodes = JsonGenerator.listToTree(nodeList);
			List<TreeNode> treeNodes = new ArrayList<TreeNode>();
			for(TreeNode node : nodes){
				if(!(node instanceof StepNode)){
					node = filterStepTreeNode(node);
					if(node != null){
						treeNodes.add(node);
					}
				}
			}
			map.put("nodes", treeNodes);
			map.put("success", true);
		} catch (Exception e) {
			e.printStackTrace();
			map.put("success", false);
		}
		return map;
	}
	
	private TreeNode filterStepTreeNode(TreeNode node){
		if(node.getChildren() != null){
			List<TreeNode> children = new ArrayList<TreeNode>();
			for(TreeNode child : node.getChildren()){
				if(child instanceof StepNode){
					children.add(child);
				}else{
					child = filterStepTreeNode(child);
					if(child != null){
						children.add(child);
					}
				}
			}
			if(!children.isEmpty()){
				node.setChildren(children);
				return node;
			}
		}
		return null;
	}

}
