insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_C&I_Suppervisor'), 'WorkflowInstance.todoPage', 32, 1, '公共流程申请权限。32-德乐店招已办', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_C&I_Channel_Manager'), 'WorkflowInstance.todoPage', 64, 1, '公共流程申请权限。64-商用油油样已办', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_CDM_Marketing'), 'WorkflowInstance.todoPage', 16, 1, '公共流程申请权限。16-乘用车店招已办', 1, getdate());
insert into wx_t_operation_permission (role_id,module_code,permission_weight,enable_flag,remark,create_user_id,create_time) values ((select role_id from wx_t_role where ch_role_name='Chevron_CDM_Marketing'), 'WorkflowInstance.todoPage', 128, 1, '公共流程申请权限。128-商用油市场资源已办', 1, getdate());
