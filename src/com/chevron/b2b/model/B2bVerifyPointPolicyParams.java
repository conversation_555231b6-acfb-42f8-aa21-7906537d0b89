package com.chevron.b2b.model;

import com.chevron.pms.model.BaseParams;
import com.common.util.DateUtil;
import com.common.util.StringUtils;

import java.util.Date;
import java.util.Map;

/**
 * B2B核销积分政策列表查询参数
 * <AUTHOR>
 * @version 1.0 2018-11-27 14:56
 */
public class B2bVerifyPointPolicyParams extends BaseParams {

	/**  产品编码  **/
    private String sku;

	/**  奖励积分  **/
    private Double startAwardPoint;

	/**  奖励积分  **/
    private Double endAwardPoint;
    
	/** 起始日期条件字符串 */
	private String productCreationDateFromStr;
	
	/** 截止日期条件字符串 */
	private String productCreationDateToStr;
	
	private String salesChannel;

	/**  启用  **/
    private Integer enableFlag;
    
    private Double capacity;

	/**
	 * 获取产品编码条件
	 * @return 产品编码条件
	 */
    public String getSku() {
        return sku;
    }
    
	/**
	 * 设置产品编码条件
	 * @param sku 产品编码条件
	 */
    public void setSku(String sku) {
        this.sku = sku;
    }

	/**
	 * 获取启用条件
	 * @return 启用条件
	 */
    public Integer getEnableFlag() {
        return enableFlag;
    }
    
	/**
	 * 设置启用条件
	 * @param enableFlag 启用条件
	 */
    public void setEnableFlag(Integer enableFlag) {
        this.enableFlag = enableFlag;
    }

	@Override
	protected Map<String, String> getFieldMap() {
		return null;
	}
	/**
	 * 获取起始日期条件
	 * @return 起始日期条件
	 */
	public Date getProductCreationDateFrom() {
		if(StringUtils.isEmpty(productCreationDateFromStr)){
			return null;
		}
		return DateUtil.parseDate(productCreationDateFromStr);
	}

	/**
	 * 获取截止日期条件
	 * @return 截止日期条件
	 */
	public Date getProductCreationDateTo() {
		if(StringUtils.isEmpty(productCreationDateToStr)){
			return null;
		}
		return DateUtil.addDays(DateUtil.parseDate(productCreationDateToStr), 1);
	}

	public Double getStartAwardPoint() {
		return startAwardPoint;
	}

	public void setStartAwardPoint(Double startAwardPoint) {
		this.startAwardPoint = startAwardPoint;
	}

	public Double getEndAwardPoint() {
		return endAwardPoint;
	}

	public void setEndAwardPoint(Double endAwardPoint) {
		this.endAwardPoint = endAwardPoint;
	}

	public String getSalesChannel() {
		return salesChannel;
	}

	public void setSalesChannel(String salesChannel) {
		this.salesChannel = salesChannel;
	}

	public String getProductCreationDateFromStr() {
		return productCreationDateFromStr;
	}

	public void setProductCreationDateFromStr(String productCreationDateFromStr) {
		this.productCreationDateFromStr = productCreationDateFromStr;
	}

	public String getProductCreationDateToStr() {
		return productCreationDateToStr;
	}

	public void setProductCreationDateToStr(String productCreationDateToStr) {
		this.productCreationDateToStr = productCreationDateToStr;
	}

	public Double getCapacity() {
		return capacity;
	}

	public void setCapacity(Double capacity) {
		this.capacity = capacity;
	}
}
