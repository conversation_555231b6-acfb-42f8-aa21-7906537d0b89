;(function($){
	//表头转矩阵
	var toHeaderMatrix = function(header){
		var headerMatrix = [];
		for(var i = 0; i < header.length; i++){
			headerMatrix[i] = [];
		}
		for(var i = 0; i < header.length; i++){
			var row = header[i];
			for(var j = 0; j < row.length; j++){
				var cell = row[j];
				headerMatrix[i][j] = cell;
				if(cell.colSpan > 1){
					for(var k = 1; k < cell.colSpan; k++){
						headerMatrix[i][j + k] = {merged: true, mergeCell: cell};
						for(var l = 1; l < cell.rowSpan; l++){
							headerMatrix[i + l][j + k] = {merged: true, mergeCell: cell};
						}
					}
				}
				for(var k = 1; k < cell.rowSpan; k++){
					headerMatrix[i + k][j] = {merged: true, mergeCell: cell};
				}
			}
		}
		return headerMatrix;
	};
	//初始化表头
	var initColumn = function(gridHeader, rowIndex, column, leafColumns, parent, grid){
		column = $.extend({parent: parent, headerRowIndex: rowIndex}, column, baseColHeader, grid.columnDefaults);
		if(column.children && column.children.length > 0){
			//非叶子列表头
			var len = column.children.length;
			for(var i = 0; i < len; i++){
				//更新表头及上级表头的列合并数
				column._increaseColumn();
				if((rowIndex == 0 || column._lastClild) &&　i == len - 1){
					column.children[i]._lastChild = true;
				}
				//迭代子列表头
				initColumn(gridHeader, rowIndex + 1, column.children[i], leafColumns, column, grid);
			}
		}else{
			//处理锁定列
			if(column.lock && (!column.parent || column._lastChild)){
				grid._lockCol = leafColumns.length;
			}
			//叶子列表头
			leafColumns.push(column);
			column.leaf = true;
			//处理叶子列表头
			if(column.rowMergeable){
				column.valueProperty = column.valueProperty || column.property;
			}
			
		}
		if(!gridHeader[rowIndex]){
			gridHeader[rowIndex] = [];
		}
		gridHeader[rowIndex].push(column);
	};
	//初始化表格表头排序状态
	var initSort = function(grid){
		if(grid.sortFields){
			var len = grid.sortFields.length;
			for(var i = 0; i < len; i++){
				var clen = grid._leafColumns.length, sortField = grid.sortFields[i], exists = false;
				for(var j = 0; j < clen; j++){
					if(grid._leafColumns[j].property == sortField){
						grid._leafColumns[j]._sortNo = i + 1;
						grid._leafColumns[j]._sortType = grid.sortTypes[i];
						exists = true;
						break;
					}
				}
				if(!exists){
					//排序属性不在表头中的后面排序属性不初始化表头排序状态
					break;
				}
			}
		}
	};
	//渲染表头单元格
	var renderHeaderCell = function(header, output){
		output.push('<td class="grid-header-td" valign="middle"');
		if(header.colSpan > 1){
			output.push(' colSpan="', header.colSpan, '"');
		}
		if(header.rowSpan > 1){
			output.push(' rowSpan="', header.rowSpan, '"');
		}
		output.push('><div');
		if(header._sortNo){
			output.push(' class="sort-no', header._sortNo, header._sortType == UI.Grid.SORT_TYPE_ASC ? ' sort-asc' : ' sort-desc', '"');
		}
		output.push('><span class="text-nowrap">', header.title, '</span>');
		if(header.leaf && header.sortable){
			output.push('<span class="sort-flag"><i class="sort-icon"></i></span>');
		}
		output.push('</div></td>');
	}
	//渲染数据表格
	var renderDataCell = function(cell, output, header){
		output.push('<td class="grid-body-td');
		if(header.tdCls){
			output.push(' ', header.tdCls);
		}
		output.push('" valign="middle"');
		if(cell.colSpan){
			output.push(' colSpan="', cell.colSpan, '"');
		}
		if(cell.rowSpan){
			output.push(' rowSpan="', cell.rowSpan, '"');
		}
		output.push('><div class="', UI.Grid.alignCls[header.align], '"><span class="text-nowrap">', cell.html, '</span></div></td>');
		delete cell.html;
	};
	//构建数据行矩阵
	var buildRowsMatrix = function(grid, rows){
		var cellMatrix = [], len = rows.length;
		for(var i = 0; i < len; i++){
			var item = rows[i], clen = grid._leafColumns.length;
			cellMatrix[i] = [];
			for(var j = 0; j < clen; j++){
				var col = grid._leafColumns[j], cell = cellMatrix[i][j] = {};
				if(col.rowMergeable && i > 0){
					//行可合并
					prerowCell = cellMatrix[i - 1][j];
					if(j > 0 && col.colMerged && prerowCell.merged && prerowCell.mergeCell.colIndex  < j && 
							prerowCell.mergeCell.rowIndex + prerowCell.mergeCell > i){
						//单元格处于合并区内
						cell.merged = true;
						cell.mergeCell = prerowCell.mergeCell;
						continue;
					}else{
						var v = record[col.valueProperty];
						if(prerowCell.value == v){
							//合并同一列
							if(prerowCell.merged){
								if(prerowCell.mergeCell.colIndex < j){
									throw '第' + (i + 1) + '行的第' + j + '列数据应和第' + i + '行的第' + j + '列单元格合并';
								}
								prerowCell.mergeCell.rowSpan++;
								cell.mergeCell = prerowCell.mergeCell;
							}else{
								prerowCell.rowSpan = 2;
								cell.mergeCell = prerowCell;
							}
							cell.merged = true;
							continue;
						}
					}
				}
				cell.html = col.format(recod[col.property], record, i);
				if(col.colMergeable && ((j <= grid._lockCol && j > 0) || (j > grid._lockCol && j > grid._lockCol + 1))){
					//列可合并
					if(cell.html == grid.colMergeableText){
						var precolCell = cellMatrix[i][j - 1];
						if(precolCell.merged){
							if(precolCell.mergeCell.rowIndex < i){
								throw '第' + (i - 1) + '行第' + j + '列单元格和第' + i + '行第' + j + '列单元格应该合并';
							}
							precolCell.mergeCell.rowSpan++;
							cell.mergeCell = precolCell.mergeCell;
						}else{
							precolCell.colSpan = 2;
							cell.mergeCell = precolCell;
						}
						continue;
					}
				}
				cell.rowIndex = i;
				cell.colIndex = j;
			}
		}
	};
	//构建合计数据矩阵
	var buildSumMatrix = function(grid, sumRows){
		var sumMatrix = [];
		for(var i = 0; i < sumRows.length; i++){
			var matrixRow = [], j = 1, row = sumRows[i], rlen = row.length,
				cell = {html: grid._sumTitleMap[row[grid.sumTypeProperty]], colSpan: 1, _header: titleHeader};
			sumMatrix.push(matrixRow);
			matrixRow.push(cell);
			//渲染标题
			for(; j < grid._sumCols[0].colIndex; j++){
				if(j == grid._lockCol){
					//标题列最多合并到锁定列
					cell.colSpan++;
					matrixRow.push({merged: true, mergeCell: cell});
					//切换处理非合并列
					cell._lock = true;
					break;
				}else{
					//合并标题列
					cell.colSpan++;
					matrixRow.push({merged: true, mergeCell: cell});
				}
			}
			//渲染合计列和空白列
			cell = null;
			var k = 0;
			for(; j < rlen; j++){
				for(k = 0; k < grid._sumCols.length; k++){
					if(j == grid._sumCols[k].colIndex){
						//处理合计列
						cell = {html: grid._sumCols[k].format(row[grid._sumCols[k].property], row, i), 
								_header: grid._leafColumns[grid._sumCols[k].colIndex]};
						matrixRow.push(cell);
						cell = null;
						break;
					}else{
						if(cell == null){
							//创建空白占位列
							cell = {html: "", colSpan: 1, _header: emptyHeader};
							matrixRow.push(cell);
						}else{
							//合并空白列
							cell.colSpan++;
							matrixRow.push({merged: true, mergeCell: cell});
						}
						if(j == grid._lockCol){
							//在锁定列处分隔空白列
							cell = null;
						}
					}
				}
			}
		}
	};
	//渲染数据
	var renderData = function(grid, rows, sumRows){
		//渲染行数据
		grid._rowsMatrix = buildRowsMatrix(grid, rows);
		grid._rowsContainer.append(grid._colLockHandler.renderRows(grid, grid._rowsMatrix).join(''));
		grid.rows = rows;
		//渲染汇总数据
		if(sumRows && sumRows.length > 0){
			grid._sumMatrix = buildSumMatrix(grid, sumRows);
			grid._sumContainer.append(grid._colLockHandler.renderSum(grid, grid._sumMatrix).join(''));
		}
	};

	//计算合计
	var calculateSum = function(sumCols, data){
		var sum = {};
		for(var i = 0; i < sumCols.length; i++){
			var sumCol = sumCols[i];
			for(var j = 0; j < data.length; j++){
				sum[sumCol.property] += data[j][sumCol.property];
			}
		}
		return sum;
	};
	//清空表格显示数据
	var emptyData = function(grid){
		grid._rowsContainer.empty();
		if(gr_sumContainerner){
			grid._sumContainer.empty();
		}
		grid._rowsMatrix = null;
		grid._sumMatrix = null;
	};

	var checkbox = {
			align: 'center',
			tdCls: 'col-checkbox',
			fixWidth: true,
			disabledProperty: '', //不可选中属性
			disabled: function(value, item, index){
				return this.disabledProperty && item[this.disabledProperty] === true;
			},
			checked: function(value, item, index, grid){
				return value === true;
			},
			format: function(value, item, index, grid){
				var disabled = this.disabled(value, item, index);
				if(disabled){
					return '<span class="input-checkbox input-checkbox-disabled"/>';
				}else{
					return '<span class="input-checkbox' + (this.selected(value, item, index, grid) ? ' input-checkbox-checked' : '') 
							+ '" onclick="UI.Grid.plugins.checkbox.click(this, ' + index + ');"/>';
				}
			}
		};
	//列表头基础数据
	var baseColHeader = {
			rowSpan: 1, 
			colSpan: 1, 
			_increaseColumn: function(){
				if(this.parent){
					this.parent._increaseColumn();
					this.colSpan++;
				}
			}
		};
	//列锁定处理器
	var ColLockHandler = {
			colLock: {
				//渲染x轴滚动区
				renderXScroll: function(grid, gridEl){
					var $xs = $('<div class="grid-xscroll"><div class="grid-empty-collock"></div><div class="grid-block-xscroll></div><div class="grid-empty-yscroll"></div></div>"')
							.appendTo(gridEl);
					grid._xscroll = $xs.find('.grid-block-xscroll');
				},
				//渲染表头
				renderHeader: function(grid, header){
					var h = ['<div class="grid-header"><div class="grid-block-tl"><table cellspacing="1" cellpadding="0" width="100%" border="0">'];
					//渲染表格左上角块
					for(var i = 0; i < header.length; i++){
						var headerRow = header[i];
						h.push('<tr>');
						for(var j = 0, k = 0; j <= grid._lockCol; j += headerRow[j].colSpan){
							renderHeaderCell(headerRow[j], h);
							headerRow[j]._selector = '.grid-block-tl tr:eq(' + i + ') td:eq(' + k++ +')';
						}
						h.push('</tr>');
					}
					h.push('</table></div><div class="grid-block-tr"><table cellspacing="1" cellpadding="0" width="100%" border="0">');
					//渲染表格右上角块
					for(var i = 0; i < header.length; i++){
						var headerRow = header[i];
						h.push('<tr>');
						for(var j = grid._lockCol + 1, k = 0; j < headerRow.length; j++){
							renderHeaderCell(headerRow[j], h);
							headerRow[j]._selector = '.grid-block-tr tr:eq(' + i + ') td:eq(' + k++ +')';
						}
						h.push('</tr>');
					}
					h.push('</table></div><div class="grid-empty-yscroll"></div></div>');
					return h;
				},
				//渲染数据行
				renderRows: function(grid, cellMatrix){
					//渲染锁定区数据单元格
					var rlen = cellMatrix.length, h = ['<div class="grid-block-ml"><table cellspacing="1" cellpadding="0" width="100%" border="0">'];
					for(var i = 0; i < rlen; i++){
						var row = cellMatrix[i];
						h.push('<tr>');
						for(var j = 0, k = 0; j <= grid._lockCol; j++){
							var cell = row[j];
							if(cell.merged){
								continue;
							}
							renderDataCell(cell, h, grid._leafColumns[j]);
							cell._selector = '.grid-block-tr tr:eq(' + i + ') td:eq(' + k++ +')';
						}
						h.push('</tr>');
					}
					h.push('</table></div><div class="grid-block-mr"><table cellspacing="1" cellpadding="0" width="100%" border="0">');
					//渲染未锁定去数据单元格
					for(var i = 0; i < rlen; i++){
						var row = cellMatrix[i], clen = row.length;
						h.push('<tr>');
						for(var j = grid._lockCol + 1, k = 0; j <= clen; j++){
							var cell = row[j];
							if(cell.merged){
								continue;
							}
							renderDataCell(cell, h, grid._leafColumns[j]);
							cell._selector = '.grid-block-tr tr:eq(' + i + ') td:eq(' + k++ +')';
						}
						h.push('</tr>');
					}
					h.push('</table></div><div class="grid-empty-yscroll"></div>');
					grid._rowsMatrix = cellMatrix;
					return h;
				},
				//渲染数据汇总行
				renderSum: function(grid, sumMatrix){
					var h = ['<div class="grid-block-bl"><table cellspacing="1" cellpadding="0" width="100%" border="0">'],
						titleHeader = $.extend({}, grid.columnDefaults),
						emptyHeader = $.extend({}, grid.columnDefaults);
					//渲染锁定列单元格
					for(var i = 0; i < sumMatrix.length; i++){
						h.push('<tr>');
						for(var j = 0, k = 0; j <= grid._lockCol; ){
							var cell = sumMatrix[i][j];
							renderDataCell(cell, h, cell._header);
							cell._selector = '.grid-block-bl tr:eq(' + i + ') td:eq(' + k++ +')';
							delete cell._header;
							j += cell.colSpan;
						}
						h.push('</tr>');
					}
					h.push('</table></div><div class="grid-block-br"><table cellspacing="1" cellpadding="0" width="100%" border="0">');
					for(var i = 0; i < sumMatrix.length; i++){
						var len = sumMatrix[i].length;
						h.push('<tr>');
						for(var j = grid._lockCol + 1, k = 0; j < len; ){
							var cell = sumMatrix[i][j];
							renderDataCell(cell, h, cell._header);
							cell._selector = '.grid-block-bl tr:eq(' + i + ') td:eq(' + k++ +')';
							delete cell._header;
							j += cell.colSpan;
						}
						h.push('</tr>');
					}
					h.push('</table></div><div class="grid-empty-yscroll"></div>');
					return h;
				},
				//设置表格宽度
				resize: function(grid, width){
					
				}
			},
			colUnlock: {
				
			}
		};
	//默认比较器
	var defaultComparator = function(o1, o2){
		return o1 == o2 ? 0 : (o1 > o2 ? 1 : -1);
	};
	//构建排序二叉树
	var buildBTree = function(grid, node, childNode){
		var gt = true;
		for(var i = 0; i < grid.sortFields; i++){
			var v = grid.getComparator(grid.sortFields[i])(chidNode, node);
			if(v != 0){
				gt = (v > 0 && grid.sortTypes[i] == UI.Grid.SORT_TYPE_ASC) || (v < 0 && grid.sortTypes[i] == UI.Grid.SORT_TYPE_DESC);
				break;
			}
		}
		if(gt){
			if(node._rightNode){
				node._rightNode = buildTree(grid, node._rightNode, childNode);
				if(!node._leftNode && node._rightNode._rightNode){
					var tempLeftNode = node._rightNode._leftNode;
					node._rightNode._leftNode = node;
					node._rightNode = tempLeftNode;
					return node._rightNode;
				}
			}else{
				node._rightNode = childNode;
			}
		}else{
			if(node._leftNode){
				node._leftNode = buildTree(grid, node._leftNode, childNode);
				if(!node._rightNode && node._leftNode._leftNode){
					var tempRightNode = node._leftNode._rightNode;
					node._leftNode._rightNode = node;
					node._leftNode = tempRightNode;
					return node._leftNode;
				}
			}else{
				node._leftNode = childNode;
			}
		}
		return node;
	};
	//用排序二叉树构建数据数组
	var buildList = function(list, node){
		if(node._leftNode){
			buildList(list, node._leftNode);
		}
		list.push(node);
		if(node._rightNode){
			buildList(list, node._rightNode);
		}
	};

	//计算总页数
	var calculateTotalPage = function(totalSize, pageSize){
		if(totalSize == 0){
			return 1;
		}
		return totalSize % pageSize == 0 ? totalSize / pageSize : (totalSize / pageSize + 1);
	};
	UI.Grid = {
		key: 'ui.grid',
		cssCls: 'ui-grid',
		SORT_TYPE_ASC: 'ASC',
		SORT_TYPE_DESC: 'DESC',
		/**
		 * 初始化控件
		 * @param dom [必填]操作dom对象
		 * @param opts [可选]控件初始设置
		 * @return {object} 控件实例对象
		 */
		init: function(dom, opts){
			var $d = $(dom).addClass(this.cssCls), p = UI.buildOpts($d, this.defaults, opts), header = [];
			p._leafColumns = [];
			p._lockCol = -1;
			//初始化列表头
			for(var i = 0; i < p.columns.length; i++){
				initColumn(header, 0, p.columns[i], p._leafColumns, null, p);
			}
			//更新列表头的行合并数
			for(var i = 0; i < p._leafColumns; i++){
				p._leafColumns[i].rowSpan = header.length - p._leafColumns[i].headerRowIndex;
			}
			//初始化表头排序状态
			initSort(p);
			//渲染表格框架
			p._colLockHandler = p._lockCol > -1 ? ColLockHandler.colLock : ColLockHandler.colUnlock;
			$d.append(p._colLockHandler.renderHeader(p, header).join('')).append('<div class="no-data-comment">' + p.noDataMsg + '</div>');
			p._rowsContainer = $('<div class="grid-rows"></div>').appendTo($d);
			//header转矩阵
			p._headerMatrix = toHeaderMatrix(header);
			//处理合计
			if(p.showSum || (p.paging && p.showPageSum)){
				var len = p._leafColumns.length, sumCols = [];
				for(var i = 0; i < len; i++){
					if(p._leafColumns[i].sum){
						p._leafColumns[i].colIndex = i;
						sumCols.push(p._leafColumns[i]);
					}
				}
				if(sumCols.length > 0){
					p._sumCols = sumCols;
					p._sumContainer = $('<div class="grid-sum"></div>').appendTo($d);
					p._sumTitleMap = {pageSum: p.pageSumTitle};
					if(p.showSum && !p.sumTitle){
						p._sumTitleMap.sum = (p.paging && p.showPageSum) ? '总合计：' : '合计：';
					}else if(p.showSum){
						p._sumTitleMap.sum = p.sumTitle;
					}
				}else{
					p.showSum = p.showPageSum = false;
				}
			}
			p._colLockHandler.renderXScroll(p, $d);
			
			$d.css({
				'whiteSpace': 'normal',
				'display': 'inline-block',
				'overflow': 'hidden'
			});
			$.extend(p, this.base);
			$d.data(this.key, p);
		},
		getInstance: function(element){
			return element.data(this.key);
		},
		baseCols: {
			rowNumb: {
				title: '序号',
				align: 'center',
				lock: true,
				tdCls: ['col-row-numb'],
				width: 50,
				fixWidth: true,
				format: function(value, item, index, grid){
					return index + 1;
				}
			},
			textInput: {
				align: 'center',
				tdCls: ['col-text-input'],
				format: function(value, item, index, grid){
					
				},
				getCtrl: function(){
					
				}
			},
			checkbox: checkbox,
			radio: {
				
			},
			selectionCheckbox: $.extend({}, checkbox, {
				title: '<span class="input-checkbox select-all-checkbox" onclick="UI.Grid.baseCheckboxCol.selectAll(this);"/>',
				align: 'center',
				lock: true,
				tdCls: 'col-checkbox',
				width: 50,
				fixWidth: true,
				disabledProperty: '', //不可选中属性
				primaryProperty: '', //记录主键属性
				disabled: function(value, item, index){
					return this.disabledProperty && item[this.disabledProperty] === true;
				},
				checked: function(value, item, index, grid){
					if(grid._selectionStatusCache && grid._selectionStatusCache[this.colCls] && 
							grid._selectionStatusCache[this.colCls][item[this.primaryProperty]] !== undefined){
						return grid._selectionStatusCache[this.colCls][item[this.primaryProperty]];
					}
					return this.selectedProperty && item[this.selectedProperty] === true;
				},
				format: function(value, item, index, grid){
					var disabled = this.disabled(value, item, index);
					if(disabled){
						return '<span class="input-checkbox input-checkbox-disabled"/>';
					}else{
						var checked = this.selected(value, item, index, grid);
						//更新表格全选状态
						if(grid._selectionStatus == 0 && checked){
							this._selectionStatus = 3;
						}else if(grid._selectionStatus == 0 && !checked){
							grid._selectionStatus = 1;
						}else if((grid._selectionStatus == 3 && !checked) || (grid._selectionStatus == 1 && checked)){
							grid._selectionStatus = 2;
						}
						return '<span class="input-checkbox' + (checked ? ' input-checkbox-checked' : '') 
								+ '" onclick="UI.Grid.baseCheckboxCol.selectRow(this, ' + index + ');"/>';
					}
				}
			})
		},
		baseRadioCol: {
			title: '选择',
			align: 'center',
		},
		plugins: {
			checkbox: {
				selectAll: function(dom){
					var $d = $(dom);
					if($d.hasClass('input-checkbox-disabled')){
						return;
					}
					var checked = $d.hasCls('input-checkbox-checked');
					if(checked){
						$d.removeClass('input-checkbox-checked');
					}else{
						$d.addClass('input-checkbox-checked');
					}
					UI.Grid.getInstance($(dom).parents('.' + UI.Grid.cssCls + ':first')).selectAll(!checked);
				},
				selectRow: function(dom, index){
					var $d = $(dom), checked = $d.hasCls('input-checkbox-checked');
					if(checked){
						$d.removeClass('input-checkbox-checked');
					}else{
						$d.addClass('input-checkbox-checked');
					}
					UI.Grid.getInstance($(dom).parents('.' + UI.Grid.cssCls + ':first')).selectRow(!checked, index);
				},
				_selectRow: function(grid, checked, index){
					//var $selAll = grid.element.find('.' + )
				},
				funExtention: {
					selectAll: function(checked){
						
					},
					selectRow: function(checked, index){
						
					}
				},
				afterGridInit: function(grid){
					grid.on('beforerenderdata', function(data){
						this._selectionStatus = 0;
					});
					grid.on('afterrenderdata', function(){
						if(this._selectionStatus == 0){
							//无可选中记录
							this.element.find('.select-all-checkbox').addClass('input-checkbox-disabled')
									.removeClass('input-checkbox-active').removeClass('input-checkbox-checked');
						}else if(this._selectionStatus == 1){
							//可选记录全部未选中
							this.element.find('.select-all-checkbox').removeClass('input-checkbox-disabled')
									.removeClass('input-checkbox-active').removeClass('input-checkbox-checked');
						}else if(this._selectionStatus == 2){
							//有可选记录选中
							this.element.find('.select-all-checkbox').removeClass('input-checkbox-disabled')
									.addClass('input-checkbox-active').removeClass('input-checkbox-checked');
						}else if(this._selectionStatus == 3){
							//所有可选记录选中
							this.element.find('.select-all-checkbox').removeClass('input-checkbox-disabled')
									.removeClass('input-checkbox-active').addClass('input-checkbox-checked');
						}
					});
					var col = this;
					grid.on('afterloaddata', function(){
						//设置表格选中状态缓存
						if(col.primaryProperty){
							this._selectionStatusCache = {};
						}
					});
				}
			},
			redio: {
				
			},
			rowSelection: {
				
			}
		},
		storeHandlers: {
			ajax: {
				//初始化数据仓库
				init: function(grid){
					//初始化数据仓库分页处理器
					if(grid.paging){
						grid._dataHandler = this.dataHandlers.paging;
					}else{
						grid._dataHandler = this.dataHandlers.unpaging;
					}
				},
				//设置搜索文本
				setSearchText: function(grid, searchText){
					this.load(grid, {searchText: searchText});
				},
				//新查询或刷新表格
				load: function(grid, params){
					params = grid._dataHandler.buildParams(grid, params);
					grid.trigger('beforeload');
					setTimeout(function(){
						$.post(grid.url, $.param(params, true), function(data){
							data = grid.dataPreprocessor(data);
							UI.Grid._renderRows(grid._dataHandler.renderData(data));
							grid.trigger('afterload');
						}, 'json');
					}, 0);
				},
				//分页处理器
				dataHandlers: {
					//分页
					paging: {
						//处理数据
						renderData: function(grid, data){
							grid.pagingHandler.update(grid, data[grid.totalCountField], data[grid.pageNumbProperty], data[grid.totalPageProperty]);
							emptyData(grid);
							var rows = data[grid.rowsProperty];
							if(rows && rows.length > 0){
								var sumData = [];
								if(grid.showPageSum){
									//处理分页合计
									var pageSum = calculateSum(grid._sumCols, rows);
									pageSum[grid.sumTypeProperty] = 'pageSum';
									sumData.push(pageSum);
								}
								if(grid.showSum){
									//处理总合计
									var totalSum = data[grid.sumRowProperty];
									if(totalSum){
										totalSum[grid.sumTypeProperty] = 'sum';
										sumData.push(totalSum);
									}
								}
								renderData(grid, rows, sumData);
							}
						},
						//构建参数
						buildParams: function(grid, newParams){
							//有新参数，表示查询刷新，重置当前页
							if(newParams){
								grid.pageNumb = 1;
								grid.params = $.extend(grid.params, newParams);
							}
							grid.params = $.extend(grid.params, {
								pageNumb: grid.pageNumb, 
								sortFields: grid.sortFields, 
								sortTypes: grid.sortTypes, 
								pageSize: grid.pageSize,
								opFlag: grid.storeHandler._opFlag
							});
							grid.storeHandler._opFlag = '';
							return grid.params;
						},
						//跳转页
						goPage: function(grid, pageNumb){
							grid.pageNumb = pageNumb;
							grid.storeHandler._opFlag = 'page';
							grid.storeHandler.load(grid);
						},
						//更新每页记录条数
						updatePageSize: function(grid, pageSize){
							grid.pageNumb = 1;
							grid.pageSize = pageSize;
							grid.storeHandler.load(grid);
						}
					},
					//不分页
					unpaging: {
						renderData: function(grid, data){
							return data;
						},
						buildParams: function(grid, newParams){
							grid.params = grid.params || {};
							$.extend(grid.params, newParams);
							return grid.params;
						}
					}
				}
			},
			cache: {
				//初始化数据仓库
				init: function(grid){
					if(grid.paging){
						grid._dataHandler = this.dataHandlers.paging;
						grid._dataHandler.init(grid);
					}else{
						grid._dataHandler = this.dataHandlers.unpaging;
					}
				},
				//设置表格结果集
				setResult: function(grid, data){
					grid.data = data;
					this.load(grid, {pageNumb: 1});
				},
				//设置搜索文本
				setSearchText: function(grid, searchText){
					grid._searchText = searchText;
					this.load(grid, {pageNumb: 1});
				},
				//表格结果集排序
				sort: function(grid, data, filter, searchText){
					if(data && data.length > 0){
						var newData = [], len = data.length, root = null;
						for(var i = 0; i < len; i++){
							var item = data[i];
							if(filter && filter.call(grid, item, searchText)){
								//初始化二叉树链接
								item._leftNode = item._rightNode = null;
								if(root == null){
									root = data[i];
								}else{
									root = buildBTree(root, data[i]);
								}
							}
						}
						buildList(newData, root);
						return newData;
					}
					return data;
				},
				//刷新表格
				load: function(grid, params){
					grid.trigger('beforeload');
					var thiz = this;
					setTimeout(function(){
						grid._data = thiz.sort(grid, grid.data, grid._searchText ? grid.filter : null, grid._searchText);
						grid.trigger('_datachange', grid._data);
						grid._dataHandler.renderData(grid, grid._data, params);
						grid.trigger('afterload');
					}, 0);
				},
				//分页处理器
				dataHandlers: {
					//分页
					paging: {
						//初始化处理器
						init: function(grid){
							var thiz = this;
							grid.on('_datachange', function(_data){
								//表格当前展示数据集更新
								//1. 更新分页辅助对象
								var totalSize = _data ? _data.length : 0;
								grid._pageUtil = thiz.createPageUtil(grid, totalSize);
								//2. 重置表格合计数据
								if(totalSize > 0 && grid.showSum){
									//处理总合计
									grid._pageUtil.totalSum = calculateSum(grid._sumCols, _data);
									grid._pageUtil.totalSum[grid.sumTypeProperty] = 'sum';
								}
							});
						},
						//创建分页辅助对象
						createPageUtil: function(grid, totalSize){
							return {
								pageNumb: 1,
								totalPage: calculateTotalPage(totalSize, grid.pageSize),
								totalSize: totalSize,
								setPageSize: function(pageSize){
										this.totalPage = calculateTotalPage(this.totalSize, grid.pageSize);
									}
							};
						},
						//获取分页数据
						getRowData: function(data, pageNumb, pageSize){
							var start = (pageNumb - 1) * pageSize, end = start + pageSize + 1;
							if(end > data.length){
								end = data.length;
							}
							return data.slice(start, end);
						},
						//渲染数据
						renderData: function(grid, data, params){
							//处理分页
							if(params){
								grid.params = $.extend(grid.params, params);
							}
							if(grid.params && grid.params.pageNumb && grid.params.pageNumb <= grid._pageUtil.totalPage){
								grid._pageUtil.pageNumb = grid.params.pageNumb;
							}
							grid.pagingHandler.update(grid, grid._pageUtil.totalSize, grid._pageUtil.pageNumb, grid._pageUtil.totalPage);
							//渲染数据
							emptyData(grid);
							if(!data || data.length == 0){
								return;
							}
							var rows = this.getRowData(data, grid._pageUtil.pageNumb, grid._pageUtil.pageSize);
							var sumData = [];
							if(grid.showPageSum){
								//处理分页合计
								var pageSum = calculateSum(grid._sumCols, rows);
								pageSum[grid.sumTypeProperty] = 'pageSum';
								sumData.push(pageSum);
							}
							if(grid._pageUtil.totalSum){
								//处理总合计
								sumData.push(grid._pageUtil.totalSum);
							}
							renderData(grid, rows, sumData);
						},
						//跳转页
						goPage: function(grid, pageNumb){
							this.renderData(grid, grid._data, {pageNumb: pageNumb});
						},
						//更新每页记录条数
						updatePageSize: function(grid, pageSize){
							grid._pageUtil.setPageSize(pageSize);
							this.renderData(grid, grid._data, {pageNumb: 1});
						}
					},
					//不分页
					unpaging: {
						renderData: function(grid, data){
							emptyData(grid);
							if(!data || data.length == 0){
								return;
							}
							var sumData = [];
							if(grid.showSum){
								//处理总合计
								var totalSum = calculateSum(grid._sumCols, data);
								totalSum[grid.sumTypeProperty] = 'sum';
								sumData.push(totalSum);
							}
							renderData(grid, rows, sumData);
						}
					}
				}
			}
		},
		pagingHandlers: {
			'default': {
				update: function(grid, totalCount, pageNumb, totalPage){
					if(!grid._paging){
						var h = ['<div class="grid-paging"><div class="paging-bar">'];
						UI.Button.create(h, '', 'var grid = UI.Grid.getInstance($(this).parents(\'.' 
								+ UI.Grid.cssCls + '\'));grid.pagingHandler.goPage(grid, 1);', {
							'class': 'paging-first-btn',
							disabled: pageNumb == 1
						});
						UI.Button.create(h, '', 'var grid = UI.Grid.getInstance($(this).parents(\'.' 
								+ UI.Grid.cssCls + '\'));grid.pagingHandler.goPage(grid, grid.pageNumb - 1);', {
							'class': 'paging-pre-btn',
							disabled: pageNumb == 1
						});
						h.push('<span class="page-bar-separator"/><span class="total-page">共<span class="total-page-value">', totalPage, 
						'</span>页</span><span class="input-page-no">第');
						UI.Int.create(h, {
							defValue: 1,
							value: pageNumb,
							disabled: totalPage == 1,
							'class': 'page-no-int',
							validator: {
								min: 1,
								max: totalPage
							}
						});
						h.push('页</span>');
						UI.Button.create(h, '', "var $g = $(this).parents('." + UI.Grid.cssCls + 
								"'), grid = UI.Grid.getInstance($g);grid.pagingHandler.goPage(grid, parseInt(grod._paging.find('.input-page-no>input').val()));", {
							'class': 'page-no-go-btn',
							disabled: totalPage == 1
						});
						h.push('<span class="page-bar-separator"/>');
						UI.Button.create(h, '', 'var grid = UI.Grid.getInstance($(this).parents(\'.' 
								+ UI.Grid.cssCls + '\'));grid.pagingHandler.goPage(grid, grid.pageNumb + 1);', {
							'class': 'paging-next-btn',
							disabled: pageNumb == totalPage
						});
						UI.Button.create(h, '', 'var grid = UI.Grid.getInstance($(this).parents(\'.' 
								+ UI.Grid.cssCls + '\'));grid.pagingHandler.goPage(grid, grid.totalPage);', {
							'class': 'paging-last-btn',
							disabled: pageNumb == totalPage
						});
						h.push('<span class="page-bar-separator"/><span class="total-record">共<span class="total-record-value">',
								totalCount, '</span>条记录</span>');
						grid._paging = $(h.join('')).insertAfter(grid._rowsContainer);
					}else{
						UI.Button.setDisabled(grid._paging.find('.paging-first-btn'), pageNumb == 1);
						UI.Button.setDisabled(grid._paging.find('.paging-pre-btn'), pageNumb == 1);
						UI.Button.setDisabled(grid._paging.find('.page-no-go-btn'), totalPage == 1);
						UI.Button.setDisabled(grid._paging.find('.paging-next-btn'), pageNumb == totalPage);
						UI.Button.setDisabled(grid._paging.find('.paging-last-btn'), pageNumb == totalPage);
						var $int = grid._paging.find('.page-no-int');
						UI.Int.setDisabled($int, totalPage == 1);
						UI.Int.setValue(pageNumb);
						grid._paging.find('.total-page-value').text(totalPage);
						grid._paging.find('.total-record-value').text(totalCount);
					}
					grid.totalPage = totalPage;
				},
				//跳转页
				goPage: function(grid, pageNumb){
					grid.pageNumb = pageNumb;
					grid.load();
				},
				//更新每页记录条数
				updatePageSize: function(grid, pageSize){
					grid.pageNumb = 1;
					grid.pageSize = pageSize;
					grid.load();
				}
			}
		},
		alignCls: {
			left: 'text-left',
			right: 'text-right',
			center: 'text-center'
		},
		columnDefaults: {
			title: '', //列表头
			property: '', //列显示对应属性
			valueProperty: '', //列值对应属性(rowMergeable为true时有效)，默认为列显示对应属性
			lock: false, //是否锁定列(一级列的最后一个为true列有效)
			format: function(value, item, index){return value ? value : (value === 0 ? '0' : '');}, //列显示文本格式化
			sortable: false, //列是否排序
			'export': true, //是否需要导出
			tdCls: '', //列td样式
			rowMergeable: false, //行是否可合并的。true-合并同一列相邻行valuePorperty相等的单元格。默认为false。
			colMergeable: false, //列是否可合并。true-同一行，合并显示文本为表格对象的colMergeableText值的单元格与同一行前一个单元格。默认为false
			align: 'left', //显示文本水平对齐方式
			nowrap: false, //强制不换行
			width: 20, //列宽(如果有列锁定且所有列的宽度总和大于总宽度时为列实际宽度，否则代表列宽占的比例)
			minWidth: 20, //最小列宽(固定宽度列时无效)
			fixWidth: false, //列固定宽度,
			hidden: false, //是否隐藏列
			sum: false //是否汇总列
		},
		defaults: {
			rowHeight: 25,
			columns: null, //列定义数组
			url: '',
			data: null,
			paging: false,
			pageSize: 10, //分页时每页显示记录条数
			recommendPageSizes: [10, 25, 50, 100], //推荐分页记录条数
			sortFields: null, //排序字段。数组类型，支持联合排序
			sortTypes: null, //排序类型。数组类型，支持联合排序。ASC-升序，DESC-降序。
			totalCountField: 'total', //远程返回集总记录条数对应属性。默认为'total'
			rowsProperty: 'rows', //远程返回集记录数组对应属性。默认为'rows'
			pageNumbProperty: 'pageNumb', //当前页码属性
			totalPageProperty: 'totalPage', //总页数属性
			params: null, //默认远程请求参数列表
			headerPadding: [3, 3, 3, 3],
			bodyPadding: [3, 3, 3, 3],
			storeHandler: null, //数据仓库处理器。ajax-远程ajax请求数据，cache-浏览器内存。默认url不为空时为ajax，否则为cache
			pagingHandler: 'default', //分页处理器
			sumRowProperty: 'sum', //Ajax分页请求时，汇总行属性名称
			showSum: false, //是否显示汇总信息(包含分页汇总和总汇总)。默认为false
			showPageSum: false, //数据分页时，是否显示当前页汇总信息
			sumTitle: null, //汇总行标题。有分页汇总时，默认为'总合计：'，否则为'合计：'
			pageSumTitle: '分页合计：', //分页合计标题。默认为'分页合计：'
			sumTypeProperty: 'sumType', //汇总行数据的类型属性名称。默认为'sumType'
			colMergeableText: '<merge/>', //列可合并特殊显示文本
			noDataMsg: '空结果集', //没有数据显示文本
			sortNoTexts: ['①', '②', '③', '④', '⑤', '⑥', '⑦', '⑧', '⑨'], //联合排序顺序编号文本
			sortNoClses: ['sort-no-1', 'sort-no-2', 'sort-no-3', 'sort-no-4', 'sort-no-5', 'sort-no-6', 'sort-no-7', 'sort-no-8', 'sort-no-9'],
									//联合排序顺序编号样式类
			loadMask: false, //加载状态。true/false-是否显示数据加载状态，object{提供show(grid)和hide(grid)接口的对象}-使用自定义数据加载状态控件
			getComparator: function(property){return defaultComparator;}, //获取指定属性的比较器
			//表格数据项过滤器。本地数据源搜索时有效
			filter: function(item, searchText){
				
			},
			//数据远程加载预处理器，处理数据是否加载成功。远程数据仓库有效
			dataPreprocessor: function(data){
				return data;
			},
			buildSortFields: function(sortFields){
				return sortFields;
			},
			buildSortTypes: function(sortTypes){
				return sortTypes;
			}
		},
		//events: 
		base: $.extend({}, UI.base, {
			data: null,
			/**
			 * 设置控件尺寸
			 * @param width [必填]控件新宽度
			 */
			resize: function(width){
				this.element.width(width);
				if(this.element.prop('scrollHeight') > this.element.prop('clientHeight')){
					this.isPart = true;
					if(this._flagInited){
						//已有未显示完整标记
						this._flag.show();
					}else{
						//初始化未显示完整标记
						this._flag = $(this.partFlag).appendTo(this.element.wrap('<span class="ui-label-wrapper"></span>'));
					}
					this.element.width(width - this._flag.width());
				}else if(this.isPart){
					this.isPart = false;
					this._flag.hide();
				}
			},
			selectRow: function(rowIndex){
				//TODO
				//if(this.trigger('beforeselectrow', row))
			},
			getItemAt: function(rowIndex){
				
			},
			load: function(params){
				this.storeHandler.load(this, params);
			},
			setData: function(data){
				data = data || [];
				this.storeHandler.setResult(this, data);
				this.data = data;
			},
			setSearchText: function(searchText){
				this.storeHandler.setSearchText(this, searchText);
			},
			/**
			 * 获取第一个匹配属性的列实例
			 * @param property 列属性
			 * return object 第一个匹配属性的列实例
			 */
			getColInstance: function(property){
				//TODO 遍历表格列
			},
			beforeEvents: {
				
			},
			afterEvents: {
				beforeselectrow: function(){}
			}
		})
	};
})(jQuery);