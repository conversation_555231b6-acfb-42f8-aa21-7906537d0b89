select max(o.organization_name) "经销商名称", a.region, p.sku, max(p.name) "产品名称", a.ext_property1 "积分类型", sum(a.point) point, sum(a.liters) liters
from (SELECT CASE 
		WHEN EXISTS (
			SELECT 1
			FROM wx_t_promotion_delivery_detail dd1
				LEFT JOIN wx_t_delivery_order_rel r1 ON dd1.id = r1.delivery_detail_id
				LEFT JOIN wx_t_sell_in_promotion_delivery pd1 ON pd1.delivery_id = r1.delivery_id
			WHERE dd.id = dd1.id
				AND pd1.delivery_id < pd.delivery_id
		) THEN 0
		ELSE dd.award_quantity
	END AS point, pd.*,dd.ext_property1
FROM wx_t_promotion_delivery_detail dd
	LEFT JOIN wx_t_delivery_order_rel r ON dd.id = r.delivery_detail_id
	LEFT JOIN wx_t_sell_in_promotion_delivery pd ON pd.delivery_id = r.delivery_id
WHERE dd.create_time>'2021-01-01' and dd.delivery_status=20 and pd.distributor_id is not null and dd.award_type=2
) a
	left join wx_t_partner_o2o_enterprise pe on pe.distributor_id=a.distributor_id
	left join wx_t_organization o on o.id=pe.partner_id
	left join wx_t_product p on p.sku=a.product_sku
group by o.id,a.region, p.sku, a.ext_property1
order by o.id, a.ext_property1