alter table wx_t_properties drop DF__wx_t_prope__code__4B6D135E

ALTER TABLE [wx_t_properties] ALTER COLUMN [code] [nvarchar](max) NULL
ALTER TABLE [wx_t_user] add ext_flag                         int                                        not null default(0);

create table wx_t_pwd_change_log (
	id                               bigint                            not null identity,
	create_user_id                   bigint                                         null,
	create_time                      datetime                                       null,
	pwd                              nvarchar(200)                                  null,
	user_id                          bigint                                         null,
	ext_property1                    nvarchar(256)                                  null,
	ext_property2                    nvarchar(256)                                  null,
	ext_property3                    nvarchar(256)                                  null,
	ext_flag                         int                                        not null,
	primary key (id)
);
CREATE INDEX ix_pwd_change_log_user_id ON wx_t_pwd_change_log (user_id);
--update wx_t_user set ext_flag=1  