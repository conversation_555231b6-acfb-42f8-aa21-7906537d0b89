package com.chevron.pmp.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.sys.auth.dao.WxTDataMapper;
import com.sys.auth.model.CheckTreeNode;
import com.sys.auth.model.TreeNode;
import com.sys.auth.model.WxSimpleUser;
import com.sys.auth.model.WxTData;
import com.sys.auth.model.WxTUser;
import com.chevron.task.business.TaskService;
import com.chevron.task.dao.WxRelationUserMapper;
import com.chevron.task.dao.WxTaskMbMapper;
import com.chevron.task.dao.WxTaskmbCheckMapper;
import com.chevron.task.dao.WxTasktypeMbMapper;
import com.chevron.task.model.TaskTypeMbChecked;
import com.chevron.task.model.WxRelationUser;
import com.chevron.task.model.WxRelationUserExample;
import com.chevron.task.model.WxTaskChecked;
import com.chevron.task.model.WxTaskMb;
import com.chevron.task.model.WxTaskmbCheck;
import com.chevron.task.model.WxTaskmbCheckExample;
import com.chevron.task.model.WxTasktypeMb;
import com.chevron.task.model.WxTasktypeMbExample;
import com.chevron.pmp.dao.StepCheckVoMapper;
import com.chevron.pmp.model.CheckSelectInfo;
import com.chevron.pmp.model.StepCheckInfo;
import com.chevron.pmp.model.StepCheckVo;
import com.chevron.pmp.service.StepCheckService;
import com.common.util.ContextUtil;
import com.common.util.json.JsonGenerator;

/**
 * 提供任务类型模板配置的服务类
 * <AUTHOR>
 *
 */
@Service
public class StepCheckServiceImpl implements StepCheckService {

	@Resource
	private WxTDataMapper wxTDataMapper;
	
	@Resource
	private WxTasktypeMbMapper wxTasktypeMbMapper;
	
	@Resource
	private StepCheckVoMapper stepCheckVoMapper;

	@Resource
	private WxTaskMbMapper wxTaskMbMapper;
	
	@Resource
	private WxRelationUserMapper wxRelationUserMapper;
	
	@Resource
	private TaskService taskService;
	
	@Resource
	private WxTaskmbCheckMapper wxTaskmbCheckMapper;

	/**
	 * 初始化维护步骤和检查项页面，加载所有步骤的主数据
	 */
	public Map<String,Object> findStepTypeMainDataList() throws Exception {
		Map<String,Object> map = new HashMap<String,Object>();
		String dataCode = "TS";
		try {
			List<WxTData> list = wxTDataMapper.selTaskTypeData(dataCode);
			List<TreeNode> treeList = new ArrayList<TreeNode>();
			List<TreeNode> resultJsonList = null;
			if (null != list && list.size() > 0) {
				CheckTreeNode tempObj = null;
				for (WxTData data : list) {
					tempObj = new CheckTreeNode();
					tempObj.setChecked(null);
					tempObj.setId(data.getDataId());
					tempObj.setText(data.getDataName());
					tempObj.setPid(data.getDataPid());
					tempObj.setCode(data.getDataCode());
					if (StringUtils.isNotEmpty(data.getDataDescript())) {
						tempObj.setRemarkType(1);
					}
					treeList.add(tempObj);
				}
			}
			resultJsonList = JsonGenerator.listToTree(treeList);
			map.put("code", "success");
			map.put("resultData", resultJsonList);
		} catch (Exception e) {
			e.printStackTrace();
			map.put("code", "systemerror");
		}
		return map;
	}

	/**
	 * 根据任务类型代码查找任务类型，若已有数据则为编辑修改，若不存在则为新增
	 * @param tmbTypeCode
	 * @return
	 * @throws Exception
	 */
	public Map<String,Object> editTaskTypeMb(String tmbTypeCode) throws Exception {
		Map<String,Object> map = new HashMap<String,Object>();
		try {
			WxTasktypeMb wxTasktypeMb = wxTasktypeMbMapper.selTaskTypeMbByTypeCode(tmbTypeCode);
			if (null != wxTasktypeMb) {
				map.put("ifExist", true);
			} else {
				map.put("ifExist", false);
			}
			map.put("resultData", wxTasktypeMb);
			map.put("code", "success");
		} catch (Exception e) {
			e.printStackTrace();
			map.put("code", "systemerror");
		}
		return map;
	}

	/**
	 * 根据步骤代码查找步骤和检查项的关联关系，若已有数据则为编辑修改，若不存在则为新增
	 * @param tmbTypeCode
	 * @return
	 * @throws Exception
	 */
	public Map<String,Object> editStepCheck(String stepId) throws Exception {
		Map<String,Object> map = new HashMap<String,Object>();
		try {
			StepCheckVo stepCheckVo = stepCheckVoMapper.selStepCheckByStepId(Long.valueOf(stepId));
			if (null != stepCheckVo) {
				map.put("ifExist", true);
			} else {
				map.put("ifExist", false);
			}
			map.put("resultData", stepCheckVo);
			map.put("code", "success");
		} catch (Exception e) {
			e.printStackTrace();
			map.put("code", "systemerror");
		}
		return map;
	}
	

	/**
	 * 根据父级别对象，返回二到四级任务检查项树结构
	 * 传入上级任务类型或模板对应的ID、类型
	 * 若检查项已选中，则设为checked
	 * 若tadkId = null，则为新增任务模板/实例，否则为编辑修改
	 * pTaskId和pMbCheckType必须非空
	 * 此接口仅针对检查项不下放时的任务模板维护以及任务实例维护
	 */
	public Map<String,Object> findTaskCheckTreeByParent(Long taskId, Integer mbCheckType, Long pTaskId, Integer pMbCheckType, List<String> checkedIdList) throws Exception {
		Map<String,Object> map = new HashMap<String,Object>();
		try {
			List<WxTaskChecked> list = taskService.selectAllCheckList(pTaskId, pMbCheckType, taskId, mbCheckType);
			System.out.println("list.size(): " + list.size());
			List<TreeNode> treeList = new ArrayList<TreeNode>();
			List<TreeNode> resultJsonList = null;
			if (null != list && list.size() > 0) {
				CheckTreeNode tempObj = null;
				for (WxTaskChecked taskChecked : list) {
					tempObj = new CheckTreeNode();
					tempObj.setId(taskChecked.getCheckId());
					tempObj.setCode(taskChecked.getCheckCode());
					tempObj.setText(taskChecked.getCheckName());
					tempObj.setPid(taskChecked.getCheckPid());
					if (null != taskId) {
						//修改时根据已有wx_taskmb_check记录判断是否选中
						//仅当level为4时才设置为true，树组件会自动向上级联选中父节点；如果非level4的父节点设为true，反而会向下级联全部选中
						tempObj.setChecked(false);
						if (null != taskChecked.getChecked() && taskChecked.getChecked() == 1
								&& null != taskChecked.getLevel() && taskChecked.getLevel() == 4) {
							tempObj.setChecked(true);
						}
					} else {
						//新增时加载后默认全部选中
						tempObj.setChecked(true);
						if(checkedIdList != null && checkedIdList.size() > 0){
							tempObj.setChecked(false);
							for(int i=0;i<checkedIdList.size();i++){
								if(tempObj.getCode().equals(checkedIdList.get(i))){
									tempObj.setChecked(true);
								}
							}
						}
					}
					if (null != taskChecked.getLevel() && taskChecked.getLevel() == 4) {
						tempObj.setPhotoType(false);
						tempObj.setCommentType(false);
						tempObj.setRemarkType(0);
					}
					if (null != taskChecked.getPhotoType() && taskChecked.getPhotoType() == 1) {
						tempObj.setPhotoType(true);
					}
					if (null != taskChecked.getCommentType() && taskChecked.getCommentType() == 1) {
						tempObj.setCommentType(true);
					}
					if (null != taskChecked.getRemarkType()) {
						tempObj.setRemarkType(taskChecked.getRemarkType());
					}
					treeList.add(tempObj);
				}
			}
			resultJsonList = JsonGenerator.listToTree(treeList);
			map.put("code", "success");
			map.put("resultData", resultJsonList);
		} catch (Exception e) {
			e.printStackTrace();
			map.put("code", "systemerror");
		}
		return map;
	}

	/**
	 * 保存任务类型配置
	 * 若tmbId = null，则为新增任务类型，否则为编辑修改
	 */
	@Transactional
	public Map<String,Object> saveWxTaskTypeMb(String tmbName, String tmbTypeCode,
			Integer tmbClassify, Integer execUserType,
			Integer innerOrgType, Integer checkStatus,
			Integer checkItemStatus, Long checkId,
			Long tmbId, WxTaskmbCheck[] taskmbCheckArray) throws Exception {
		Map<String,Object> map = new HashMap<String,Object>();
		try {
			boolean ifCreateNew = false;
			WxTUser cru = ContextUtil.getCurUser();
			//保存wx_tasktype_mb表
			WxTasktypeMb tasktypeMb = null;
			if (null != tmbId) {
				tasktypeMb = wxTasktypeMbMapper.selectByPrimaryKey(tmbId);
			} else {
				ifCreateNew = true;
				tasktypeMb = wxTasktypeMbMapper.selTaskTypeMbByTypeCode(tmbTypeCode);
				if (null != tasktypeMb) {
					//任务类型记录已存在
					map.put("code", "systemerror");
					map.put("errorMessage", "任务类型已存在");
					return map;
				} else {
					tasktypeMb = new WxTasktypeMb();
				}
			}
			tasktypeMb.setTmbName(tmbName);
			tasktypeMb.setTmbTypeCode(tmbTypeCode);
			tasktypeMb.setTmbClassify(tmbClassify);
			if (tmbClassify == 1) {
				tasktypeMb.setExecUserType(execUserType);
			} else {
				tasktypeMb.setExecUserType(null);
			}
			tasktypeMb.setInnerOrgType(innerOrgType);
			tasktypeMb.setCheckStatus(checkStatus);
			tasktypeMb.setCheckItemStatus(checkItemStatus);
			tasktypeMb.setCheckId(checkId);
			tasktypeMb.setCreateUser(cru.getUserId());
			tasktypeMb.setXgUser(cru.getUserId());
			tasktypeMb.setXgSj(new Date());
			tasktypeMb.setStatus(1);
			tasktypeMb.setTenantId(cru.getOrgId());//modify by bo.liu 0818 cru.getTenantId()
			if (ifCreateNew) {
				wxTasktypeMbMapper.insertSelective(tasktypeMb);
				tmbId = tasktypeMb.getTmbId();
			} else {
				wxTasktypeMbMapper.updateByPrimaryKey(tasktypeMb);
				//仅当修改已有记录时，批量删除wx_taskmb_check表
				deleteBatchWxTaskmbCheck(1, tmbId);
			}
			//checkItemStatus == 1，不下放检查项，批量插入wx_taskmb_check表
			if (checkItemStatus == 1 && taskmbCheckArray != null && taskmbCheckArray.length > 0) {
				saveBatchWxTaskmbCheck(cru, 1, tmbId, taskmbCheckArray);
			}
			map.put("tmbId", tmbId);
			map.put("code", "success");
		} catch (Exception e) {
			e.printStackTrace();
			map.put("code", "systemerror");
		}
		return map;
	}

	/**
	 * 批量删除检查项记录 wx_taskmb_check
	 * mb_check_type = mbCheckType and task_id = taskId(tmbId)
	 */
	public void deleteBatchWxTaskmbCheck(Integer mbCheckType, Long taskId) {
		WxTaskmbCheckExample example = new WxTaskmbCheckExample();
		example.createCriteria().andMbCheckTypeEqualTo(mbCheckType).andTaskIdEqualTo(taskId);
		wxTaskmbCheckMapper.deleteByExample(example);
	}

	/**
	 * 批量删除任务执行人或观察者记录 wx_relation_user
	 * source_type = sourceType and task_id = taskId(mbId)
	 */
	public void deleteBatchWxRelationUser(Integer sourceType, Long taskId) {
		WxRelationUserExample example = new WxRelationUserExample();
		example.createCriteria().andSourceTypeEqualTo(sourceType).andTaskIdEqualTo(taskId);
		wxRelationUserMapper.deleteByExample(example);
	}

	/**
	 * 批量保存检查项记录 wx_taskmb_check
	 */
	public void saveBatchWxTaskmbCheck(WxTUser cru, Integer mbCheckType, Long taskId, WxTaskmbCheck[] taskmbCheckArray) {
		for (WxTaskmbCheck wxTaskmbCheck : taskmbCheckArray) {
			wxTaskmbCheck.setMbCheckType(mbCheckType);
			wxTaskmbCheck.setTaskId(taskId);
			wxTaskmbCheck.setCreateUser(cru.getUserId());
			wxTaskmbCheck.setXgUser(cru.getUserId());
			wxTaskmbCheck.setTenantId(cru.getOrgId());//modify by bo.liu 0818 cru.getTenantId()
		}
		wxTaskmbCheckMapper.insertByBatch(Arrays.asList(taskmbCheckArray));
		// TODO 测试事务回滚 不起作用
//		WxTaskmbCheck wxTaskmbCheck = new WxTaskmbCheck();
//		wxTaskmbCheck.setComment("123456789012345678901234567890123456789012345678901234567890");
//		wxTaskmbCheckMapper.insertSelective(wxTaskmbCheck);
	}

	/**
	 * 批量保存任务执行人或观察者记录 wx_relation_user
	 */
	public void saveBatchWxRelationUser(WxTUser cru, Integer relationUserType, Integer sourceType, Long mbId, Long[] relationUserIdList) {
		List<WxRelationUser> wxRelationUserList = new ArrayList<WxRelationUser> ();
		for (Long userId : relationUserIdList) {
			if (null != userId && userId > 0) {
				WxRelationUser rUser = new WxRelationUser();
				rUser.setUserId(userId);
				rUser.setRelationUserType(relationUserType);
				rUser.setSourceType(sourceType);
				rUser.setTaskId(mbId);
				rUser.setXgUser(cru.getUserId());
				rUser.setTenantId(cru.getOrgId());//modify by bo.liu 0818 cru.getTenantId()
				wxRelationUserList.add(rUser);
			}
		}
		if (null != wxRelationUserList && wxRelationUserList.size() > 0) {
			wxRelationUserMapper.insertByBatch(wxRelationUserList);
		}
	}

	/**
	 * 初始化任务模板编辑页面
	 * 加载已存在的任务类型列表
	 */
	public Map<String,Object> findTaskTypeMbList() throws Exception {
		Map<String,Object> map = new HashMap<String,Object>();
		try {
			WxTasktypeMbExample example = new WxTasktypeMbExample();
			example.createCriteria().andStatusEqualTo(new Integer(1)).andTenantIdEqualTo(new Long(1));
			List<WxTasktypeMb> list = wxTasktypeMbMapper.selectByExample(example);
			List<TaskTypeMbChecked> tasktypeList = new ArrayList<TaskTypeMbChecked>();
			if (null != list && list.size() > 0) {
				for (WxTasktypeMb taskTypeMb : list) {
					TaskTypeMbChecked tasktype = new TaskTypeMbChecked();
					tasktype.setValue(taskTypeMb.getTmbTypeCode());
					tasktype.setText(taskTypeMb.getTmbName());
					tasktypeList.add(tasktype);
				}
			}
			map.put("code", "success");
			map.put("resultData", tasktypeList);
		} catch (Exception e) {
			e.printStackTrace();
			map.put("code", "systemerror");
		}
		return map;
	}


	/**
	 * 根据任务模板id查找任务模板
	 * 若新建模板，传入tmbTypeCode；若修改已有模板，传入tmbTypeCode以及mbId
	 * @param tmbTypeCode
	 * @param mbId
	 * @return
	 * @throws Exception
	 */
	public Map<String,Object> findWxTaskMbById(String tmbTypeCode, Long mbId) throws Exception {
		Map<String,Object> map = new HashMap<String,Object>();
		try {
			WxTasktypeMb wxTasktypeMb = wxTasktypeMbMapper.selTaskTypeMbByTypeCode(tmbTypeCode);
			WxTaskMb wxTaskMb = null;
			if (null != mbId) {
				//修改已有模板
				wxTaskMb = wxTaskMbMapper.selectByPrimaryKey(mbId);
				List<WxSimpleUser> userExecList = taskService.selectMbExecUser(mbId, 1, 2);
				List<WxSimpleUser> userWatchList = taskService.selectMbExecUser(mbId, 2, 2);
				map.put("userExecList", userExecList);
				map.put("userWatchList", userWatchList);
			}
			map.put("wxTasktypeMb", wxTasktypeMb);
			map.put("wxTaskMb", wxTaskMb);
			map.put("code", "success");
		} catch (Exception e) {
			e.printStackTrace();
			map.put("code", "systemerror");
		}
		return map;
	}

	/**
	 * 保存任务模板信息
	 * 若mbId = null，则为新增任务模板，否则为编辑修改
	 */
	@Transactional
	public Map<String,Object> saveWxTaskMb(String mbName, String tmbTypeCode, String taskDescription,
			Long tmbId, Integer mbRange, Integer checkItemStatus,
			Long mbId, Long[] execUserIdList, Long[] watchUserIdList,
			WxTaskmbCheck[] taskmbCheckArray) throws Exception {
		Map<String,Object> map = new HashMap<String,Object>();
		try {
			boolean ifCreateNew = false;
			WxTUser cru = ContextUtil.getCurUser();
			//保存wx_task_mb表
			WxTaskMb taskMb = null;
			if (null != mbId) {
				taskMb = wxTaskMbMapper.selectByPrimaryKey(mbId);
			} else {
				ifCreateNew = true;
				taskMb = new WxTaskMb();
			}
			taskMb.setMbName(mbName);
			taskMb.setTaskDescription(taskDescription);
			taskMb.setTmbTypeCode(tmbTypeCode);
			taskMb.setTmbId(tmbId);
			taskMb.setMbRange(mbRange);
			taskMb.setCreateUser(cru.getUserId());
			taskMb.setCreateOrg(cru.getOrgId());
			taskMb.setXgUser(cru.getUserId());
			taskMb.setXgSj(new Date());
			taskMb.setStatus(1);
			taskMb.setTenantId(cru.getOrgId());//modify by bo.liu 0818 cru.getTenantId()
			if (ifCreateNew) {
				wxTaskMbMapper.insertSelective(taskMb);
				mbId = taskMb.getMbId();
			} else {
				wxTaskMbMapper.updateByPrimaryKeySelective(taskMb);
				//仅当修改已有记录时，批量删除wx_taskmb_check表
				deleteBatchWxTaskmbCheck(2, mbId);
				//批量删除执行人及观察者
				deleteBatchWxRelationUser(2, mbId);
			}
			//checkItemStatus == 1，不下放检查项，批量插入wx_taskmb_check表
			if (checkItemStatus == 1 && taskmbCheckArray != null && taskmbCheckArray.length > 0) {
				saveBatchWxTaskmbCheck(cru, 2, mbId, taskmbCheckArray);
			}
			//批量插入执行人
			if (null != execUserIdList && execUserIdList.length > 0) {
				saveBatchWxRelationUser(cru, 1, 2, mbId, execUserIdList);
			}
			//批量插入观察者
			if (null != watchUserIdList && watchUserIdList.length > 0) {
				saveBatchWxRelationUser(cru, 2, 2, mbId, watchUserIdList);
			}
			map.put("mbId", mbId);
			map.put("code", "success");
		} catch (Exception e) {
			e.printStackTrace();
			map.put("code", "systemerror");
		}
		return map;
	}

	

	@Override
	public Map<String, Object> findCheckSelectInfos(Long stepId) {
		Map<String, Object> map = new HashMap<String, Object>(2);
		try {
			map.put("data", stepCheckVoMapper.selStepCheckSelectInfo(stepId));
			map.put("success", true);
		} catch (Exception e) {
			e.printStackTrace();
			map.put("success", false);
		}
		return map;
	}

	@Override
	public Map<String, Object> saveStepInfo(StepCheckInfo[] stepCheckInfos) {
		Map<String, Object> map = new HashMap<String, Object>(1);
		try {
			for(int i = 0; i < stepCheckInfos.length; i++){
				Long stepId = stepCheckInfos[i].getStepId();
				CheckSelectInfo[] selectedChecks = stepCheckInfos[i].getSelectedChecks();
				//删除原步骤关联检查项信息
				stepCheckVoMapper.deleteByStep(stepId);
				//组装检查项选中信息
				WxTUser cru = ContextUtil.getCurUser();
				StringBuilder sb = null;
				for(CheckSelectInfo item : selectedChecks){
					item.setCreateUser(cru.getUserId());
					item.setStepId(stepId);
					String name = item.getCheckName();
					if(name.indexOf("(") > 0){
						name = name.substring(0, name.indexOf("("));
					}
					if(name.indexOf("（") > 0){
						name = name.substring(0, name.indexOf("（"));
					}
					if(sb == null){
						sb = new StringBuilder(250).append(name);
					}else{
						sb.append(",").append(name);
					}
				}
				stepCheckVoMapper.insertBatch(selectedChecks);
				//修改步骤描述
				WxTData data = new WxTData();
				data.setDataId(stepId);
				if(sb.length() > 250){
					data.setDataDescript(sb.substring(0, 247).toString() + "...");
				}else{
					data.setDataDescript(sb.toString());
				}
				wxTDataMapper.updateByPrimaryKeySelective(data);
			}
			map.put("success", true);
		} catch (Exception e) {
			e.printStackTrace();
			map.put("success", false);
		}
		return map;
	}
}
