package com.chevron.point.dto;

import java.sql.Date;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 微信企业号积分账户查询详情实体
 * <AUTHOR>
 *
 */
public class SCRMPointDetail {

	/**
	 * 明细时间： yyyy-MM-dd HH:mm:ss
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date date;
	/**
	 * 明细类别
	 */
	private String changeType;
	/**
	 * 积分值
	 */
	private String changeValue;
	/**
	 * 备注
	 */
	private String remark;

	/**
	 * 关联业务编码
	 */
	private String referenceno;

	public Date getDate() {
		return date;
	}
	public void setDate(Date date) {
		this.date = date;
	}
	public String getChangeType() {
		return changeType;
	}
	public void setChangeType(String changeType) {
		this.changeType = changeType;
	}
	public String getChangeValue() {
		return changeValue;
	}
	public void setChangeValue(String changeValue) {
		this.changeValue = changeValue;
	}
	public String getRemark() {
		return remark;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
	public String getReferenceno() {
		return referenceno;
	}
	public void setReferenceno(String referenceno) {
		this.referenceno = referenceno;
	}
}
