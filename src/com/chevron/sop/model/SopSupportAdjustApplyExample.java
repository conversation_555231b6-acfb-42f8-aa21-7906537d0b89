package com.chevron.sop.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Sop Support Adjust Apply单表查询条件
 * <AUTHOR>
 * @version 1.0 2020-07-02 10:54
 */
public class SopSupportAdjustApplyExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public SopSupportAdjustApplyExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andReqNoIsNull() {
            addCriterion("req_no is null");
            return (Criteria) this;
        }

        public Criteria andReqNoIsNotNull() {
            addCriterion("req_no is not null");
            return (Criteria) this;
        }

        public Criteria andReqNoEqualTo(String value) {
            addCriterion("req_no =", value, "reqNo");
            return (Criteria) this;
        }

        public Criteria andReqNoNotEqualTo(String value) {
            addCriterion("req_no <>", value, "reqNo");
            return (Criteria) this;
        }

        public Criteria andReqNoGreaterThan(String value) {
            addCriterion("req_no >", value, "reqNo");
            return (Criteria) this;
        }

        public Criteria andReqNoGreaterThanOrEqualTo(String value) {
            addCriterion("req_no >=", value, "reqNo");
            return (Criteria) this;
        }

        public Criteria andReqNoLessThan(String value) {
            addCriterion("req_no <", value, "reqNo");
            return (Criteria) this;
        }

        public Criteria andReqNoLessThanOrEqualTo(String value) {
            addCriterion("req_no <=", value, "reqNo");
            return (Criteria) this;
        }

        public Criteria andReqNoIn(List<String> values) {
            addCriterion("req_no in", values, "reqNo");
            return (Criteria) this;
        }

        public Criteria andReqNoNotIn(List<String> values) {
            addCriterion("req_no not in", values, "reqNo");
            return (Criteria) this;
        }

        public Criteria andReqNoBetween(String value1, String value2) {
            addCriterion("req_no between", value1, value2, "reqNo");
            return (Criteria) this;
        }

        public Criteria andReqNoNotBetween(String value1, String value2) {
            addCriterion("req_no not between", value1, value2, "reqNo");
            return (Criteria) this;
        }

        public Criteria andReqUserIdIsNull() {
            addCriterion("req_user_id is null");
            return (Criteria) this;
        }

        public Criteria andReqUserIdIsNotNull() {
            addCriterion("req_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andReqUserIdEqualTo(Long value) {
            addCriterion("req_user_id =", value, "reqUserId");
            return (Criteria) this;
        }

        public Criteria andReqUserIdNotEqualTo(Long value) {
            addCriterion("req_user_id <>", value, "reqUserId");
            return (Criteria) this;
        }

        public Criteria andReqUserIdGreaterThan(Long value) {
            addCriterion("req_user_id >", value, "reqUserId");
            return (Criteria) this;
        }

        public Criteria andReqUserIdGreaterThanOrEqualTo(Long value) {
            addCriterion("req_user_id >=", value, "reqUserId");
            return (Criteria) this;
        }

        public Criteria andReqUserIdLessThan(Long value) {
            addCriterion("req_user_id <", value, "reqUserId");
            return (Criteria) this;
        }

        public Criteria andReqUserIdLessThanOrEqualTo(Long value) {
            addCriterion("req_user_id <=", value, "reqUserId");
            return (Criteria) this;
        }

        public Criteria andReqUserIdIn(List<Long> values) {
            addCriterion("req_user_id in", values, "reqUserId");
            return (Criteria) this;
        }

        public Criteria andReqUserIdNotIn(List<Long> values) {
            addCriterion("req_user_id not in", values, "reqUserId");
            return (Criteria) this;
        }

        public Criteria andReqUserIdBetween(Long value1, Long value2) {
            addCriterion("req_user_id between", value1, value2, "reqUserId");
            return (Criteria) this;
        }

        public Criteria andReqUserIdNotBetween(Long value1, Long value2) {
            addCriterion("req_user_id not between", value1, value2, "reqUserId");
            return (Criteria) this;
        }

        public Criteria andReqTimeIsNull() {
            addCriterion("req_time is null");
            return (Criteria) this;
        }

        public Criteria andReqTimeIsNotNull() {
            addCriterion("req_time is not null");
            return (Criteria) this;
        }

        public Criteria andReqTimeEqualTo(Date value) {
            addCriterion("req_time =", value, "reqTime");
            return (Criteria) this;
        }

        public Criteria andReqTimeNotEqualTo(Date value) {
            addCriterion("req_time <>", value, "reqTime");
            return (Criteria) this;
        }

        public Criteria andReqTimeGreaterThan(Date value) {
            addCriterion("req_time >", value, "reqTime");
            return (Criteria) this;
        }

        public Criteria andReqTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("req_time >=", value, "reqTime");
            return (Criteria) this;
        }

        public Criteria andReqTimeLessThan(Date value) {
            addCriterion("req_time <", value, "reqTime");
            return (Criteria) this;
        }

        public Criteria andReqTimeLessThanOrEqualTo(Date value) {
            addCriterion("req_time <=", value, "reqTime");
            return (Criteria) this;
        }

        public Criteria andReqTimeIn(List<Date> values) {
            addCriterion("req_time in", values, "reqTime");
            return (Criteria) this;
        }

        public Criteria andReqTimeNotIn(List<Date> values) {
            addCriterion("req_time not in", values, "reqTime");
            return (Criteria) this;
        }

        public Criteria andReqTimeBetween(Date value1, Date value2) {
            addCriterion("req_time between", value1, value2, "reqTime");
            return (Criteria) this;
        }

        public Criteria andReqTimeNotBetween(Date value1, Date value2) {
            addCriterion("req_time not between", value1, value2, "reqTime");
            return (Criteria) this;
        }

        public Criteria andFormStatusIsNull() {
            addCriterion("form_status is null");
            return (Criteria) this;
        }

        public Criteria andFormStatusIsNotNull() {
            addCriterion("form_status is not null");
            return (Criteria) this;
        }

        public Criteria andFormStatusEqualTo(Integer value) {
            addCriterion("form_status =", value, "formStatus");
            return (Criteria) this;
        }

        public Criteria andFormStatusNotEqualTo(Integer value) {
            addCriterion("form_status <>", value, "formStatus");
            return (Criteria) this;
        }

        public Criteria andFormStatusGreaterThan(Integer value) {
            addCriterion("form_status >", value, "formStatus");
            return (Criteria) this;
        }

        public Criteria andFormStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("form_status >=", value, "formStatus");
            return (Criteria) this;
        }

        public Criteria andFormStatusLessThan(Integer value) {
            addCriterion("form_status <", value, "formStatus");
            return (Criteria) this;
        }

        public Criteria andFormStatusLessThanOrEqualTo(Integer value) {
            addCriterion("form_status <=", value, "formStatus");
            return (Criteria) this;
        }

        public Criteria andFormStatusIn(List<Integer> values) {
            addCriterion("form_status in", values, "formStatus");
            return (Criteria) this;
        }

        public Criteria andFormStatusNotIn(List<Integer> values) {
            addCriterion("form_status not in", values, "formStatus");
            return (Criteria) this;
        }

        public Criteria andFormStatusBetween(Integer value1, Integer value2) {
            addCriterion("form_status between", value1, value2, "formStatus");
            return (Criteria) this;
        }

        public Criteria andFormStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("form_status not between", value1, value2, "formStatus");
            return (Criteria) this;
        }

        public Criteria andApprovalCompletionTimeIsNull() {
            addCriterion("approval_completion_time is null");
            return (Criteria) this;
        }

        public Criteria andApprovalCompletionTimeIsNotNull() {
            addCriterion("approval_completion_time is not null");
            return (Criteria) this;
        }

        public Criteria andApprovalCompletionTimeEqualTo(Date value) {
            addCriterion("approval_completion_time =", value, "approvalCompletionTime");
            return (Criteria) this;
        }

        public Criteria andApprovalCompletionTimeNotEqualTo(Date value) {
            addCriterion("approval_completion_time <>", value, "approvalCompletionTime");
            return (Criteria) this;
        }

        public Criteria andApprovalCompletionTimeGreaterThan(Date value) {
            addCriterion("approval_completion_time >", value, "approvalCompletionTime");
            return (Criteria) this;
        }

        public Criteria andApprovalCompletionTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("approval_completion_time >=", value, "approvalCompletionTime");
            return (Criteria) this;
        }

        public Criteria andApprovalCompletionTimeLessThan(Date value) {
            addCriterion("approval_completion_time <", value, "approvalCompletionTime");
            return (Criteria) this;
        }

        public Criteria andApprovalCompletionTimeLessThanOrEqualTo(Date value) {
            addCriterion("approval_completion_time <=", value, "approvalCompletionTime");
            return (Criteria) this;
        }

        public Criteria andApprovalCompletionTimeIn(List<Date> values) {
            addCriterion("approval_completion_time in", values, "approvalCompletionTime");
            return (Criteria) this;
        }

        public Criteria andApprovalCompletionTimeNotIn(List<Date> values) {
            addCriterion("approval_completion_time not in", values, "approvalCompletionTime");
            return (Criteria) this;
        }

        public Criteria andApprovalCompletionTimeBetween(Date value1, Date value2) {
            addCriterion("approval_completion_time between", value1, value2, "approvalCompletionTime");
            return (Criteria) this;
        }

        public Criteria andApprovalCompletionTimeNotBetween(Date value1, Date value2) {
            addCriterion("approval_completion_time not between", value1, value2, "approvalCompletionTime");
            return (Criteria) this;
        }

        public Criteria andChannelIsNull() {
            addCriterion("channel is null");
            return (Criteria) this;
        }

        public Criteria andChannelIsNotNull() {
            addCriterion("channel is not null");
            return (Criteria) this;
        }

        public Criteria andChannelEqualTo(String value) {
            addCriterion("channel =", value, "channel");
            return (Criteria) this;
        }

        public Criteria andChannelNotEqualTo(String value) {
            addCriterion("channel <>", value, "channel");
            return (Criteria) this;
        }

        public Criteria andChannelGreaterThan(String value) {
            addCriterion("channel >", value, "channel");
            return (Criteria) this;
        }

        public Criteria andChannelGreaterThanOrEqualTo(String value) {
            addCriterion("channel >=", value, "channel");
            return (Criteria) this;
        }

        public Criteria andChannelLessThan(String value) {
            addCriterion("channel <", value, "channel");
            return (Criteria) this;
        }

        public Criteria andChannelLessThanOrEqualTo(String value) {
            addCriterion("channel <=", value, "channel");
            return (Criteria) this;
        }

        public Criteria andChannelIn(List<String> values) {
            addCriterion("channel in", values, "channel");
            return (Criteria) this;
        }

        public Criteria andChannelNotIn(List<String> values) {
            addCriterion("channel not in", values, "channel");
            return (Criteria) this;
        }

        public Criteria andChannelBetween(String value1, String value2) {
            addCriterion("channel between", value1, value2, "channel");
            return (Criteria) this;
        }

        public Criteria andChannelNotBetween(String value1, String value2) {
            addCriterion("channel not between", value1, value2, "channel");
            return (Criteria) this;
        }

        public Criteria andYearIsNull() {
            addCriterion("year is null");
            return (Criteria) this;
        }

        public Criteria andYearIsNotNull() {
            addCriterion("year is not null");
            return (Criteria) this;
        }

        public Criteria andYearEqualTo(String value) {
            addCriterion("year =", value, "year");
            return (Criteria) this;
        }

        public Criteria andYearNotEqualTo(String value) {
            addCriterion("year <>", value, "year");
            return (Criteria) this;
        }

        public Criteria andYearGreaterThan(String value) {
            addCriterion("year >", value, "year");
            return (Criteria) this;
        }

        public Criteria andYearGreaterThanOrEqualTo(String value) {
            addCriterion("year >=", value, "year");
            return (Criteria) this;
        }

        public Criteria andYearLessThan(String value) {
            addCriterion("year <", value, "year");
            return (Criteria) this;
        }

        public Criteria andYearLessThanOrEqualTo(String value) {
            addCriterion("year <=", value, "year");
            return (Criteria) this;
        }

        public Criteria andYearIn(List<String> values) {
            addCriterion("year in", values, "year");
            return (Criteria) this;
        }

        public Criteria andYearNotIn(List<String> values) {
            addCriterion("year not in", values, "year");
            return (Criteria) this;
        }

        public Criteria andYearBetween(String value1, String value2) {
            addCriterion("year between", value1, value2, "year");
            return (Criteria) this;
        }

        public Criteria andYearNotBetween(String value1, String value2) {
            addCriterion("year not between", value1, value2, "year");
            return (Criteria) this;
        }

        public Criteria andQuarterIsNull() {
            addCriterion("quarter is null");
            return (Criteria) this;
        }

        public Criteria andQuarterIsNotNull() {
            addCriterion("quarter is not null");
            return (Criteria) this;
        }

        public Criteria andQuarterEqualTo(String value) {
            addCriterion("quarter =", value, "quarter");
            return (Criteria) this;
        }

        public Criteria andQuarterNotEqualTo(String value) {
            addCriterion("quarter <>", value, "quarter");
            return (Criteria) this;
        }

        public Criteria andQuarterGreaterThan(String value) {
            addCriterion("quarter >", value, "quarter");
            return (Criteria) this;
        }

        public Criteria andQuarterGreaterThanOrEqualTo(String value) {
            addCriterion("quarter >=", value, "quarter");
            return (Criteria) this;
        }

        public Criteria andQuarterLessThan(String value) {
            addCriterion("quarter <", value, "quarter");
            return (Criteria) this;
        }

        public Criteria andQuarterLessThanOrEqualTo(String value) {
            addCriterion("quarter <=", value, "quarter");
            return (Criteria) this;
        }

        public Criteria andQuarterIn(List<String> values) {
            addCriterion("quarter in", values, "quarter");
            return (Criteria) this;
        }

        public Criteria andQuarterNotIn(List<String> values) {
            addCriterion("quarter not in", values, "quarter");
            return (Criteria) this;
        }

        public Criteria andQuarterBetween(String value1, String value2) {
            addCriterion("quarter between", value1, value2, "quarter");
            return (Criteria) this;
        }

        public Criteria andQuarterNotBetween(String value1, String value2) {
            addCriterion("quarter not between", value1, value2, "quarter");
            return (Criteria) this;
        }

        public Criteria andSignageIsNull() {
            addCriterion("signage is null");
            return (Criteria) this;
        }

        public Criteria andSignageIsNotNull() {
            addCriterion("signage is not null");
            return (Criteria) this;
        }

        public Criteria andSignageEqualTo(Double value) {
            addCriterion("signage =", value, "signage");
            return (Criteria) this;
        }

        public Criteria andSignageNotEqualTo(Double value) {
            addCriterion("signage <>", value, "signage");
            return (Criteria) this;
        }

        public Criteria andSignageGreaterThan(Double value) {
            addCriterion("signage >", value, "signage");
            return (Criteria) this;
        }

        public Criteria andSignageGreaterThanOrEqualTo(Double value) {
            addCriterion("signage >=", value, "signage");
            return (Criteria) this;
        }

        public Criteria andSignageLessThan(Double value) {
            addCriterion("signage <", value, "signage");
            return (Criteria) this;
        }

        public Criteria andSignageLessThanOrEqualTo(Double value) {
            addCriterion("signage <=", value, "signage");
            return (Criteria) this;
        }

        public Criteria andSignageIn(List<Double> values) {
            addCriterion("signage in", values, "signage");
            return (Criteria) this;
        }

        public Criteria andSignageNotIn(List<Double> values) {
            addCriterion("signage not in", values, "signage");
            return (Criteria) this;
        }

        public Criteria andSignageBetween(Double value1, Double value2) {
            addCriterion("signage between", value1, value2, "signage");
            return (Criteria) this;
        }

        public Criteria andSignageNotBetween(Double value1, Double value2) {
            addCriterion("signage not between", value1, value2, "signage");
            return (Criteria) this;
        }

        public Criteria andSeminarIsNull() {
            addCriterion("seminar is null");
            return (Criteria) this;
        }

        public Criteria andSeminarIsNotNull() {
            addCriterion("seminar is not null");
            return (Criteria) this;
        }

        public Criteria andSeminarEqualTo(Double value) {
            addCriterion("seminar =", value, "seminar");
            return (Criteria) this;
        }

        public Criteria andSeminarNotEqualTo(Double value) {
            addCriterion("seminar <>", value, "seminar");
            return (Criteria) this;
        }

        public Criteria andSeminarGreaterThan(Double value) {
            addCriterion("seminar >", value, "seminar");
            return (Criteria) this;
        }

        public Criteria andSeminarGreaterThanOrEqualTo(Double value) {
            addCriterion("seminar >=", value, "seminar");
            return (Criteria) this;
        }

        public Criteria andSeminarLessThan(Double value) {
            addCriterion("seminar <", value, "seminar");
            return (Criteria) this;
        }

        public Criteria andSeminarLessThanOrEqualTo(Double value) {
            addCriterion("seminar <=", value, "seminar");
            return (Criteria) this;
        }

        public Criteria andSeminarIn(List<Double> values) {
            addCriterion("seminar in", values, "seminar");
            return (Criteria) this;
        }

        public Criteria andSeminarNotIn(List<Double> values) {
            addCriterion("seminar not in", values, "seminar");
            return (Criteria) this;
        }

        public Criteria andSeminarBetween(Double value1, Double value2) {
            addCriterion("seminar between", value1, value2, "seminar");
            return (Criteria) this;
        }

        public Criteria andSeminarNotBetween(Double value1, Double value2) {
            addCriterion("seminar not between", value1, value2, "seminar");
            return (Criteria) this;
        }

        public Criteria andOilChangeCarIsNull() {
            addCriterion("oil_change_car is null");
            return (Criteria) this;
        }

        public Criteria andOilChangeCarIsNotNull() {
            addCriterion("oil_change_car is not null");
            return (Criteria) this;
        }

        public Criteria andOilChangeCarEqualTo(Double value) {
            addCriterion("oil_change_car =", value, "oilChangeCar");
            return (Criteria) this;
        }

        public Criteria andOilChangeCarNotEqualTo(Double value) {
            addCriterion("oil_change_car <>", value, "oilChangeCar");
            return (Criteria) this;
        }

        public Criteria andOilChangeCarGreaterThan(Double value) {
            addCriterion("oil_change_car >", value, "oilChangeCar");
            return (Criteria) this;
        }

        public Criteria andOilChangeCarGreaterThanOrEqualTo(Double value) {
            addCriterion("oil_change_car >=", value, "oilChangeCar");
            return (Criteria) this;
        }

        public Criteria andOilChangeCarLessThan(Double value) {
            addCriterion("oil_change_car <", value, "oilChangeCar");
            return (Criteria) this;
        }

        public Criteria andOilChangeCarLessThanOrEqualTo(Double value) {
            addCriterion("oil_change_car <=", value, "oilChangeCar");
            return (Criteria) this;
        }

        public Criteria andOilChangeCarIn(List<Double> values) {
            addCriterion("oil_change_car in", values, "oilChangeCar");
            return (Criteria) this;
        }

        public Criteria andOilChangeCarNotIn(List<Double> values) {
            addCriterion("oil_change_car not in", values, "oilChangeCar");
            return (Criteria) this;
        }

        public Criteria andOilChangeCarBetween(Double value1, Double value2) {
            addCriterion("oil_change_car between", value1, value2, "oilChangeCar");
            return (Criteria) this;
        }

        public Criteria andOilChangeCarNotBetween(Double value1, Double value2) {
            addCriterion("oil_change_car not between", value1, value2, "oilChangeCar");
            return (Criteria) this;
        }

        public Criteria andEquipmentToolsIsNull() {
            addCriterion("equipment_tools is null");
            return (Criteria) this;
        }

        public Criteria andEquipmentToolsIsNotNull() {
            addCriterion("equipment_tools is not null");
            return (Criteria) this;
        }

        public Criteria andEquipmentToolsEqualTo(Double value) {
            addCriterion("equipment_tools =", value, "equipmentTools");
            return (Criteria) this;
        }

        public Criteria andEquipmentToolsNotEqualTo(Double value) {
            addCriterion("equipment_tools <>", value, "equipmentTools");
            return (Criteria) this;
        }

        public Criteria andEquipmentToolsGreaterThan(Double value) {
            addCriterion("equipment_tools >", value, "equipmentTools");
            return (Criteria) this;
        }

        public Criteria andEquipmentToolsGreaterThanOrEqualTo(Double value) {
            addCriterion("equipment_tools >=", value, "equipmentTools");
            return (Criteria) this;
        }

        public Criteria andEquipmentToolsLessThan(Double value) {
            addCriterion("equipment_tools <", value, "equipmentTools");
            return (Criteria) this;
        }

        public Criteria andEquipmentToolsLessThanOrEqualTo(Double value) {
            addCriterion("equipment_tools <=", value, "equipmentTools");
            return (Criteria) this;
        }

        public Criteria andEquipmentToolsIn(List<Double> values) {
            addCriterion("equipment_tools in", values, "equipmentTools");
            return (Criteria) this;
        }

        public Criteria andEquipmentToolsNotIn(List<Double> values) {
            addCriterion("equipment_tools not in", values, "equipmentTools");
            return (Criteria) this;
        }

        public Criteria andEquipmentToolsBetween(Double value1, Double value2) {
            addCriterion("equipment_tools between", value1, value2, "equipmentTools");
            return (Criteria) this;
        }

        public Criteria andEquipmentToolsNotBetween(Double value1, Double value2) {
            addCriterion("equipment_tools not between", value1, value2, "equipmentTools");
            return (Criteria) this;
        }

        public Criteria andOtherAmountIsNull() {
            addCriterion("other_amount is null");
            return (Criteria) this;
        }

        public Criteria andOtherAmountIsNotNull() {
            addCriterion("other_amount is not null");
            return (Criteria) this;
        }

        public Criteria andOtherAmountEqualTo(Double value) {
            addCriterion("other_amount =", value, "otherAmount");
            return (Criteria) this;
        }

        public Criteria andOtherAmountNotEqualTo(Double value) {
            addCriterion("other_amount <>", value, "otherAmount");
            return (Criteria) this;
        }

        public Criteria andOtherAmountGreaterThan(Double value) {
            addCriterion("other_amount >", value, "otherAmount");
            return (Criteria) this;
        }

        public Criteria andOtherAmountGreaterThanOrEqualTo(Double value) {
            addCriterion("other_amount >=", value, "otherAmount");
            return (Criteria) this;
        }

        public Criteria andOtherAmountLessThan(Double value) {
            addCriterion("other_amount <", value, "otherAmount");
            return (Criteria) this;
        }

        public Criteria andOtherAmountLessThanOrEqualTo(Double value) {
            addCriterion("other_amount <=", value, "otherAmount");
            return (Criteria) this;
        }

        public Criteria andOtherAmountIn(List<Double> values) {
            addCriterion("other_amount in", values, "otherAmount");
            return (Criteria) this;
        }

        public Criteria andOtherAmountNotIn(List<Double> values) {
            addCriterion("other_amount not in", values, "otherAmount");
            return (Criteria) this;
        }

        public Criteria andOtherAmountBetween(Double value1, Double value2) {
            addCriterion("other_amount between", value1, value2, "otherAmount");
            return (Criteria) this;
        }

        public Criteria andOtherAmountNotBetween(Double value1, Double value2) {
            addCriterion("other_amount not between", value1, value2, "otherAmount");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIsNull() {
            addCriterion("create_user_id is null");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIsNotNull() {
            addCriterion("create_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdEqualTo(Long value) {
            addCriterion("create_user_id =", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotEqualTo(Long value) {
            addCriterion("create_user_id <>", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdGreaterThan(Long value) {
            addCriterion("create_user_id >", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdGreaterThanOrEqualTo(Long value) {
            addCriterion("create_user_id >=", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLessThan(Long value) {
            addCriterion("create_user_id <", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLessThanOrEqualTo(Long value) {
            addCriterion("create_user_id <=", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIn(List<Long> values) {
            addCriterion("create_user_id in", values, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotIn(List<Long> values) {
            addCriterion("create_user_id not in", values, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdBetween(Long value1, Long value2) {
            addCriterion("create_user_id between", value1, value2, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotBetween(Long value1, Long value2) {
            addCriterion("create_user_id not between", value1, value2, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdIsNull() {
            addCriterion("update_user_id is null");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdIsNotNull() {
            addCriterion("update_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdEqualTo(Long value) {
            addCriterion("update_user_id =", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdNotEqualTo(Long value) {
            addCriterion("update_user_id <>", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdGreaterThan(Long value) {
            addCriterion("update_user_id >", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdGreaterThanOrEqualTo(Long value) {
            addCriterion("update_user_id >=", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdLessThan(Long value) {
            addCriterion("update_user_id <", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdLessThanOrEqualTo(Long value) {
            addCriterion("update_user_id <=", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdIn(List<Long> values) {
            addCriterion("update_user_id in", values, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdNotIn(List<Long> values) {
            addCriterion("update_user_id not in", values, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdBetween(Long value1, Long value2) {
            addCriterion("update_user_id between", value1, value2, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdNotBetween(Long value1, Long value2) {
            addCriterion("update_user_id not between", value1, value2, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}
