package com.sys.utils.service.impl;

import com.common.constants.Constants;
import com.common.exception.WxPltException;
import com.common.util.MessageResourceUtil;
import com.sys.utils.business.ReportViewBizService;
import com.sys.utils.model.ReportView;
import com.sys.utils.service.ReportViewService;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Report View操作RPC接口实现
 * <AUTHOR>
 * @version 1.0 2018-12-27 14:20
 */
@Service
public class ReportViewServiceImpl implements ReportViewService {
	
	@Autowired
	private ReportViewBizService reportViewBizService;
	
	private final static Logger log = Logger.getLogger(ReportViewServiceImpl.class);

	@Override
	public Map<String, Object> save(ReportView record) {
		Map<String, Object> map = new HashMap<String, Object>(5);
		try {
			if(record.getId() == null){
				reportViewBizService.insert(record);
			}else{
				reportViewBizService.update(record);
			}
			map.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (WxPltException e) {
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, e.getMessage());
		} catch (Exception e) {
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error("save fail. " + e.getMessage(), e);
		}
		return map;
	}

	@Override
	public Map<String, Object> delete(List<Long> ids) {
		Map<String, Object> map = new HashMap<String, Object>(5);
		try {
			reportViewBizService.delete(ids);
			map.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (WxPltException e) {
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, e.getMessage());
		} catch (Exception e) {
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error("delete fail. " + e.getMessage(), e);
		}
		return map;
	}
}
