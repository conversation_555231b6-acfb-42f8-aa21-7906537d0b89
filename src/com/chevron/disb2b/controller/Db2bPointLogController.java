package com.chevron.disb2b.controller;

import com.chevron.disb2b.business.Db2bPointLogBizService;
import com.chevron.disb2b.model.Db2bPointLog;
import com.chevron.disb2b.model.Db2bPointLogParams;
import com.chevron.disb2b.model.Db2bProductPage;
import com.chevron.disb2b.model.Db2bProductParams;
import com.common.constants.Constants;
import com.common.exception.WxPltException;
import com.common.util.ContextUtil;
import com.common.util.JsonResponse;
import com.common.util.JsonUtil;
import com.common.util.ResponseStatus;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 积分日志Spring MVC Controller
 * <AUTHOR>
 * @version 1.0 2019-12-06 17:20
 */
@Controller
@RequestMapping(value="/db2bpointlog")
public class Db2bPointLogController {
	@Autowired
	private Db2bPointLogBizService db2bPointLogBizService;
	
	private final static Logger log = Logger.getLogger(Db2bPointLogController.class);

	/**
	 * 积分日志列表页面查询
	 * @param params 查询参数
	 * @return 查询结果
	 */
	@ResponseBody
	@RequestMapping(value="/data.do")
	public JsonResponse queryForPage(Db2bPointLogParams params, HttpServletRequest request){
		JsonResponse resultMap = new JsonResponse();
		log.info("queryForPage: " + JsonUtil.writeValue(params));

		try {
			db2bPointLogBizService.queryForPage(params, resultMap);
			log.info("queryForPage success." );
		} catch (WxPltException e) {
			resultMap.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
			resultMap.setErrorMsg(e.getMessage());
		} catch (Exception e) {
			resultMap.handleException(e, this, ContextUtil.getCurUserId(), 
					"com.chevron.disb2b.controller.Db2bPointLogController.queryForPage", JsonUtil.writeValue(params));
		}
		return resultMap;
	}
	
	/**
	 * 积分日志列表详情弹出框查询
	 * @param params 查询参数
	 * @return 查询结果
	 */
	@ResponseBody
	@RequestMapping(value="/getDetailForPage.do")
	public Map<String, Object> getDetailForPage(Db2bPointLogParams params){
		Map<String, Object> map = new HashMap<String, Object>();
		if(params == null){
			params = new Db2bPointLogParams();
		}
		List<Db2bPointLog> list = null;
		try {
			list = db2bPointLogBizService.queryDetailForPage(params);
		} catch (WxPltException e) {
			e.printStackTrace();
		}
		map.put(Constants.RESULT_LST_KEY, list);
		map.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		return map;
	}
}
