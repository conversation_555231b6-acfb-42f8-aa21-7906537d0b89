;(function($){
	UI.Input = {
		jsonToAttr: function(obj){
			var strs = null;
			for(var k in obj){
				if(strs){
					strs.push(',');
				}else{
					strs = ['{'];
				}
				if(typeof obj[k] == 'string'){
					strs.push(k, ":'", obj[k], "'");
				}else if(typeof obj[k] == 'object'){
					strs.push(k, ":", UI.Input.jsonToAttr(obj[k]));
				}else{
					strs.push(k, ":", obj[k]);
				}
			}
			strs.push("}");
			return strs.join('');
		}
	};
	UI.Button = {
		attrs: ['id', 'style', 'name'],
		create: function(writer, text, onclick, opts){
			var cls = 'ui-button';
			if(opts && opts['class']){
				cls += ' ' + opts['class'];
			}
			writer.push('<span class="', cls, '"><button onmouseenter="$(this).addClass(\'ui-button-hover\');" onmouseout="$(this).removeClass(\'ui-button-hover\');" onclick="', onclick, '"');
			if(opts){
				if(opts.disabled){
					writer.push(' disabled="disabled" class="ui-button-disabled"');
				}
				//处理其他属性
				for(var i = 0; i < this.attrs.length; i++){
					var attr = this.attrs[i];
					if(opts[attr]){
						writer.push(' ', attr, '="', opts[attr], '"');
					}
				}
			}
			writer.push('>', text, '</button></span>');
		},
		setDisabled: function($d, disabled){
			$d = $d.find('button');
			if(disabled){
				$d.addClass('ui-button-disabled');
				$d.removeClass('ui-button-hover');
			}else{
				$d.removeClass('ui-button-disabled');
			}
			$d.prop('disabled', disabled);
		}
	};
	UI.Text = {
		'class': 'UI.Text',
		defValCssClass: 'default-value',
		attrs: ['id', 'value', 'style', 'class', 'name'],
		processOpts: function(opts){
			return opts;
		},
		create: function(writer, opts){
			writer.push('<input type="text" onfocus="this.select();');
			opts = this.processOpts(opts);
			if(opts){
				//处理文本输入默认值
				if(opts.defValue !== undefined){
					opts.required = false;
					if(opts.value === undefined || opts.value === "" || opts.value === null){
						opts.value = opts.defValue;
						opts['class'] = this.defValCssClass + (opts['class'] ? ' ' + opts['class'] : '');
					}
					writer.push("if($(this).hasClass('", this.defValCssClass, 
							"')){this.value = '';$(this).removeClass('", this.defValCssClass, "');}");
					opts.onchange = (opts.onchange ? opts.onchange + ';' : '') + "if(this.value==''){this.value='" 
							+ opts.defValue + "';$(this).addClass('" + this.defValCssClass + "');}";
				}
				//处理验证
				if(opts.validator){
					var validator = this.buildValidator(opts.validator);
					writer.push(this['class'], '.resetValidateStatus(this);');
					if(opts.onfocus){
						writer.push(opts.onfocus);
					}
					writer.push('" onchange="if(', this['class'], '.validate(this) === false){return false;}');
					if(opts.onchange){
						writer.push(opts.onchange);
					}
					writer.push('" validator="', UI.Input.jsonToAttr(validator), '"');
				}else{
					if(opts.onfocus){
						writer.push(opts.onfocus);
					}
					writer.push('"');
					if(opts.onchange){
						writer.push(' onchange"', opts.onchange, '"');
					}
				}
				//处理其他属性
				for(var i = 0; i < this.attrs.length; i++){
					var attr = this.attrs[i];
					if(opts[attr]){
						writer.push(' ', attr, '="', opts[attr], '"');
					}
				}
				if(opts.readonly){
					writer.push(' readonly="readonly"');
				}
				if(opts.disabled){
					writer.push(' disabled="disabled"');
				}
			}
		},
		buildValidator: function(validator){
			var v = {};
			//处理非空验证
			if(validator.required !== undefined){
				if(typeof validator.required == 'object'){
					v.required = validator.required;
				}else{
					v.required = {value: validator.required};
				}
				if(!v.required.msg){
					v.required.msg = this.getDefRequiredMsg();
				}
			}
			//处理正则验证
			if(validator.pattern){
				if(typeof validator.pattern == 'object'){
					v.pattern = validator.pattern;
				}else{
					v.pattern = {value: validator.pattern};
				}
				if(!v.pattern.msg){
					v.pattern.msg = this.getDefPatternMsg();
				}
			}
			//处理最大长度验证
			if(validator.maxLen){
				if(typeof validator.maxLen == 'object'){
					v.maxLen = validator.maxLen;
				}else{
					v.maxLen = {value: validator.maxLen};
				}
				if(!v.maxLen.msg){
					v.maxLen.msg = this.getDefMaxLenMsg(v.maxLen.value);
				}
			}
			//处理最小长度验证
			if(validator.minLen){
				if(typeof validator.minLen == 'object'){
					v.minLen = validator.minLen;
				}else{
					v.minLen = {value: validator.minLen};
				}
				if(!v.minLen.msg){
					v.minLen.msg = this.getDefMinLenMsg(v.minLen.value);
				}
			}
			return $.extend(validator, v);
		},
		validate: function(dom){
			var $d = $(dom), validator = this.processValidator(eval('(' + $d.attr('validator') + ')'));
			//非空验证
			if(validator.required && validator.required.value && dom.value == ''){
				this.showError($d, validator.required.msg);
				return false;
			}
			//最小长度验证
			if(validator.minLen && dom.value.length < validator.minLen.value){
				this.showError($d, validator.minLen.msg);
				return false;
			}
			//最大长度验证
			if(validator.maxLen && dom.value.length > validator.maxLen.value){
				this.showError($d, validator.maxLen.msg);
				return false;
			}
			//正则验证
			if(validator.pattern && !validator.pattern.value.test(dom.value)){
				this.showError($d, validator.pattern.msg);
				return false;
			}
			return true;
		},
		processValidator: function(validator){
			return validator;
		},
		resetValidateStatus: function(dom){
			
		},
		getDefRequiredMsg: function(){
			return "不能为空";
		},
		getDefPatternMsg: function(){
			return '输入格式不对';
		},
		getDefMaxLenMsg: function(maxLen){
			return '长度不能大于' + maxLen;
		},
		getDefMinLenMsg: function(minLen){
			return '长度不能小于' + minLen;
		},
		setDisabled: function($d, disabled){
			$d.prop('disabled', disabled);
		},
		setReadonly: function($d, readonly){
			$d.prop('readonly', readonly);
		},
		setValue: function($d, value){
			$d.val(value);
		},
		showError: function($d, msg){
			alert(msg);
		}
	};
	UI.Email = $.extend({}, UI.Text, {
		'class': 'UI.Email',
		processValidator: function(validator){
			validator.pattern = {value: /^([a-zA-Z0-9]+[_|\_|\.]?)*[a-zA-Z0-9]+@([a-zA-Z0-9]+[_|\_|\.]?)*[a-zA-Z0-9]+\.[a-zA-Z]{2,3}$/};
			if(validator.msg){
				validator.pattern.msg = validator.msg;
			}else{
				validator.pattern.msg = "邮箱地址格式不正确";
			}
			return validator;
		}
	});
	UI.Mobile = $.extend({}, UI.Text, {
		'class': 'UI.Mobile',
		processValidator: function(validator){
			validator.pattern = {value: /^[0-9]{11}$/};
			if(validator.msg){
				validator.pattern.msg = validator.msg;
			}else{
				validator.pattern.msg = "手机号码格式不正确";
			}
			return validator;
		}
	});
	UI.Telphone = $.extend({}, UI.Text, {
		'class': 'UI.Telphone',
		processValidator: function(validator){
			validator.pattern = {value: /^[+]{0,1}(\d){1,3}[ ]?([-]?((\d)|[ ]){1,12})+$/};
			if(validator.msg){
				validator.pattern.msg = validator.msg;
			}else{
				validator.pattern.msg = "电话号码格式不正确";
			}
			return validator;
		}
	});
	
	UI.Number = {
		'class': 'UI.Number',
		defValCssClass: 'default-value',
		attrs: ['id', 'value', 'style', 'class', 'name'],
		processOpts: function(opts){
			if(!opts){
				opts = {validator: {msg: this.getDefPatternMsg()}};
			}else if(!opts.validator){
				opts.validator = {msg: this.getDefPatternMsg()};
			}else if(!opts.validator.msg){
				opts.validator.msg = this.getDefPatternMsg();
			}
			return opts;
		},
		create: function(writer, opts){
			writer.push('<input type="text" onfocus="this.select();');
			opts = this.processOpts(opts);
			if(opts){
				//处理文本输入默认值
				if(opts.defValue !== undefined){
					opts.required = false;
					if(opts.value === undefined || opts.value === "" || opts.value === null){
						opts.value = opts.defValue;
						opts['class'] = this.defValCssClass + (opts['class'] ? ' ' + opts['class'] : '');
					}
					writer.push("if($(this).hasClass('", this.defValCssClass, 
							"')){this.value = '';$(this).removeClass('", this.defValCssClass, "');}");
					opts.onchange = (opts.onchange ? opts.onchange + ';' : '') + "if(this.value==''){this.value='" 
							+ opts.defValue + "';$(this).addClass('" + this.defValCssClass + "');}";
				}
				//处理验证
				if(opts.validator){
					var validator = this.buildValidator(opts.validator);
					writer.push(this['class'], '.resetValidateStatus(this);');
					if(opts.onfocus){
						writer.push(opts.onfocus);
					}
					writer.push('" onchange="if(', this['class'], '.validate(this) === false){return false;}');
					if(opts.onchange){
						writer.push(opts.onchange);
					}
					writer.push('" validator="', UI.Input.jsonToAttr(validator), '"');
				}else{
					if(opts.onfocus){
						writer.push(opts.onfocus);
					}
					writer.push('"');
					if(opts.onchange){
						writer.push(' onchange"', opts.onchange, '"');
					}
				}
				//处理其他属性
				for(var i = 0; i < this.attrs.length; i++){
					var attr = this.attrs[i];
					if(opts[attr]){
						writer.push(' ', attr, '="', opts[attr], '"');
					}
				}
				if(opts.readonly){
					writer.push(' readonly="readonly"');
				}
				if(opts.disabled){
					writer.push(' disabled="disabled"');
				}
			}
			writer.push('/>');
		},
		buildValidator: function(validator){
			var v = {};
			//处理非空验证
			if(validator.required !== undefined){
				if(typeof validator.required == 'object'){
					v.required = validator.required;
				}else{
					v.required = {value: validator.required};
				}
				if(!v.required.msg){
					v.required.msg = this.getDefRequiredMsg();
				}
			}
			//处理最大值验证
			if(validator.max){
				if(typeof validator.max == 'object'){
					v.max = validator.max;
				}else{
					v.max = {value: validator.max};
				}
				if(!v.max.msg){
					v.max.msg = this.getDefMaxMsg(v.max.value);
				}
			}
			//处理最小值验证
			if(validator.min){
				if(typeof validator.min == 'object'){
					v.min = validator.min;
				}else{
					v.min = {value: validator.min};
				}
				if(!v.min.msg){
					v.min.msg = this.getDefMinMsg(v.min.value);
				}
			}
			return $.extend(validator, v);
		},
		validate: function(dom){
			var $d = $(dom), validator = this.processValidator(eval('(' + $d.attr('validator') + ')'));
			//非空验证
			if(validator.required && validator.required.value && dom.value == ''){
				this.showError($d, validator.required.msg);
				return false;
			}
			//数字格式验证
			if(this.patternValidate(dom) === false){
				this.showError($d, validator.msg);
				return false;
			}
			//最小值验证
			if(validator.min && parseFloat(dom.value) < validator.min.value){
				this.showError($d, validator.min.msg);
				return false;
			}
			//最大值验证
			if(validator.max && parseFloat(dom.value) > validator.max.value){
				this.showError($d, validator.max.msg);
				return false;
			}
			return true;
		},
		patternValidate: function(dom){
			if(isNaN(dom.value)){
				return false;
			}
			return true;
		},
		processValidator: function(validator){
			return validator;
		},
		resetValidateStatus: function(dom){
			
		},
		getDefPatternMsg: function(){
			return "数字格式不对";
		},
		getDefRequiredMsg: function(){
			return "值不能为空";
		},
		getDefMaxMsg: function(max){
			return '值不能大于' + max;
		},
		getDefMinMsg: function(min){
			return '值不能小于' + min;
		},
		setDisabled: function($d, disabled){
			$d.prop('disabled', disabled);
		},
		setReadonly: function($d, readonly){
			$d.prop('readonly', readonly);
		},
		setValue: function($d, value){
			$d.val(value);
		},
		setValidator: function($d, validator){
			$d.attr('validator', UI.Input.jsonToAttr(this.buildValidator(validator)));
		},
		showError: function($d, msg){
			alert(msg);
		}
	};
	UI.Int = $.extend({}, UI.Number, {
		patternValidate: function(dom){
			if(!/^[-]?[1-9]\\d*$/.test(this.value)){
				return false;
			}
			return true;
		},
		getDefPatternMsg: function(){
			return "整数格式不对";
		}
	});
	UI.Date = {
			
	};
	UI.Checkbox = {
			
	};
	UI.Radio = {
			
	};
})(jQuery);
