SELECT a.*
FROM (
	SELECT 'CIO_Seminar_Level2_Package' AS expense_code
		, convert(varchar(6), t_activity.release_time, 112) AS month
		, pe.distributor_id
		, t_seminar_activity_detail.apply_high_packs * ((
			SELECT ac.amount
			FROM wx_t_amount_config_by_month ac
			WHERE ac.module_name = 'cioBudget'
				AND ac.config_type = 'cio.mktPackage.unitPrice.seminarLevel2Package'
				AND (ac.effictive_from_month IS NULL
					OR ac.effictive_from_month <= t_activity.release_time)
				AND (ac.effictive_to_month IS NULL
					OR dateadd(month, 1, ac.effictive_to_month) >= t_activity.release_time)
		) * (
			SELECT ac.amount
			FROM wx_t_amount_config_by_month ac
			WHERE ac.module_name = 'cioBudget'
				AND ac.config_type = 'cio.mktPackage.taxRate'
				AND (ac.effictive_from_month IS NULL
					OR ac.effictive_from_month <= t_activity.release_time)
				AND (ac.effictive_to_month IS NULL
					OR dateadd(month, 1, ac.effictive_to_month) >= t_activity.release_time)
		) + (
			SELECT ac.amount
			FROM wx_t_amount_config_by_month ac
			WHERE ac.module_name = 'cioBudget'
				AND ac.config_type = 'cio.seminar14A.unitPrice.level2'
				AND (ac.effictive_from_month IS NULL
					OR ac.effictive_from_month <= t_activity.release_time)
				AND (ac.effictive_to_month IS NULL
					OR dateadd(month, 1, ac.effictive_to_month) >= t_activity.release_time)
		) + (
			SELECT ac.amount
			FROM wx_t_amount_config_by_month ac
			WHERE ac.module_name = 'cioBudget'
				AND ac.config_type = 'cio.seminarTCP.unitPrice.level2'
				AND (ac.effictive_from_month IS NULL
					OR ac.effictive_from_month <= t_activity.release_time)
				AND (ac.effictive_to_month IS NULL
					OR dateadd(month, 1, ac.effictive_to_month) >= t_activity.release_time)
		)) expense_value
	FROM wx_promote_application_batch promote_appl_batch
		INNER JOIN wx_promote_activity t_activity ON promote_appl_batch.batchid = t_activity.apply_batch_id
		INNER JOIN wx_promote_seminar_activity_detail t_seminar_activity_detail ON t_seminar_activity_detail.activity_id = t_activity.id
		LEFT JOIN wx_t_partner_o2o_enterprise pe ON t_activity.activity_organizers_id = pe.partner_id
	WHERE promote_appl_batch.approve_status = 3
		AND t_activity.activity_type = 'YTH'
		AND promote_appl_batch.current_step = 'marketing'
		AND promote_appl_batch.prokey = 'promoteApplication'
		AND t_seminar_activity_detail.apply_high_packs > 0
	UNION ALL
	SELECT 'CIO_Seminar_Level1_Package' AS expense_code
		, convert(varchar(6), t_activity.release_time, 112) AS month
		, pe.distributor_id
		, t_seminar_activity_detail.apply_packs_count * ((
			SELECT ac.amount
			FROM wx_t_amount_config_by_month ac
			WHERE ac.module_name = 'cioBudget'
				AND ac.config_type = 'cio.mktPackage.unitPrice.seminarLevel1Package'
				AND (ac.effictive_from_month IS NULL
					OR ac.effictive_from_month <= t_activity.release_time)
				AND (ac.effictive_to_month IS NULL
					OR dateadd(month, 1, ac.effictive_to_month) >= t_activity.release_time)
		) * (
			SELECT ac.amount
			FROM wx_t_amount_config_by_month ac
			WHERE ac.module_name = 'cioBudget'
				AND ac.config_type = 'cio.mktPackage.taxRate'
				AND (ac.effictive_from_month IS NULL
					OR ac.effictive_from_month <= t_activity.release_time)
				AND (ac.effictive_to_month IS NULL
					OR dateadd(month, 1, ac.effictive_to_month) >= t_activity.release_time)
		) + (
			SELECT ac.amount
			FROM wx_t_amount_config_by_month ac
			WHERE ac.module_name = 'cioBudget'
				AND ac.config_type = 'cio.seminar14A.unitPrice.level1'
				AND (ac.effictive_from_month IS NULL
					OR ac.effictive_from_month <= t_activity.release_time)
				AND (ac.effictive_to_month IS NULL
					OR dateadd(month, 1, ac.effictive_to_month) >= t_activity.release_time)
		) + (
			SELECT ac.amount
			FROM wx_t_amount_config_by_month ac
			WHERE ac.module_name = 'cioBudget'
				AND ac.config_type = 'cio.seminarTCP.unitPrice.level1'
				AND (ac.effictive_from_month IS NULL
					OR ac.effictive_from_month <= t_activity.release_time)
				AND (ac.effictive_to_month IS NULL
					OR dateadd(month, 1, ac.effictive_to_month) >= t_activity.release_time)
		))
	FROM wx_promote_application_batch promote_appl_batch
		INNER JOIN wx_promote_activity t_activity ON promote_appl_batch.batchid = t_activity.apply_batch_id
		INNER JOIN wx_promote_seminar_activity_detail t_seminar_activity_detail ON t_seminar_activity_detail.activity_id = t_activity.id
		LEFT JOIN wx_t_partner_o2o_enterprise pe ON t_activity.activity_organizers_id = pe.partner_id
	WHERE promote_appl_batch.approve_status = 3
		AND t_activity.activity_type = 'YTH'
		AND promote_appl_batch.current_step = 'marketing'
		AND promote_appl_batch.prokey = 'promoteApplication'
		AND t_seminar_activity_detail.apply_packs_count > 0
	UNION ALL
	SELECT 'CIO_Opening_Level2_Package' AS expense_code
		, convert(varchar(6), t_activity.release_time, 112) AS month
		, pe.distributor_id, t_xd_open.gd_packs_number * ((
			SELECT ac.amount
			FROM wx_t_amount_config_by_month ac
			WHERE ac.module_name = 'cioBudget'
				AND ac.config_type = 'cio.mktPackage.unitPrice.openingLevel2Package'
				AND (ac.effictive_from_month IS NULL
					OR ac.effictive_from_month <= t_activity.release_time)
				AND (ac.effictive_to_month IS NULL
					OR dateadd(month, 1, ac.effictive_to_month) >= t_activity.release_time)
		) * (
			SELECT ac.amount
			FROM wx_t_amount_config_by_month ac
			WHERE ac.module_name = 'cioBudget'
				AND ac.config_type = 'cio.mktPackage.taxRate'
				AND (ac.effictive_from_month IS NULL
					OR ac.effictive_from_month <= t_activity.release_time)
				AND (ac.effictive_to_month IS NULL
					OR dateadd(month, 1, ac.effictive_to_month) >= t_activity.release_time)
		))
	FROM wx_promote_application_batch promote_appl_batch
		INNER JOIN wx_promote_activity t_activity ON promote_appl_batch.batchid = t_activity.apply_batch_id
		INNER JOIN wx_promote_xd_open_detail t_xd_open ON t_xd_open.activity_id = t_activity.id
		LEFT JOIN wx_t_partner_o2o_enterprise pe ON t_activity.activity_organizers_id = pe.partner_id
	WHERE promote_appl_batch.approve_status = 3
		AND promote_appl_batch.current_step = 'marketing'
		AND promote_appl_batch.prokey = 'promoteApplication'
		AND t_xd_open.gd_packs_number > 0
		AND t_activity.activity_type = 'DLXDKY'
	UNION ALL
	SELECT 'CIO_Opening_Level1_Package' AS expense_code
		, convert(varchar(6), t_activity.release_time, 112) AS month
		, pe.distributor_id, t_xd_open.packs_number * ((
			SELECT ac.amount
			FROM wx_t_amount_config_by_month ac
			WHERE ac.module_name = 'cioBudget'
				AND ac.config_type = 'cio.mktPackage.unitPrice.openingLevel1Package'
				AND (ac.effictive_from_month IS NULL
					OR ac.effictive_from_month <= t_activity.release_time)
				AND (ac.effictive_to_month IS NULL
					OR dateadd(month, 1, ac.effictive_to_month) >= t_activity.release_time)
		) * (
			SELECT ac.amount
			FROM wx_t_amount_config_by_month ac
			WHERE ac.module_name = 'cioBudget'
				AND ac.config_type = 'cio.mktPackage.taxRate'
				AND (ac.effictive_from_month IS NULL
					OR ac.effictive_from_month <= t_activity.release_time)
				AND (ac.effictive_to_month IS NULL
					OR dateadd(month, 1, ac.effictive_to_month) >= t_activity.release_time)
		))
	FROM wx_promote_application_batch promote_appl_batch
		INNER JOIN wx_promote_activity t_activity ON promote_appl_batch.batchid = t_activity.apply_batch_id
		INNER JOIN wx_promote_xd_open_detail t_xd_open ON t_xd_open.activity_id = t_activity.id
		LEFT JOIN wx_t_partner_o2o_enterprise pe ON t_activity.activity_organizers_id = pe.partner_id
	WHERE promote_appl_batch.approve_status = 3
		AND promote_appl_batch.current_step = 'marketing'
		AND promote_appl_batch.prokey = 'promoteApplication'
		AND t_xd_open.packs_number > 0
		AND t_activity.activity_type = 'DLXDKY'
	UNION ALL
	SELECT 'CIO_Roadshow_Package' AS expense_code
		, convert(varchar(6), t_activity.release_time, 112) AS month
		, pe.distributor_id, t_roadshow_detail.road_show_gif_package_count * (
			SELECT ac.amount
			FROM wx_t_amount_config_by_month ac
			WHERE ac.module_name = 'cioBudget'
				AND ac.config_type = 'cio.mktPackage.unitPrice.roadshowPackage'
				AND (ac.effictive_from_month IS NULL
					OR ac.effictive_from_month <= t_activity.release_time)
				AND (ac.effictive_to_month IS NULL
					OR dateadd(month, 1, ac.effictive_to_month) >= t_activity.release_time)
		)
	FROM wx_promote_application_batch promote_appl_batch
		INNER JOIN wx_promote_activity t_activity ON promote_appl_batch.batchid = t_activity.apply_batch_id
		INNER JOIN wx_promote_roadshow_activity_detail t_roadshow_detail ON t_roadshow_detail.activity_id = t_activity.id
		LEFT JOIN wx_t_partner_o2o_enterprise pe ON t_activity.activity_organizers_id = pe.partner_id
	WHERE promote_appl_batch.approve_status = 3
		AND promote_appl_batch.current_step = 'marketing'
		AND promote_appl_batch.prokey = 'promoteApplication'
		AND t_roadshow_detail.road_show_gif_package_count > 0
		AND t_activity.activity_type = 'DLLY'
	UNION ALL
	SELECT 'CIO_Customer_Interact_Package' AS expense_code
		, convert(varchar(6), t_activity.release_time, 112) AS month
		, pe.distributor_id, t_roadshow_detail.road_show_consumer_packs_count * ((
			SELECT ac.amount
			FROM wx_t_amount_config_by_month ac
			WHERE ac.module_name = 'cioBudget'
				AND ac.config_type = 'cio.mktPackage.unitPrice.customerInteractPackage'
				AND (ac.effictive_from_month IS NULL
					OR ac.effictive_from_month <= t_activity.release_time)
				AND (ac.effictive_to_month IS NULL
					OR dateadd(month, 1, ac.effictive_to_month) >= t_activity.release_time)
		) * (
			SELECT ac.amount
			FROM wx_t_amount_config_by_month ac
			WHERE ac.module_name = 'cioBudget'
				AND ac.config_type = 'cio.mktPackage.taxRate'
				AND (ac.effictive_from_month IS NULL
					OR ac.effictive_from_month <= t_activity.release_time)
				AND (ac.effictive_to_month IS NULL
					OR dateadd(month, 1, ac.effictive_to_month) >= t_activity.release_time)
		))
	FROM wx_promote_application_batch promote_appl_batch
		INNER JOIN wx_promote_activity t_activity ON promote_appl_batch.batchid = t_activity.apply_batch_id
		INNER JOIN wx_promote_roadshow_activity_detail t_roadshow_detail ON t_roadshow_detail.activity_id = t_activity.id
		LEFT JOIN wx_t_partner_o2o_enterprise pe ON t_activity.activity_organizers_id = pe.partner_id
	WHERE promote_appl_batch.approve_status = 3
		AND promote_appl_batch.current_step = 'marketing'
		AND promote_appl_batch.prokey = 'promoteApplication'
		AND t_roadshow_detail.road_show_consumer_packs_count > 0
		AND t_activity.activity_type = 'DLLY'
	UNION ALL
	SELECT 'CIO_Ka_Package' AS expense_code
		, convert(varchar(6), t_activity.release_time, 112) AS month
		, pe.distributor_id, d.packs_number * ((
			SELECT ac.amount
			FROM wx_t_amount_config_by_month ac
			WHERE ac.module_name = 'cioBudget'
				AND ac.config_type = 'cio.mktPackage.unitPrice.kaPackage'
				AND (ac.effictive_from_month IS NULL
					OR ac.effictive_from_month <= t_activity.release_time)
				AND (ac.effictive_to_month IS NULL
					OR dateadd(month, 1, ac.effictive_to_month) >= t_activity.release_time)
		) * (
			SELECT ac.amount
			FROM wx_t_amount_config_by_month ac
			WHERE ac.module_name = 'cioBudget'
				AND ac.config_type = 'cio.mktPackage.taxRate'
				AND (ac.effictive_from_month IS NULL
					OR ac.effictive_from_month <= t_activity.release_time)
				AND (ac.effictive_to_month IS NULL
					OR dateadd(month, 1, ac.effictive_to_month) >= t_activity.release_time)
		))
	FROM wx_promote_application_batch promote_appl_batch
		INNER JOIN wx_promote_activity t_activity ON promote_appl_batch.batchid = t_activity.apply_batch_id
		INNER JOIN wx_promote_agriculture_detail d ON d.activity_id = t_activity.id
		LEFT JOIN wx_t_partner_o2o_enterprise pe ON t_activity.activity_organizers_id = pe.partner_id
	WHERE promote_appl_batch.approve_status = 3
		AND promote_appl_batch.current_step = 'marketing'
		AND promote_appl_batch.prokey = 'promoteApplication'
		AND d.packs_number > 0
		AND t_activity.activity_type = 'NKKSGCJX'
) a
ORDER BY a.expense_code, a.distributor_id, a.month