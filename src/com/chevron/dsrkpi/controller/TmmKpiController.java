package com.chevron.dsrkpi.controller;


import java.io.OutputStreamWriter;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.chevron.dsrkpi.model.CityTotalAmount;
import com.chevron.dsrkpi.model.TmmBonusCalParam;
import com.chevron.dsrkpi.model.TmmCollectKpiDTO;
import com.chevron.dsrkpi.model.TmmPerformaceDTO;
import com.chevron.dsrkpi.service.DsrKpiService;
import com.common.exception.WxPltException;
import com.common.util.ResponseStatus;
import freemarker.template.Configuration;
import freemarker.template.Template;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import com.chevron.dsrkpi.model.TmmInfoDecVo;
import com.chevron.dsrkpi.model.TmmInfoVo;
import com.chevron.dsrkpi.service.impl.TmmKpiServiceImpl;
import com.chevron.exportdata.ExportCol;
import com.chevron.exportdata.IExportCol;
import com.chevron.exportdata.IPropertyHelper;
import com.chevron.exportdata.PoiWriteExcel;
import com.common.util.CommonUtil;
import com.common.util.ContextUtil;
import com.common.util.DateUtil;
import com.common.util.JsonResponse;
import com.sys.auth.business.OperationPermissionBizService;
import com.sys.auth.model.WxTUser;
import com.sys.utils.business.ReportViewBizService;

import cn.hutool.core.collection.CollectionUtil;
import org.springframework.web.bind.annotation.ResponseBody;


@Controller
@RequestMapping(value="/tmmKpi")
public class TmmKpiController {
	
	@Autowired
	private ReportViewBizService reportViewBizService;
	
	@Autowired
	private OperationPermissionBizService operationPermissionBizService;
	
	@Autowired
	private TmmKpiServiceImpl tmmKpiServiceImpl;

	@Autowired
    private DsrKpiService dsrKpiService;
	
	private final static Logger log = Logger.getLogger(TmmKpiController.class);
	
	@SuppressWarnings("unchecked")
	@RequestMapping(value = "/excel/tmmExport.do")
	public String exportByExcel(HttpServletRequest request, HttpServletResponse response){
		try {
			List<IExportCol> exportCols = new ArrayList<IExportCol>();
			ExportCol exportCol = new ExportCol("chRegionName", "区域");
			exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
			exportCols.add(exportCol);
			exportCol = new ExportCol("customerNameCn", "经销商名称");
			exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
			exportCols.add(exportCol);
			exportCol = new ExportCol("chName", "TMM");
			exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
			exportCol.setWidth(20);
			exportCols.add(exportCol);
			/*exportCol = new ExportCol("year_awards", "年终奖励");
			exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
			exportCol.setDataFormat("#,##0.00#");
			exportCol.setDataType("number");
			exportCol.setWidth(20);		
			exportCols.add(exportCol);*/
			
			TreeMap<String, IExportCol> kpiColsMap = new TreeMap<String, IExportCol>();
			Map<String, Object> current = new HashMap<String, Object>();
			List<Map<String, Object>> newDataList = new ArrayList<Map<String,Object>>();
			Map<String, String> existsColMap = new HashMap<String, String>();
			
			Map<String, Object> params = reportViewBizService.buildRequestParams(request);
			WxTUser curUser = ContextUtil.getCurUser();
			
			int permissionWeight = operationPermissionBizService.getPermissionWeight(curUser.getUserId(), "CAI_KPI_INFO");
			if(1 == (permissionWeight&1)) {
				permissionWeight = 1;
			}else if(4 == (permissionWeight&4)) {
				permissionWeight = 4;
			}else {
				return null;
			}
			String year = (String) params.get("year");
            year= "2020";
			JsonResponse queryTmmByCondition = tmmKpiServiceImpl.queryTmmByCondition(year);
			List<TmmInfoVo> tmmInfoVos = (List<TmmInfoVo>) queryTmmByCondition.get("resultLst");
			if(CollectionUtil.isEmpty(tmmInfoVos)){
				return null;
			}
			for (TmmInfoVo tmmInfoVo : tmmInfoVos) {
				Map<String, Object> tmmMap = (Map<String, Object>) current.get(tmmInfoVo.getDsrId().toString());
				if (null == tmmMap || tmmMap.isEmpty()) {
					tmmMap = new HashMap<String, Object>();
					tmmMap.put("chRegionName", tmmInfoVo.getChRegionName());
					tmmMap.put("customerNameCn", tmmInfoVo.getCustomerNameCn());
					tmmMap.put("chName", tmmInfoVo.getChName());
					tmmMap.put("ytdTmmFactVisit", tmmInfoVo.getYtdTmmFactVisit());
					tmmMap.put("ytdTmmAimsVisist", tmmInfoVo.getYtdTmmAimsVisist());
					tmmMap.put("ytdTmmFactEntry", tmmInfoVo.getYtdTmmFactEntry());
					tmmMap.put("ytdTmmFactPotentialEntry", tmmInfoVo.getYtdTmmFactPotentialEntry());
					current.put(tmmInfoVo.getDsrId().toString(), tmmMap);
				}
				if(null == existsColMap.get("YTD")) {
					existsColMap.put("YTD", "YTD");
					exportCol = new ExportCol("ytdTmmFactVisit", "实际拜访");
					exportCol.setParentsPath("YTD");
					exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
					exportCol.setDataType("number");
					exportCol.setWidth(20);
					exportCols.add(exportCol);
					
					exportCol = new ExportCol("ytdTmmAimsVisist", "目标拜访");
					exportCol.setParentsPath("YTD");
					exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
					exportCol.setDataType("number");
					exportCol.setWidth(20);
					exportCols.add(exportCol);
					
					exportCol = new ExportCol("ytdTmmFactPotentialEntry", "新增潜在客户");
					exportCol.setParentsPath("YTD");
					exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
					exportCol.setDataType("number");
					exportCol.setWidth(20);
					exportCols.add(exportCol);
					
					exportCol = new ExportCol("ytdTmmFactEntry", "新增合作客户");
					exportCol.setParentsPath("YTD");
					exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
					exportCol.setDataType("number");
					exportCol.setWidth(20);
					exportCols.add(exportCol);
				}
				
				List<TmmInfoDecVo> tmmInfoDecList = tmmInfoVo.getTmmInfoDecList();
				for (TmmInfoDecVo tmmInfoDecVo : tmmInfoDecList) {
					
					tmmMap.put(tmmInfoDecVo.getMon()+"/tmmFactVisit", tmmInfoDecVo.getTmmFactVisit());
					tmmMap.put(tmmInfoDecVo.getMon()+"/tmmAimsVisist", tmmInfoDecVo.getTmmAimsVisist());
					tmmMap.put(tmmInfoDecVo.getMon()+"/tmmFactPotentialEntry", tmmInfoDecVo.getTmmFactPotentialEntry());
					tmmMap.put(tmmInfoDecVo.getMon()+"/tmmFactEntry", tmmInfoDecVo.getTmmFactEntry());
					current.put(tmmInfoVo.getDsrId().toString(), tmmMap);
					
					String mon = tmmInfoDecVo.getMon()+"月";
					if(null == existsColMap.get(mon)) {
						exportCol = new ExportCol(tmmInfoDecVo.getMon()+"/tmmFactVisit", "实际拜访");
						exportCol.setParentsPath(mon);
						exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
						exportCol.setDataType("number");
						exportCol.setWidth(20);
						kpiColsMap.put(tmmInfoDecVo.getMon()+"/AtmmFactVisit", exportCol);
						
						exportCol = new ExportCol(tmmInfoDecVo.getMon()+"/tmmAimsVisist", "目标拜访");
						exportCol.setParentsPath(mon);
						exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
						exportCol.setDataType("number");
						exportCol.setWidth(20);
						kpiColsMap.put(tmmInfoDecVo.getMon()+"/BtmmAimsVisist", exportCol);
						
						exportCol = new ExportCol(tmmInfoDecVo.getMon()+"/tmmFactPotentialEntry", "新增潜在客户");
						exportCol.setParentsPath(mon);
						exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
						exportCol.setDataType("number");
						exportCol.setWidth(20);
						kpiColsMap.put(tmmInfoDecVo.getMon()+"/CtmmFactPotentialEntry", exportCol);
						
						exportCol = new ExportCol(tmmInfoDecVo.getMon()+"/tmmFactEntry", "新增合作客户");
						exportCol.setParentsPath(mon);
						exportCol.setPropertyHelper(IPropertyHelper.mapHelper);
						exportCol.setDataType("number");
						exportCol.setWidth(20);
						kpiColsMap.put(tmmInfoDecVo.getMon()+"/DtmmFactEntry", exportCol);
					}
				}
			}
			exportCols.addAll(kpiColsMap.values());
			newDataList.addAll((Collection)current.values());
			CommonUtil.setExportResponseHeader(request, response, "TMM KPI详情" + DateUtil.getCurrentDate("yyyyMMdd"), "xlsx");
			PoiWriteExcel.exportLargeData(newDataList, exportCols, response.getOutputStream(), "sheet1", true);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			request.setAttribute("errorMsg", "Export Failed: " + e.getMessage());
			return "forward:/common/jsp/downloadError.jsp";
		}
		return null;
	}

    @RequestMapping(value = "/excel/export.do")
    public String exportExcel(HttpServletRequest request, HttpServletResponse response,String provideTime){
	    try {
            provideTime= "2020-12";
            TmmBonusCalParam tmmBonusCalParam = new TmmBonusCalParam();
            tmmBonusCalParam.setProvideTime(provideTime);
            List<TmmCollectKpiDTO> tmmCollectKpiDTOS = dsrKpiService.cdmTmmBonusDetail(tmmBonusCalParam);
            List<TmmCollectKpiDTO> tmms = new ArrayList<TmmCollectKpiDTO>();
            TmmCollectKpiDTO sum = null;
            for (TmmCollectKpiDTO tmmCollectKpiDTO : tmmCollectKpiDTOS) {
                if(!tmmCollectKpiDTO.getAggregated()){
                    tmms.add(tmmCollectKpiDTO);
                }else{
                    sum = tmmCollectKpiDTO;
                }
            }
            HashSet<String> citys = new HashSet<String>();
            for (CityTotalAmount cityTotalAmount : sum.getCityTotalAmount()) {
                citys.add(cityTotalAmount.getCity());
            }
            HashMap<String, Object> root = new HashMap<String, Object>();
            root.put("tmms", tmms);
            root.put("data", sum);
            root.put("citys", citys);
            root.put("year", provideTime.substring(0, 4));
            Configuration configuration = new Configuration();
            configuration.setDefaultEncoding("UTF-8");
            configuration.setServletContextForTemplateLoading(request.getServletContext(),"/WEB-INF/templates/");
            Template template = configuration.getTemplate("/performance/cdm_tmm_performance.ftl");
            response.setContentType("application/vnd.ms-excel;charset=UTF-8");
            response.addHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(provideTime+"金富力TMM绩效统计", "UTF-8") + ".xls");
            template.process(root, new OutputStreamWriter(response.getOutputStream(), "UTF-8"));
            return null;
        }catch (Exception e) {
            log.error(e.getMessage(), e);
            request.setAttribute("errorMsg", "Export Failed: " + e.getMessage());
            return "forward:/common/jsp/downloadError.jsp";
        }
    }

    @RequestMapping(value = "/kpiInfo/dsr.do")
    @ResponseBody
    public JsonResponse tmmInfoBydsrIds(@RequestBody TmmBonusCalParam tmmBonusCalParam) {
        JsonResponse jsonResponse = new JsonResponse();
        try {
            List<TmmCollectKpiDTO> tmmCollectKpiDTOS = dsrKpiService.cdmTmmBonusDetail(tmmBonusCalParam);
            jsonResponse.setListResult(tmmCollectKpiDTOS);
        } catch (WxPltException e) {
            e.printStackTrace();
            jsonResponse.setReponseStatus(ResponseStatus.WARNING);
            jsonResponse.setErrorMsg(e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            jsonResponse.setReponseStatus(ResponseStatus.ERROR_EXCEPTION);
            jsonResponse.setErrorMsg("系统错误，请稍后再试。");
        }
        return jsonResponse;
    }

    @ResponseBody
    @RequestMapping(value = "/kpi.do")
    public JsonResponse tmmKpiInfo(String provideTime){
        provideTime = "2020-12";
        JsonResponse jsonResponse = new JsonResponse();
        try {
            TmmBonusCalParam tmmBonusCalParam = new TmmBonusCalParam();
            tmmBonusCalParam.setProvideTime(provideTime);
            List<TmmCollectKpiDTO> tmmCollectKpiDTOS = dsrKpiService.cdmTmmBonusDetail(tmmBonusCalParam);
            jsonResponse.setListResult(tmmCollectKpiDTOS);
        }catch (WxPltException e){
            e.printStackTrace();
            jsonResponse.setReponseStatus(ResponseStatus.WARNING);
            jsonResponse.setErrorMsg(e.getMessage());
        }catch (Exception e) {
            e.printStackTrace();
            jsonResponse.setReponseStatus(ResponseStatus.ERROR_EXCEPTION);
            jsonResponse.setErrorMsg("系统错误，请稍后再试。");
        }
        return jsonResponse;
    }

    @ResponseBody
    @RequestMapping(value = "/check/grant.do")
    public JsonResponse grant(String provideTime){
        JsonResponse jsonResponse = new JsonResponse();
        try {
            Boolean flag = dsrKpiService.grant(provideTime);
            jsonResponse.setDataResult(flag);
            final String time = provideTime;
            new Thread(){
                public void run() {
                    dsrKpiService.sendTmmGrantEmail(time);
                };
            }.start();
        }catch (WxPltException e){
            e.printStackTrace();
            jsonResponse.setReponseStatus(ResponseStatus.WARNING);
            jsonResponse.setErrorMsg(e.getMessage());
        }catch (Exception e) {
            e.printStackTrace();
            jsonResponse.setReponseStatus(ResponseStatus.ERROR_EXCEPTION);
            jsonResponse.setErrorMsg("系统错误，请稍后再试。");
        }
        return jsonResponse;
    }

    @RequestMapping(value = "/check/grantFlag.do")
    @ResponseBody
    public JsonResponse grantFlag(String provideTime){
        JsonResponse jsonResponse = new JsonResponse();
        try {
            Map<String, Boolean> map = dsrKpiService.checkGrantFlag(provideTime);
            jsonResponse.putAll(map);
        }catch (WxPltException e){
            e.printStackTrace();
            jsonResponse.setReponseStatus(ResponseStatus.WARNING);
            jsonResponse.setErrorMsg(e.getMessage());
        }catch (Exception e) {
            e.printStackTrace();
            jsonResponse.setReponseStatus(ResponseStatus.ERROR_EXCEPTION);
            jsonResponse.setErrorMsg("系统错误，请稍后再试。");
        }
        return jsonResponse;
    }

    @ResponseBody
    @RequestMapping(value = "/confirm.do")
    public JsonResponse tmmKpiConfirm(String provideTime){
        JsonResponse jsonResponse = new JsonResponse();
        try {
            dsrKpiService.tmmKpiConfirm(provideTime);
            jsonResponse.setDataResult(true);
        }catch (Exception e) {
            e.printStackTrace();
            jsonResponse.setReponseStatus(ResponseStatus.ERROR_EXCEPTION);
            jsonResponse.setErrorMsg("系统错误，请稍后再试。");
        }
        return jsonResponse;
    }

    @RequestMapping(value = "/check/confirmFlag.do")
    @ResponseBody
    public JsonResponse confirmFlag(String provideTime){
        JsonResponse jsonResponse = new JsonResponse();
        try {
            Map<String, Boolean> map = dsrKpiService.checkConfirmFlag(provideTime);
            jsonResponse.putAll(map);
        }catch (Exception e) {
            e.printStackTrace();
            jsonResponse.setReponseStatus(ResponseStatus.ERROR_EXCEPTION);
            jsonResponse.setErrorMsg("系统错误，请稍后再试。");
        }
        return jsonResponse;
    }


}
