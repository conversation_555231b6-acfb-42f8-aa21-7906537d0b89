package com.chevron.pms.service.impl;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import com.common.exception.auth.WxAuthException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import com.chevron.importdata.ImportDataPageModelUtil;
import com.chevron.interfaces.model.MessageVo;
import com.chevron.master.business.WorkshopMasterBizService;
import com.chevron.master.model.WorkshopMaster;
import com.chevron.plc.dao.OutStockProductVoMapper;
import com.chevron.plc.dao.OutStockVoMapper;
import com.chevron.plc.model.OutStockProductDetaiView;
import com.chevron.plc.model.OutStockProductVo;
import com.chevron.plc.model.OutStockVo;
import com.chevron.plc.model.OutStockVoExample;
import com.chevron.pms.business.OrderVoBizService;
import com.chevron.pms.dao.OrderLineVoMapper;
import com.chevron.pms.dao.OrderVoMapper;
import com.chevron.pms.dao.PartnerAddressMapper;
import com.chevron.pms.dao.ProductVoMapper;
import com.chevron.pms.model.OrderCondition;
import com.chevron.pms.model.OrderLineVo;
import com.chevron.pms.model.OrderParams;
import com.chevron.pms.model.OrderVo;
import com.chevron.pms.model.OrderVoExample;
import com.chevron.pms.model.OrderVoExample.Criteria;
import com.chevron.pms.model.PartnerAddress;
import com.chevron.pms.model.ProductVo;
import com.chevron.pms.service.OrderBizService;
import com.chevron.pms.service.OrderService;
import com.chevron.pms.service.WorkShopService;
import com.chevron.task.model.WxTaskMain;
import com.chevron.task.model.WxTaskMb;
import com.chevron.task.model.app.Task;
import com.chevron.task.service.AppTaskService;
import com.chevron.thirdorder.business.WebThirdOrderService;
import com.common.config.MyPropertyConfigurer;
import com.common.constants.Constants;
import com.common.exception.WxPltException;
import com.common.util.CommonUtil;
import com.common.util.ContextUtil;
import com.common.util.DateUtil;
import com.common.util.DateUtils;
import com.common.util.JsonResponse;
import com.common.util.JsonUtil;
import com.common.util.MessageResourceUtil;
import com.common.util.ResponseMap;
import com.common.util.ResponseStatus;
import com.common.util.StringUtils;
import com.sys.auth.business.OperationPermissionBizService;
import com.sys.auth.model.WxTUser;
import com.sys.organization.dao.OrganizationVoMapper;
import com.sys.organization.model.PartnerView;
import com.sys.properties.service.SequenceTypes;
import com.sys.properties.service.WxTPropertiesService;

import cn.hutool.core.collection.CollectionUtil;
import org.springframework.util.CollectionUtils;

@Service
public class OrderServiceImpl implements OrderService {

	private Logger log = LoggerFactory.getLogger(OrderServiceImpl.class);

	@Autowired
	private OrderVoMapper orderVoMapper;

	@Autowired
	private OrderLineVoMapper orderLineVoMapper;

	@Autowired
	private OrderBizService orderBizService;
	
	@Resource
	WebThirdOrderService webDDBXService;
	
	@Resource
	WorkShopService workShopService;
	
	@Autowired
	OutStockVoMapper outStockMapper;
	
	@Autowired
	OutStockProductVoMapper outStockProductMapper;
	
	@Autowired
	OrganizationVoMapper parnerMapper;
	
	@Resource
	private WxTPropertiesService propertiesServiceImpl;
	
	@Autowired
	private AppTaskService appTaskService;

	@Autowired
	private PartnerAddressMapper addressMapper;
	
	@Autowired
	private WorkshopMasterBizService workshopMasterBizService;
	
	@Autowired
	private ProductVoMapper productVoMapper;
	
	@Autowired
	private OrderVoBizService orderVoBizService;
	
	@Autowired
	private OperationPermissionBizService operationPermissionBizService;
	
	public final static String SUCCESS_CODE = "success";
	public final static String ERROR_CODE = "error";
	
	public final static String RESULT_CODE_KEY = "code";
	public final static String RESULT_LST_KEY = "resultLst";
	public final static String RESULT_ERROR_MSG_KEY = "errorMsg";
	@Override
	public Map<String, Object> queryOrders(String[] paramNames,
			String[] paramValues) {
		log.info("paramNames: {}", (Object) paramNames);
		log.info("paramValues: {}", (Object) paramValues);
		Map<String, Object> resultMap = new HashMap<String, Object>();

		Map<String, Object> paramMap = new HashMap<String, Object>();
		if (paramNames != null && paramValues != null) {
			if (paramNames.length != paramValues.length) {
				resultMap.put("code", "error");
				resultMap.put("msg", "参数个数与参数值个数不一致!");
				return resultMap;
			}

			for (int i = 0; i < paramNames.length; i++) {
				if (!StringUtils.isNull(paramNames[i])) {
					paramMap.put(paramNames[i], paramValues[i]);
				}
			}
		}
		List<OrderVo> orderList = orderVoMapper
				.queryOrdersByCondition(paramMap);
		resultMap.put("code", "success");
		resultMap.put("resultlst", orderList);

		return resultMap;
	}

	@Override
	public Map<String, Object> queryOrderLinesByOrderId(String[] orderIds) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		if (orderIds == null || orderIds.length == 0) {
			resultMap.put("code", "error");
			resultMap.put("codeMsg", "no order id input!");
			return resultMap;
		}
		Map<String, Object> paramMap = new HashMap<String, Object>();
		List<String> orderIdList = new ArrayList<String>();
		for (String orderId : orderIds) {
			orderIdList.add(orderId);
		}
		paramMap.put("orderIdList", orderIdList);
		List<OrderLineVo> orderLineList = orderLineVoMapper
				.queryOrderLinesByCondition(paramMap);
		resultMap.put("code", "success");
		resultMap.put("resultlst", orderLineList);

		return resultMap;
	}

	@Override
	@Transactional(rollbackFor = { WxPltException.class }, readOnly = false, propagation = Propagation.REQUIRES_NEW)
	public Map<String, Object> confirmPotentialOrders(String[] orderIds,
			OrderLineVo[] orderLines) {
		Map<String, Object> resultMap = new HashMap<String, Object>();

		if (orderIds == null || orderIds.length == 0 || orderLines == null
				|| orderLines.length == 0) {
			resultMap.put("code", "error");
			resultMap.put("codeMsg", "illegal parameter");
			return resultMap;
		}

		OrderVo potentialOrder = getOrderById(orderIds[0]);

		try {
			// 1, create actual order
			OrderVo actualOrder = orderBizService.createActualOrder(
					potentialOrder.getWorkShopId(), orderLines);

			// 2, update potential order
			if (actualOrder != null) {
				updatePotentialOrderWithActualOrderInfo(orderIds, actualOrder);
			}

			resultMap.put("code", "success");
		} catch (WxPltException e) {
			TransactionAspectSupport.currentTransactionStatus()
					.setRollbackOnly();
			resultMap.put("code", "error");
			resultMap.put("codeMsg", e.getExpMsg());
		}
		return resultMap;
	}

	private void updatePotentialOrderWithActualOrderInfo(String[] orderIds,
			OrderVo actualOrder) {
		if (orderIds == null || orderIds.length == 0) {
			return;
		}
		for (String orderId : orderIds) {
			OrderVo potentialOrder = new OrderVo();
			potentialOrder.setId(Long.valueOf(orderId));
			potentialOrder
					.setReferenceOrderType(OrderBizService.ORDER_REFERENCE_TYPE_ACTUAL);
			potentialOrder.setReferenceOrderNo(actualOrder.getOrderNo());
			potentialOrder.setStatus("1");
			potentialOrder.setUpdateTime(DateUtil.getCurrentDate());
			orderVoMapper.updateByPrimaryKeySelective(potentialOrder);
		}
	}

	private OrderVo getOrderById(String orderId) {
		Map<String, Object> paramMap = new HashMap<String, Object>();
		paramMap.put("orderId", orderId);
		List<OrderVo> potentialOrderList = orderVoMapper
				.queryOrdersByCondition(paramMap);
		if (potentialOrderList != null && potentialOrderList.size() == 1) {
			OrderVo potentialOrder = potentialOrderList.get(0);
			return potentialOrder;
		}
		return null;
	}

	@Override
	public MessageVo cancelOrderByPlateNumber(String plateNumber)
			throws Exception {
		MessageVo messageVo = new MessageVo();

		List<OrderVo> orderVoList = queryOrderByPlateNumber(plateNumber);
		if (null == orderVoList || orderVoList.size() == 0) {
			messageVo.setFlag("F");
			messageVo.setMessage("No data for " + plateNumber);
		} else {
			OrderVo orderVo = orderVoList.get(0);
			orderVo.setStatus("6"); // 确认订单取消状态值。并且确认订单取消的情况。
			orderVoMapper.updateByPrimaryKey(orderVo);
			messageVo.setFlag("T");
			messageVo.setMessage("cancel success!");
		}
		return messageVo;
	}

	@Override
	public List<OrderVo> queryOrderByPlateNumber(String plateNumber) {

		OrderVoExample example = new OrderVoExample();
		Criteria criteria = example.createCriteria();
		criteria.andPlateNumberEqualTo(plateNumber);
		criteria.andStatusEqualTo("3");
		return orderVoMapper.selectByExample(example);
	}

	@SuppressWarnings("unchecked")
	@Override
	// @Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
	public MessageVo modifyOrderByPlateNumber(Map<String, Object> reqMap)
			throws Exception {
		MessageVo msgVo = new MessageVo();
		msgVo.setFlag("success");
		// 0.查询billid运单号不为null status为5判断是否已发货
		List<OrderVo> lstOrder = orderVoMapper
				.queryOrderLstForThridAppUse(reqMap);
		if (lstOrder.size() > 0) {
			msgVo.setFlag("error");
			msgVo.setMessage("this order has sended!!!");
			return msgVo;
		}
		// 1.根据车牌号获取最近时间的订单，用于进行修改
		List<OrderVo> modifyOrderVoLst = new ArrayList<OrderVo>();
		lstOrder = (List<OrderVo>) reqMap.get("lstOrders");
		for (OrderVo orderVo : lstOrder) {
			reqMap.put("plateNumber", orderVo.getPlateNumber());
			reqMap.put("nowdate", new Date());
			List<OrderVo> lstTempOrder = orderVoMapper
					.getOrdersByMoreConditionForDDBXInterface(reqMap);
			if (null != lstTempOrder && lstTempOrder.size() > 0) {
				orderVo.setId(lstTempOrder.get(0).getId());
				modifyOrderVoLst.add(orderVo);
			}
		}
		if (modifyOrderVoLst.size() == 0) {
			msgVo.setFlag("error");
			msgVo.setMessage("There is no find the corresponding order can be modified");
			return msgVo;
		}
		reqMap.put("lstOrders", modifyOrderVoLst);
		// 2.进行订单数据修改
		orderVoMapper.updateOrderBatchForThridAppUse(reqMap);
		return msgVo;
	}

	@Override
	public List<OrderVo> queryOrderByPlateNumberEffectTimeStatusList(
			String plateNumber, Date effectTime, List<String> statusList) {
		OrderVoExample example = new OrderVoExample();
		Criteria criteria = example.createCriteria();
		criteria.andPlateNumberEqualTo(plateNumber);
		criteria.andStatusIn(statusList);
		criteria.andCreateTimeBetween(effectTime, addOneYear(effectTime));
		return orderVoMapper.selectByExample(example);
	}

	private Date addOneYear(Date effectTime) {
		Calendar cal = Calendar.getInstance();
		cal.setTime(effectTime);
		cal.add(Calendar.YEAR, 1);
		Date invalidTime = cal.getTime();
		return invalidTime;
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public Map<String, Object> insertSpOrder(OrderVo order,OrderLineVo[] orderLines) throws Exception {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		// 解析orderLines
		Double totalProductPrice = Double.valueOf(0);
		for (OrderLineVo newOrderLineVo : orderLines)// 原始的
		{
			if ("机油".equals(newOrderLineVo.getType())) {
				newOrderLineVo.setType("JY");
			} else if ("机油添加剂".equals(newOrderLineVo.getType())) {
				newOrderLineVo.setType("YYY");
			} else if ("礼盒".equals(newOrderLineVo.getType())) {
				newOrderLineVo.setType("LH");
			} else if ("其他".equals(newOrderLineVo.getType())) {
				newOrderLineVo.setType("QT");
			}
			if (newOrderLineVo.getPrice() != null
					&& newOrderLineVo.getPrice() > 0
					&& newOrderLineVo.getActualAmount() != null
					&& newOrderLineVo.getActualAmount() > 0) {
				Double productPriceAcount = newOrderLineVo.getPrice()
						* newOrderLineVo.getActualAmount();
				totalProductPrice = productPriceAcount + totalProductPrice;
			}
			newOrderLineVo.setAmount(newOrderLineVo.getActualAmount());
			//newOrderLineVo.setStatus(OrderVo.ORDER_STATUS_1);//已经确认
			newOrderLineVo.setStatus(OrderVo.ORDER_STATUS_10);//已经确认
		}
		/*Map<String,Object> orderNoResult = webDDBXService.createOrderNo(OrderVo.ORDER_CODE_SP);
		String code = (String)orderNoResult.get(WebThirdOrderService.RESULT_CODE_KEY);
		if(!code.equals(MessageContants.SUCCESS_CODE))
		{
			resultMap.put(WebThirdOrderService.RESULT_CODE_KEY, code);
			String errorMsg = (String)orderNoResult.get(WebThirdOrderService.RESULT_ERROR_MSG_KEY);
			resultMap.put(WebThirdOrderService.RESULT_ERROR_MSG_KEY, errorMsg);
			return resultMap;
		}
		String orderNoTemp = (String)orderNoResult.get("orderNo");*/
		//order.setOrderNo(CommonUtil.generateCode("SPO"));
		
		String sequenceNo =  propertiesServiceImpl.getSequenceByType(SequenceTypes.SP_WORKSHOP_ORDER,6,1);
		order.setOrderNo(CommonUtil.generateOrderCode(4)+sequenceNo);
		
		//order.setOrderNo(CommonUtil.generateOrderCode(4));
		order.setTotalProductPrice(totalProductPrice);
		order.setTotalOrderPrice(totalProductPrice);
		order.setStatus(OrderVo.ORDER_STATUS_10);//已确认
		// 插入订单
		order.setOrderType(OrderVo.PA_ORDER_TYPE);
		order.setSource(OrderVo.PA_ORDER_SOURCE);
		order.setSourceId(OrderVo.PA_ORDER_SOURCE_ID);
		order.setCreator(String.valueOf(ContextUtil.getCurUserId()));
		order.setCreateTime(new Date());
		order.setUpdateTime(new Date());
		order.setPartnerConfirmTime(new Date());
		orderVoMapper.insertSelective(order);
		List<OrderLineVo> newOrderLineVoLst = new ArrayList<OrderLineVo>();// 要插入的
		for (OrderLineVo newOrderLineVo : orderLines)// 原始的
		{
			newOrderLineVo.setOrderId(order.getId());// 指定的订单id
			newOrderLineVoLst.add(newOrderLineVo);
		}

		// 5.批量插入orderline记录 //进行分页
		ImportDataPageModelUtil newOrderLinePage = new ImportDataPageModelUtil(
				newOrderLineVoLst, 100);
		int toltalPage = newOrderLinePage.getTotalPages();
		for (int i = 1; i <= toltalPage; i++) {

			@SuppressWarnings("unchecked")
			List<OrderLineVo> insertOrderLineLst = newOrderLinePage.getObjects(i);
			orderLineVoMapper.insertOrderLineBatch(insertOrderLineLst);

		}
		//生成出库单
		Map<String, Object> createOutStock = createOutStock(order, newOrderLineVoLst);
		//生产待销量确认任务
		String  stockOutNo = (String) createOutStock.get("stockOutNo");//出库单详情id
		Task task = new Task();
		task.setOrderNo(stockOutNo);
		task.setWorkshopId(order.getWorkShopId());
		task.setTemplateCode(WxTaskMain.TASK_TYPE_CDM_DXLQD);
		Map<String, Object> queryTemplateList = appTaskService.queryTemplateList(WxTaskMain.TASK_TYPE_CDM_DXLQD);
		@SuppressWarnings("unchecked")
		List<WxTaskMb> list = (List<WxTaskMb>) queryTemplateList.get("templateList");
		task.setTaskTemplateId(list.get(0).getMbId());
		task.setTaskStartTime(new Date());
		task.setTaskFinishTime(new Date());
		appTaskService.createTask(task);
		//判断此门店是否有门店联系人 联系人电话  ，没有则更新相关信息
		Long workshopId = order.getWorkShopId();
		WorkshopMaster workshop = workshopMasterBizService.getBean(workshopId);
		if(StringUtils.isEmpty(workshop.getContactPerson()) || StringUtils.isEmpty(workshop.getContactPersonTel())){
			WorkshopMaster record = new WorkshopMaster();
			record.setId(workshopId);
			record.setContactPerson(order.getReceiveUserName());
			record.setContactPersonTel(order.getReceivePhoneNo());
			workshopMasterBizService.update(record);
		}
		resultMap.put("result", "success");
		return resultMap;
	}
	
	
	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public Map<String, Object> insertOrUpdateSpOrder(OrderVo order,OrderLineVo[] orderLines,String type) throws Exception {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		// 解析orderLines
		Double totalProductPrice = Double.valueOf(0);
		for (OrderLineVo newOrderLineVo : orderLines)// 原始的
		{
			if ("机油".equals(newOrderLineVo.getType())) {
				newOrderLineVo.setType("JY");
			} else if ("机油添加剂".equals(newOrderLineVo.getType())) {
				newOrderLineVo.setType("YYY");
			} else if ("礼盒".equals(newOrderLineVo.getType())) {
				newOrderLineVo.setType("LH");
			} else if ("其他".equals(newOrderLineVo.getType())) {
				newOrderLineVo.setType("QT");
			}
			if (newOrderLineVo.getPrice() != null
					&& newOrderLineVo.getPrice() > 0
					&& newOrderLineVo.getActualAmount() != null
					&& newOrderLineVo.getActualAmount() > 0) {
				Double productPriceAcount = newOrderLineVo.getPrice()
						* newOrderLineVo.getActualAmount();
				totalProductPrice = productPriceAcount + totalProductPrice;
			}
			newOrderLineVo.setAmount(newOrderLineVo.getActualAmount());
			//newOrderLineVo.setStatus(OrderVo.ORDER_STATUS_1);//已经确认
			if(type.equals(OrderVo.ORDER_STATUS_TEMPORARY)) {
				newOrderLineVo.setStatus(OrderVo.ORDER_STATUS_12);//已经确认
			}else {
				newOrderLineVo.setStatus(OrderVo.ORDER_STATUS_10);//已经确认
			}
		}
		/*Map<String,Object> orderNoResult = webDDBXService.createOrderNo(OrderVo.ORDER_CODE_SP);
		String code = (String)orderNoResult.get(WebThirdOrderService.RESULT_CODE_KEY);
		if(!code.equals(MessageContants.SUCCESS_CODE))
		{
			resultMap.put(WebThirdOrderService.RESULT_CODE_KEY, code);
			String errorMsg = (String)orderNoResult.get(WebThirdOrderService.RESULT_ERROR_MSG_KEY);
			resultMap.put(WebThirdOrderService.RESULT_ERROR_MSG_KEY, errorMsg);
			return resultMap;
		}
		String orderNoTemp = (String)orderNoResult.get("orderNo");*/
		//order.setOrderNo(CommonUtil.generateCode("SPO"));
		
		if(null == order.getOrderNo()) {
			String sequenceNo =  propertiesServiceImpl.getSequenceByType(SequenceTypes.SP_WORKSHOP_ORDER,6,1);
			order.setOrderNo(CommonUtil.generateOrderCode(4)+sequenceNo);
		}
		
		//order.setOrderNo(CommonUtil.generateOrderCode(4));
		order.setTotalProductPrice(totalProductPrice);
		order.setTotalOrderPrice(totalProductPrice);
		if(type.equals(OrderVo.ORDER_STATUS_TEMPORARY)) {
			order.setStatus(OrderVo.ORDER_STATUS_12);//暂存
		}else {
			order.setStatus(OrderVo.ORDER_STATUS_10);//已确认
		}
		// 插入订单
		if(null == order.getOrderType()) {
			order.setOrderType(OrderVo.PA_ORDER_TYPE);
		}
		if(null == order.getSource()) {
			order.setSource(OrderVo.PA_ORDER_SOURCE);
		}
		if(null == order.getSourceId()) {
			order.setSourceId(OrderVo.PA_ORDER_SOURCE_ID);
		}
		order.setCreator(String.valueOf(ContextUtil.getCurUserId()));
		order.setCreateTime(new Date());

		if(null == order.getId()) {
			orderVoMapper.insertSelective(order);
		}else {
			orderVoMapper.updateByPrimaryKey(order); //更改order
		}
		List<OrderLineVo> newOrderLineVoLst = new ArrayList<OrderLineVo>();// 要插入的
		List<OrderLineVo> upDateOrderLineVoLst = new ArrayList<OrderLineVo>();// 要更改的的
		for (OrderLineVo newOrderLineVo : orderLines)// 原始的
		{
			if(null == newOrderLineVo.getOrderId() || newOrderLineVo.getOrderId() == 0) {
				newOrderLineVo.setOrderId(order.getId());// 指定的订单id
				newOrderLineVoLst.add(newOrderLineVo);
			}else {
				upDateOrderLineVoLst.add(newOrderLineVo);
			}
		}

		// 5.批量插入orderline记录 //进行分页
		ImportDataPageModelUtil newOrderLinePage = new ImportDataPageModelUtil(
				newOrderLineVoLst, 100);
		int toltalPage = newOrderLinePage.getTotalPages();
		for (int i = 1; i <= toltalPage; i++) {
			@SuppressWarnings("unchecked")
			List<OrderLineVo> insertOrderLineLst = newOrderLinePage.getObjects(i);
			orderLineVoMapper.insertOrderLineBatch(insertOrderLineLst);

		}
		
		// 6.批量更新orderline记录 //进行分页
		if(CollectionUtil.isNotEmpty(upDateOrderLineVoLst)) {
			for (OrderLineVo orderLineVo : upDateOrderLineVoLst) {
				orderLineVoMapper.updateByPrimaryKeySelective(orderLineVo);
			}
		}
		
		if(OrderVo.ORDER_STATUS_INSERT.equals(type)) {
			//生成出库单
			Map<String, Object> createOutStock = createOutStock(order, newOrderLineVoLst);
			//生产待销量确认任务
			String  stockOutNo = (String) createOutStock.get("stockOutNo");//出库单详情id
			Task task = new Task();
			task.setOrderNo(stockOutNo);
			task.setWorkshopId(order.getWorkShopId());
			task.setTemplateCode(WxTaskMain.TASK_TYPE_CDM_DXLQD);
			Map<String, Object> queryTemplateList = appTaskService.queryTemplateList(WxTaskMain.TASK_TYPE_CDM_DXLQD);
			@SuppressWarnings("unchecked")
			List<WxTaskMb> list = (List<WxTaskMb>) queryTemplateList.get("templateList");
			task.setTaskTemplateId(list.get(0).getMbId());
			task.setTaskStartTime(new Date());
			task.setTaskFinishTime(new Date());
			appTaskService.createTask(task);
		}
		
		//判断此门店是否有门店联系人 联系人电话  ，没有则更新相关信息
		Long workshopId = order.getWorkShopId();
		WorkshopMaster workshop =  workshopMasterBizService.getBean(workshopId);
		if(StringUtils.isEmpty(workshop.getContactPerson()) || StringUtils.isEmpty(workshop.getContactPersonTel())){
			WorkshopMaster record = new WorkshopMaster();
			record.setId(workshopId);
			record.setContactPerson(order.getReceiveUserName());
			record.setContactPersonTel(order.getReceivePhoneNo());
			workshopMasterBizService.update(record);
		}
		resultMap.put("result", "success");
		return resultMap;
	}
	
	@Override
	public Map<String, Object> getOrdersAndOrderLinesByOrderID(String orderId)
			throws Exception {
		Map<String,Object> resultMap = new HashMap<String,Object>();
		Map<String,Object> reqMap = new HashMap<String,Object>();
		//0.查询订单信息
		reqMap.put("orderId", Long.parseLong(orderId));
		OrderVo orderVo = orderVoMapper.getOrderContainPartnerInfoByOrderId(reqMap);
		if(orderVo.getAddress() == null || orderVo.getAddress().equals("")){
			Map<String,Object> workshopMap = workShopService.getWorkShopAndAdressDetailById(orderVo.getWorkShopId());
			if(workshopMap.get("workshop") != null && workshopMap.get("address") != null){
				WorkshopMaster workshop = (WorkshopMaster)workshopMap.get("workshop");
				orderVo.setAddress((String)workshopMap.get("address") + workshop.getWorkShopAddress());
			}
		}
		resultMap.put("orderVo", orderVo);
		//1.获取订单行信息
		List<OrderLineVo> lstOrderLinevo = orderLineVoMapper.getOrderLinesByConditionForDDBXOrder(reqMap);
		//2.获取出库单实际发货情况
		List<OutStockProductDetaiView> outstockproductDetailList = outStockProductMapper.selectOutStockProductByOrderNo(orderVo.getOrderNo(), "workshop");
		List<OrderLineVo> lstOrderLineWithStockInfo = new ArrayList<OrderLineVo>();
		if(outstockproductDetailList == null || outstockproductDetailList.isEmpty()){
			lstOrderLineWithStockInfo = lstOrderLinevo;
		}else{
			for(OutStockProductDetaiView osp : outstockproductDetailList){
				OrderLineVo orderLineTemp = new OrderLineVo();
				orderLineTemp.setSku(osp.getSku());
				orderLineTemp.setProductName(osp.getProductName());
				if(osp.getExpectOutCount() != null){
					orderLineTemp.setAmount(osp.getExpectOutCount().intValue());
				}
				if(osp.getActualOutCount() != null){
					orderLineTemp.setActualOutCount(osp.getActualOutCount().intValue());
				}				
				lstOrderLineWithStockInfo.add(orderLineTemp);
			}
		}
		resultMap.put("lstOrderLinevo", lstOrderLinevo);
		resultMap.put("lstOustockOrderLinevo", lstOrderLineWithStockInfo);
		resultMap.put("code", "success");
		return resultMap;
	}
	
	@Override
	@Transactional(propagation=Propagation.REQUIRED, rollbackFor=Exception.class)
	public Map<String, Object> modifyOrderAndOrderLineStatusByOrderId(
			String orderId, OrderLineVo[] orderLines,String modifyType) {
		Map<String,Object> resultMap = new HashMap<String,Object>();
		Map<String,Object> reqMap = new HashMap<String,Object>();
		//resultMap.put("orderId", orderId);//回显用于查询
		resultMap.put("code", "success");
		resultMap.put("isgotoOrderMainPage", "false");
		//如果比是全部取消，那么关闭窗口时候不需要刷新列表查询
		reqMap.put("isColseRefresh","false");
		try
		{
			
			OrderVo potentialOrder = getOrderById(orderId);
			if(!potentialOrder.getStatus().equals(OrderVo.ORDER_STATUS_0)){
				resultMap.put("code", "error");
				resultMap.put("codeMsg", "订单状态已更新，请勿重复提交!");
				return resultMap;
			}
			
			
			Long[] orderLineIds = new Long[orderLines.length];
			List<Long> idList = new ArrayList<Long>();
			for(OrderLineVo orderLine : orderLines){
				idList.add(orderLine.getId());
			}
			orderLineIds = idList.toArray(orderLineIds);
			
			//0.判断是取消还是确认操作，如果是取消就删除订单行信息
			reqMap.put("orderLineIds", orderLineIds);
			reqMap.put("orderId",Long.parseLong(orderId));
			if(modifyType.equals("0"))
			{
				orderLineVoMapper.delteBatchOrderLineByIds(reqMap);
				List<OrderLineVo> lstOrderLineVo = orderLineVoMapper.getOrderLinesByConditionForDDBXOrder(reqMap);
				if(null==lstOrderLineVo || lstOrderLineVo.isEmpty())//如果全部取消
				{
					//修改订单状态为已取消操作
					reqMap.put("status", "6");//已取消
					orderVoMapper.updateOrderByCondition(reqMap);
					resultMap.put("isgotoOrderMainPage", "true");
				}else
				{
					//需要更新订单的总价
					double totalProductPrice = 0.00;
					for(OrderLineVo orderLineVo: lstOrderLineVo)
					{
						if (orderLineVo.getPrice() != null
								&& orderLineVo.getPrice() > 0
								&& orderLineVo.getAmount()!= null
								&& orderLineVo.getAmount() > 0) {
							Double productPriceAcount = orderLineVo.getPrice()
									* orderLineVo.getActualAmount();
							totalProductPrice += productPriceAcount;
						}
					}
					reqMap.put("totalProductPrice",totalProductPrice);
					orderVoMapper.updateOrderByCondition(reqMap);
				}
				
				
				return resultMap;
			}
			//确认操作
			//1.更新订单行状态
			reqMap.put("status",OrderVo.ORDER_STATUS_1);//更新已确认状态
			for(OrderLineVo orderLine : orderLines){
				OrderLineVo updateObj = new OrderLineVo();
				updateObj.setId(orderLine.getId());
				updateObj.setAmount(orderLine.getAmount());
				updateObj.setActualAmount(orderLine.getAmount());
				updateObj.setStatus(OrderVo.ORDER_STATUS_1);
				updateObj.setUpdateTime(new Date());
				orderLineVoMapper.confirmAmountAndStatus(updateObj);
			}
			/*orderLineVoMapper.updateBatchOrderLineByIds(reqMap);*/
			
			//2.查询对应的订单行状态是否全部确认
			List<OrderLineVo> lstOrderLineVo = orderLineVoMapper.getOrderLinesByConditionForDDBXOrder(reqMap);
			boolean isAllConfirm = true;//默认以为是全部为确认了
			for(OrderLineVo orderLineVo: lstOrderLineVo)
			{
				if(orderLineVo.getStatus().equals(OrderVo.ORDER_STATUS_0))
				{
					isAllConfirm = false;
				}
			}
			
			//3.如果存在全部确认，那么修改订单状态为1 已确认
			if(isAllConfirm)
			{
				
				reqMap.put("orderType", "A");
				reqMap.put("isColseRefresh","true");
				resultMap.put("isgotoOrderMainPage", "true");
				orderVoMapper.updateOrderByCondition(reqMap);//.updateByPrimaryKeySelective(orderVo);
				//生成出库单
				OrderVo order = getOrderById(orderId);
				createOutStock(order, lstOrderLineVo);
			}
			
			
		}catch(Exception ex)
		{
			TransactionAspectSupport.currentTransactionStatus()
			.setRollbackOnly();
			resultMap.put("code", "error");
			resultMap.put("codeMsg", ex.getLocalizedMessage());
		}
		return resultMap;
	}
	

	
	@Override
	public Map<String, Object> queryOrdersByConditions(
			OrderCondition orderCondition) {
		Map<String,Object> resultMap = new HashMap<String,Object>();
		List<OrderVo> lstOrders = new ArrayList<OrderVo>();
		resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
		try
		{
			if(null==orderCondition)
			{
				resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
				resultMap.put(RESULT_ERROR_MSG_KEY,MessageResourceUtil.getMessage("system.params.is.null.error"));
				return resultMap;
			}
			String is_custommybatisintercepor =  MyPropertyConfigurer.getVal("IS_CUSTOMMYBATISINTERCEPOR");
			if("false".equals(is_custommybatisintercepor))
			{
				orderCondition.setIsOpernCustomMybatisInterceptor("2");//关闭的
			}
			
			//0.解析条件
			String orderType = orderCondition.getOrderType();
			if(StringUtils.isNull(orderType))
			{
				orderType = null;
				//过滤掉服务订单类型
				orderCondition.setNotqueryOrderTypeDA(OrderVo.DDFW_ORDER_TYPE);
				//过滤掉服务包订单类型
				orderCondition.setNotqueryOrderTypeDP(OrderVo.DDXB_ORDER_TYPE);
				//过滤掉合伙人网店管家发货的方式
				orderCondition.setNotqueryOrderTypeSPDA(OrderVo.SPID_ORDER_ES_TYPE);
			}
			
			orderCondition.setOrderType(orderType);
			String workShopName = orderCondition.getWorkshopName();
			orderCondition.setWorkshopName(StringUtils.isNull(workShopName) ? null : workShopName);
			
			String pDateFrom = orderCondition.getDateFrom();
			if (!StringUtils.isNull(pDateFrom)){
				Date dateFrom = DateUtils.stringToDate(pDateFrom, 3);
				orderCondition.setDateStart(dateFrom);
			}
			
			String pDateTo = orderCondition.getDateTo();
			if (!StringUtils.isNull(pDateTo)){
				Date dateTo = DateUtils.stringToDate(pDateTo, 3);
				orderCondition.setDateEnd(dateTo);
			}
			//合伙人
			String partnerId = orderCondition.getPartnerId();
			WxTUser currentUser = ContextUtil.getCurUser();
			Long mUserType = currentUser.getmUserTypes();
			Long mUserPartnerId = currentUser.getOrgId();
			if(mUserType==WxTUser.CHEVRON_USER_ROLE)
			{
				
				if(null == partnerId ||partnerId.equals("-999")||partnerId.length() == 0 )
				{
					mUserPartnerId = null;
				}else
				{
					mUserPartnerId = Long.parseLong(partnerId);
				}
			}
				
			orderCondition.setmPartnerId(mUserPartnerId);
			//订单状态
			String orderStatus = orderCondition.getOrderStatus();
			if(null==orderStatus || orderStatus.trim().equals("-1"))
			{
				orderStatus = null;
			}
			orderCondition.setOrderStatus(orderStatus);
			
			if(StringUtils.isNotBlank(orderCondition.getOrderStatusList())) {
				String[] split = orderCondition.getOrderStatusList().split(",");
				List<Long> b2bOrderStatusList = new ArrayList<Long>();
				List<Long> orderStatusList = new ArrayList<Long>();
				for (String status : split) {
					if(OrderVo.ORDER_STATUS_13.equals(status)) {
						orderCondition.setOutStatus(status);
						continue;
					}
					if(OrderVo.ORDER_STATUS_11.equals(status)) {
						orderCondition.setEndStatus(status);
						continue;
					}
					
					if(Long.valueOf(status)<2) {
						b2bOrderStatusList.add(Long.valueOf(status));
					}else {
						orderStatusList.add(Long.valueOf(status));
					}
				}
				orderCondition.setB2bOrderStatusList(b2bOrderStatusList);
				orderCondition.setOrderStatusLists(orderStatusList);
			}
			if("APP_OUTSTOCK".equals(orderCondition.getFunFlag())) {
				int permissionWeight = operationPermissionBizService.getPermissionWeight(currentUser.getUserId(), "App");
				if((permissionWeight & 256) > 0) {
					//物流商
					orderCondition.setExtProperty8(currentUser.getUserId().toString());
				}else {
					orderCondition.setCreator(currentUser.getUserId());
				}
			}
			
			
			//1.查询数据
			Long totalRecord = 0L;
			lstOrders = orderVoMapper.getCGOrdersByCondition(orderCondition);
			if("false".equals(is_custommybatisintercepor))//没有开数据权限
			{
				totalRecord = orderVoMapper.countGetCGOrdersByCondition(orderCondition);
			}else
			{
				
				totalRecord = orderCondition.getTotalCount();
			}
			resultMap.put(RESULT_LST_KEY, lstOrders);
			resultMap.put("totalRecord", totalRecord);
			
		}catch (Exception e) {
			log.error(e.getMessage());
			resultMap.put(RESULT_CODE_KEY, ERROR_CODE);
			resultMap.put(RESULT_ERROR_MSG_KEY, WxAuthException.System_Exception_msg);
		}
		return resultMap;
	}
	
	/**
	 * 创建出库单
	 */
	public Map<String, Object> createOutStock(OrderVo order, List<OrderLineVo> orderLines) throws Exception {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		
		// 订单状态更新成已发货
		Date curDate = new Date();
		order.setUpdateTime(curDate);
		//order.setStatus(OrderVo.ORDER_STATUS_1);
		order.setStatus(OrderVo.ORDER_STATUS_10);
		int count = orderVoMapper.updateByPrimaryKey(order);
		if (count != 1) {
			throw new Exception();
		}
		//获取订单全部信息
		if(order.getSource().equals(OrderVo.PA_ORDER_SOURCE_TASK)){
			Map<String,Object> reqMap = new HashMap<String,Object>();
			reqMap.put("orderId", order.getId());
			order = orderVoMapper.getOrderContainPartnerInfoByOrderId(reqMap);
			if(order.getAddress() == null || order.getAddress().equals("")){
				Map<String,Object> workshopMap = workShopService.getWorkShopAndAdressDetailById(order.getWorkShopId());
				if(workshopMap.get("workshop") != null && workshopMap.get("address") != null){
					WorkshopMaster workshop = (WorkshopMaster)workshopMap.get("workshop");
					order.setAddress((String)workshopMap.get("address") + workshop.getWorkShopAddress());
				}
			}
		}else{
			PartnerView partner = parnerMapper.selectPartnersByWorkshopId(order.getWorkShopId());
			order.setOrgId(partner.getId().toString());
			order.setOrgName(partner.getName());;
		}
		// 出库单生成
		OutStockVo outStockVo = new OutStockVo();
		outStockVo.setStockFrom(order.getOrgId());
		outStockVo.setStockFromType(Constants.STOCK_TYPE_SP);
		outStockVo.setStockTo(order.getWorkShopId().toString());
		outStockVo.setStockToType(Constants.STOCK_TYPE_WORKSHOP);
		outStockVo.setStockFromOrgname(order.getOrgName());
		outStockVo.setStockToOrgname(order.getWorkshopName());
		outStockVo.setStockToAddress(order.getAddress());
		outStockVo.setStockToPhone(order.getReceivePhoneNo());
		outStockVo.setStockToContactPerson(order.getReceiveUserName());
		outStockVo.setOrderNo(String.valueOf(order.getOrderNo()));
		outStockVo.setOrderType(com.common.constants.Constants.STOCK_ORDER_TYPE_WORKSHOP);
		outStockVo.setStatus(com.common.constants.Constants.STOCK_OUT_STATUS_DAICHUKU);
		outStockVo.setCreator(String.valueOf(ContextUtil.getCurUserId()));
		outStockVo.setCreateTime(curDate);
		//生成出库单号
		String sequenceNo =  propertiesServiceImpl.getSequenceByType(SequenceTypes.OUT_STOCKNO_CODE,4,1);
		outStockVo.setStockOutNo(CommonUtil.generateOrderCode(12)+sequenceNo);
		/*outStockVo.setStockOutNo(order.getOrderNo());*/
		count = outStockMapper.insertSelective(outStockVo);
		resultMap.put("stockOutNo", outStockVo.getStockOutNo());
		if (count != 1) {
			throw new Exception();
		}
		for(OrderLineVo ol:  orderLines){
			OutStockProductVo product = new OutStockProductVo();
			product.setStockOutNo(outStockVo.getStockOutNo());
			product.setSku(ol.getSku());
			product.setExpectOutCount(ol.getAmount().longValue());
			count = outStockProductMapper.insertSelective(product);
			if (count != 1) {
				throw new Exception();
			}
		}
		resultMap.put(RESULT_CODE_KEY, SUCCESS_CODE);
		return resultMap;
	}

	@Override
	public Map<String, Object> generataOrderOutstockPDF(Long orderId) throws Exception{
		Map<String,Object> resultMap = new HashMap<String,Object>();
		Map<String,Object> reqMap = new HashMap<String,Object>();
		//0.查询订单信息
		reqMap.put("orderId", orderId);
		OrderVo orderVo = orderVoMapper.getOrderContainPartnerInfoByOrderId(reqMap);
		if(orderVo.getAddress() == null || orderVo.getAddress().equals("")){
			Map<String,Object> workshopMap = workShopService.getWorkShopAndAdressDetailById(orderVo.getWorkShopId());
			if(workshopMap.get("workshop") != null && workshopMap.get("address") != null){
				WorkshopMaster workshop = (WorkshopMaster)workshopMap.get("workshop");
				orderVo.setAddress((String)workshopMap.get("address") + workshop.getWorkShopAddress());
			}
		}
		/*resultMap.put("orderVo", orderVo);*/
		//1.获取订单行信息
		List<OrderLineVo> lstOrderLinevo = orderLineVoMapper.getOrderLinesByConditionForDDBXOrder(reqMap);
		resultMap.put("lstOrderLinevo", lstOrderLinevo);
		//2.查询发货地址
		PartnerAddress address = new PartnerAddress();
		if(orderVo.getSpAddressId() != null){
			address = addressMapper.getAddressDetailById(orderVo.getSpAddressId());			
		}
		/*resultMap.put("deliverAddress", address);*/
		//3.查询出库单
		OutStockVoExample ose = new OutStockVoExample();
		OutStockVoExample.Criteria oseCriteria = ose.createCriteria();
		oseCriteria.andOrderNoEqualTo(orderVo.getOrderNo());
		List<OutStockVo> osList = outStockMapper.selectByExample(ose);
		OutStockVo oustock = osList.get(0);
		/*resultMap.put("outstock", osList.get(0));*/
		
		//插入门店地址信息
		resultMap.put("workshopContact", orderVo.getReceiveUserName());
		resultMap.put("workshopName", orderVo.getWorkshopName());
		resultMap.put("workshopAddress", orderVo.getAddress());
		resultMap.put("workshopMobile", orderVo.getReceivePhoneNo());
		
		//门店执行人信息
		Long workshopId = orderVo.getWorkShopId();
		Map<String, Object> params = new HashMap<String, Object>(5);
		params.put("id", workshopId);
		resultMap.put("excutor", workshopMasterBizService.querySimpleByParams(params).get(0).getExecuteUserName());
		//插入合伙人地址信息
		if(address.getId() != null){
			resultMap.put("partnerContactPerson", address.getContactPerson());
			resultMap.put("partnerName", orderVo.getOrgName());
			resultMap.put("partnerAddress", address.getAddress());
			resultMap.put("partnerProvince", address.getCityName());
			resultMap.put("partnerMobile", address.getMobile());
		}else {
			/*resultMap.put("partnerContactPerson", "");*/
			resultMap.put("partnerName", orderVo.getOrgName());
			/*resultMap.put("partnerAddress", "");
			resultMap.put("partnerProvince", "");
			resultMap.put("partnerMobile", "");*/
		}
		
		
		//插入出库单信息
		resultMap.put("outstockNo", oustock.getStockOutNo());
		resultMap.put("outstockOrderTime", DateUtil.getDateStr(orderVo.getCreateTime()));
		resultMap.put("outstockOrderNo", oustock.getOrderNo());
		
		
		return resultMap;
	}

	@Override
	public Map<String, Object> getO2oCardOrder(Long orderId) {
		Map<String, Object> map = new HashMap<String, Object>(5);
		try {
			map.put("order", orderVoMapper.getO2oCardOrder(orderId));
			map.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (Exception e) {
			e.printStackTrace();
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error(e.getMessage(), e);
		}
		return map;
	}

	@Override
	public Map<String, Object> getO2oOrder(Long orderId) {
		Map<String, Object> map = new HashMap<String, Object>(5);
		try {
			map.put("order", orderVoMapper.getO2oOrder(orderId));
			map.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (Exception e) {
			e.printStackTrace();
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error(e.getMessage(), e);
		}
		return map;
	}

	@Override
	public Map<String, Object> getWorkshopOrder(Long orderId) {
		Map<String, Object> map = new HashMap<String, Object>(5);
		try {
			OrderVo orderVo = orderVoMapper.getWorkshopOrder(orderId);
			if(orderVo != null){
				Map<String, Object> reqMap = new HashMap<String, Object>();
				reqMap.put("orderId", orderId);
				orderVo.setOrderLines(orderLineVoMapper.getOrderLinesByOrderId(reqMap));
			}
			List<OutStockVo> outstockList = outStockMapper.queryOutStockByOrderNo(orderVo.getOrderNo());
			if(outstockList != null && outstockList.size() > 0){
				OutStockVo outStock = outstockList.get(0);
				if(outStock.getStatus().equals(Constants.STOCK_OUT_STATUS_CHUKU)){
					List<OutStockProductDetaiView> productList = 
							outStockProductMapper.selectOutStockProductByStockOutNo(outStock.getStockOutNo());
					List<OrderLineVo> orderLines = new ArrayList<OrderLineVo>();
					for(OutStockProductDetaiView detail : productList){
						OrderLineVo ordreLine= new OrderLineVo();
						ProductVo product = productVoMapper.selectBySku(detail.getSku());
						ordreLine.setOrderId(orderId);
						ordreLine.setProductId(product.getId());
						ordreLine.setSku(detail.getSku());
						ordreLine.setProductName(product.getName());
						ordreLine.setAmount(detail.getActualOutCount() == null ? null : detail.getActualOutCount().intValue());
						ordreLine.setUnits(product.getUnits());
						ordreLine.setActualAmount(detail.getActualOutCount() == null ? null : detail.getActualOutCount().intValue());
						ordreLine.setType(product.getCategory());				
						ordreLine.setProductPhoto(String.valueOf(product.getIconId()));
						ordreLine.setCapacity(product.getCapacity());
						ordreLine.setViscosity(product.getViscosity());
						orderLines.add(ordreLine);
					}
					orderVo.setOrderLines(orderLines);
				}				
			}
			map.put("order", orderVo);
			map.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (Exception e) {
			e.printStackTrace();
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error(e.getMessage(), e);
		}
		return map;
	}

	@Override
	public Map<String, Object> queryOrderForApp(OrderParams params) {
		Map<String, Object> map = new HashMap<String, Object>(5);
		try {
			map.put(Constants.RESULT_LST_KEY, orderVoMapper.queryOrderForApp(params));
			map.put(Constants.RESULT_TOTAL_KEY, params.getTotalCount());
			map.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (Exception e) {
			e.printStackTrace();
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error(e.getMessage(), e);
		}
		return map;
	}

	@Override
	@Transactional(rollbackFor={WxPltException.class}, readOnly=false, propagation=Propagation.REQUIRES_NEW)
	public JsonResponse delOrdersAndOrderLinesByOrderIDs(String orderIds) {
		JsonResponse resMap = new JsonResponse();
		if(StringUtils.isBlank(orderIds)) {
			resMap.put(JsonResponse.KEY_CODE, ResponseStatus.WARNING.getCode());
			resMap.put(JsonResponse.KEY_ERROR_MSG, "请输入正确的订单号");
			return resMap;
		}
		String[] idStrings = orderIds.split(",");
		List<Long> orderIdList = new ArrayList<Long>(idStrings.length); 
		for(String id : idStrings) {
			if(StringUtils.isNotBlank(id)) {
				orderIdList.add(Long.parseLong(id));
			}
		}
		if(orderIdList.isEmpty()) {
			resMap.put(JsonResponse.KEY_CODE, ResponseStatus.WARNING.getCode());
			resMap.put(JsonResponse.KEY_ERROR_MSG, "请输入正确的订单号");
			return resMap;
		}
		Map<String, Object> reqMap = new HashMap<String, Object>();
		
		//查询orderNos
		List<OrderVo> orderInfoList = orderVoMapper.queryBatchByOrderId(orderIdList);
		List<String> orderNoList = new ArrayList<String>();
		for (OrderVo orderVo : orderInfoList) {
			orderNoList.add(orderVo.getOrderNo());
		}
		//批量更改出库订单状态
		if(!CollectionUtil.isEmpty(orderNoList)) {
			Map<String,Object> outStockMap = new HashMap<String, Object>();
			outStockMap.put("status", Constants.STOCK_OUT_STATUS_DEL);
			outStockMap.put("orderNoList", orderNoList);
			outStockMapper.updateStatusByOrderNo(outStockMap);
		}
		
		//批量删除订单信息
		reqMap.put("orderIds", orderIdList);
		orderVoMapper.delteBatchOrderByOrderIds(reqMap);
		//批量删除订单行
		reqMap.put("orderLineOrderIds", orderIdList);
		orderLineVoMapper.delteBatchOrderLineByOrderIds(reqMap);
		return resMap;
	}

	@Override
	@Transactional(rollbackFor={WxPltException.class}, readOnly=false, propagation=Propagation.REQUIRES_NEW)
	public JsonResponse cancelOrdersAndOrderLinesByOrderIDs(String orderIds) {
		JsonResponse resMap = new JsonResponse();
		if(StringUtils.isBlank(orderIds)) {
			resMap.put(JsonResponse.KEY_CODE, ResponseStatus.WARNING.getCode());
			resMap.put(JsonResponse.KEY_ERROR_MSG, "请输入正确的订单号");
			return resMap;
		}
		String[] idStrings = orderIds.split(",");
		List<Long> orderIdList = new ArrayList<Long>(idStrings.length);
		for(String id : idStrings) {
			if(StringUtils.isNotBlank(id)) {
				orderIdList.add(Long.parseLong(id));
			}
		}
		if(orderIdList.isEmpty()) {
			resMap.put(JsonResponse.KEY_CODE, ResponseStatus.WARNING.getCode());
			resMap.put(JsonResponse.KEY_ERROR_MSG, "请输入正确的订单号");
			return resMap;
		}
		//批量查询订单
		Map<String,Object> reqMap = new HashMap<String, Object>();
		reqMap.put("orderIds", orderIdList);
		reqMap.put("status", OrderVo.ORDER_STATUS_6); //已取消状态
		reqMap.put("updateTime", new Date()); //确认销量时间
		
		//查询orderNos
		List<OrderVo> orderInfoList = orderVoMapper.queryBatchByOrderId(orderIdList);
		List<String> orderNoList = new ArrayList<String>();
		for (OrderVo orderVo : orderInfoList) {
			orderNoList.add(orderVo.getOrderNo());
		}
		//批量更改出库订单状态
		if(!CollectionUtil.isEmpty(orderNoList)) {
			Map<String,Object> outStockMap = new HashMap<String, Object>();
			outStockMap.put("status", Constants.STOCK_OUT_STATUS_CANCEL);
			outStockMap.put("orderNoList", orderNoList);
			outStockMapper.updateStatusByOrderNo(outStockMap);
		}
		
		//批量更改订单状态
		orderVoMapper.updateAllOrderStatusByOrderIds(reqMap);
		orderLineVoMapper.updateAllOrderLineStatusByOrderIds(reqMap);
		return resMap;
	}

	@Override
	@Transactional(rollbackFor={WxPltException.class}, readOnly=false, propagation=Propagation.REQUIRES_NEW)
	public JsonResponse updateOrderPartnerTypeByPartnerConfirm(String orderIds,String partnerConfirmType) {
		JsonResponse resMap = new JsonResponse();
		if(StringUtils.isBlank(orderIds)) {
			resMap.put(JsonResponse.KEY_CODE, ResponseStatus.WARNING.getCode());
			resMap.put(JsonResponse.KEY_ERROR_MSG, "请输入正确的订单号");
			return resMap;
		}
		String[] idStrings = orderIds.split(",");
		List<Long> orderIdList = new ArrayList<Long>(idStrings.length);
		for(String id : idStrings) {
			if(StringUtils.isNotBlank(id)) {
				orderIdList.add(Long.parseLong(id));
			}
		}
		if(orderIdList.isEmpty()) {
			resMap.put(JsonResponse.KEY_CODE, ResponseStatus.WARNING.getCode());
			resMap.put(JsonResponse.KEY_ERROR_MSG, "请输入正确的订单号");
			return resMap;
		}
		//批量查询订单
		Map<String,Object> reqMap = new HashMap<String, Object>();
		reqMap.put("orderIds", orderIdList);
		reqMap.put("partnerConfirmType", partnerConfirmType); //确认销量状态
		reqMap.put("updateTime", new Date()); 
		reqMap.put("status", OrderVo.ORDER_STATUS_11); //销量已确定
		//批量更改订单状态
		orderVoMapper.updateAllOrderStatusByOrderIds(reqMap);
		
		
	/*	//更改商城订单已出库状态
		List<String> shopOrderList = orderVoMapper.getShopOrderByIds(orderIdList);
		if(CollectionUtil.isNotEmpty(shopOrderList)) {
			for (String string : shopOrderList) {
				
			}
		}*/
	
		return resMap;
	}

	@Override
	@Transactional(rollbackFor={WxPltException.class}, readOnly=false, propagation=Propagation.REQUIRES_NEW)
	public JsonResponse rejectOrderPartnerTypeByPartnerConfirm(String orderIds,String partnerConfirmType) {
		JsonResponse resMap = new JsonResponse();
		if(StringUtils.isBlank(orderIds)) {
			resMap.put(JsonResponse.KEY_CODE, ResponseStatus.WARNING.getCode());
			resMap.put(JsonResponse.KEY_ERROR_MSG, "请输入正确的订单号");
			return resMap;
		}
		String[] idStrings = orderIds.split(",");
		List<Long> orderIdList = new ArrayList<Long>(idStrings.length);
		for(String id : idStrings) {
			if(StringUtils.isNotBlank(id)) {
				orderIdList.add(Long.parseLong(id));
			}
		}
		if(orderIdList.isEmpty()) {
			resMap.put(JsonResponse.KEY_CODE, ResponseStatus.WARNING.getCode());
			resMap.put(JsonResponse.KEY_ERROR_MSG, "请输入正确的订单号");
			return resMap;
		}
		//批量查询订单
		Map<String,Object> reqMap = new HashMap<String, Object>();
		reqMap.put("orderIds", orderIdList);
		reqMap.put("partnerConfirmType", "0"); //确认销量状态
		reqMap.put("updateTime", new Date()); 
		reqMap.put("status", OrderVo.ORDER_STATUS_14); //销量已确定
		//批量更改订单状态
		orderVoMapper.updateAllOrderStatusByOrderIds(reqMap);

		return resMap;
	}

	@Override
	public JsonResponse getOrdersConfrimListByCondition(OrderCondition orderCondition) {
		JsonResponse resMap = new JsonResponse();
		WxTUser curUser = ContextUtil.getCurUser();
		//查询数据
		if(WxTUser.USER_MODEL_SP.equals(curUser.getUserModel())) {
			orderCondition.setPartnerId(curUser.getOrgId().toString());
		}
		String pDateFrom = orderCondition.getDateFrom();
		if (!StringUtils.isNull(pDateFrom)){
			Date dateFrom = DateUtils.stringToDate(pDateFrom, 3);
			orderCondition.setDateStart(dateFrom);
		}
		
		String pDateTo = orderCondition.getDateTo();
		if (!StringUtils.isNull(pDateTo)){
			Date dateTo = DateUtils.stringToDate(pDateTo, 3);
			orderCondition.setDateEnd(dateTo);
		}
		List<OrderVo> ordersConfrimList = orderVoMapper.getOrdersConfrimListByCondition(orderCondition);
		if(!CollectionUtils.isEmpty(ordersConfrimList)){
			for (OrderVo order : ordersConfrimList) {
				Double totalLiters = 0.00;
				//1.获取订单行信息
				Map<String,Object> reqMap = new HashMap<String,Object>();
				//0.查询订单信息
				reqMap.put("orderId", order.getId());
				List<OrderLineVo> orderLines = orderLineVoMapper.getOrderLinesByConditionForDDBXOrder(reqMap);
				if(!CollectionUtils.isEmpty(orderLines)){
					for (OrderLineVo orderLine : orderLines) {
						if(StringUtils.isNotBlank(orderLine.getCapacity()) && orderLine.getAmount() != null){
							totalLiters = totalLiters + Double.parseDouble(orderLine.getCapacity())*orderLine.getAmount();
						}
					}
				}
				order.setTotalLiters(totalLiters);
			}
		}
		resMap.setTotalOfPaging(orderCondition.getTotalCount());
		resMap.setListResult(ordersConfrimList);
		return resMap;
	}

	@Override
	public Map<String, Object> getOrdersSalesVolume(OrderCondition orderCondition) {
		Map<String,Object> resultMap = new HashMap<String,Object>();
		//权限控制
		WxTUser curUser = ContextUtil.getCurUser();
		//查询数据
		orderCondition = new OrderCondition();
		orderCondition.setPaging(false);
		orderCondition.closeOrder();
		if(WxTUser.USER_MODEL_SP.equals(curUser.getUserModel())) {
			orderCondition.setPartnerId(curUser.getOrgId().toString());
		}

		Double toConfirmTotalLiters = 0.00;
		Double confirmedTotalLiters = 0.00;
		orderCondition.setOrderStatus(OrderVo.ORDER_STATUS_10);
//		//1.获取订单行信息
//		List<OrderLineVo> toConfirmOrderLines = orderLineVoMapper.getOrderLinesByOrderStatus(orderCondition);
//		if(!CollectionUtils.isEmpty(toConfirmOrderLines)){
//			for (OrderLineVo orderLine : toConfirmOrderLines) {
//				if(StringUtils.isNotBlank(orderLine.getCapacity()) && orderLine.getAmount() != null){
//					toConfirmTotalLiters = toConfirmTotalLiters + Double.parseDouble(orderLine.getCapacity())*orderLine.getAmount();
//				}
//			}
//		}
		toConfirmTotalLiters = orderLineVoMapper.getOrderTotalLitersByOrderStatus(orderCondition);

		orderCondition.setOrderStatus(OrderVo.ORDER_STATUS_11);
		//1.获取订单行信息
//		List<OrderLineVo> confirmedOrderLines = orderLineVoMapper.getOrderLinesByOrderStatus(orderCondition);
//		if(!CollectionUtils.isEmpty(confirmedOrderLines)){
//			for (OrderLineVo orderLine : confirmedOrderLines) {
//				if(StringUtils.isNotBlank(orderLine.getCapacity()) && orderLine.getAmount() != null){
//					confirmedTotalLiters = confirmedTotalLiters + Double.parseDouble(orderLine.getCapacity())*orderLine.getAmount();
//				}
//			}
//		}
		confirmedTotalLiters = orderLineVoMapper.getOrderTotalLitersByOrderStatus(orderCondition);

		resultMap.put("toConfirmTotalLiters",toConfirmTotalLiters);
		resultMap.put("confirmedTotalLiters",confirmedTotalLiters);

		return resultMap;
	}

	@Override
	public ResponseMap saveByEcrm(OrderVo record) {
		ResponseMap map = new ResponseMap();
		log.info("save: " + JsonUtil.writeValue(record));
		try {
			record.setSource(OrderVo.ORDER_SOURCE_ETO);
			record.setStatus(OrderVo.ORDER_STATUS_10);
			orderVoBizService.insert(record);
			map.put("orderNo", record.getOrderNo());
			log.info("save success." );
		} catch (WxPltException e) {
			map.setReponseStatus(ResponseStatus.ERROR_ILLEGAL_INPUT);
			map.setErrorMsg(e.getMessage());
		} catch (Exception e) {
			map.handleException(e, this, ContextUtil.getCurUserId(), 
					"com.chevron.pms.service.impl.OrderServiceImpl.saveByEcrm", JsonUtil.writeValue(record));
		}
		return map;
	}


}
