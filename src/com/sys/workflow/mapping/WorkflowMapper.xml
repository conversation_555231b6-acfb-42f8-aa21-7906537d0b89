<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sys.workflow.dao.WorkflowMapper">
	<!-- 结果集映射 -->
	<resultMap id="BaseResultMap" type="com.sys.workflow.model.Workflow">
		<id column="workflow_code" property="workflowCode" jdbcType="VARCHAR"/>
		<result column="workflow_name" property="workflowName" jdbcType="VARCHAR"/>
		<result column="handle_bean" property="handleBean" jdbcType="VARCHAR"/>
		<result column="page_themes" property="pageThemes" jdbcType="VARCHAR"/>
		<result column="workflow_version" property="workflowVersion" jdbcType="INTEGER"/>
		<result column="end_notify" property="endNotify" jdbcType="INTEGER"/>
		<result column="end_notify_template" property="endNotifyTemplate" jdbcType="VARCHAR"/>
		<result column="end_email_cc" property="endEmailCc" jdbcType="VARCHAR"/>
		<result column="def_todo_email_temp" property="defTodoEmailTemp" jdbcType="VARCHAR"/>
		<result column="def_reject_notify_template" property="defRejectNotifyTemplate" jdbcType="VARCHAR"/>
		<result column="sms_notify_template" property="smsNotifyTemplate" jdbcType="VARCHAR"/>
		<result column="def_sms_notify_temp" property="defSmsNotifyTemp" jdbcType="VARCHAR"/>
		<result column="flow_status" property="flowStatus" jdbcType="INTEGER"/>
		<result column="property_view_weight1" property="propertyViewWeight1" jdbcType="BIGINT"/>
		<result column="property_edit_weight1" property="propertyEditWeight1" jdbcType="BIGINT"/>
		<result column="property_required_weight1" property="propertyRequiredWeight1" jdbcType="BIGINT"/>
		<result column="property_view_weight2" property="propertyViewWeight2" jdbcType="BIGINT"/>
		<result column="property_edit_weight2" property="propertyEditWeight2" jdbcType="BIGINT"/>
		<result column="property_required_weight2" property="propertyRequiredWeight2" jdbcType="BIGINT"/>
		<result column="property_view_weight3" property="propertyViewWeight3" jdbcType="BIGINT"/>
		<result column="property_edit_weight3" property="propertyEditWeight3" jdbcType="BIGINT"/>
		<result column="property_required_weight3" property="propertyRequiredWeight3" jdbcType="BIGINT"/>
		<result column="delete_flag" property="deleteFlag" jdbcType="INTEGER"/>
		<result column="create_user_id" property="createUserId" jdbcType="BIGINT"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_user_id" property="updateUserId" jdbcType="BIGINT"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
	</resultMap>
	
	<!-- 查询Example条件 -->
	<sql id="Example_Where_Clause">
		<where>
			<foreach collection="oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 更新操作Example条件 -->
	<sql id="Update_By_Example_Where_Clause">
		<where>
			<foreach collection="example.oredCriteria" item="criteria" separator="or">
				<if test="criteria.valid">
					<trim prefix="(" suffix=")" prefixOverrides="and">
						<foreach collection="criteria.criteria" item="criterion">
							<choose>
								<when test="criterion.noValue">
									and ${criterion.condition}
								</when>
								<when test="criterion.singleValue">
									and ${criterion.condition} #{criterion.value}
								</when>
								<when test="criterion.betweenValue">
									and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
								</when>
								<when test="criterion.listValue">
									and ${criterion.condition}
									<foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
										#{listItem}
									</foreach>
								</when>
							</choose>
						</foreach>
					</trim>
				</if>
			</foreach>
		</where>
	</sql>
	
	<!-- 查询表字段集合 -->
	<sql id="Base_Column_List">
		workflow_code,workflow_name,handle_bean,page_themes,workflow_version,end_notify,end_notify_template,end_email_cc,
		def_todo_email_temp,def_reject_notify_template,sms_notify_template,def_sms_notify_temp,flow_status,
		property_view_weight1,property_edit_weight1,property_required_weight1,property_view_weight2,property_edit_weight2,
		property_required_weight2,property_view_weight3,property_edit_weight3,property_required_weight3,delete_flag,
		create_user_id,create_time,update_user_id,update_time
	</sql>

	<!-- 修改页面根据主键更新记录编辑字段 -->
	<update id="updateForEditPage" parameterType="com.sys.workflow.model.Workflow">
		update wx_t_workflow set
				workflow_name = #{workflowName,jdbcType=VARCHAR},
				handle_bean = #{handleBean,jdbcType=VARCHAR},
				page_themes = #{pageThemes,jdbcType=VARCHAR},
				workflow_version = #{workflowVersion,jdbcType=INTEGER},
				end_notify = #{endNotify,jdbcType=INTEGER},
				end_notify_template = #{endNotifyTemplate,jdbcType=VARCHAR},
				end_email_cc = #{endEmailCc,jdbcType=VARCHAR},
				def_todo_email_temp = #{defTodoEmailTemp,jdbcType=VARCHAR},
				def_reject_notify_template = #{defRejectNotifyTemplate,jdbcType=VARCHAR},
				sms_notify_template = #{smsNotifyTemplate,jdbcType=VARCHAR},
				def_sms_notify_temp = #{defSmsNotifyTemp,jdbcType=VARCHAR},
				update_user_id = #{updateUserId,jdbcType=BIGINT},
				update_time = #{updateTime,jdbcType=TIMESTAMP}
		where workflow_code = #{workflowCode,jdbcType=VARCHAR}
	</update>
	
	<!-- 根据主键更新记录有值字段 -->
	<update id="updateByPrimaryKeySelective" parameterType="com.sys.workflow.model.Workflow">
		update wx_t_workflow
		<set>
			<if test="workflowName != null" >
				workflow_name = #{workflowName,jdbcType=VARCHAR},
			</if>
			<if test="handleBean != null" >
				handle_bean = #{handleBean,jdbcType=VARCHAR},
			</if>
			<if test="pageThemes != null" >
				page_themes = #{pageThemes,jdbcType=VARCHAR},
			</if>
			<if test="workflowVersion != null" >
				workflow_version = #{workflowVersion,jdbcType=INTEGER},
			</if>
			<if test="endNotify != null" >
				end_notify = #{endNotify,jdbcType=INTEGER},
			</if>
			<if test="endNotifyTemplate != null" >
				end_notify_template = #{endNotifyTemplate,jdbcType=VARCHAR},
			</if>
			<if test="endEmailCc != null" >
				end_email_cc = #{endEmailCc,jdbcType=VARCHAR},
			</if>
			<if test="defTodoEmailTemp != null" >
				def_todo_email_temp = #{defTodoEmailTemp,jdbcType=VARCHAR},
			</if>
			<if test="defRejectNotifyTemplate != null" >
				def_reject_notify_template = #{defRejectNotifyTemplate,jdbcType=VARCHAR},
			</if>
			<if test="smsNotifyTemplate != null" >
				sms_notify_template = #{smsNotifyTemplate,jdbcType=VARCHAR},
			</if>
			<if test="defSmsNotifyTemp != null" >
				def_sms_notify_temp = #{defSmsNotifyTemp,jdbcType=VARCHAR},
			</if>
			<if test="flowStatus != null" >
				flow_status = #{flowStatus,jdbcType=INTEGER},
			</if>
			<if test="propertyViewWeight1 != null" >
				property_view_weight1 = #{propertyViewWeight1,jdbcType=BIGINT},
			</if>
			<if test="propertyEditWeight1 != null" >
				property_edit_weight1 = #{propertyEditWeight1,jdbcType=BIGINT},
			</if>
			<if test="propertyRequiredWeight1 != null" >
				property_required_weight1 = #{propertyRequiredWeight1,jdbcType=BIGINT},
			</if>
			<if test="propertyViewWeight2 != null" >
				property_view_weight2 = #{propertyViewWeight2,jdbcType=BIGINT},
			</if>
			<if test="propertyEditWeight2 != null" >
				property_edit_weight2 = #{propertyEditWeight2,jdbcType=BIGINT},
			</if>
			<if test="propertyRequiredWeight2 != null" >
				property_required_weight2 = #{propertyRequiredWeight2,jdbcType=BIGINT},
			</if>
			<if test="propertyViewWeight3 != null" >
				property_view_weight3 = #{propertyViewWeight3,jdbcType=BIGINT},
			</if>
			<if test="propertyEditWeight3 != null" >
				property_edit_weight3 = #{propertyEditWeight3,jdbcType=BIGINT},
			</if>
			<if test="propertyRequiredWeight3 != null" >
				property_required_weight3 = #{propertyRequiredWeight3,jdbcType=BIGINT},
			</if>
			<if test="deleteFlag != null" >
				delete_flag = #{deleteFlag,jdbcType=INTEGER},
			</if>
			<if test="createUserId != null" >
				create_user_id = #{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null" >
				create_time = #{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null" >
				update_user_id = #{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null" >
				update_time = #{updateTime,jdbcType=TIMESTAMP},
			</if>
		</set>
		where workflow_code = #{workflowCode,jdbcType=VARCHAR}
	</update>
	
	<!-- 根据主键删除记录 -->
	<delete id="deleteByPrimaryKey" parameterType="com.sys.workflow.model.WorkflowExample">
    	delete from wx_t_workflow
		where workflow_code = #{workflowCode,jdbcType=VARCHAR}
	</delete>

	<!-- 插入记录有值字段 -->
	<insert id="insertSelective" parameterType="com.sys.workflow.model.Workflow">
		insert into wx_t_workflow
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="workflowCode != null">
				workflow_code,
			</if>
			<if test="workflowName != null">
				workflow_name,
			</if>
			<if test="handleBean != null">
				handle_bean,
			</if>
			<if test="pageThemes != null">
				page_themes,
			</if>
			<if test="workflowVersion != null">
				workflow_version,
			</if>
			<if test="endNotify != null">
				end_notify,
			</if>
			<if test="endNotifyTemplate != null">
				end_notify_template,
			</if>
			<if test="endEmailCc != null">
				end_email_cc,
			</if>
			<if test="defTodoEmailTemp != null">
				def_todo_email_temp,
			</if>
			<if test="defRejectNotifyTemplate != null">
				def_reject_notify_template,
			</if>
			<if test="smsNotifyTemplate != null">
				sms_notify_template,
			</if>
			<if test="defSmsNotifyTemp != null">
				def_sms_notify_temp,
			</if>
			<if test="flowStatus != null">
				flow_status,
			</if>
			<if test="propertyViewWeight1 != null">
				property_view_weight1,
			</if>
			<if test="propertyEditWeight1 != null">
				property_edit_weight1,
			</if>
			<if test="propertyRequiredWeight1 != null">
				property_required_weight1,
			</if>
			<if test="propertyViewWeight2 != null">
				property_view_weight2,
			</if>
			<if test="propertyEditWeight2 != null">
				property_edit_weight2,
			</if>
			<if test="propertyRequiredWeight2 != null">
				property_required_weight2,
			</if>
			<if test="propertyViewWeight3 != null">
				property_view_weight3,
			</if>
			<if test="propertyEditWeight3 != null">
				property_edit_weight3,
			</if>
			<if test="propertyRequiredWeight3 != null">
				property_required_weight3,
			</if>
			<if test="deleteFlag != null">
				delete_flag,
			</if>
			<if test="createUserId != null">
				create_user_id,
			</if>
			<if test="createTime != null">
				create_time,
			</if>
			<if test="updateUserId != null">
				update_user_id,
			</if>
			<if test="updateTime != null">
				update_time,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="workflowCode != null">
				#{workflowCode,jdbcType=VARCHAR},
			</if>
			<if test="workflowName != null">
				#{workflowName,jdbcType=VARCHAR},
			</if>
			<if test="handleBean != null">
				#{handleBean,jdbcType=VARCHAR},
			</if>
			<if test="pageThemes != null">
				#{pageThemes,jdbcType=VARCHAR},
			</if>
			<if test="workflowVersion != null">
				#{workflowVersion,jdbcType=INTEGER},
			</if>
			<if test="endNotify != null">
				#{endNotify,jdbcType=INTEGER},
			</if>
			<if test="endNotifyTemplate != null">
				#{endNotifyTemplate,jdbcType=VARCHAR},
			</if>
			<if test="endEmailCc != null">
				#{endEmailCc,jdbcType=VARCHAR},
			</if>
			<if test="defTodoEmailTemp != null">
				#{defTodoEmailTemp,jdbcType=VARCHAR},
			</if>
			<if test="defRejectNotifyTemplate != null">
				#{defRejectNotifyTemplate,jdbcType=VARCHAR},
			</if>
			<if test="smsNotifyTemplate != null">
				#{smsNotifyTemplate,jdbcType=VARCHAR},
			</if>
			<if test="defSmsNotifyTemp != null">
				#{defSmsNotifyTemp,jdbcType=VARCHAR},
			</if>
			<if test="flowStatus != null">
				#{flowStatus,jdbcType=INTEGER},
			</if>
			<if test="propertyViewWeight1 != null">
				#{propertyViewWeight1,jdbcType=BIGINT},
			</if>
			<if test="propertyEditWeight1 != null">
				#{propertyEditWeight1,jdbcType=BIGINT},
			</if>
			<if test="propertyRequiredWeight1 != null">
				#{propertyRequiredWeight1,jdbcType=BIGINT},
			</if>
			<if test="propertyViewWeight2 != null">
				#{propertyViewWeight2,jdbcType=BIGINT},
			</if>
			<if test="propertyEditWeight2 != null">
				#{propertyEditWeight2,jdbcType=BIGINT},
			</if>
			<if test="propertyRequiredWeight2 != null">
				#{propertyRequiredWeight2,jdbcType=BIGINT},
			</if>
			<if test="propertyViewWeight3 != null">
				#{propertyViewWeight3,jdbcType=BIGINT},
			</if>
			<if test="propertyEditWeight3 != null">
				#{propertyEditWeight3,jdbcType=BIGINT},
			</if>
			<if test="propertyRequiredWeight3 != null">
				#{propertyRequiredWeight3,jdbcType=BIGINT},
			</if>
			<if test="deleteFlag != null">
				#{deleteFlag,jdbcType=INTEGER},
			</if>
			<if test="createUserId != null">
				#{createUserId,jdbcType=BIGINT},
			</if>
			<if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId != null">
				#{updateUserId,jdbcType=BIGINT},
			</if>
			<if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
			</if>
		</trim>
	</insert>
	
	<!-- 更新满足Example条件的有值字段 -->
	<update id="updateByExampleSelective" parameterType="map">
		update wx_t_workflow
		<set>
			<if test="record.workflowName != null">
				workflow_name = #{record.workflowName,jdbcType=VARCHAR},
			</if>
			<if test="record.handleBean != null">
				handle_bean = #{record.handleBean,jdbcType=VARCHAR},
			</if>
			<if test="record.pageThemes != null">
				page_themes = #{record.pageThemes,jdbcType=VARCHAR},
			</if>
			<if test="record.workflowVersion != null">
				workflow_version = #{record.workflowVersion,jdbcType=INTEGER},
			</if>
			<if test="record.endNotify != null">
				end_notify = #{record.endNotify,jdbcType=INTEGER},
			</if>
			<if test="record.endNotifyTemplate != null">
				end_notify_template = #{record.endNotifyTemplate,jdbcType=VARCHAR},
			</if>
			<if test="record.endEmailCc != null">
				end_email_cc = #{record.endEmailCc,jdbcType=VARCHAR},
			</if>
			<if test="record.defTodoEmailTemp != null">
				def_todo_email_temp = #{record.defTodoEmailTemp,jdbcType=VARCHAR},
			</if>
			<if test="record.defRejectNotifyTemplate != null">
				def_reject_notify_template = #{record.defRejectNotifyTemplate,jdbcType=VARCHAR},
			</if>
			<if test="record.smsNotifyTemplate != null">
				sms_notify_template = #{record.smsNotifyTemplate,jdbcType=VARCHAR},
			</if>
			<if test="record.defSmsNotifyTemp != null">
				def_sms_notify_temp = #{record.defSmsNotifyTemp,jdbcType=VARCHAR},
			</if>
			<if test="record.flowStatus != null">
				flow_status = #{record.flowStatus,jdbcType=INTEGER},
			</if>
			<if test="record.propertyViewWeight1 != null">
				property_view_weight1 = #{record.propertyViewWeight1,jdbcType=BIGINT},
			</if>
			<if test="record.propertyEditWeight1 != null">
				property_edit_weight1 = #{record.propertyEditWeight1,jdbcType=BIGINT},
			</if>
			<if test="record.propertyRequiredWeight1 != null">
				property_required_weight1 = #{record.propertyRequiredWeight1,jdbcType=BIGINT},
			</if>
			<if test="record.propertyViewWeight2 != null">
				property_view_weight2 = #{record.propertyViewWeight2,jdbcType=BIGINT},
			</if>
			<if test="record.propertyEditWeight2 != null">
				property_edit_weight2 = #{record.propertyEditWeight2,jdbcType=BIGINT},
			</if>
			<if test="record.propertyRequiredWeight2 != null">
				property_required_weight2 = #{record.propertyRequiredWeight2,jdbcType=BIGINT},
			</if>
			<if test="record.propertyViewWeight3 != null">
				property_view_weight3 = #{record.propertyViewWeight3,jdbcType=BIGINT},
			</if>
			<if test="record.propertyEditWeight3 != null">
				property_edit_weight3 = #{record.propertyEditWeight3,jdbcType=BIGINT},
			</if>
			<if test="record.propertyRequiredWeight3 != null">
				property_required_weight3 = #{record.propertyRequiredWeight3,jdbcType=BIGINT},
			</if>
			<if test="record.deleteFlag != null">
				delete_flag = #{record.deleteFlag,jdbcType=INTEGER},
			</if>
			<if test="record.createUserId != null">
				create_user_id = #{record.createUserId,jdbcType=BIGINT},
			</if>
			<if test="record.createTime != null">
				create_time = #{record.createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="record.updateUserId != null">
				update_user_id = #{record.updateUserId,jdbcType=BIGINT},
			</if>
			<if test="record.updateTime != null">
				update_time = #{record.updateTime,jdbcType=TIMESTAMP},
			</if>
		</set>
		<if test="_parameter != null">
			<include refid="Update_By_Example_Where_Clause"/>
		</if>
	</update>
	
	<!-- 删除满足Example条件的记录 -->
	<delete id="deleteByExample" parameterType="com.sys.workflow.model.WorkflowExample">
		delete from wx_t_workflow
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</delete>
	
	<!-- 统计满足Example条件的记录条数 -->
	<select id="countByExample" parameterType="com.sys.workflow.model.WorkflowExample" resultType="int">
		select count(1) from wx_t_workflow
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
	</select>
	
	<!-- 查询满足Example条件的记录集合 -->
	<select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sys.workflow.model.WorkflowExample">
		select
		<if test="distinct">
			distinct
		</if>
		'true' as QUERYID,
		<include refid="Base_Column_List"/>
		from wx_t_workflow
		<if test="_parameter != null">
			<include refid="Example_Where_Clause"/>
		</if>
		<if test="orderByClause != null">
			order by ${orderByClause}
		</if>
	</select>

	<!-- 根据主键查询记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="com.sys.workflow.model.WorkflowExample">
    	select
		<include refid="Base_Column_List"/>
		from wx_t_workflow
		where workflow_code = #{workflowCode,jdbcType=VARCHAR}
	</select>

	<!-- 列表查询 -->
	<select id="queryByParams" resultMap="BaseResultMap" parameterType="map">
		select t1.workflow_code, t1.workflow_name, t1.handle_bean, t1.page_themes, t1.workflow_version, t1.end_notify,
			 t1.end_notify_template, t1.end_email_cc, t1.def_todo_email_temp, t1.def_reject_notify_template,
			 t1.sms_notify_template, t1.def_sms_notify_temp, t1.flow_status, t1.property_view_weight1, t1.property_edit_weight1,
			 t1.property_required_weight1, t1.property_view_weight2, t1.property_edit_weight2, t1.property_required_weight2,
			 t1.property_view_weight3, t1.property_edit_weight3, t1.property_required_weight3, t1.delete_flag,
			 t1.create_user_id, t1.create_time, t1.update_user_id, t1.update_time
		  from wx_t_workflow t1
		 where 1=1
		<if test="workflowCode != null and workflowCode != ''">
			and t1.workflow_code like '%' + #{workflowCode, jdbcType=VARCHAR} + '%'
		</if>
		<if test="workflowName != null and workflowName != ''">
			and t1.workflow_name like '%' + #{workflowName, jdbcType=VARCHAR} + '%'
		</if>
	</select>

	<!-- 分页查询 -->	
	<select id="queryForPage" resultMap="BaseResultMap" parameterType="com.sys.workflow.model.WorkflowParams">
		select t1.workflow_code, t1.workflow_name, t1.handle_bean, t1.page_themes, t1.workflow_version, t1.end_notify,
			 t1.end_notify_template, t1.end_email_cc, t1.def_todo_email_temp, t1.def_reject_notify_template,
			 t1.sms_notify_template, t1.def_sms_notify_temp, t1.flow_status, t1.property_view_weight1, t1.property_edit_weight1,
			 t1.property_required_weight1, t1.property_view_weight2, t1.property_edit_weight2, t1.property_required_weight2,
			 t1.property_view_weight3, t1.property_edit_weight3, t1.property_required_weight3, t1.delete_flag,
			 t1.create_user_id, t1.create_time, t1.update_user_id, t1.update_time
		  from wx_t_workflow t1
		 where t1.delete_flag=0
		<if test="workflowCode != null and workflowCode != ''">
			and t1.workflow_code like '%' + #{workflowCode, jdbcType=VARCHAR} + '%'
		</if>
		<if test="workflowName != null and workflowName != ''">
			and t1.workflow_name like '%' + #{workflowName, jdbcType=VARCHAR} + '%'
		</if>
	</select>
</mapper>
