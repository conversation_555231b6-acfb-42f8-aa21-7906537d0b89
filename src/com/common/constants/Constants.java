package com.common.constants;

import com.common.util.StringUtils;
import com.sys.auth.dao.WxTPropertiesMapper;
import com.sys.auth.model.WxTProperties;
import com.sys.dic.dao.DicItemVoMapper;
import com.sys.dic.model.DicItemVo;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;

@Service
public class Constants implements ConstantsI {
	public final static Logger log = Logger.getLogger(Constants.class);
	
	public final static int CHANNEL_WEIGHT_CDM = 1;
	public final static int CHANNEL_WEIGHT_CI = 2;
	public final static int CHANNEL_WEIGHT_CONSUMER = 1;
	public final static int CHANNEL_WEIGHT_COMMERCIAL = 2;
	public final static int CHANNEL_WEIGHT_OEM = 4;
	public final static int CHANNEL_WEIGHT_INDUSTRIAL = 8;
	public final static int CHANNEL_WEIGHT_PROJECT = 16;
	public final static int CHANNEL_WEIGHT_CONSTRUCTION = 64;
    public final static String BU_INDIRECT= "Indirect";
    public final static String BU_DIRECT= "Direct";
	public final static String SALES_CHANNEL_INDIRECT = "Indirect";

	public final static String SALES_CHANNEL_DIRECT_INDUSTRIAL = "Industrial";


    public final static Integer FLAG_Y = 1;
    public final static Integer FLAG_N = 0;

	
	private static Map<String, Integer> channelWeightMap = new HashMap<String, Integer>(5);
	private static Map<String, Integer> buChannelWeightMap = new HashMap<String, Integer>(2);
	private static List<String> indirectRegions;
    private static List<String> directRegions;
	private static Map<String, List<String>> regionsBuMap = new HashMap<String, List<String>>(5);
	private static Map<String, String> salesChannelRegionMap = new HashMap<String, String>();
	private static Map<Integer, String> brandMap = new HashMap<Integer, String>(3);
	private static Map<String, Integer> brandNameMap = null;
	
	/** 金富力 */
	public final static int BRAND_CDM = 1;
	/** 德乐 */
	public final static int BRAND_DELO = 2;
	/** 工程机械 */
	public final static int BRAND_CONSTRUCTION = 4;
	static {
		brandMap.put(BRAND_CDM, "金富力");
		brandMap.put(BRAND_DELO, "德乐");
		brandMap.put(BRAND_CONSTRUCTION, "工程机械");
		brandNameMap = new HashMap<String, Integer>(brandMap.size());
		for(Integer value : brandMap.keySet()) {
			brandNameMap.put(brandMap.get(value), value);
		}
		channelWeightMap.put("cdm", CHANNEL_WEIGHT_CDM+CHANNEL_WEIGHT_PROJECT);
		channelWeightMap.put("c&i", CHANNEL_WEIGHT_CI+CHANNEL_WEIGHT_PROJECT);
		channelWeightMap.put("consumer", CHANNEL_WEIGHT_CDM);
		channelWeightMap.put("commercial", CHANNEL_WEIGHT_COMMERCIAL);
		channelWeightMap.put("oem", CHANNEL_WEIGHT_OEM);
		channelWeightMap.put("industrial", CHANNEL_WEIGHT_INDUSTRIAL);
		channelWeightMap.put("construction", CHANNEL_WEIGHT_CONSTRUCTION);
		channelWeightMap.put("indirect", CHANNEL_WEIGHT_CDM + CHANNEL_WEIGHT_CI);
		channelWeightMap.put("project", CHANNEL_WEIGHT_PROJECT);
		buChannelWeightMap.put("Indirect", CHANNEL_WEIGHT_CDM + CHANNEL_WEIGHT_CI);
		buChannelWeightMap.put("Direct", CHANNEL_WEIGHT_OEM + CHANNEL_WEIGHT_INDUSTRIAL);
		
	}
	
	public static int getChannelWeight(String salesChannel) {
		if(salesChannel == null) {
			return 0;
		}
		Integer r = channelWeightMap.get(salesChannel.toLowerCase());
		if(r == null) {
			return 0;
		}
		return r;
	}
	
	/**
	 * 获取品牌名称
	 * @param brand
	 * @return
	 */
	public static String getBrandName(int brand) {
		return brandMap.get(brand);
	}
	
	public static Integer getBrandByName(String brandName) {
		return brandNameMap.get(brandName);
	}
	
	public static Map<String, Integer> getBuChannelWeightMap(){
		return buChannelWeightMap;
	}
	
	
	// 浏览器语言
	public static final String LANGUAGE_CHINESE = "zh_CN";
	public static final String LANGUAGE_FOREIGN = "en_US";
	//雪佛龙机构ID

	public static final Long CHEVRON_ID = 1L;
	public static final Long ADMINISTRATOR_ID=1L;
	public static final String ADMINISTRATOR_NAME="chevronadmin";

	/** 加密策略 */
	public static final String HASH_ALGORITHM = "SHA-1";
	/** 迭代次数 */
	public static final int HASH_INTERATIONS = 1024;
	/** 盐长 */
	public static final int SALT_SIZE = 8;

	public static final String RESULT_SUCCESS = "success";
	public static final String RESULT_ERROR = "error";

	// 组织树
	public static final String BASE_DATATYPE_ORG = "org";
	// 主数据根节点
	public static final String BASE_ROOT_ID = "0";

	// 数据库状态有效
	public static final String STATUS_VALID = "1";

	// 数据库状态无效
	public static final String STATUS_INVALID = "0";
	public static final String msg_keyCode_taskName = "{taskName}";
	public static final String msg_keyCode_autoTaskName = "{autoTaskName}";
	public static final String msg_CreateTaskFinish = "您有新的任务,任务名称:{taskName},请注意办理!";// 创建任务
	public static final String msg_ProcessTaskMainFinish = "任务:{taskName},已办理完成.";// 主任务办理完成
	public static final String msg_CcTaskZg = "您的任务:{taskName},被抽查为不合格，请整改!";// 抽查任务为需整改
	public static final String msg_DpwxBxxYcz_checkList = "{checkList}";
	public static final String msg_DpwxBxxYcz = "{checkList},正在被任务:{taskName} 处理.";
	public static final String msg_DpwxBxxYcz_wftj = "任务无法创建！";
	public static final String msg_DpwxWxx_create = "您是否要创建如下检查项的{autoTaskName}任务？ {checkList}";
	public static final Long sleep_timemillis = 500l;
	public static final String default_password = "default_password";
	public static final String default_salt = "default_salt";
	public static final String position_code = "position_code";
	public static final String store_position_code = "store_position_code";
	public static final String report_file_path = "report_file_path";
	public static final String file_upload_path = "file_upload_path";
	public static final String folder_grain_size = "folder_grain_size";
	public static final String remote_user = "remote_user";
	public static final String remote_user_password = "remote_user_password";
	public static final String remote_url = "remote_url";
	public static final String appType_az = "android";
	public static final String appType_ios = "ios";
	public static final String android_version = "android_version";
	public static final String android_changeLog = "android_changeLog";
	public static final String android_downloadUrl = "android_downloadUrl";
	public static final String app_downloadUrl = "app_downloadUrl";

	public static final String ios_version = "ios_version";
	public static final String ios_changeLog = "ios_changeLog";
	public static final String ios_downloadUrl = "ios_downloadUrl";
	public static final String DOWNLOAD_URL = "download_url";
	public static final String ENV_IS_PROD = "env.is_prod";
	public static final String O2O_SERVICE_REGISTER_MECHANIC = "o2o_service.registerMechanic";
	public static final String O2O_SERVICE_UPDATE_SP = "o2o_service.updateSp";
	public static final String O2O_SERVICE_UPDATE_REGISTER_MECHANIC = "o2o.service.updateRegisterMechanic";
	public static final String O2O_SERVICE_REGISTER_MECHANIC_KEY = "o2o_service.registerMechanic.key";
	public static final String SUB_SYSTEM_TOKEN_KEY = "subsystem.token.key";
	public static final String DMS_ENCRYPT_KEY = "dms.encrypt.key";
	public static final String APP_HOST = "app.host";
	public static final String O2O_SERVICE_SEND_MESSAGE = "o2o.service.sendMessage";
	public static final String B2B_SCRM_ACCESS_SECRET_KEY = "b2b.srcm.access_secret";
	public static final String B2B_SCRM_SYN_MECHANIC_URL = "b2b.srcm.syn.mechanic.url";
	//大地
	public static final String ES_API_U_CODE = "es.api.uCode";
	public static final String ES_API_SECRET = "es.api.secret";
	//特殊sp
	public static final String ES_API_SPID_UCODE = "es.api.spid.uCode";
	public static final String ES_API_SPID_SECRET  = "es.api.spid.secret";
	//滴滴易养车,后续通用模板也使用
	public static final String ES_API_DDYYC_UCODE = "es.api.ddyyc.uCode";
	public static final String ES_API_DDYYC_SECRET  = "es.api.ddyyc.secret";

	public static final String O2O_GET_WECHAT_TEMP_CODE_URL = "o2o.getWechatTempCode.url";
	public static final String O2O_GET_WECHAT_TEMP_CODE_KEY = "o2o.getWechatTempCode.key";
	public static final String UPLOD_URL = "file_upload_path";
	public static final String DOWNLOAD_URL_UUID = "/download.do";

	//获取微信企业号接口属性
	public static final String WECHAT_GET_ALL_ENTERPRISE_URL = "wechat.getAllEnterprise.url";
	public static final String WECHAT_GET_ALL_ENTERPRISE_KEY = "wechat.getAllEnterprise.key";

	public static final String VERIFICATION_MAX_REWARD_PER_LITER = "verification.maxRewardPerLiter";

	public static final String HTTPS_ENABLED = "https.enabled";
	public static final String HTTPS_PORT = "https.port";

	public static final String PUSH_MESSAGE_DEPLOY_STATUS = "pushMessage.deployStatus";

	//产品数据字典相关
	//品牌
	public static final String PRODUCT_BRAND = "product.brand";
	//机油类型
	public static final String PRODUCT_OIL_TYPE = "product.oilType";
	//粘度
	public static final String PRODUCT_VISCOSITY = "product.viscosity";
	//容量
	public static final String PRODUCT_CAPACITY = "product.capacity";
	//产品单位
	public static final String PRODUCT_UNITS = "product.units";
	//产品类别
	public static final String PRODUCT_CATEGORY = "product.category";
	
	//采购订单最小起始量
	public static final String PARTNER_ORDER_MIN_LITER_COUNT = "partnerOrder.minLiterCount";

	//角色相关 start
	public static final String SERVICE_PARTNER_BD_CODE = "Service_Partner_BD";

	public static final String CHEVRON_BD_CODE = "Chevron_BD";

	public static final String Chevron_BD_DISCOUNT_CODE = "Chevron_BD_DISCOUNT";

	public static final String CHEVRON_PRICE_AUDIT = "Chevron_CDM_Channel_Manager"; // 雪弗龙价格审批更新为雪佛龙CDM大区经理
	public static final String Chevron_CI_Channel_Manager = "Chevron_C&I_Channel_Manager"; // 雪佛龙商用油渠道经理

	public static final String Chevron_CI_Suppervisor = "Chevron_C&I_Suppervisor"; // 雪佛龙商用油大区经理

	public static final String Chevron_CDM_Suppervisor = "Chevron_CDM_Suppervisor"; // 雪佛龙乘用车大区经理

	public static final String Chevron_Admin ="admin"; // 超级管理员




	public static final String CHEVRON_SAP_CSR = "Chevron_SAP_CSR";

	public static final String Chevron_Industrial_CSR = "Chevron_Industrial_CSR";

	public static final String Chevron_Industrial_Dealer = "Chevron_Industrial_Dealer";

	public static final String Chevron_Industrial_Sales = "Chevron_Industrial_Sales";

	public static final String Chevron_Industrial_Supervisor = "Chevron_Industrial_Supervisor";

	public static final String Chevron_Industrial_Channel_Manager = "Chevron_Industrial_Channel_Manager";

	public static final String Chevron_SAP_MODIFY = "Chevron_CDM_Assistant"; // 雪弗龙SAP 录入员 更新为雪佛龙CDM销售助理

	public static final String ADMIN_CODE = "admin";
	
	public static final String ROLE_SYSTEM_INTEGRATION = "Chevron_System_Integration";

	public static final String SERVICE_PARTNER_MANAGER_CODE  = "Service_Partner_Manager";

	public static final String SERVICE_PARTNER_CSR_CODE = "Service_Partner_CSR";

	public static final String CHEVRON_MANAGER_CODE = "Chevron_Manager";

	public static final String WORKSHOP_MANAGER_CODE = "Workshop_Manager";

	public static final String WORKSHOP_MECHANIC_CODE = "Workshop_Mechanic";

	public static final String ORGANIZATION_PARTNER_NAME = "Partner";

	public static final String ORGANIZATION_WORKSHOP_NAME = "Workshop";

	public static final String ZWY_WAREHOUSE_CLERK_CODE = "ZWY_CLERK";//中外运出/入库员

	public static final String ZWY_ADMIN_CODE ="ZWY_Admin";//中外运管理员

	public static final String SERVICE_PARTNER_CLERK = "Service_Partner_CLERK";//合伙人出/入库员
	
	public static final String CHEVRON_MARKETING_CODE = "chevron_Marketing";
	
	public static final String CHEVRON_PROMOTE_SALES = "Chevron_Promote_Sales";
	//角色相关 end

	public static final String EYANGCHE_WSDL_ADDRESS = "http://www.wedrive.com.cn:51518/daappgate/wschevron?wsdl";

	public static final String SYSTEM_DEFAULT_PASSWORD = "1qaz2wsx";

	public static final String WORKSHOP_WRITEOFF_MENU_NAME = "门店核销";

	public static final String SERVICE_PARTNER_ADMIN_CODE = "Service_Partner_Admin";//Partner admin角色

	public static final String OEM_DELIVERY_TYPE = "OEM仓库存储数据";
	
	public static final String OEM_DELIVERY_CODE_TYPE = "OEM物流码数据";

	public static final String OEM_PRODUCT_PACKAGING_TYPE = "箱垛码关联数据";

	public static final String SUCCESS = "success";

	public static final String FAIL = "fail";
	
	public static final String PRODUCT_PRICE_RULE = "productprice.partnerpower";
	public static final String PRODUCT_LADDER_PRICE_RULE = "ladderprice.partnerpower";
	public static final String PRODUCT_KA_PRICE_RULE = "kaprice.partnerpower";
	public static final String PRODUCT_PROMOTIONCAMPAIGN_PRICE_RULE = "promotioncampaign.partnerpower";
	
	
	public static final String FUEL_OIL_ADDITIVES_FREE_RULE = "fueloiladditives.freerule";

	//物流码扫码扫码失败
	public static final String PRODUCT_LOGISTIC_FORMAT_ERROR = "此物流码无效";
	public static final String PRODUCT_LOGISTIC_PRODUCT_NOT_FOUND = "此物流码查询失败，无对应商品信息";
	public static final String PRODUCT_LOGISTIC_PRODUCT_BOTTLE_OUTSTOCKED = "此瓶码在该机构已有出库记录";
	public static final String PRODUCT_LOGISTIC_PRODUCT_BOTTLE_INSTOCKED = "此瓶码在该机构已有入库记录";
	public static final String PRODUCT_LOGISTIC_PRODUCT_BOX_OUTSTOCKED = "此箱码所包含的商品在该机构已有出库记录";
	public static final String PRODUCT_LOGISTIC_PRODUCT_BOX_INSTOCKED = "此箱码所包含的商品在该机构已有入库记录";
	public static final String PRODUCT_LOGISTIC_PRODUCT_STACK_OUTSTOCKED = "此垛码所包含的商品在该机构已有出库记录";
	public static final String PRODUCT_LOGISTIC_PRODUCT_STACK_INSTOCKED = "此垛码所包含的商品在该机构已有入库记录";
	public static final String PRODUCT_LOGISTIC_CODE_OUTSTOCKED = "此物流码或者关联的物流码已经出库，关联码值：";
	
	public static final String PRODUCT_LOGISTIC_PRODUCT_STACK_WORKSHOPVALIDATE = "此码已有门店核销记录";
	
	//扫码扫码失败
	public static final String OEM_PACKAGING_CODE_FORMAT_ERROR = "此码查询失败，无对应相关信息";

	//email about start
	//系统发送者
	public static final String MAIL_SENDER = "mail.sender";
	public static final String MAIL_SENDERNICKNAME = "mail.sendernickname";
	public static final String MAIL_SENDER_AUTHPASSWORD = "mail.authpassword";
	//雪佛龙接收者
	public static final String MAIL_ACCEPTER = "mail.accepter";
	///////Pitpack积分确认接收者
	public static final String MAIL_PITPACK_CONFIRM_ACCEPTER = "mail.pitpack.confirm.accepter";
	public static final String MAIL_PITPACK_CONFIRM_CC = "mail.pitpack.confirm.cc";

	//大地保险接收者
	public static final String MAIL_DD_ACCEPTER = "mail.dd.accepter";
	public static final String MAIL_DD_CC = "mail.dd.cc";
	public static final String MAIL_YXC_SAP_ACCEPTER = "mail.yxc.sap.accepter";
	public static final String MAIL_YXC_SAP_CC = "mail.yxc.sap.cc";

	public static final String MAIL_FW_ACCEPTER = "mail.fw.accepter";
	public static final String MAIL_FW_CC = "mail.fw.cc";
	//email about end

	// kuaidi100相关 begin
	public static final String KUAIDI100_CUSTOMER = "kuaidi100.customer";
	public static final String KUAIDI100_KEY = "kuaidi100.key";
	// kuaidi100相关 end

	//约定口令  中驰接口对接  公共用于签名的
	public static String ZC_API_KEY = "chevron_2016";
	public static String ZC_API_VALIDTIME = "validTime";
	public static String ZC_API_MERID = "merId";
	public final static String RETURN_PRODUCT_TYPE = "1";// 退货通知类型
	public final static String REPLEMENT_PRODUCT_TYPE = "2";// 补货通知类型
	public final static String RETURN_PRODUCT_DO = "/returnedPurchase.do";
	public final static String REPLEMENT_PRODUCT_DO = "/replenishment.do";

	public final static String ZCAPI_SUCCESS_CODE="0000";
	public final static String ZCAPI_SUCCESS_MSG="操作成功";
	public final static String ZCAPI_ERROR_CODE_1001="1001";
	public final static String ZCAPI_ERROR_MSG_1001="参数缺失";
	public final static String ZCAPI_ERROR_CODE_1002="1002";
	public final static String ZCAPI_ERROR_MSG_1002="参数错误";
	public final static String ZCAPI_ERROR_CODE_1003="1003";
	public final static String ZCAPI_ERROR_MSG_1003="信息签名错误";
	public final static String ZCAPI_ERROR_CODE_1006="1006";
	public final static String ZCAPI_ERROR_MSG_1006="系统异常，您可以拨打电话 4006686065 联系管理员！";
	public final static String ZCAPI_ERROR_CODE_1007="1007";
	public final static String ZCAPI_ERROR_MSG_1007="明细不能为空";
	public final static String ZCAPI_ERROR_CODE_1008="1008";
	public final static String ZCAPI_ERROR_MSG_1008="明细长度不在有效范围";
	public final static String ZCAPI_ERROR_CODE_1009="1009";
	public final static String ZCAPI_ERROR_MSG_1009="明细参数必填";
	public final static String ZCAPI_ERROR_CODE_1010="1010";
	public final static String ZCAPI_ERROR_MSG_1010="退货数量传递错误";
	public final static String ZCAPI_ERROR_CODE_1011="1011";
	public final static String ZCAPI_ERROR_MSG_1011="参数为null";
	public final static String ZCAPI_ERROR_CODE_1012="1012";
	public final static String ZCAPI_ERROR_MSG_1012="这些参数值需要必填:";
	public final static String ZCAPI_ERROR_CODE_1013="1013";
	public final static String ZCAPI_ERROR_MSG_1013="服务端已存在指定的需求单号：";
	public final static int ZCAPI_BODY_MAX_LENGTH = 20;

	public final static String RESULT_CODE_KEY = "code";
	public final static String RESULT_CODE_MSG_KEY = "codeMsg";
	public final static String SUCCESS_CODE = "success";
	public final static String ERROR_CODE = "error";
	public static final String RESULT_DATA_CODE = "data";
	public final static String RESULT_LST_KEY = "resultLst";
	public final static String RESULT_TOTAL_KEY = "total";
	public final static String RESULT_ERROR_MSG_KEY = "errorMsg";
	public final static String RESULT_SUCCESS_MSG_KEY = "successMsg";
	public final static String ZC_PO_DCL_STATUS="0";//待处理
	public final static String ZC_PO_QR_STATUS="1";//确认
	public final static String ZC_PO_QX_STATUS="-1";//取消

	public final static String ZCAPI_ERROR_MSG_POHEADERNO_NULL="确认需求单号为null";
	public final static String ZCAPI_ERROR_MSG_PARAMS_NULL="没找到完整的请求数据";
	public final static String ZCAPI_ERROR_MSG_REMOTESERVICE_API="调用远程接口异常错误";
	public final static String SYSTEM_UNEXPECTED_EXCEPTION="系统异常，您可以拨打电话 4006686065 联系管理员！";


	public static final String AUTOZI_LOG_TYPE_RDCCDC_WAREHOUSE = "中驰RDC-CDC仓库清单";

	public static final String AUTOZI_LOG_TYPE_RDCCDC_INVENTORY = "中驰RDC-CDC库存清单";

	public static final String AUTOZI_LOG_TYPE_RDCCDC_TRANSFER = "中驰RDC-CDC调拨清单";

	public static final String AUTOZI_LOG_TYPE_CUSTOMER_ORDER = "中驰销售订单表";

	public static final String AUTOZI_LOG_TYPE_CUSTOMER_RETURN = "中驰销售退货表";
	
	//高桥验真
	public static final String GQ_VERIFICATION_OWNER = "GQ";
	public static final String GQ_VERIFICATION_URL = "gq_verification_url";
	public static final String GQ_VERIFICATION_TOKEN = "gq_verification_token";

	//didi验真
	public static final String DIDI_VERIFICATION_OWNER = "didi";
	
	
	/***
	 * 产品生命周期物流相关
	 * ***/
	//促销包装：包
	public static final String PARTNER_ORDER_LINE_UNIT_TYPE_PACKAGE = "package";
	//扫描：箱
	public static final String STOCK_LINE_CODE_TYPE_BOX = "box";
	//扫描：垛
	public static final String STOCK_LINE_CODE_TYPE_STACK = "stack";
	//扫描：物流码
	public static final String STOCK_LINE_CODE_TYPE_LOGISTIC = "logistic";
	
	//无差异
	public static final String INVENTORT_ACCOUNT_AGREE_WITH_PGYSICAL = "0";
	//盘亏
	public static final String INVENTORT_LOSSES = "1";
	//盘盈
	public static final String INVENTORT_PROFIT = "2";
	
	//出入库标志Flag
	public static final String STOCK_FLAG_IN_STOCK = "inStock";
	
	public static final String STOCK_FLAG_OUT_STOCK = "outStock";

	//出入库类型：天津工厂
	public static final String STOCK_TYPE_TJFACTORY = "tjfactory";
	//出入库类型：中外运
	public static final String STOCK_TYPE_ZWY = "zwy";
	//出入库类型：顺丰
	public static final String STOCK_TYPE_SF = "sf";
	//出入库类型：合伙人
	public static final String STOCK_TYPE_SP = "sp";
	//出入库类型：门店
	public static final String STOCK_TYPE_WORKSHOP = "workshop";
	
	public static final String STOCK_TYPE_WDGJ = "wdgj";

	//出库状态: 待出库
	public static final String STOCK_OUT_STATUS_DAICHUKU = "1";
	//出库状态: 已出库
	public static final String STOCK_OUT_STATUS_CHUKU = "2";
	//出库状态: 已取消
	public static final String STOCK_OUT_STATUS_CANCEL = "4";
	//出库状态: 已删除
	public static final String STOCK_OUT_STATUS_DEL = "8";

	//入库状态: 待入库
	public static final String STOCK_IN_STATUS_DAIRUKU = "1";
	//入库状态: 已入库
	public static final String STOCK_IN_STATUS_RUKU = "2";

	//库存订单类型
	public static final String STOCK_ORDER_TYPE_SP = "SP";
	public static final String STOCK_ORDER_TYPE_WORKSHOP = "workshop";

	public static final String PARTNER_APP_TYPE="1";//合伙人app
	public static final String SCANCODE_APP_TYPE="2";//扫码枪app
	public static final String MECHANIC_APP_TYPE="3";//技师核销app

	//中外运 sp名称
	public static final String ZHONGWAIYUN_ORG_NAME = "中外运";


	/**
	 * 网店管家订单接口有关
	 *
	 */
	public static final String ES_API_ORDER_URL = "es.api.order.url";
	public static final String ES_API_ORDER_APPKEY= "es.api.order.appkey";
	public static final String ES_API_ORDER_APPSECRET = "es.api.order.appsecret";
	public static final String ES_API_ORDER_ACCESSTOKEN= "es.api.order.accesstoken";
	public static final String METHOD_ES_API_ORDER_SELLBACK ="wdgj.erp.sellback.query";
	public static final String METHOD_ES_API_ORDER_SALES ="wdgj.com.sales.detail";//"wdgj.com.sales.summary";//
	public static final String METHOD_ES_API_ORDER_STOCKOUT ="wdgj.erp.stockout.list";
	public static final String ES_SCAN_LAST_DATE ="es.api.order.getsalereportfrompp";
	public static final String ES_SELLBACL_PAGE_SIZE = "100";

	//最后的时间点  每天定时扫描销售汇总单的时间点
	public static final String ES_GET_SALE_ORDER_LAST_TIME = "es.api.order.getsalefromes";
	public static final String ES_ORDER_MAIL_ACCEPTER = "es.api.order.accepter";
	public static final String ES_ORDER_MAIL_CC = "es.api.order.cc";
	public static final String ES_ORDER_TESU_SUK = "es.api.order.tesusku";
	public static final String ES_ORDER_WORKSHOPNAME = "es.api.order.workshopname";

	public static final String ES_GET_OUTSTOCK_ORDER_LAST_TIME = "es.api.order.getstockoutorderformes";
	public static final String ES_GET_AVRATO_ORDER_LAST_TIME = "es.api.order.getavratoorderformes";


	public static final String PORDER_WORKSHOP_LOCATION_ONFLAG = "psorder.workshop.location.onflag";

	/**
	 * 微信异常报警接口
	 */
	public static final String ALARM_API_URL = "alarm.api.url";

	public static final String ORDER_DDBX_XBFLAG ="order.ddbx.xbflag";
	/**
	 * 啡威格服务订单推送url
	 */
	public static final String FWG_ORDER_SND_URL = "order.fwg.fworder.push.url";

	/**
	 * 微信服务号用户信息获取接口
	 */
	public static final String WX_USERINFO_URL = "wx.userinfo.url";

	/**
	 * 微信企业号sdk签名获取接口
	 */
	public static final String WX_SDK_SIGNATURE_URL = "wx.sdk.signature.url";
	
	public static final String OILSELECT_MSG_EMY_SDK = "oilselect.msg.emy.sdk";
    public static final String OILSELECT_MSG_EMY_KEY = "oilselect.msg.emy.key";
    public static final String OILSELECT_MSG_EMY_BATCH_SDK = "oilselect.msg.emy.batch.sdk";
    public static final String OILSELECT_MSG_EMY_BATCH_KEY = "oilselect.msg.emy.batch.key";
	public static final String OILSELECT_MSG_EMY_EXTENDCODE = "oilselect.msg.emy.extendcode";
	public static final String OILSELECT_MSG_EMY_HOST = "oilselect.msg.emy.host";
	public static final String OILSELECT_MSG_EMY_SIGNATURE = "oilselect.msg.emy.signature";
	public static final String umengWebId = "umeng.webid";
	public static final String defaultUmengWebId = "1273266517";
	// 新合伙人开始的时间点
	public static final String LATESTPARTNER_STARTTIME="latestpartner.starttime";
	
	/**
	 * 滴滴商城接口相关
	 */
	public static final String DIDI_API_CLIENTID = "didi.clientid";
	public static final String DIDI_API_SECRET = "didi.secret";
	public static final String DIDI_API_URL = "didi.apiurl";
	
	
	//customer cio
	public final static String REGION_TYPE = "REGION_PROMOTION"; 
	public final static String PROMOTE_MARKETING = "marketing";//市场
	public final static String PROMOTE_CHANNELMANAGER = "channelManager";//大区经理
	public final static String PROMOTE_MARKETINGREGIONMANAGER = "marketingRegionManager";//市场区域经理
	public final static String PROMOTE_SUPERVISOR = "supervisor";//小区经理
	public final static String PROMOTE_SALES= "sales";//销售
	public final static String PROMOTE_DEALER = "Dealer";//经销商
	public final static String PROMOTE_CAI_CHEVRON_MANAGER = "Chevron_C&I_Admin";
	public final static String PROMOTE_PLAN_MANAGE_PAGE = "promoteDistributePlanApprovePage2.jsp";
	public final static String PROMOTE_ACTIVITY_MANAGE_PAGE = "promoteActivityApplyPage.jsp";
	public final static String PROMOTE_ACTIVITY_FEEDBACK_PAGE = "promoteActivityFeedbackPage.jsp";
	public final static String PROMOTE_NEW_ACTIVITY_MANAGE_PAGE = "promoteNewActivityApplyPage.jsp";
	public final static String PROMOTE_NEW_ACTIVITY_MANAGE_PAGES = "/SPA/application/index.jsp#/list";

	//Sales Channel
	public final static String SALES_CHANNEL_CDM = "CDM";
	public final static String SALES_CHANNEL_CAI = "C&I"; 
	public final static String SALES_CHANNEL_CONSUMER = "Consumer";
	public final static String SALES_CHANNEL_COMMERCIAL = "Commercial"; 
	
	public final static String QR_SALES_FLAG_INDUSTRT = "INDUSTRY";
	public final static String QR_SALES_FLAG_TUHU = "tuhu";
	
	//product property
	public final static String PRODUCT_PROPERTY_SN_ABOVE = "SN Above";
	public final static String PRODUCT_PROPERTY_SN_BELOW = "SN Below";
	
	//delo qrcode url
	public final static String DELO_QR_CODE_URL = "deloQrcodeUrl";
	public final static String DELO_QR_CODE_URL_PRODUCTDETAIL = "deloQrcodeUrlProductDetail";
	public final static String DELO_QR_CODE_URL_OILSELECTOR = "deloQrcodeUrlOilSelector";
	
	//sap price code flag
	public final static String PARTNER_SAPPRICE_CODE_FLAG = "partner_sapprice_code_flag";
	public final static String PARTNER_SAPPRICE_SAPCODE_FLAG = "sap_code";
	public final static String PARTNER_SAPPRICE_SHIPTOCODE_FLAG = "ship_to_code";
	
	public final static int RANDOM_CODE6_MODULE_O2O_ORDER = 1; //O2O订单服务随机码模块标记。值1, 2, 4 ...位操作

	//pp监测系统是否可登录的账号密码
	public final static String SYSTEM_CHECK_LOGIN_KEY="system.check.login.key";
	//pp监测系统是否可登录的通知邮箱
	public final static String SYSTEM_CHECK_LOGIN_EMAIL="system.check.login.emails";
	//pp监测系统是否可登录的微信公众号通知
	public final static String SYSTEM_CHECK_LOGIN_OPENID="system.check.login.openids";

	//pp监测系统是否可登录的微信公众号通知
	public final static String SYSTEM_CHECK_LOGIN_MOBILE="system.check.login.mobiles";
	//中驰接口
	/*
	 * 以下代码均为系统参数相关
	 */
	private static Map<String, List<String>> systemPropertiesMap = new HashMap<String, List<String>>();

	@Resource
	private WxTPropertiesMapper wxTPropertiesMapper;
	@Resource
	private DicItemVoMapper dicItemVoMapper;

	@PostConstruct
	public void initSystemProperties() throws Exception {
		List<WxTProperties> properties = wxTPropertiesMapper.selectByExample(null);
		setSystemProperties(properties);
		/*List<DicItemVo> dicItem = dicItemVoMapper.selectByExample(null);
		setDicItemSystemProperties(dicItem);*/
		initRegions();
	}
	
	protected void initRegions() {
		//初始化Indirect区域
		List<DicItemVo> list = dicItemVoMapper.selectByCode(REGION_INDIRECT);
		indirectRegions = new ArrayList<String>(list.size());
		for(DicItemVo vo : list) {
			indirectRegions.add(vo.getDicItemCode());
			salesChannelRegionMap.put(vo.getDicItemCode(), SALES_CHANNEL_INDIRECT);
		}
		regionsBuMap.put(BU_INDIRECT, indirectRegions);
		//初始化Direct区域
		list = dicItemVoMapper.selectByCode(REGION_DIRECT);
		directRegions = new ArrayList<String>(list.size());
		for(DicItemVo vo : list) {
			directRegions.add(vo.getDicItemCode());
			salesChannelRegionMap.put(vo.getDicItemCode(), vo.getDicItemDesc());
		}
		regionsBuMap.put(BU_DIRECT, directRegions);
	}

	/*
	 * 根据数据库实体设置系统参数map
	 */
	private void setSystemProperties(List<WxTProperties> properties) {
		if (properties != null && properties.size() > 0) {
			for (WxTProperties prop : properties) {
				String codeType = prop.getCodetype();
				if (!systemPropertiesMap.containsKey(prop.getCodetype())){
					systemPropertiesMap.put(codeType, new ArrayList<String>());
				}
				List<String> codeList = systemPropertiesMap.get(codeType);
				codeList.add(prop.getCode());
			}
		}
	}
	private void setDicItemSystemProperties(List<DicItemVo> dicItemList) {
		if (dicItemList != null && !dicItemList.isEmpty()) {
			for (DicItemVo item : dicItemList) {
				String codeType = item.getDicTypeCode();
				if (!systemPropertiesMap.containsKey(item.getDicTypeCode())){
					systemPropertiesMap.put(codeType, new ArrayList<String>());
				}
				List<String> codeList = systemPropertiesMap.get(codeType);
				codeList.add(item.getDicItemCode());
			}
		}
	}

	/*
	 * 获取系统参数的静态方法 如果此codeType为单条记录则返回String 如果此codeType为多条记录则返回List<String>
	 */
	public static Object getSystemPropertyByCodeType(String codeType) {
		List<String> codeList = systemPropertiesMap.get(codeType);
		if (codeList == null) {
			return null;
		}
		if (codeList.size() == 1){
			return codeList.get(0);
		}
		return codeList;
	}

	/*
	 * 全量更新系统参数map
	 */
	public Map<String, Object> updateSystemPropertiesAll(List<WxTProperties> properties) throws Exception {
		Map<String, Object> resMap = new HashMap<String, Object>();
		systemPropertiesMap.clear();
		setSystemProperties(properties);
		resMap.put("code", "success");
		return resMap;
	}
	public Map<String, Object> updateDicItemSystemPropertiesAll(List<DicItemVo> dicItemList) throws Exception {
		Map<String, Object> resMap = new HashMap<String, Object>();
		systemPropertiesMap.clear();
		setDicItemSystemProperties(dicItemList);
		resMap.put("code", "success");
		return resMap;
	}


	public Map<String, List<String>> getSystemPropertiesMap() {
		return systemPropertiesMap;
	}
	
	public static List<String> getRegionsByBu(String bu){
    	return regionsBuMap.get(bu);
	}

    @Override
    public List<String> getRegionByBu(String bu) {
    	return regionsBuMap.get(bu);
//        ArrayList<String> list = new ArrayList<String>();
//        String code = null;
//        if(BU_INDIRECT.equals(bu)){
//            code = REGION_INDIRECT;
//        }else if(BU_DIRECT.equals(bu)){
//            code = REGION_DIRECT;
//        }else {
//            return list;
//        }
//        List<DicItemVo> dicItemVos = dicItemVoMapper.selectByCode(code);
//        for (DicItemVo dicItemVo : dicItemVos) {
//            list.add(dicItemVo.getDicItemCode());
//        }
//        return list;
    }

    /**
     * B2B虚拟礼品
     */
    public static final String VIRTUAL_MATERIAL_TYPE = "XN";

    /**
     * b2b虚拟礼品兑换一天上限次数
     */
    public static final String VIRTUAL_GIFT_EXCHANGE_TIME_UP = "virtual.gift.exchange.time.up";

    /**
     * b2b虚拟礼品兑换一天上限积分数
     */
    public static final String VIRTUAL_GIFT_EXCHANGE_POINT_UP = "virtual.gift.exchange.point.up";

    public static final String VIRTUAL_GIFT_USER_ORDER_NO_PREFIX = "DAKAHUI_";
 
    /**
     * 金富力TMM绩效分配比例字典类型
     */
    public static final String CDM_TMM_PERFORMANCE_PAY_ALLOCATION =  "cdm.tmm.performance.pay.allocation";

    /**
     * QBR-DMS产品分类字典类型
     */
    public static final String QBR_DMS_PRODUCT_CATEGORY =  "qbr.dms.product.category";

    /**
     * 车队类型
     */
    public static final String CAI_FLEET_TYPE =  "cai.fleet.type";

	
	   
    /**
     * QBR-DMS产品分类字典类型
     */
    public static final String QBR_DMS_PRODUCT_CHANNEL =  "qbr.dms.product.channel";


	/**
	 * bpm服务地址相关
	 */
	public static final String BPM_REQUEST_TOKEN = "bpm.request.token";
	public static final String BPM_PROCESS_START = "bpm.process.start";
	public static final String BPM_TASK_COMPLETE = "bpm.task.complete";
	public static final String BPM_TASK_TODO_LIST = "bpm.task.todolist";

	public static final String BPM_GETTODONUM = "bpm.gettodonum";
	public static final String BPM_TASK_DONE_LIST = "bpm.task.donelist";
	public static final String BPM_TASK_GET_TODO = "bpm.task.get.todo";
	public static final String BPM_TASK_GET_DONE = "bpm.task.get.done";
	public static final String BPM_HISTORY_COMMENT = "bpm.history.comment";
	public static final String BPM_HISTORY_STEP = "bpm.history.step";
	public static final String BPM_TASK_RECALL = "bpm.task.recall";
	public static final String BPM_TASK_REASSIGN = "bpm.task.reassign";

	/**
     * 星火计划费用经销商支持
     */
    public static final String ORG_APPLY_EXPENSE_NOT_SUPPORT =  "org.apply.expense.not.support";

	public static List<String> getIndirectRegions() {
		return indirectRegions;
	}
	
	public static String getSalesChannelByRegion(String region) {
		return salesChannelRegionMap.get(region);
	}

	public static List<String> getRegionBySalesChannel(String salesChannel,String bu){
        List<String> regionByBu = regionsBuMap.get(bu);
        ArrayList<String> list = new ArrayList<String>();
        if (StringUtils.isNotBlank(salesChannel)) {
            Iterator<String> iterator = regionByBu.iterator();
            while (iterator.hasNext()) {
                String next = iterator.next();
                if (salesChannelRegionMap.get(next).equals(salesChannel)) {
                    list.add(next);
                }
            }
        }
        return list;
    }
	
	public static int getChannelWeightByBrand(int brand) {
		if(brand == 4) {
			return 2;
		}
		return brand;
	}

	public static final String PROMOTION_EXCHANGE = "PROMOTION_EXCHANGE";

    public static final String PROMOTION_POINT_EXCHANGE = "PROMOTION_POINT_EXCHANGE";

    public static final String COUPON_EXCHANGE = "COUPON_EXCHANGE";

    public static final String WARNING_CONFIG_CODE_TYPE = "warning.config.code.type";

    public static final String PROMOTION_POINT_CATEGORY = "Promotion.Point.Category";

    public static final String REGION_INDIRECT = "Region.Indirect";

    public static final String REGION_DIRECT = "Region.Direct";

    /** 被串货经销商FLSR是否同意上报 **/
    public static final String TO_FLSR_CONFIRM_PROCESS_DAY = "to_flsr_confirm_process_day";
    
    /** 串货FLSR提交调查结果自动处理天数 **/
    public static final String FROM_FLSR_SUBMIT_SURVEY_PROCESS_DAY = "from_flsr_submit_survey_process_day";

    /** 串货ASM提交调查结果自动处理天数 **/
    public static final String FROM_ASM_SUBMIT_SURVEY_PROCESS_DAY = "from_asm_submit_survey_process_day";

    /** 被串货ASM处理天数 **/
    public static final String TO_ASM_SUBMIT_FEEDBACK_PROCESS_DAY = "to_asm_submit_feedback_process_day";

    /** 串货ASM反馈信息处理天数 **/
    public static final String FROM_ASM_RESPONSE_PROCESS_DAY = "from_asm_response_process_day";

    /** 当前环境是否生产环境 **/
    public static final String IS_PRODUCT = "is_product";

    /** 合伙人管理类型标签 **/
    public static final String PARTNER_PROPERTY_LABEL = "partner.partnerProperty.label";

    /** 商用用油样申请 **/
    public static final String LABEL_HTML = "<span style=\"display: inline-block; background-color: rgb(226, 24, 54); " +
            "color: rgb(255, 255, 255); margin-left: 3px; padding: 4px 6px; font-size: 10px; line-height: 10px; " +
            "border-radius: 4px; cursor: pointer;\">未上传</span>";

    /** 串货 **/
    public static final String FLEEING_GOODS = "FLEEING.GOODS";

    /** 油样上传邮件发送 **/
    public static final String OILSAMPLEAPPLY_UPLOAD_NOTIFY = "oilsampleapply.file.upload.notify";

    /** 新客户录入tab权限配置字典 **/
    public static final String NEW_CUSTOMER_TASK_PERMISSION = "new.customer.permission";

    /** 新客户录入tab权限配置字典 **/
    public static final String CUSTOMER_VISIT_TASK_PERMISSION = "customer.visit.task.permission";
	/** 用户对应角色 **/
    public static final String IS_ABM = "isABM";
    public static final String IS_ASM = "isASM";
    public static final String IS_MKT = "isMKT";
    public static final String IS_SALES = "isSALES";


}
