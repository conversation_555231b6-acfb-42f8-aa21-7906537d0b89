INSERT INTO [dbo].[wx_t_point_account] ([POINT_ACCOUNT_TYPE],[POINT_ACCOUNT_OWNER_ID],
[POINT_ACCOUNT_OWNER_NAME],[IS_ENABLED],[STATE]      ,[DELETE_FLAG],[CREATION_TIME],[CREATED_BY])   
VALUES ('SP',56601,'赤峰千合商贸有限公司',1,null,0,getdate(),1)

INSERT INTO [dbo].[wx_t_point_value_detail]([POINT_TYPE],[POINT_ACCOUNT_ID],[POINT_VALUE],[POINT_PAYED],
[BUSINESS_ID],[COMMENTS],[DELETE_FLAG]           ,[CREATION_TIME],[CREATED_BY],[LAST_UPDATE_TIME],[LAST_UPDATED_BY],
[TRANS_TIME])     VALUES ('CDM_STOCK_POINT',277,10208,0,(select id from wx_t_point_business 
where BUSINESS_TYPE_CODE='CALTEX_POINT_FROM_BI' and RELATED_CODE='PI20191017'),'手工BI系统积分计算 10月'           ,
0,getdate(),1,getdate(),1,'2019-10-01 00:00:00.000')

INSERT INTO [dbo].[wx_t_point_value_detail_log]           ([POINT_VALUE_ID],[MODIFIED_VALUE],[POINT_ACCOUNT_ID],
[BUSINESS_ID],[COMMENTS]           ,[DELETE_FLAG],[CREATION_TIME],[CREATED_BY],[LAST_UPDATE_TIME],[LAST_UPDATED_BY])     
VALUES           ((select pvd.id from wx_t_point_value_detail pvd where pvd.BUSINESS_ID=
(select id from wx_t_point_business where BUSINESS_TYPE_CODE='CALTEX_POINT_FROM_BI' and RELATED_CODE='PI20191017') 
and pvd.POINT_ACCOUNT_ID=277)           ,10208           ,277           ,
(select id from wx_t_point_business where BUSINESS_TYPE_CODE='CALTEX_POINT_FROM_BI' and RELATED_CODE='PI20191017')           ,'BI系统积分计算 10月',0,getdate(),1,getdate(),1)

select * from wx_t_point_business
select * from wx_t_point_business_type