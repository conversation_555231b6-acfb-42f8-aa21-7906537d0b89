1. 待汇总列表 POST
/v2elitecollectform/level1data.do?limit=10&start=0&field=id&direction=desc&executor=130972
参数：
	executor：被代理人用户ID
返回：
	resultLst：V2EliteCollectForm集合
	
2. 提交BS Leader汇总 RPC
v2EliteCollectFormService.submitLevel2Collect(Long executor)
参数：executor：被代理人用户ID	

3. 汇总详情 RPC
{"id":2,"jsonrpc":"2.0","method":"v2EliteCollectFormService.getCollectFundInfo","params":[2020,"Indirect"]}
参数：year: null-当前精英计划年份
		bu: null-全部
{
	"jsonrpc": "2.0",
	"id": 2,
	"result": {
		"chartData": {
			"Reimbursement Fund": [1812.23, 756.23, 0.0, 2.0, 0.0, 0.0],
			"Earned Fund": [3000.0, 1000.0, 0.0, 5000.0, 1000.0, 0.0]
		},
		"code": "success",
		"x": ["Consumer MKT Fund", "Consumer IVI Fund", "Consumer Annual Reward", "Commercial MKT Fund", "Commercial IVI Fund", "Commercial Spark"],
		"gridData": [{
			"consumerIviFund": "1,000",
			"commercialMktFund": "5,000",
			"consumerTotal": "4,000",
			"commercialTotal": "6,000",
			"commercialIviFund": "1,000",
			"title": "Earned Fund",
			"consumerMktFund": "3,000",
			"consumerAnnualFund": "0"
		}, {
			"consumerIviFund": "756.23",
			"commercialMktFund": "2",
			"consumerTotal": "2,568.46",
			"commercialTotal": "2",
			"commercialIviFund": "0",
			"title": "Reimbursement",
			"consumerMktFund": "1,812.23",
			"consumerAnnualFund": "0"
		}, {
			"consumerIviFund": "0",
			"commercialMktFund": "2",
			"consumerTotal": "2,666",
			"commercialTotal": "2",
			"commercialIviFund": "0",
			"title": "Invoice",
			"consumerMktFund": "2,666",
			"consumerAnnualFund": "0"
		}, {
			"consumerIviFund": "0",
			"commercialMktFund": "1,187.77",
			"consumerTotal": "1,431.54",
			"commercialTotal": "5,998",
			"commercialIviFund": "243.77",
			"title": "Fund Balance",
			"consumerMktFund": "1,187.77"
		}]
	}
}		

4. 提交问题反馈 RPC
v2EliteCollectFormService.issueFeedback(String issueDesc)
参数：issueDesc：问题描述		
	
页面参数：
executor: 被代理人用户ID

1.获取列表页面权限 RPC
operationPermissionService.getOperationPermissionByUser(Long userId, String moduleCode)
参数：
	userId: 被代理人用户ID/null
	moduleCode：EliteFundV2.collect
返回：
	weight：1-提交BS Leader 汇总权限

2. 提交/通过 RPC
v2EliteCollectFormService.accept(V2EliteCollectForm form, String remark, Long executor, Long versionNo)
参数：
	form:V2EliteCollectForm。非可编辑页面传{id:xxx}只需传ID
	remark:通过备注。可编辑页面没有备注传null
	executor:被代理人
	versionNo：流程版本号。申请页面传null。其他form.workflowInstance.versionNo
	
3. 获取表单明细 RPC
v2EliteCollectFormService.detail(Long id, String stepCode, Long executor)
参数：
	id: 表单ID
	stepCode: 流程当前步骤编码
	executor：被代理人用户ID
返回：
	currentStep：WorkflowStep
	form: V2EliteCollectForm
	form.workflowInstance.versionNo：版本号
	form.workflowInstance.acceptFlag：通过按钮标记。1-有权限，0-无权限
	form.workflowInstance.rejectFlag：驳回按钮标记。1-有权限，0-无权限
	form.workflowInstance.recallFlag：撤回按钮标记。1-有权限，0-无权限
	form.workflowInstance.abortFlag：终止按钮标记。1-有权限，0-无权限（先不处理）


4. 待办列表 POST
/v2elitecollectform/tododata.do?limit=10&start=0&field=executeTime&direction=desc&executor=65921
参数：
	distributorId: 经销商ID(BI经销商ID)
	distributorName：经销商名称
	fundType：基金类型
	applyProject：申报项目
	executor：被代理人用户ID
返回：
	resultLst：EliteFundForm集合
	resultLst[0].workflowInstance.currentStep 当前办理步骤
	resultLst[0].workflowInstance.listOperateName 列表中审批操作名称
	resultLst[0].workflowInstance.applyTime 申请时间
	resultLst[0].workflowInstance.requestUserName 申请人
	resultLst[0].workflowInstance.latestStepHistory.actionName 状态。resultLst[0].workflowInstance.latestStepHistory为null时显示草稿
	resultLst[0].workflowInstance.acceptFlag：通过按钮标记。1-有权限，0-无权限
	resultLst[0].workflowInstance.rejectFlag：驳回按钮标记。1-有权限，0-无权限
	resultLst[0].workflowInstance.abortFlag：终止按钮标记。1-有权限，0-无权限（先不处理）

5. 已办列表 POST
/v2elitecollectform/donedata.do?limit=10&start=0&field=executeTime&direction=desc&executor=122995
参数：
	distributorId: 经销商ID(BI经销商ID)
	distributorName：经销商名称
	fundType：基金类型
	applyProject：申报项目
	executor：被代理人用户ID
返回：
	resultLst：EliteFundForm集合
	resultLst[0].workflowInstance.currentStep 当前办理步骤
	resultLst[0].workflowInstance.listOperateName 列表中审批操作名称
	resultLst[0].workflowInstance.applyTime 申请时间
	resultLst[0].workflowInstance.requestUserName 申请人
	resultLst[0].latestStepHistory.actionName 状态	
	resultLst[0].workflowInstance.recallFlag：撤回按钮标记。1-有权限，0-无权限
	resultLst[0].workflowInstance.abortFlag：终止按钮标记。1-有权限，0-无权限（先不处理）
	
6. 全部列表 POST
/v2elitecollectform/alldata.do?limit=10&start=0&field=executeTime&direction=desc&executor=122995
参数：
	distributorId: 经销商ID(BI经销商ID)
	distributorName：经销商名称
	fundType：基金类型
	applyProject：申报项目
	executor：被代理人用户ID
返回：
	resultLst：EliteFundForm集合
	resultLst[0].workflowInstance.currentStep 当前办理步骤
	resultLst[0].workflowInstance.listOperateName 列表中审批操作名称
	resultLst[0].workflowInstance.applyTime 申请时间
	resultLst[0].workflowInstance.requestUserName 申请人
	resultLst[0].latestStepHistory.actionName 状态	
	resultLst[0].workflowInstance.recallFlag：撤回按钮标记。1-有权限，0-无权限
	resultLst[0].workflowInstance.abortFlag：终止按钮标记。1-有权限，0-无权限（先不处理）

7. 流程办理历史 RPC
v2EliteCollectFormService.getWorkflowStepHistory(String id)
参数：
	id：表单ID
返回：
	data：List<WorkflowStepHistory>
	data[0].workflowStep.stepName：步骤名称
	data[0].executorName：执行人姓名
	data[0].executor：执行人ID
	data[0].actualExecutorName：代办人姓名（actualExecutor不为空且不等于executor时有效）
	data[0].actualExecutor：代办人ID（actualExecutor不为空且不等于executor时有效）
	data[0].approveStatusText：办理状态
	data[0].executeTime：执行时间
	data[0].approveRemark：审批备注

8. 流程图 RPC
v2EliteCollectFormService.getWorkflowStepInstances(String id)
参数：
	id：表单ID
返回：
	data：List<WorkflowStepInstance>
	data[0].workflowStep.stepName：步骤名称
	data[0].worInsExeList：List<WorInsExeList> 办理人列表
	data[0].worInsExeList[0].executor：执行人ID
	data[0].worInsExeList[0].executorName：执行人姓名
	data[0].worInsExeList[0].actualExecutorName：代办人姓名（actualExecutor不为空且不等于executor时有效）
	data[0].worInsExeList[0].actualExecutor：代办人ID（actualExecutor不为空且不等于executor时有效）

9. 驳回 RPC
v2EliteCollectFormService.reject(V2EliteCollectForm form, String remark, Long executor, Long versionNo)
参数：
	id:表单ID
	remark：流程备注
	executor：被代理人用户ID
	versionNo：版本号

10. 撤回 RPC
v2EliteCollectFormService.recall(V2EliteCollectForm form, String remark, Long executor, Long versionNo)
参数：
	id:表单ID
	remark：流程备注
	executor：被代理人用户ID
	versionNo：版本号

		
13931559111	