package com.sys.auth.dao;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.sys.auth.model.WxTProperties;
import com.sys.auth.model.WxTPropertiesExample;

public interface WxTPropertiesMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wx_t_properties
     *
     * @mbggenerated Tue Sep 08 15:56:55 CST 2015
     */
    int countByExample(WxTPropertiesExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wx_t_properties
     *
     * @mbggenerated Tue Sep 08 15:56:55 CST 2015
     */
    int deleteByExample(WxTPropertiesExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wx_t_properties
     *
     * @mbggenerated Tue Sep 08 15:56:55 CST 2015
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wx_t_properties
     *
     * @mbggenerated Tue Sep 08 15:56:55 CST 2015
     */
    int insert(WxTProperties record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wx_t_properties
     *
     * @mbggenerated Tue Sep 08 15:56:55 CST 2015
     */
    int insertSelective(WxTProperties record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wx_t_properties
     *
     * @mbggenerated Tue Sep 08 15:56:55 CST 2015
     */
    List<WxTProperties> selectByExample(WxTPropertiesExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wx_t_properties
     *
     * @mbggenerated Tue Sep 08 15:56:55 CST 2015
     */
    WxTProperties selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wx_t_properties
     *
     * @mbggenerated Tue Sep 08 15:56:55 CST 2015
     */
    int updateByExampleSelective(@Param("record") WxTProperties record, @Param("example") WxTPropertiesExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wx_t_properties
     *
     * @mbggenerated Tue Sep 08 15:56:55 CST 2015
     */
    int updateByExample(@Param("record") WxTProperties record, @Param("example") WxTPropertiesExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wx_t_properties
     *
     * @mbggenerated Tue Sep 08 15:56:55 CST 2015
     */
    int updateByPrimaryKeySelective(WxTProperties record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wx_t_properties
     *
     * @mbggenerated Tue Sep 08 15:56:55 CST 2015
     */
    int updateByPrimaryKey(WxTProperties record);

    WxTProperties selectByMap(Map<String,Object> reqMap);

    Long getSequenceByType(@Param("sequenceType") Integer sequenceType);
    
    int updateByCodeType(Map<String,Object> reqMap);
    
    Long getSequenceBySequenceName(Map<String,Object> reqMap);

}