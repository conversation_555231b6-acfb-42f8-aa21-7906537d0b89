/*    ==Scripting Parameters==

    Source Server Version : SQL Server 2016 (13.0.4604)
    Source Database Engine Edition : Microsoft SQL Server Standard Edition
    Source Database Engine Type : Standalone SQL Server

    Target Server Version : SQL Server 2017
    Target Database Engine Edition : Microsoft SQL Server Standard Edition
    Target Database Engine Type : Standalone SQL Server
*/

USE [pmpdb01]
GO
/****** Object:  StoredProcedure [dbo].[pr_syn_sellin_promotion]    Script Date: 23/10/2020 11:32:03 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
ALTER PROCEDURE [dbo].[pr_syn_sellin_promotion]
as
begin 
	set xact_abort on
	SET NOCOUNT ON
	begin try 
		BEGIN TRAN
		declare @start_time datetime /*同步开始时间*/
		declare @end_time datetime/*同步截止时间*/
		declare @check_flag int/*同步验证标记*/
		set @end_time=DATEADD(MINUTE, -10, getdate())
		set @check_flag=1
		select @start_time=max(ods.create_time) from [wx_t_promotion_delivery_detail] pdd 
		left join [PP_MID].[cmt].[campaign_offer_detail_summary] ods on pdd.[ext_property9]=ods.[campaign_offer_detail_summary_id]
		
		insert into wx_t_sell_in_promotion_delivery (sales_order, region,[distributor_id], sold_to_code,product_sku,post_year_month,
		pack_units,quantity, liters, revenue_rmb, [base_trans_sell_in_id],[pricing_date])
		select distinct bsi.[sales_reference_doc] sales_order, bso.region,bsi.[distributor_id],
				bsi.[customer_code] sold_to_code,bsi.[product_code_SAP] product_sku, bsi.[trans_time] post_year_month,
				di.dic_item_name pack_units,
				convert(int, round((bsi.liters - bsi.[free_gift_liters_business] - bsi.[below_standard_price_liters_business])/bp.unit, 0)) quantity, 
				round((bsi.liters - bsi.[free_gift_liters_business] - bsi.[below_standard_price_liters_business])/bp.unit, 0) * bp.unit liters,
				bsi.revenue_rmb, cod.[base_trans_sell_in_id],bsi.[pricing_date]
				from [PP_MID].[cmt].[campaign_offer_detail] cod 
		left join [PP_MID].dbo.[syn_dw_to_pp_sap_sell_in] bsi on cod.[base_trans_sell_in_id]=bsi.[base_trans_sell_in_id]
				left join [PP_MID].[dbo].[syn_dw_to_pp_dim_sales_org_hier] bso on bsi.org_hier_id=bso.org_hier_id
				left join [PP_MID].[dbo].[syn_dw_to_pp_product] bp on bp.[product_code_SAP]=bsi.[product_code_SAP]
				left join wx_t_dic_item di on di.dic_type_code='BiProduct.packType' and di.dic_item_code=bp.[pack_type]
		where not exists (select 1 from wx_t_sell_in_promotion_delivery pd where pd.base_trans_sell_in_id=cod.base_trans_sell_in_id)
		
		insert into [wx_t_promotion_delivery_detail] ([promotion_id]
		      ,[award_type]
		      ,[source_type]
		      ,[source_id]
		      ,[award_quantity]
		      ,[promotion_desc]
		      ,[delivery_status]
		      ,[ext_property1]
		      ,[ext_property2]
		      ,[ext_property3]
		      ,[ext_property4]
			  ,[ext_property7]
			  ,[ext_property8]
		      ,[ext_property9]
		      ,[create_time]
		      ,[offer_key])
		select convert(bigint, [campaign_code]) [promotion_id],
		 convert(int, rc.[ext_property1]) [award_type],
		 'delivery' [source_type],
		 -1 [source_id],
		 ods.[value] [award_quantity],
		 o.ext_property1 [promotion_desc],
		 10 [delivery_status],
		 rc.[reward_category_name] [ext_property1],
		 rc.[ext_property2] [ext_property2],
		 rc.[ext_property3] [ext_property3],
		 rc.ext_property4 [ext_property4],
		 ods.distributor_id [ext_property7],
		 ods.product_channel [ext_property8],
		 ods.[campaign_offer_detail_summary_id] [ext_property9],
		 getdate() create_time,
		 ods.offer_id offer_key
		  FROM [PP_MID].[cmt].[campaign_offer_detail_summary] ods
		  left join [PP_MID].[cmt].[offer] o on ods.offer_id=o.offer_id
		  left join [PP_MID].[cmt].[campaign] c on o.campaign_id=c.campaign_id
		  left join [PP_MID].[cmt].[reward_category] rc on rc.reward_category_id=o.reward_category_id
		  where (@start_time is null or ods.create_time>@start_time) and ods.create_time<@end_time

		  insert into wx_t_delivery_order_rel (delivery_detail_id, delivery_id)
			select distinct pdd.id, pd.delivery_id from [wx_t_promotion_delivery_detail] pdd
			left join [PP_MID].[cmt].[campaign_offer_detail_summary] ods on pdd.ext_property9=ods.campaign_offer_detail_summary_id
			left join [PP_MID].[cmt].[campaign_offer_detail] cod on ods.offer_id=cod.offer_id and ods.distributor_id=cod.distributor_id
				and CONVERT(varchar(100), ods.create_time, 112)=CONVERT(varchar(100), cod.create_time, 112)
			left join wx_t_sell_in_promotion_delivery pd on pd.base_trans_sell_in_id=cod.base_trans_sell_in_id
			where (@start_time is null or ods.create_time>@start_time) and ods.create_time<@end_time
		
		select @check_flag=case when (select sum(ods.[value]) from [wx_t_promotion_delivery_detail] pdd
					left join [PP_MID].[cmt].[campaign_offer_detail_summary] ods on pdd.ext_property9=ods.campaign_offer_detail_summary_id
					where (@start_time is null or ods.create_time>@start_time) and ods.create_time<@end_time)
				=(select sum(ods.[value]) from [PP_MID].[cmt].[campaign_offer_detail_summary] ods 
					where (@start_time is null or ods.create_time>@start_time) and ods.create_time<@end_time) then 1 else 0 end			
		IF @check_flag=0
		begin
			ROLLBACK TRAN
			insert into wx_log (operator,log_type,ext_property1,ext_property2,ext_property3,create_time) values (1,'syn.bi.error','同步订单促销',CONVERT(varchar(100), @start_time, 21),'同步数量不一致',@end_time);
		end
		ELSE
		begin
			COMMIT TRAN	
			insert into wx_log (operator,log_type,ext_property1,ext_property2, create_time) values (1,'syn.bi.success','同步订单促销',CONVERT(varchar(100), @start_time, 21),@end_time);
		end
	end try
	begin catch
		ROLLBACK TRAN
		insert into wx_log (operator,log_type,ext_property1,ext_property2,ext_property3,create_time) values (1,'syn.bi.error','同步订单促销',CONVERT(varchar(100), @start_time, 21),ERROR_MESSAGE(),@end_time);
	end catch
end