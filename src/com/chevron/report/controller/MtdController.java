package com.chevron.report.controller;

import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import com.chevron.report.business.MtdBizService;
import com.chevron.report.model.MtdEmailConfig;
import com.common.exception.WxPltException;
import com.common.util.ContextUtil;
import com.common.util.StringUtils;

@Controller
@RequestMapping(value="/mtd")
public class MtdController {

	@Autowired
	private MtdBizService mtdBizService;
	
	@ResponseBody
	@RequestMapping(value = "/test.do", method = { RequestMethod.POST,  RequestMethod.GET })
	public Map<String, Object> loadData(HttpServletRequest request, HttpServletResponse response){
		try {
			MtdEmailConfig emailConfig = mtdBizService.getEmailConfig();
			String to = null;
			String cc = null;
			if("1".equals(emailConfig.getAutoToCustomer())) {
				to = emailConfig.getCustomerEmailTo();
				cc = emailConfig.getCustomerEmailCc();
			}else {
				to = emailConfig.getInternalEmailTo();
				to = emailConfig.getInternalEmailCc();
			}
			if(StringUtils.isBlank(to)) {
				throw new RuntimeException("未配置接收邮箱");
			}
			mtdBizService.sendMtdReport(null, to, cc, emailConfig.getWarningEmailHonorific(), emailConfig.getWarningEmailTo(), 
					emailConfig.getWarningEmailCc(), System.currentTimeMillis() + "", false, ContextUtil.getCurUser().getUserId(),null,null,null,null, false);
		} catch (WxPltException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return null;
	}

}
