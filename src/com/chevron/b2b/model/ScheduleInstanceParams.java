package com.chevron.b2b.model;

import com.chevron.pms.model.BaseParams;
import com.common.util.DateUtil;
import com.common.util.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 计划实例列表查询参数
 * <AUTHOR>
 * @version 1.0 2018-12-20 17:16
 */
public class ScheduleInstanceParams extends BaseParams {

	/**  经销商ID  **/
    private Long distributorId;

	/**  门店ID  **/
    private Long workshopId;

	/**  执行人姓名  **/
    private String executorName;

	/**  状态  **/
    private Integer status;

	/** 起始日期条件字符串 */
	private String publishDateFromStr;
	
	/** 截止日期条件字符串 */
	private String publishDateToStr;

	/** 起始日期条件字符串 */
	private String submitDateFromStr;
	
	/** 截止日期条件字符串 */
	private String submitDateToStr;

	/** 起始日期条件字符串 */
	private String startDateFromStr;
	
	/** 截止日期条件字符串 */
	private String startDateToStr;

	/** 起始日期条件字符串 */
	private String endDateFromStr;
	
	/** 截止日期条件字符串 */
	private String endDateToStr;
	
	private String userId;
	
	private String scheduleType;
	
	private Integer[] statusArray;
	
	private Integer startStatus;
	
	private Integer endStatus;
	
	private String salesChannel;
	
	private Long scheduleId;
	
	private boolean includeAgreeNumb;

	/**
	 * 获取经销商ID条件
	 * @return 经销商ID条件
	 */
    public Long getDistributorId() {
        return distributorId;
    }
    
	/**
	 * 设置经销商ID条件
	 * @param distributorId 经销商ID条件
	 */
    public void setDistributorId(Long distributorId) {
        this.distributorId = distributorId;
    }

	/**
	 * 获取门店ID条件
	 * @return 门店ID条件
	 */
    public Long getWorkshopId() {
        return workshopId;
    }
    
	/**
	 * 设置门店ID条件
	 * @param workshopId 门店ID条件
	 */
    public void setWorkshopId(Long workshopId) {
        this.workshopId = workshopId;
    }

	/**
	 * 获取执行人姓名条件
	 * @return 执行人姓名条件
	 */
    public String getExecutorName() {
        return executorName;
    }
    
	/**
	 * 设置执行人姓名条件
	 * @param executorName 执行人姓名条件
	 */
    public void setExecutorName(String executorName) {
        this.executorName = executorName;
    }

	/**
	 * 获取状态条件
	 * @return 状态条件
	 */
    public Integer getStatus() {
        return status;
    }
    
	/**
	 * 设置状态条件
	 * @param status 状态条件
	 */
    public void setStatus(Integer status) {
        this.status = status;
    }

	/**
	 * 获取起始日期条件字符串
	 * @return 起始日期条件字符串
	 */
	public String getPublishDateFromStr() {
		return publishDateFromStr;
	}

	/**
	 * 获取截止日期条件字符串
	 * @return 截止日期条件字符串
	 */
	public String getPublishDateToStr() {
		return publishDateToStr;
	}

	/**
	 * 设置起始日期条件字符串
	 * @param publishDateFromStr 起始日期条件字符串
	 */
	public void setPublishDateFromStr(String publishDateFromStr) {
		this.publishDateFromStr = publishDateFromStr;
	}

	/**
	 * 设置截止日期条件字符串
	 * @param publishDateToStr 截止日期条件字符串
	 */
	public void setPublishDateToStr(String publishDateToStr) {
		this.publishDateToStr = publishDateToStr;
	}

	/**
	 * 获取起始日期条件
	 * @return 起始日期条件
	 */
	public Date getPublishDateFrom() {
		if(StringUtils.isEmpty(publishDateFromStr)){
			return null;
		}
		return DateUtil.parseDate(publishDateFromStr);
	}

	/**
	 * 获取截止日期条件
	 * @return 截止日期条件
	 */
	public Date getPublishDateTo() {
		if(StringUtils.isEmpty(publishDateToStr)){
			return null;
		}
		return DateUtil.addDays(DateUtil.parseDate(publishDateToStr), 1);
	}

	/**
	 * 获取起始日期条件字符串
	 * @return 起始日期条件字符串
	 */
	public String getSubmitDateFromStr() {
		return submitDateFromStr;
	}

	/**
	 * 获取截止日期条件字符串
	 * @return 截止日期条件字符串
	 */
	public String getSubmitDateToStr() {
		return submitDateToStr;
	}

	/**
	 * 设置起始日期条件字符串
	 * @param submitDateFromStr 起始日期条件字符串
	 */
	public void setSubmitDateFromStr(String submitDateFromStr) {
		this.submitDateFromStr = submitDateFromStr;
	}

	/**
	 * 设置截止日期条件字符串
	 * @param submitDateToStr 截止日期条件字符串
	 */
	public void setSubmitDateToStr(String submitDateToStr) {
		this.submitDateToStr = submitDateToStr;
	}

	/**
	 * 获取起始日期条件
	 * @return 起始日期条件
	 */
	public Date getSubmitDateFrom() {
		if(StringUtils.isEmpty(submitDateFromStr)){
			return null;
		}
		return DateUtil.parseDate(submitDateFromStr);
	}

	/**
	 * 获取截止日期条件
	 * @return 截止日期条件
	 */
	public Date getSubmitDateTo() {
		if(StringUtils.isEmpty(submitDateToStr)){
			return null;
		}
		return DateUtil.addDays(DateUtil.parseDate(submitDateToStr), 1);
	}

	/**
	 * 获取起始日期条件字符串
	 * @return 起始日期条件字符串
	 */
	public String getEndDateFromStr() {
		return endDateFromStr;
	}

	/**
	 * 获取截止日期条件字符串
	 * @return 截止日期条件字符串
	 */
	public String getEndDateToStr() {
		return endDateToStr;
	}

	/**
	 * 设置起始日期条件字符串
	 * @param endDateFromStr 起始日期条件字符串
	 */
	public void setEndDateFromStr(String endDateFromStr) {
		this.endDateFromStr = endDateFromStr;
	}

	/**
	 * 设置截止日期条件字符串
	 * @param endDateToStr 截止日期条件字符串
	 */
	public void setEndDateToStr(String endDateToStr) {
		this.endDateToStr = endDateToStr;
	}

	/**
	 * 获取起始日期条件
	 * @return 起始日期条件
	 */
	public Date getEndDateFrom() {
		if(StringUtils.isEmpty(endDateFromStr)){
			return null;
		}
		return DateUtil.parseDate(endDateFromStr);
	}

	/**
	 * 获取截止日期条件
	 * @return 截止日期条件
	 */
	public Date getEndDateTo() {
		if(StringUtils.isEmpty(endDateToStr)){
			return null;
		}
		return DateUtil.addDays(DateUtil.parseDate(endDateToStr), 1);
	}

	/**
	 * 获取起始日期条件字符串
	 * @return 起始日期条件字符串
	 */
	public String getStartDateFromStr() {
		return startDateFromStr;
	}

	/**
	 * 获取截止日期条件字符串
	 * @return 截止日期条件字符串
	 */
	public String getStartDateToStr() {
		return startDateToStr;
	}

	/**
	 * 设置起始日期条件字符串
	 * @param startDateFromStr 起始日期条件字符串
	 */
	public void setStartDateFromStr(String startDateFromStr) {
		this.startDateFromStr = startDateFromStr;
	}

	/**
	 * 设置截止日期条件字符串
	 * @param startDateToStr 截止日期条件字符串
	 */
	public void setStartDateToStr(String startDateToStr) {
		this.startDateToStr = startDateToStr;
	}

	/**
	 * 获取起始日期条件
	 * @return 起始日期条件
	 */
	public Date getStartDateFrom() {
		if(StringUtils.isEmpty(startDateFromStr)){
			return null;
		}
		return DateUtil.parseDate(startDateFromStr);
	}

	/**
	 * 获取截止日期条件
	 * @return 截止日期条件
	 */
	public Date getStartDateTo() {
		if(StringUtils.isEmpty(startDateToStr)){
			return null;
		}
		return DateUtil.addDays(DateUtil.parseDate(startDateToStr), 1);
	}

	@Override
	protected Map<String, String> getFieldMap() {
		return null;
	}

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public String getScheduleType() {
		return scheduleType;
	}

	public void setScheduleType(String scheduleType) {
		this.scheduleType = scheduleType;
	}

	public Integer[] getStatusArray() {
		return statusArray;
	}

	public void setStatusArray(String statusArrayStr) {
		List<Integer> list = new ArrayList<Integer>();
		if(StringUtils.isNotBlank(statusArrayStr)) {
			String[] arr = statusArrayStr.split(",");
			for(String item : arr) {
				if(StringUtils.isNotBlank(item)) {
					list.add(Integer.parseInt(item));
				}
			}
		}
		if(list.isEmpty()) {
			this.statusArray = null;
		}else {
			this.statusArray = list.toArray(new Integer[list.size()]);
		}
	}

	public Integer getStartStatus() {
		return startStatus;
	}

	public void setStartStatus(Integer startStatus) {
		this.startStatus = startStatus;
	}

	public Integer getEndStatus() {
		return endStatus;
	}

	public void setEndStatus(Integer endStatus) {
		this.endStatus = endStatus;
	}

	public String getSalesChannel() {
		return salesChannel;
	}

	public void setSalesChannel(String salesChannel) {
		this.salesChannel = salesChannel;
	}

	public Long getScheduleId() {
		return scheduleId;
	}

	public void setScheduleId(Long scheduleId) {
		this.scheduleId = scheduleId;
	}

	public boolean isIncludeAgreeNumb() {
		return includeAgreeNumb;
	}

	public void setIncludeAgreeNumb(boolean includeAgreeNumb) {
		this.includeAgreeNumb = includeAgreeNumb;
	}
}
