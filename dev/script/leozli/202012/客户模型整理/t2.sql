truncate table PP_MID.[dbo].[pp_distributor_customer20201112]
INSERT INTO PP_MID.[dbo].[pp_distributor_customer20201112]
           ([partner_id]
           ,[customer_type]
           ,[customer_id]
           ,[customer_name]
           ,[dist_code]
           ,[address]
           ,[customer_status]
           ,[dms_key]
           ,dms_distributor_code
           ,[dms_customer_code]
           ,[contact_person]
           ,[contact_person_tel]
           ,[from_source]
           ,[customer_create_time]
           ,[customer_label]
           ,[business_property]
           ,[delete_flag]
           ,[create_time]
           ,[update_time]
           ,[customer_segement]
           ,[dsr_code]
           ,[customer_onboard_time]
           , customer_subtype
           ,customer_code)
select partner_id
           ,[customer_type]
           ,id [customer_id]
           ,[customer_name]
           ,[dist_code]
           ,[address]
           ,[customer_status]
           ,[dms_key]
           ,dms_distributor_code
           ,[dms_customer_code]
           ,[contact_person]
           ,[contact_person_tel]
           ,[from_source]
           ,case when a.min_time is not null and a.min_time < a.[customer_create_time] then a.min_time else a.[customer_create_time] end [customer_create_time]
           ,case when [customer_label]=0 and customer_type=1 then 65536 when customer_label=0 then 268435456 else [customer_label] end [customer_label]
           ,case when [business_property]=0 then 1073741824 else [business_property] end [business_property]
           ,0 [delete_flag], 
           getdate() [create_time]
           ,getdate() [update_time]
           ,[customer_segement]
           ,a.excute_user_id [dsr_code]
           ,isnull((select min(ws.create_time) from wx_t_workshop_status ws where ws.workshop_id=a.id and ws.workshop_with_status='3'), a.[customer_create_time]) [customer_onboard_time]
           , (SELECT top 1 d.item_code
                           FROM PP_MID.[dbo].pp_dict20201112 d
                           WHERE d.type_code = 'DistributorCustomer.customerLabel'
                                 AND d.item_code & (case when [customer_label]=0 then 65536 else [customer_label] end) > 0 order by d.sort_numb)
           , customer_code
           from (
select (case when w.customer_type=8 then 3 else w.customer_type end) [customer_type],pe.partner_id, w.id,
w.work_shop_name [customer_name], w.region_id [dist_code], w.work_shop_address [address], w.customer_code,
case when w.status=20 then -100 else w.status end [customer_status],
w.dms_key [dms_key], dw.Distributor_Code dms_distributor_code, dw.[Customer_Code] [dms_customer_code], 
w.[contact_person] [contact_person], 
w.[contact_person_tel] [contact_person_tel], 
isnull((select vtm.[value_after_transform]
from [wx_t_value_transform_map] vtm where vtm.[transform_type]='DmsCube.DistributorCustomer.fromSourceMapping' and vtm.[value_before_transform]=w.from_source
),w.[from_source]) [from_source], w.excute_user_id,
w.type [customer_segement],
w.create_time [customer_create_time], w.creator,(select min(ws.create_time) from wx_t_workshop_status ws where ws.workshop_id=w.id) min_time,
(case when w.customer_type=1/*****门店*****/ then
				 (case when w.ext_flag&65536>0 then 2 else 0 end/*陈列之星-1*/)
				 + (case when exists (select 1 from wx_t_workshop_employee we where we.version_no=1 and we.workshop_id=w.id) then 1024 else 0 end/*B2B店-10*/)
				 + (case when exists (select 1 from wx_task_sub s join wx_task_main m on m.task_main_id = s.task_main_id 
						where m.tmb_type_code in ('TT_2_CDMCLZX')   and s.task_status = 4 and s.check_evaluation>=6 and s.org_id=w.id) then 32768 else 0 end/*陈列大比拼-15*/)
				  + (case when w.ext_flag&6144>0 then 2048 else 0 end/*店招店(11,12)-11*/) 
				  + (case when w.ext_flag&32768>0 then 4096 else 0 end/*定位店(15)-12*/)
				  + (case when w.ext_flag&1024>0 then 8192 else 0 end/*参与德乐400MGX陈列活动(10)-13*/)
				  + (case when w.ext_flag&16>0 then 16384 else 0 end/*导航店(4)-30*/)
				  + (case when w.ext_flag&16384>0 then 131072 else 0 end/*黑金店*/) 
			when w.customer_type=2/*****车队*****/ then 
				(case when w.ext_flag&1024>0 then 8192 else 0 end/*参与德乐400MGX陈列活动(10)-13*/)
				+ (case when w.ext_flag&6144>0 then 2048 else 0 end/*店招店(11,12)-11*/) 
			else 0 end)  [customer_label],
w.business_weight|(w.from_source & 3) [business_property] from wx_t_work_shop w 
left join wx_t_workshop_partner wp on w.id=wp.workshop_id
left join wx_t_partner_o2o_enterprise pe on pe.partner_id=wp.partner_id
left join dw_dms_workshop dw on dw.[Distributor_Code] + '/' + dw.[Customer_Code]=w.dms_key
left join wx_t_organization o on o.id=wp.partner_id
where --not exists (select 1 from PP_MID.dbo.pp_distributor_customer20201112 c where c.customer_type=1 and c.customer_id=w.id) and 
o.status=1 and w.status in (3, 20) and w.delete_flag=0
and w.from_source & (1+2+48+64+128+256) > 0
and wp.partner_id!=9 and pe.distributor_id is not null 
and not exists (select 1 from PP_MID.dbo.syn_dw_to_pp_customer_org_sales bir 
left join dw_region_sales_channel_rel bisr on bir.region=bisr.region_name
where bir.distributor_id=pe.distributor_id and bisr.bu='Direct')
--and not exists (select 1 from wx_t_dic_item di1 where di1.dic_type_code='SynToBi.DistributorCustomer.excludeDistributor' and di1.dic_item_code=pe.distributor_id)
) a

truncate table PP_MID.DBO.pp_user;
insert into PP_MID.DBO.pp_user (user_name_cn,user_code,org_id,user_type,enable_flag,create_time,update_time)
	select u.ch_name, u.user_id, u.org_id,'distributor', u.status,getdate(),getdate() from wx_t_user u 
	left join wx_t_organization o on o.id=u.org_id
	left join wx_t_partner_o2o_enterprise pe on pe.partner_id=o.id
	where o.type=1 and o.status=1 and (u.type is null or u.type !='1') and o.id!=9
	and not exists (select 1 from PP_MID.dbo.syn_dw_to_pp_customer_org_sales bir 
	left join dw_region_sales_channel_rel bisr on bir.region=bisr.region_name
	where bir.distributor_id=pe.distributor_id and bisr.bu='Direct')
	and not exists (select 1 from PP_MID.DBO.pp_user bu where bu.user_code=u.user_id)
	
truncate table PP_MID.dbo.pp_dist_customer_inspections;	
insert into PP_MID.dbo.pp_dist_customer_inspections (id,customer_type,customer_id,inspections_time,
	business_type,enable_flag,dsr_code,create_time,update_time) 
	select s.task_id, 
	(case when w.customer_type=8 then 3 else w.customer_type end) customer_type,
	s.org_id, s.xg_sj, 
	case when m.tmb_type_code in ('TT_2_FLEET_ZF', 'TT_2_XD_CAI') then 2 
		when m.tmb_type_code in ('TT_2_XD') then 1 else 4 end business_type,
	1 enable_flag, m.excute_user_id, getdate(), getdate()
	from wx_task_sub s LEFT JOIN wx_task_main m ON s.task_main_id = m.task_main_id
	left join wx_t_organization o on o.id=m.tenant_id
	left join wx_t_partner_o2o_enterprise pe on pe.partner_id=o.id
	left join wx_t_work_shop w on w.id=s.org_id
	where m.tmb_type_code IN ('TT_2_XD','TT_2_XD_CAI','TT_2_FLEET_ZF','TT_2_PROJECT_CLIENT_VISIT') 
	and s.task_status='4' and m.tenant_id!=9 and o.status=1
	and not exists (select 1 from PP_MID.dbo.syn_dw_to_pp_customer_org_sales bir 
	left join dw_region_sales_channel_rel bisr on bir.region=bisr.region_name
	where bir.distributor_id=pe.distributor_id and bisr.bu='Direct') 
	and not exists (select 1 from PP_MID.dbo.pp_dist_customer_inspections bi where bi.id=s.task_id)	

truncate table PP_MID.dbo.pp_sell_through;	
insert into PP_MID.dbo.pp_sell_through ([customer_type]
		  ,[customer_id]
		  ,[year_month]
		  ,[order_no]
		  ,[product_code]
		  ,[sales_unit]
		  ,[sales_amount]
		  ,[sales_quantity]
		  ,[sales_liters]
		  ,[saler_code]
		  ,[order_creator]
		  ,[order_creator_mobile]
		  ,[order_status]
		  ,[order_create_time]
		  ,[order_paied_time]
		  ,[order_cancel_time]
		  ,[order_delivery_time]
		  ,[create_time]
		  ,[update_time])
	SELECT (case when w.customer_type=8 then 3 else w.customer_type end) [customer_type], 
	wto.work_shop_id, wto.create_time,
		wto.order_no, isnull(tbc.sku,tp.sku) sku, tp.units, null,wol.amount [sales_quantity],
		wol.amount*convert(float, isnull(tbp.capacity, tp.capacity)) [sales_liters],
		isnull((select u.user_id from wx_t_user u left join wx_t_organization o on o.id=u.org_id where (u.type is null or u.type != 1) and o.type=1 and u.user_id=wto.creator),
			 isnull(ws.excute_user_id, isnull((select top 1 m.excute_user_id from wx_task_sub s LEFT JOIN wx_task_main m ON s.task_main_id = m.task_main_id
					where s.org_id=ws.id and m.tmb_type_code IN ('TT_2_CLZX', 'TT_2_SD','TT_2_XD','TT_2_XD_CAI') order by s.xg_sj desc), 
					(select u.user_id from wx_t_user u left join wx_t_organization o on o.id=u.org_id where (u.type is null or u.type != 1) and o.type=1 and u.user_id=ws.creator)))) [saler_code], 
		null [order_creator], null, 100 [order_status], wto.create_time [order_create_time],
		null [order_paied_time], null [order_cancel_time], wto.update_time [order_delivery_time],getdate(),getdate()
					FROM wx_t_order wto
		LEFT JOIN wx_t_order_line wol ON wto.id = wol.order_id
		left join wx_t_work_shop w on w.id=wto.work_shop_id
		LEFT JOIN wx_t_product tp ON wol.sku = tp.sku
		left join wx_t_map_bundle_product tbc on tbc.bsku=wol.sku
		left join wx_t_product tbp ON tbc.sku = tbp.sku
		LEFT JOIN wx_t_work_shop ws ON ws.id = wto.work_shop_id
		LEFT JOIN wx_T_workshop_partner wp ON ws.id = wp.workshop_id
		left join wx_t_organization o on o.id=wp.partner_id
		left join wx_t_partner_o2o_enterprise pe on pe.partner_id=o.id
	WHERE (wto.status=11 or (wto.partner_confirm_type = 1 and wto.status!=6) or EXISTS (
						SELECT 1
						FROM wx_t_db2b_order db_or
						WHERE  charindex(db_or.order_no, wto.source) > 0
							AND db_or.delete_flag = 0
							AND db_or.status IN (1)
					))
		AND ws.delete_flag = 0
		AND wol.id IS NOT NULL
		AND wto.create_time >= '2020-01-01'
		AND wto.order_type not in('DA','DP','SPDA')
		AND wp.partner_id!=9 
		and o.status=1
		and not exists (select 1 from PP_MID.dbo.syn_dw_to_pp_customer_org_sales bir 
		left join dw_region_sales_channel_rel bisr on bir.region=bisr.region_name
		where bir.distributor_id=pe.distributor_id and bisr.bu='Direct')
		 and not exists (select 1 from PP_MID.dbo.pp_sell_through bst where bst.[order_no]=wto.order_no)	