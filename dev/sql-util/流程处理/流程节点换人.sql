--新流程步骤待办人列表
select w.workflow_name, ws.step_name 
--update wl set wl.source_key=(select user_id from wx_t_user where login_name='<PERSON>HGJ')
from wx_t_workflow_exe_list wl left join wx_t_workflow_step ws on ws.step_id=wl.step_id
left join wx_t_workflow w on w.workflow_code=ws.workflow_code 
where wl.source_type='USER' and wl.source_key=(select user_id from wx_t_user u where u.login_name='YU<PERSON>')

--新流程待办人
select el.* 
--update el set el.executor=(select user_id from wx_t_user where login_name='LHGJ')
from wx_t_workflow_instance wi left join wx_t_workflow_step_instance si on wi.flow_instance_id=si.workflow_instance_id
left join wx_t_wor_ins_exe_list el on el.step_instance_id=si.step_instance_id
where el.executor=(select user_id from wx_t_user u where u.login_name='OQFU') and execute_status=10
and wi.next_step_no=si.step_no

--老店招流程待办
select * 
--update n set n.operator=(select user_id from wx_t_user where login_name='Selina')
from wx_t_mkt_flow_node n where operator=(select user_id from wx_t_user where login_name='HOYD') and status=0