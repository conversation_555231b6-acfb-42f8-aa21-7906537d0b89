 select *
 --delete
  from wx_t_mechanic_qrcode where qr_code='dxv10jpe5579f4ru'
 select *
 --delete
  from wx_t_mec_ver_point where qrcode='dxv10jpe5579f4ru'

select *
--delete l
from wx_t_point_value_detail_log l 
where exists (select 1 from wx_t_point_value_detail d
LEFT JOIN dbo.wx_t_point_business pb ON d.BUSINESS_ID = pb.ID  where pb.id=8131
 and l.POINT_VALUE_ID=d.id)

select *
--delete d
from wx_t_point_value_detail d 
LEFT JOIN dbo.wx_t_point_business pb ON d.BUSINESS_ID = pb.ID where pb.id=8131;

select top 1000 *
--delete pb
 from dbo.wx_t_point_business pb where EARN_TYPE='QR_CODE' and RELATED_CODE=461
 order by id desc
 select * from wx_t_point_account where id=1121
