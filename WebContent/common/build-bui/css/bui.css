.clearfix {
  *zoom: 1;
}
.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}
.clearfix:after {
  clear: both;
}
.hide-text {
  overflow: hidden;
  text-indent: 100%;
  white-space: nowrap;
}
.input-block-level {
  display: block;
  width: 100%;
  min-height: 28px;
  /* Make inputs at least the height of their button counterpart */

  /* Makes inputs behave like true block-level elements */

  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -ms-box-sizing: border-box;
  box-sizing: border-box;
}
.bui-menu {
  list-style: none;
  margin: 0;
}
.bui-menu .bui-menu-item {
  cursor: pointer;
}
.bui-side-menu {
  height: 100%;
  text-align: left;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 0 3px;
}
.bui-side-menu .bui-menu-title,
.bui-side-menu .bui-menu-title s,
.bui-side-menu .menu-leaf a,
.bui-side-menu .menu-leaf em {
  background: url("../img/menu-500-188.png") -9999px -9999px no-repeat;
}
.bui-side-menu a {
  color: #5c637b;
  cursor: pointer;
  outline: none;
}
.bui-side-menu a:hover {
  color: #414da7;
  text-decoration: none;
}
.bui-side-menu a:active {
  color: #5c637b;
}
.bui-side-menu .bui-menu-item-collapsed .bui-menu {
  display: none;
}
.bui-side-menu .bui-menu-item-collapsed .bui-menu-title s {
  background-position: 0 -30px;
}
.bui-side-menu .menu-leaf {
  outline: 0;
  line-height: 20px;
}
.bui-side-menu .menu-leaf a {
  height: 20px;
  text-indent: 24px;
  overflow: hidden;
  margin-bottom: 5px;
  display: block;
}
.bui-side-menu .menu-leaf a em {
  display: block;
  margin: 0 1px;
  font-style: normal;
  text-align: left;
  overflow: hidden;
  height: 19px;
  line-height: 22px;
  _line-height: 20px;
}
.bui-side-menu .bui-menu-item-selected a {
  color: #ffffff;
  background-position: 0 -130px;
}
.bui-side-menu .bui-menu-item-selected a em {
  background-position: right -100px;
}
.bui-side-menu .menu-second {
  padding-top: 1px;
  outline: 0;
  cursor: pointer;
}
.bui-side-menu .menu-second .bui-menu {
  margin-top: 5px;
}
.bui-side-menu .bui-menu-title-text {
  float: left;
  overflow: hidden;
  height: 14px;
  line-height: 14px;
  display: block;
  _margin-top: -2px;
}
.bui-side-menu .bui-menu-title {
  margin: 0 1px;
  background-position: 0 15px;
  height: 17px;
  color: #636775;
  font-weight: bold;
  overflow: hidden;
  line-height: 25px;
  vertical-align: middle;
  padding: 8px 0 5px 5px;
}
.bui-side-menu .bui-menu-title s {
  width: 10px;
  height: 10px;
  overflow: hidden;
  display: block;
  float: left;
  margin-right: 5px;
  margin-top: 1px;
  background-position: 0 -71px;
}
.bui-pop-menu {
  border: 1px solid #c3c3d6;
  background-color: #ffffff;
  padding: 2px 0;
  position: absolute;
}
.bui-pop-menu .bui-menu-item {
  padding: 3px 15px;
}
.bui-pop-menu .bui-menu-item-hover {
  background-color: #3366cc;
  color: #ffffff;
}
.bui-context-menu {
  position: absolute;
  border: 1px solid #c3c3d6;
  background-color: #e8e9ef;
  padding: 2px;
  z-index: 1100;
  background: url("../img/separator-2-340.gif") repeat-y 28px 0 #e8e9ef;
}
.bui-context-menu .bui-menu-item {
  white-space: nowrap;
  overflow: hidden;
  position: relative;
  padding: 0;
}
.bui-context-menu .bui-menu-item .x-caret {
  position: absolute;
  right: 10px;
  top: 10px;
}
.bui-context-menu .bui-menu-item-sparator {
  border-top: 1px solid #c3c3d6;
  height: 1px;
  background-color: white;
  margin-left: 27px;
}
.bui-context-menu .bui-menu-item-link {
  cursor: default;
  display: block;
  *zoom: 1;
  width: 134px;
  height: 24px;
  margin: 1px;
  padding: 0 2px;
  text-decoration: none;
  border: 1px solid transparent;
  _border-color: tomato;
  _filter: chroma(color=#ff6347);
}
.bui-context-menu .bui-menu-item-icon {
  display: inline-block;
  *display: inline;
  *zoom: 1;
  width: 16px;
  height: 16px;
  margin: 0 11px 0 2px;
  border: 0 solid white;
}
.bui-context-menu .bui-menu-item-hover {
  background-color: #e8e9ef;
}
.bui-context-menu .bui-menu-item-open .bui-menu-item-link,
.bui-context-menu .bui-menu-item-hover .bui-menu-item-link {
  background-color: #E0E6FC;
  border: 1px solid #A9B9F5;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  border-radius: 2px;
  cursor: pointer;
  overflow: hidden;
}
.bui-context-menu .bui-menu-item-text {
  color: #333333;
  font-size: 11px;
  _font-size: 12px;
  line-height: 20px;
  *zoom: 1;
}
.bui-context-menu .bui-menu-item-disabled {
  outline: none;
}
.bui-context-menu .bui-menu-item-disabled .bui-menu-item-text {
  color: #999999;
}
.bui-context-menu .bui-menu-item-disabled .bui-menu-item-icon {
  opacity: 0.5;
  filter: alpha(opacity=50);
}
.button-tabs .bui-tab-item-selected {
  color: #ffffff;
  font-weight: bold;
  background-color: #6c8ffc;
  background-image: -moz-linear-gradient(top, #6c9dfc, #6c79fc);
  background-image: -ms-linear-gradient(top, #6c9dfc, #6c79fc);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#6c9dfc), to(#6c79fc));
  background-image: -webkit-linear-gradient(top, #6c9dfc, #6c79fc);
  background-image: -o-linear-gradient(top, #6c9dfc, #6c79fc);
  background-image: linear-gradient(top, #6c9dfc, #6c79fc);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#6c9dfc', endColorstr='#6c79fc', GradientType=0);
  border-color: #6c79fc #6c79fc #2135fa;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
  border: 1px solid #5984de;
}
.button-tabs .bui-tab-item-selected:hover,
.button-tabs .bui-tab-item-selected:active,
.button-tabs .bui-tab-item-selected.active,
.button-tabs .bui-tab-item-selected.disabled {
  background-color: #6c79fc;
}
.button-tabs .bui-tab-item-selected[disabled] {
  background-color: #6c79fc;
}
.button-tabs .bui-tab-item-selected:active,
.button-tabs .bui-tab-item-selected.active {
  background-color: #3a4bfb \9;
}
.nav-tabs .bui-tab-item {
  border: 1px solid #c3c3d6;
  border-bottom-color: transparent;
  margin-bottom: -1px;
  margin-right: 3px;
}
.nav-tabs .bui-tab-item-text,
.nav-tabs a {
  padding: 5px 15px;
  background-color: #eeeeee;
  position: relative;
  display: inline-block;
  *display: inline;
  /* IE7 inline-block hack */

  *zoom: 1;
}
.nav-tabs .bui-tab-item-selected {
  background-color: #ffffff;
  -webkit-border-radius: 2px 2px 0 0;
  -moz-border-radius: 2px 2px 0 0;
  border-radius: 2px 2px 0 0;
}
.nav-tabs .bui-tab-item-selected .bui-tab-item-text,
.nav-tabs .bui-tab-item-selected a {
  background-color: #ffffff;
  position: relative;
  border-bottom: -1px;
  z-index: 10;
}
.link-tabs .bui-tab-item-selected a {
  color: #3366cc;
}
.tab-nav-bar,
.tab-nav-arrow,
.tab-nav-wrapper,
.tab-nav-inner,
.bui-nav-tab-item,
.tab-item-close,
.bui-nav-tab-item .tab-item-inner,
.bui-nav-tab-item .l,
.bui-nav-tab-item .r {
  background: url("../img/tab-140-120.gif") -9999px -9999px no-repeat;
}
.tab-nav-bar {
  position: relative;
  width: 100%;
  z-index: 1;
  overflow: hidden;
  height: 21px;
  background-position: 0 20px;
  background-repeat: repeat-x;
}
.tab-content-container {
  width: 100%;
}
.tab-content-container iframe {
  border: none;
}
.tab-content {
  height: 100%;
}
.tab-nav-arrow {
  display: block;
  position: absolute;
  text-decoration: none;
  overflow: hidden;
  width: 13px;
  height: 13px;
  text-indent: -99px;
  top: 4px;
}
.bui-tab-force .tab-nav-arrow {
  display: none;
}
.bui-tab-force .tab-nav-wrapper {
  margin: 0;
}
.bui-nav-tab .tab-nav-list {
  padding-left: 15px;
}
.arrow-left {
  background-position: 0 -60px;
  left: 5px;
}
.arrow-left-active .arrow-left {
  background-position: -40px -60px;
  cursor: pointer;
}
.arrow-right {
  background-position: -20px -60px;
  right: 5px;
}
.arrow-right-active .arrow-right {
  background-position: -100px -60px;
  cursor: pointer;
}
.tab-nav-wrapper {
  margin: 0 23px;
  background-position: 0 -100px;
  background-repeat: repeat-x;
}
.tab-nav-inner {
  background-position: 0 20px;
  background-repeat: repeat-x;
  background-color: #e2eaf4;
  margin: 0 2px;
  overflow: hidden;
  position: relative;
}
.tab-nav-list {
  position: relative;
  left: 0;
  padding: 0;
  margin: 0;
  *zoom: 1;
}
.tab-nav-list:before,
.tab-nav-list:after {
  display: table;
  content: "";
}
.tab-nav-list:after {
  clear: both;
}
.bui-nav-tab-item {
  color: #263e74;
  float: left;
  width: 140px;
  height: 21px;
  position: relative;
  cursor: pointer;
  padding: 0;
  z-index: 1;
}
.bui-nav-tab-item .tab-item-inner {
  background-position: -15px -4px;
  margin: 0 20px 0 5px;
  background-color: transparent;
}
.bui-nav-tab-item .l,
.bui-nav-tab-item .r {
  position: absolute;
  display: block;
  height: 21px;
  top: 0;
}
.bui-nav-tab-item .l {
  background-position: 0 -4px;
  width: 20px;
  left: -15px;
}
.bui-nav-tab-item .r {
  background-position: right -4px;
  width: 33px;
  right: 0;
}
.tab-item-title {
  cursor: pointer;
}
.tab-nav-actived {
  z-index: 2;
}
.tab-nav-actived .tab-item-inner {
  background-position: -15px -29px;
}
.tab-nav-actived .l {
  background-position: 0 -29px;
  z-index: 2;
}
.tab-nav-actived .r {
  background-position: right -29px;
  z-index: 2;
}
.tab-item-close {
  display: block;
  position: absolute;
  text-decoration: none;
  overflow: hidden;
  cursor: pointer;
  width: 13px;
  height: 13px;
  text-indent: -99px;
  z-index: 3;
  top: 4px;
  right: 17px;
  background-position: 0 -80px;
}
.tab-item-close:hover {
  background-position: -20px -80px;
}
.bui-select-list {
  border: 1px solid #c3c3d6;
  background-color: #ffffff;
  overflow: auto;
}
.bui-select-list .bui-list-item {
  padding: 2px 4px 2px 8px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -o-user-select: none;
  user-select: none;
  cursor: pointer;
  border: 1px solid #ffffff;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
  margin: 1px;
  padding-left: 8px;
  *zoom: 1;
}
.bui-select-list .bui-list-item-hover {
  background: #dee5ff;
  border: 1px solid #ccd7ff;
}
.bui-select-list .bui-list-item-selected {
  background: #ccd7ff;
  border: 1px solid #99afff;
}
.bui-select-list .bui-list-item-disabled {
  background: none;
  color: #cccccc;
  cursor: default;
}
.bui-select-list .bui-list-item-disabled a {
  color: #cccccc;
  cursor: default;
}
.bui-select-list .bui-list-item-disabled a:hover {
  text-decoration: none;
}
.bui-select {
  display: inline-block;
  *display: inline;
  *zoom: 1;
}
.bui-select .bui-select-input {
  _border: 1px solid #c3c3d6;
  border-right: none;
  vertical-align: middle;
  outline: none;
  _height: 22px;
  _padding: 1px 4px;
  width: 118px;
}
.bui-select .x-icon {
  vertical-align: middle;
  cursor: pointer;
  height: 20px;
  width: 20px;
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  border-radius: 0;
}
.bui-select .icon-caret {
  vertical-align: middle;
  margin-top: -4px;
}
.bui-select-disabled .x-icon {
  opacity: 0.5;
  filter: alpha(opacity=50);
  cursor: default;
}
/** list-checkbox **/
.x-checkbox {
  background: url("../img/check_icon-100-100.gif") no-repeat 0px 0px transparent;
}
.x-radio {
  background: url("../img/radio_icon-64-40.gif") no-repeat 0 0 transparent;
}
.x-checkbox,
.x-radio {
  width: 13px;
  height: 20px;
  *height: 17px;
  vertical-align: top;
  *vertical-align: baseline;
  margin-right: 5px;
  display: inline-block;
  *display: inline;
  /* IE7 inline-block hack */

  *zoom: 1;
}
.x-checkbox:hover,
.x-radio:hover,
.bui-list-item-hover .x-checkbox,
.bui-list-item-hover .x-radio {
  background-position: -16px 0px;
}
.bui-list-item-selected .x-checkbox,
.bui-list-item-selected .x-radio,
.checked .x-checkbox {
  background-position: 0 -20px;
}
.bui-list-item-disabled .x-checkbox,
.bui-list-item-disabled .x-radio {
  background-position: -48px 0px;
}
.bui-simple-list-disabled .x-checkbox,
.bui-list-item-selected.bui-list-item-disabled .x-checkbox,
.bui-simple-list-disabled .x-radio,
.bui-list-item-selected.bui-list-item-disabled .x-radio {
  background-position: -48px -20px;
}
.bui-combox {
  border: 1px solid #c3c3d6;
}
.bui-combox ul {
  *zoom: 1;
}
.bui-combox ul:before,
.bui-combox ul:after {
  display: table;
  content: "";
}
.bui-combox ul:after {
  clear: both;
}
.bui-combox .bui-list-item {
  float: left;
  display: inline-block;
  float: left;
  margin: 2px;
  padding: 1px 6px;
  transition: color 200ms;
  -moz-transition: color 200ms;
  /* Firefox 4 */

  -webkit-transition: color 200ms;
  /* Safari 和 Chrome */

  -o-transition: color 200ms;
  /* Opera */

  background-color: #5bc0de;
  color: #eeeeee;
}
.bui-combox .bui-list-item button {
  border: none;
  padding: 0;
  background: transparent;
  margin-left: 5px;
  cursor: pointer;
  color: #eeeeee;
}
.bui-combox .bui-list-item-warn {
  background-color: #ee5f5b;
}
.bui-combox .bui-list-item-active {
  background-color: #fbb450;
}
.bui-combox .bui-combox-input {
  border: none;
}
.bui-combox .bui-combox-input:focus {
  border: none;
  outline: none;
}
.bui-tag-follow .bui-simple-list {
  float: left;
}
.bui-tag-follow .bui-combox-input {
  float: left;
  margin-top: 2px;
  width: 50px;
}
.bui-overlay {
  position: absolute;
  left: -9999px;
  top: -9999px;
}
.bui-dialog {
  background-color: #ffffff;
  border: 1px solid #c3c3d6;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  border-radius: 2px;
  -webkit-box-shadow: inset 0px 0px 0px 1px #ffffff;
  -moz-box-shadow: inset 0px 0px 0px 1px #ffffff;
  box-shadow: inset 0px 0px 0px 1px #ffffff;
  position: absolute;
  background-color: #e8e9ef;
  z-index: 1070;
}
.bui-picker {
  z-index: 1200;
}
.bui-dialog {
  /*-----icon-------*/

}
.bui-dialog .bui-contentbox {
  background-color: #e8e9ef;
  margin: 1px;
}
.bui-dialog .bui-stdmod-header {
  height: 22px;
  line-height: 22px;
  font-size: 12px;
  font-weight: bold;
  padding-left: 4px;
  color: #333333;
  cursor: move;
  -webkit-user-select: none;
  -moz-user-select: none;
  -o-user-select: none;
  user-select: none;
}
.bui-dialog .bui-stdmod-body {
  background-color: #ffffff;
  border: 1px solid #c3c3d6;
  margin: 0 4px;
  padding: 20px;
}
.bui-dialog .bui-stdmod-footer {
  margin: 4px 0;
  text-align: center;
  -webkit-user-select: none;
  -moz-user-select: none;
  -o-user-select: none;
  user-select: none;
}
.bui-dialog .bui-stdmod-footer .button {
  margin-right: 15px;
  vertical-align: baseline;
}
.bui-dialog .bui-stdmod-footer .button:last-child {
  margin-right: 0;
}
.bui-dialog a.bui-ext-close {
  position: absolute;
  right: 6px;
  top: 4px;
  width: 16px;
  height: 16px;
  text-decoration: none;
  outline: none;
  overflow: hidden;
  cursor: pointer;
  text-decoration: none;
  z-index: 1;
}
.bui-dialog .bui-ext-close-x {
  display: block;
  font-size: 17px;
  *font-size: 16px;
  height: 14px;
  width: 14px;
  line-height: 14px;
  cursor: pointer;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  border-radius: 2px;
}
.bui-message {
  padding-bottom: 10px;
  z-index: 1300;
}
.bui-message .bui-stdmod-body {
  padding: 0 35px 20px;
  background-color: transparent;
  border: none;
}
.bui-message .bui-stdmod-body .x-icon {
  float: left;
}
.bui-message .bui-message-content {
  margin: 5px 0 0 40px;
  line-height: 24px;
  font-weight: bold;
  font-size: 14px;
}
.bui-message .bui-message-content p {
  font-weight: normal;
}
.image-pbar button,
.bui-grid .bui-pagingbar button,
.image-pbar .bui-bar-item-button,
.bui-grid .bui-pagingbar .bui-bar-item-button,
.image-pbar .bui-pb-page,
.bui-grid .bui-pagingbar .bui-pb-page {
  background: url("../img/table-191-450.gif") no-repeat -999px -999px transparent;
  overflow: hidden;
}
.image-pbar button,
.bui-grid .bui-pagingbar button {
  height: 16px;
  width: 16px;
  margin: 0;
  text-indent: -100px;
  *text-indent: 0;
  *font-size: 0;
  border: none;
  overfow: hidden;
}
.image-pbar .bui-button-disabled,
.bui-grid .bui-pagingbar .bui-button-disabled {
  opacity: 1;
  filter: alpha(opacity=100);
}
.image-pbar .bui-pb-first button,
.bui-grid .bui-pagingbar .bui-pb-first button {
  background-position: 3px 2px;
}
.image-pbar .bui-pb-first .bui-button-disabled,
.bui-grid .bui-pagingbar .bui-pb-first .bui-button-disabled {
  background-position: 3px -18px;
}
.image-pbar .bui-pb-prev button,
.bui-grid .bui-pagingbar .bui-pb-prev button {
  background-position: -57px 2px;
}
.image-pbar .bui-pb-prev .bui-button-disabled,
.bui-grid .bui-pagingbar .bui-pb-prev .bui-button-disabled {
  background-position: -57px -18px;
}
.image-pbar .bui-pb-next button,
.bui-grid .bui-pagingbar .bui-pb-next button {
  background-position: -37px 2px;
}
.image-pbar .bui-pb-next .bui-button-disabled,
.bui-grid .bui-pagingbar .bui-pb-next .bui-button-disabled {
  background-position: -37px -18px;
}
.image-pbar .bui-pb-last button,
.bui-grid .bui-pagingbar .bui-pb-last button {
  background-position: -17px 2px;
}
.image-pbar .bui-pb-last .bui-button-disabled,
.bui-grid .bui-pagingbar .bui-pb-last .bui-button-disabled {
  background-position: -17px -18px;
}
.image-pbar .bui-bar-item,
.bui-grid .bui-pagingbar .bui-bar-item {
  outline: none;
  margin: 0 2px;
  vertical-align: middle;
}
.image-pbar .bui-bar-item-button,
.bui-grid .bui-pagingbar .bui-bar-item-button {
  padding: 1px;
}
.image-pbar .bui-bar-item-button-hover,
.bui-grid .bui-pagingbar .bui-bar-item-button-hover {
  background-position: -130px 2px;
}
.image-pbar .bui-bar-item-button-disabled,
.bui-grid .bui-pagingbar .bui-bar-item-button-disabled {
  background-position: -999px -999px;
}
.image-pbar .bui-pb-page,
.bui-grid .bui-pagingbar .bui-pb-page {
  color: #9d261d;
  font: 11px tahoma, arial, verdana, sans-serif;
  height: 14px;
  margin: 0;
  width: 20px;
  vertical-align: baseline;
  *vertical-align: middle;
  background-color: white;
  padding: 0 2px;
}
.image-pbar .bui-bar-item-text,
.bui-grid .bui-pagingbar .bui-bar-item-text {
  vertical-align: middle;
}
.image-pbar .bui-pb-skip,
.bui-grid .bui-pagingbar .bui-pb-skip {
  background-position: -999px -999px;
}
.image-pbar .bui-pb-skip button,
.bui-grid .bui-pagingbar .bui-pb-skip button {
  text-indent: 0;
  background-position: -80px -20px;
  height: 20px;
  font-size: 11px;
  width: 41px;
}
.image-pbar .bui-pb-skip button:hover,
.bui-grid .bui-pagingbar .bui-pb-skip button:hover {
  background-position: -150px -20px;
}
.image-pbar .bui-bar-item-separator,
.bui-grid .bui-pagingbar .bui-bar-item-separator {
  margin: 0 5px;
}
.pagination .bui-bar-item {
  float: left;
  margin: 0;
}
.pagination .disabled {
  _background-color: #ffffff;
}
.pagination .disabled a:hover {
  text-decoration: none;
}
.bar-btn-add,
.bar-btn-del,
.bar-btn-edit,
.bar-btn-close,
.bar-btn-import,
.bar-btn-export,
.bar-btn-save,
.bar-btn-create,
.bui-bar-item-separator {
  background: url("../img/table-191-450.gif") no-repeat -999px -999px transparent;
}
.bui-bar-item-separator {
  height: 14px;
  margin: 0 3px 0 2px;
  width: 2px;
  background-position: -80px 0;
  vertical-align: middle;
}
.bui-grid-button-bar {
  float: left;
}
.bui-grid-button-bar .bui-bar-item {
  margin-right: 10px;
}
.bui-grid-button-bar .bar-btn-add,
.bui-grid-button-bar .bar-btn-edit,
.bui-grid-button-bar .bar-btn-del,
.bui-grid-button-bar .bar-btn-close,
.bui-grid-button-bar .bar-btn-import,
.bui-grid-button-bar .bar-btn-export,
.bui-grid-button-bar .bar-btn-save,
.bui-grid-button-bar .bar-btn-create {
  padding-left: 18px;
}
.bui-grid-button-bar .bar-btn-edit {
  background-position: 2px -307px;
}
.bui-grid-button-bar .bar-btn-add {
  background-position: -48px -137px;
}
.bui-grid-button-bar .bar-btn-del {
  background-position: 2px -247px;
}
.bui-grid-button-bar .bar-btn-close {
  background-position: -48px -157px;
}
.bui-grid-button-bar .bar-btn-import {
  background-position: 2px -187px;
}
.bui-grid-button-bar .bar-btn-export {
  background-position: 2px -217px;
}
.bui-grid-button-bar .bar-btn-save {
  background-position: 2px -277px;
}
.bui-grid-button-bar .bar-btn-create {
  background-position: 2px -307px;
}
.bui-grid-button-bar .button-small [class^="icon-"] {
  margin: -2px 2px 0 -5px;
}
.bui-simple-list-focused {
  outline: none;
}
.bui-grid-table .sort-asc .bui-grid-sort-icon,
.bui-grid-table .sort-desc .bui-grid-sort-icon,
.bui-grid-table .bui-grid-hd-menu-trigger,
.bui-grid-table .bui-grid-cascade-icon {
  background-image: url("../img/table-191-450.gif");
  background-repeat: no-repeat;
}
.bui-simple-grid .bui-grid-table {
  border: 1px solid #dddddd;
}
.bui-drag-line {
  position: absolute;
  border-left: 1px solid #ccc;
}
.bui-grid-header {
  border-top: 1px solid #dddddd;
  *height: 25px;
}
.bui-grid-group-header {
  *height: 51px;
}
.bui-grid-group-header .bui-grid-hd-empty {
  display: none;
}
.bui-grid-body,
.bui-grid-height .bui-grid-body .bui-grid-table {
  border-bottom: 1px solid #dddddd;
}
.bui-grid-body,
.bui-grid-header {
  border-right: 1px solid #dddddd;
  border-left: 1px solid #dddddd;
}
.bui-grid-header {
  overflow: hidden;
  *position: relative;
}
.bui-grid-header .table {
  margin: 0;
}
.bui-grid-header .bui-grid-table {
  *position: absolute;
  *top: 0;
  *left: 0;
  *z-index: 100;
}
.bui-grid-table {
  width: 100%;
  table-layout: fixed;
  border-collapse: separate;
}
.bui-grid,
.bui-simple-grid {
  /*width:100%;    */

  background-color: #ffffff;
}
.bui-grid th.left,
.bui-simple-grid th.left,
.bui-grid td.left,
.bui-simple-grid td.left {
  text-align: left;
}
.bui-grid th.right,
.bui-simple-grid th.right,
.bui-grid td.right,
.bui-simple-grid td.right {
  text-align: right;
}
.bui-grid th.center,
.bui-simple-grid th.center,
.bui-grid td.center,
.bui-simple-grid td.center {
  text-align: center;
}
.bui-grid-border .bui-grid-hd,
.bui-grid-border .bui-grid-cell,
.bui-grid-border .bui-grid-header-row td {
  border-left: 1px solid #c5c5c5;
}
.bui-grid-border .bui-grid-cell-empty,
.bui-grid-border .bui-grid-hd-empty {
  border-left: none;
}
.bui-grid-header-row td,
.bui-grid-header-row th {
  padding: 0;
  margin: 0;
}
th.bui-grid-hd-empty,
td.bui-grid-cell-empty {
  padding: 0;
  margin: 0;
  border-left: none;
}
.bui-grid-table {
  /* row */

}
.bui-grid-table .bui-grid-hd {
  border-bottom: 1px solid #c5c5c5;
  text-align: left;
  cursor: default;
}
.bui-grid-table tr td:first-child,
.bui-grid-table tr th:first-child {
  border-left-width: 0;
}
.bui-grid-table .bui-grid-hd-inner {
  background-color: #f4f4f4;
  background-image: -moz-linear-gradient(top, #ffffff, #e4e4e4);
  background-image: -ms-linear-gradient(top, #ffffff, #e4e4e4);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#ffffff), to(#e4e4e4));
  background-image: -webkit-linear-gradient(top, #ffffff, #e4e4e4);
  background-image: -o-linear-gradient(top, #ffffff, #e4e4e4);
  background-image: linear-gradient(top, #ffffff, #e4e4e4);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffffff', endColorstr='#e4e4e4', GradientType=0);
}
.bui-grid-table .bui-grid-hd-inner {
  height: 24px;
  overflow: hidden;
  font-weight: normal;
  background-position: 0 0;
  position: relative;
}
.bui-grid-table .bui-grid-db-hd .bui-grid-hd-inner {
  padding: 12px 0 13px;
}
.bui-grid-table .bui-grid-hd-title {
  line-height: 22px;
  font-size: 12px;
  padding: 0 4px 0 4px;
}
.bui-grid-table .bui-grid-sort-icon {
  display: inline-block;
  *display: inline;
  *zoom: 1;
  height: 15px;
  width: 15px;
}
.bui-grid-table .bui-grid-hd-menu-trigger {
  cursor: pointer;
  display: none;
  position: absolute;
  right: 0;
  top: 0;
  width: 14px;
  height: 24px;
  background-position: 0 -140px;
}
.bui-grid-table th.sortable {
  cursor: pointer;
}
.bui-grid-table .bui-grid-hd-hover .bui-grid-hd-inner,
.bui-grid-table .bui-grid-hd-open .bui-grid-hd-inner {
  background-color: #e0e6fc;
  background-image: -moz-linear-gradient(top, #e0e6fc, #e0e6fc);
  background-image: -ms-linear-gradient(top, #e0e6fc, #e0e6fc);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#e0e6fc), to(#e0e6fc));
  background-image: -webkit-linear-gradient(top, #e0e6fc, #e0e6fc);
  background-image: -o-linear-gradient(top, #e0e6fc, #e0e6fc);
  background-image: linear-gradient(top, #e0e6fc, #e0e6fc);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#e0e6fc', endColorstr='#e0e6fc', GradientType=0);
}
.bui-grid-table .bui-grid-hd-hover .bui-grid-hd-menu-trigger,
.bui-grid-table .bui-grid-hd-open .bui-grid-hd-menu-trigger {
  display: block;
}
.bui-grid-table .sort-asc .bui-grid-sort-icon {
  background-position: 5px -52px;
}
.bui-grid-table .sort-asc:hover .bui-grid-sort-icon {
  background-position: 5px -92px;
}
.bui-grid-table .sort-desc .bui-grid-sort-icon {
  background-position: 5px -72px;
}
.bui-grid-table .sort-desc:hover .bui-grid-sort-icon {
  background-position: 5px -112px;
}
.bui-grid-table .grid-header-checked-column,
.bui-grid-table .bui-grid-row-checked-column {
  text-align: center;
  width: 30px;
  vertical-align: middle;
  border-left-width: 0;
}
.bui-grid-table .grid-header-checked-column .bui-grid-hd-inner,
.bui-grid-table .bui-grid-row-checked-column .bui-grid-cell-inner {
  width: 30px;
}
.bui-grid-table .bui-grid-cell {
  overflow: hidden;
  border-top: 1px solid #ededed;
}
.bui-grid-table .bui-grid-row-group td {
  border-top: 1px solid #ededed;
}
.bui-grid-table .bui-grid-cell-empty {
  border-top: 1px solid #ededed;
}
.bui-grid-table .bui-grid-body .bui-grid-row-first td {
  border-top: none;
}
.bui-grid-table .bui-grid-body .bui-grid-table {
  border-bottom: 1px solid #ededed;
}
.bui-grid-table .bui-grid-header-row .bui-grid-cell-empty {
  line-height: 0;
  border: none;
}
.bui-grid-table .bui-grid-cell-inner {
  padding: 2px 0;
  position: relative;
  overflow: hidden;
}
.bui-grid-table .bui-grid-cell-text {
  padding: 0 4px;
  display: block;
  min-height: 20px;
  min-width: 25px;
  _height: 20px;
}
.bui-grid-table .bui-grid-error-cell .bui-grid-cell-text {
  padding-right: 20px;
}
.bui-grid-table .bui-grid-error-cell {
  position: relative;
}
.bui-grid-table .bui-grid-cell,
.bui-grid-table .bui-grid-cell-empty {
  word-break: break-all;
  word-wrap: break-word;
}
.bui-grid-table .grid-command {
  color: #3366cc;
  cursor: pointer;
  display: inline-block;
  margin-right: 5px;
}
.bui-grid-table .grid-command:hover {
  color: #ff6600;
}
.bui-grid-table .grid-command.disable {
  color: #ccc;
}
td.bui-grid-cell-empty {
  height: 0;
  line-height: 0;
}
.bui-grid-width .bui-grid-body {
  overflow-x: auto;
  overflow-y: hidden;
}
.bui-grid-height .bui-grid-body {
  overflow-x: auto;
  overflow-y: scroll;
  position: relative;
}
/**stripe**/
.bui-grid-strip .bui-grid-row-odd {
  background-color: #ffffff;
}
.bui-grid-strip .bui-grid-row-even {
  background-color: #fafafa;
}
.bui-simple-grid .bui-grid-table .bui-grid-row-hover,
.bui-grid .bui-grid-table .bui-grid-row-hover {
  background-color: #dee5ff;
}
.bui-simple-grid .bui-grid-table .bui-grid-row-selected,
.bui-grid .bui-grid-table .bui-grid-row-selected {
  background-color: #ccd7ff;
}
/**grid-bar**/
.bui-grid .bui-pagingbar {
  float: right;
  margin: 3px 0 0;
}
.bui-grid-tbar,
.bui-grid-bbar {
  *zoom: 1;
}
.bui-grid-tbar:before,
.bui-grid-bbar:before,
.bui-grid-tbar:after,
.bui-grid-bbar:after {
  display: table;
  content: "";
}
.bui-grid-tbar:after,
.bui-grid-bbar:after {
  clear: both;
}
.bui-grid-tbar {
  height: 34px;
  line-height: 34px;
}
.bui-grid-bbar {
  background-color: #f4f4f4;
  background-image: -moz-linear-gradient(top, #ffffff, #e4e4e4);
  background-image: -ms-linear-gradient(top, #ffffff, #e4e4e4);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#ffffff), to(#e4e4e4));
  background-image: -webkit-linear-gradient(top, #ffffff, #e4e4e4);
  background-image: -o-linear-gradient(top, #ffffff, #e4e4e4);
  background-image: linear-gradient(top, #ffffff, #e4e4e4);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffffff', endColorstr='#e4e4e4', GradientType=0);
  border: 1px solid #dddddd;
  border-top: none;
}
.bui-grid-radio-container,
.bui-grid-checkBox-container {
  text-align: center;
}
.bui-grid .x-grid-checkbox {
  background: url("../img/check_icon-100-100.gif") no-repeat 0px 3px transparent;
  width: 13px;
  height: 20px;
  display: inline-block;
  *display: inline;
  /* IE7 inline-block hack */

  *zoom: 1;
}
.x-grid-checkbox:hover {
  background-position: -16px 3px;
}
.bui-grid-row-selected .x-grid-checkbox,
.checked .x-grid-checkbox {
  background-position: 0 -18px;
}
.bui-grid-row-disabled .x-grid-checkbox {
  background-position: -48px 3px;
}
.bui-grid-row-selected.bui-grid-row-disabled .x-grid-checkbox {
  background-position: -48px -18px;
}
.bui-grid-cascade {
  vertical-align: middle;
}
.bui-grid-cascade-icon {
  cursor: pointer;
  display: inline-block;
  *display: inline;
  *zoom: 1;
  height: 10px;
  width: 10px;
  background-position: 0 -440px;
}
.bui-grid-cascade-expand .bui-grid-cascade-icon {
  background-position: -30px -440px;
}
.bui-grid-cascade-collapse {
  display: none;
}
.grid-column-menu .bui-menu-item-selected .icon {
  background-position: -144px -72px;
}
.bui-grid-cell-error {
  position: absolute;
  right: 10px;
  top: 6px;
}
.bui-grid-header .table {
  height: auto;
}
.bui-grid-summary-row {
  font-weight: bold;
}
.bui-grid-summary-row:first-child td {
  border-top: 1px solid #c5c5c5;
}
td.x-grid-rownumber {
  background-color: #e4e4e4;
  background-image: -moz-linear-gradient(left, #ffffff, #e4e4e4);
  background-image: -ms-linear-gradient(left, #ffffff, #e4e4e4);
  background-image: -webkit-gradient(linear, 0 0, 100% 0, from(#ffffff), to(#e4e4e4));
  background-image: -webkit-linear-gradient(left, #ffffff, #e4e4e4);
  background-image: -o-linear-gradient(left, #ffffff, #e4e4e4);
  background-image: linear-gradient(left, #ffffff, #e4e4e4);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffffff', endColorstr='#e4e4e4', GradientType=1);
  text-align: center;
}
/*Mask*/
.lp-ext-mask,
.lp-el-mask,
.bui-ext-mask {
  height: 100%;
  left: 0;
  opacity: 0.25;
  filter: alpha(opacity=25);
  position: absolute;
  top: 0;
  width: 100%;
  z-index: 1040;
  background-color: #333333;
}
.x-masked SELECT {
  _visibility: hidden;
}
.x-masked .bui-dialog SELECT {
  _visibility: visible;
}
.x-masked-relative {
  position: relative;
}
.lp-el-mask-msg,
.bui-ext-mask-msg {
  background: none repeat-x scroll 0 -16px #e8e9ef;
  border: 1px solid #c3c3d6;
  left: 0;
  padding: 2px;
  position: absolute;
  top: 0;
  z-index: 1050;
}
.lp-el-mask-msg div {
  border: 1px solid;
  cursor: wait;
  padding: 5px 10px;
  background-color: #ffffff;
  border-color: #c3c3d6;
  color: #333333;
}
.x-mask-loading div {
  background: none no-repeat scroll 5px 5px #ffffff;
  line-height: 16px;
  padding: 5px 10px 5px 25px;
  background-image: url("../img/load-16-16.gif");
}
/**chart**/
.node-info-container,
.node-title-container {
  position: absolute;
}
.node-info-container {
  border: 1px dashed #c3c3d6;
  padding: 5px;
  line-height: 14px;
  font-size: 11px;
  z-index: 10;
  opacity: 0.9;
  filter: alpha(opacity=90);
  background-color: #ffffff;
}
.node-info-container li {
  line-height: 14px;
}
.node-info-container .bui-caret {
  position: absolute;
  top: -7px;
  *top: -12px;
  left: 2px;
}
.node-info-container .table {
  border: 1px solid #c3c3d6;
  border-left: 0;
  border-bottom: 0;
  border-collapse: separate;
  border-spacing: 0;
}
.node-info-container .table td,
.node-info-container .table th {
  border-left: 1px solid #c3c3d6;
  border-bottom: 1px solid #c3c3d6;
}
.node-info-container-selected {
  z-index: 11;
}
.node-pos-top .bui-caret {
  top: auto;
  left: auto;
  bottom: -7px;
  left: 2px;
}
input.bui-form-field-error,
textarea.bui-form-field-error,
.bui-form-field-error input[type="text"] {
  border: 1px dotted red;
}
.bui-form-field-error .bui-select input[type="text"] {
  border-right: none;
}
.x-field-error {
  margin-left: 5px;
}
.x-field-error .x-field-error-text {
  padding-left: 5px;
  color: #fe0000;
}
.bui-form-field-disabled.calendar {
  background-color: #ebebe4;
}
.bui-form-field-checklist .bui-list-item,
.bui-form-field-radiolist .bui-list-item {
  display: inline-block;
  *display: inline;
  /* IE7 inline-block hack */

  *zoom: 1;
  margin-right: 10px;
}
.bui-form-tip-container {
  position: relative;
}
.bui-form-tip {
  padding-left: 5px;
  color: #999999;
}
.bui-form-tip .tip-text {
  margin-left: 5px;
  *white-space: nowrap;
}
.bui-form-tip .icon {
  opacity: 0.5;
  filter: alpha(opacity=50);
}
.calendar {
  background: url("../img/calendar-200-300.gif") no-repeat right -130px #ffffff;
}
.bui-calendar {
  width: 180px;
  border: 1px solid #c3c3d6;
  background-color: #ffffff;
  position: relative;
}
.x-datepicker-arrow .icon {
  margin-top: 2px;
  *overflow: hidden;
}
.x-datepicker-arrow .icon-caret-right {
  margin-left: 2px;
}
.x-datepicker-header {
  position: relative;
  height: 26px;
  line-height: 26px;
  background-color: #3366cc;
}
.x-datepicker-month {
  text-align: center;
}
.month-text-container {
  display: inline-block;
  *display: inline;
  *zoom: 1;
  cursor: pointer;
  font-weight: bold;
  color: #ffffff;
}
.month-text-container .bui-caret {
  border-top-color: #ffffff;
  margin-left: 5px;
}
.x-datepicker-arrow {
  line-height: 14px;
  text-align: center;
  display: inline-block;
  cursor: pointer;
}
.x-datepicker-prev,
.x-datepicker-next {
  position: absolute;
  top: 5px;
  width: 14px;
  height: 14px;
  border: 1px solid #3340cc;
}
.x-datepicker-prev {
  left: 5px;
}
.x-datepicker-next {
  right: 5px;
}
/**panel**/
.bui-calendar-panel {
  outline: none;
}
table.x-datepicker-inner {
  width: 100%;
  table-layout: fixed;
}
.x-datepicker-inner,
.x-datepicker-inner td,
.x-datepicker-inner th {
  border-collapse: separate;
}
table.x-datepicker-inner tr {
  height: 20px;
}
table.x-datepicker-inner th {
  width: 25px;
  height: 19px;
  padding: 0;
  color: #333333;
  font: normal 10px tahoma, arial, verdana, sans-serif;
  text-align: right;
  background-color: #f4f4f4;
  background-image: -moz-linear-gradient(top, #ffffff, #e4e4e4);
  background-image: -ms-linear-gradient(top, #ffffff, #e4e4e4);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#ffffff), to(#e4e4e4));
  background-image: -webkit-linear-gradient(top, #ffffff, #e4e4e4);
  background-image: -o-linear-gradient(top, #ffffff, #e4e4e4);
  background-image: linear-gradient(top, #ffffff, #e4e4e4);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffffff', endColorstr='#e4e4e4', GradientType=0);
  border-bottom: 1px solid #c3c3d6;
  cursor: default;
}
table.x-datepicker-inner th span {
  display: block;
  padding-right: 7px;
}
.x-datepicker-date {
  border: 1px solid;
  height: 17px;
  border-color: white;
  text-align: right;
  padding: 0;
}
.x-datepicker-date a {
  outline: none;
  padding-right: 4px;
  display: block;
  zoom: 1;
  font: normal 11px tahoma, arial, verdana, sans-serif;
  color: #333333;
  text-decoration: none;
  text-align: right;
  border: 1px solid #ffffff;
}
.x-datepicker-prevday a,
.x-datepicker-nextday a,
.x-datepicker-disabled a {
  text-decoration: none!important;
  color: #aaaaaa;
}
.x-datepicker-date a:hover {
  text-decoration: none!important;
  color: #333333;
  background-color: #dee5ff;
  border: 1px solid #ccd7ff;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
}
.x-datepicker-today a {
  border: 1px solid;
  color: #3366cc;
  border: 1px solid #99afff;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
}
.x-datepicker-active {
  cursor: pointer;
  color: #333333;
}
.x-datepicker-disabled a:hover {
  color: #aaaaaa;
  border-color: #ffffff;
  background: none;
}
.x-datepicker-selected a {
  background: repeat-x left top;
  background-color: #ccd7ff;
  border: 1px solid #99afff;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
}
/**footer**/
input.x-datepicker-time {
  width: 15px;
  height: 15px;
  cursor: pointer;
}
.x-datepicker-second {
  margin-right: 5px;
}
.bui-calendar-footer,
.x-monthpicker-footer {
  height: 30px;
  line-height: 30px;
  border-top: 1px solid #c3c3d6;
  text-align: center;
  background-color: #f4f4f4;
  background-image: -moz-linear-gradient(top, #ffffff, #e4e4e4);
  background-image: -ms-linear-gradient(top, #ffffff, #e4e4e4);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#ffffff), to(#e4e4e4));
  background-image: -webkit-linear-gradient(top, #ffffff, #e4e4e4);
  background-image: -o-linear-gradient(top, #ffffff, #e4e4e4);
  background-image: linear-gradient(top, #ffffff, #e4e4e4);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffffff', endColorstr='#e4e4e4', GradientType=0);
}
.bui-calendar-footer .button,
.x-monthpicker-footer .button {
  margin-right: 10px;
}
.bui-calendar-footer button.last,
.x-monthpicker-footer button.last {
  margin-right: 0;
}
.bui-calendar-footer .bui-bar-item-button,
.x-monthpicker-footer .bui-bar-item-button {
  *margin-top: 4px;
}
/**monthpicker**/
.bui-monthpicker,
.x-monthpicker {
  width: 180px;
  border: 1px solid #c3c3d6;
  position: absolute;
  background-color: #ffffff;
  top: 0;
  left: 0;
}
.bui-calendar .bui-monthpicker {
  top: -1px;
  left: -1px;
}
.x-monthpicker-yearnav .icon {
  margin-top: 2px;
  *margin-left: 2px;
  *overflow: hidden;
}
.x-monthpicker-months,
.x-monthpicker-years {
  height: 167px;
  width: 88px;
}
.x-monthpicker-months {
  float: left;
  border-right: 1px solid #c3c3d6;
  width: 87px;
}
.x-monthpicker-years {
  margin-left: 88px;
}
.x-monthpicker-item,
.x-timepicker .bui-list-item {
  float: left;
  margin: 4px 0 5px 0;
  font: normal 11px tahoma, arial, verdana, sans-serif;
  text-align: center;
  vertical-align: middle;
  height: 18px;
  width: 43px;
  border: 0 none;
}
.x-monthpicker-item a,
.x-timepicker .bui-list-item a {
  display: block;
  margin: 0 2px;
  text-decoration: none;
  color: #333333;
  border: 1px solid white;
  line-height: 17px;
}
.x-monthpicker-item-selected a,
.x-timepicker .bui-list-item-selected a {
  background-color: #ccd7ff;
  border: 1px solid #99afff;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
}
.x-monthpicker-item-hover a,
.x-timepicker .bui-list-item-hover a {
  color: black;
  background-color: #dee5ff;
  border: 1px solid #ccd7ff;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
}
.x-monthpicker-yearnav {
  height: 28px;
}
.x-monthpicker-yearnav .x-icon {
  margin: 6px 12px 5px 13px;
  cursor: pointer;
  opacity: 0.7;
  filter: alpha(opacity=70);
}
.x-monthpicker-yearnav .x-icon:hover {
  opacity: 0.8;
  filter: alpha(opacity=80);
}
.x-timepicker {
  width: 180px;
  background-color: #ffffff;
  border: 1px solid #c3c3d6;
  border-bottom-color: #c3c3d6;
}
.x-timepicker ul {
  *zoom: 1;
}
.x-timepicker ul:before,
.x-timepicker ul:after {
  display: table;
  content: "";
}
.x-timepicker ul:after {
  clear: both;
}
.x-timepicker .bui-list-item {
  height: 15px;
  width: 20px;
}
.x-editor-tips {
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
  border: 1px solid #e5a098;
  background-color: #ffffff;
  padding: 4px;
}
.bui-editor {
  z-index: 1080;
}
.bui-record-editor {
  padding: 3px 0 3px 3px;
  border: 1px solid #c3c3d6;
  background-color: white;
}
.bui-record-editor .button {
  *margin-top: 5px;
}
.bui-record-editor .bui-bar {
  width: 150px;
  position: absolute;
  left: 40%;
  background-color: white;
  border: 1px solid #c3c3d6;
  border-top: none;
  border-radius: 0 0 3px 3px;
  bottom: -37px;
}
.bui-record-editor .bui-form-field {
  padding-right: 3px;
}
.bui-record-editor .x-form-text {
  background-color: #ffffff;
  display: inline-block;
  height: 18px;
  padding: 1px 4px;
  border: 1px solid #c3c3d6;
}
.bui-editor .bui-form-check-field {
  background-color: #ffffff;
  text-align: center;
}
.bui-editor .bui-form-check-field input {
  vertical-align: middle;
}
.bui-tree-list {
  border: 1px solid #c3c3d6;
  overflow: auto;
  background-color: #ffffff;
}
.bui-tree-list ul {
  overflow: hidden;
}
.bui-tree-item {
  height: 20px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
}
.bui-tree-item-hover {
  background-color: #dee5ff;
}
.bui-tree-item-selected {
  background-color: #ccd7ff;
}
.bui-tree-item-disabled {
  color: #cccccc;
}
.bui-tree-list .x-tree-icon,
.bui-tree-grid .x-tree-icon {
  display: inline-block;
  *display: inline;
  /* IE7 inline-block hack */

  *zoom: 1;
  vertical-align: top;
  height: 20px;
  width: 16px;
}
.bui-tree-list .x-tree-elbow-expander,
.bui-tree-grid .x-tree-elbow-expander,
.bui-tree-list .x-tree-elbow-dir,
.bui-tree-grid .x-tree-elbow-dir,
.bui-tree-list .x-tree-elbow-leaf,
.bui-tree-grid .x-tree-elbow-leaf,
.bui-tree-list .x-tree-elbow-line,
.bui-tree-grid .x-tree-elbow-line,
.bui-tree-list .x-tree-elbow,
.bui-tree-grid .x-tree-elbow,
.bui-tree-list .x-tree-elbow-expander-end,
.bui-tree-grid .x-tree-elbow-expander-end,
.bui-tree-list .x-tree-elbow-end,
.bui-tree-grid .x-tree-elbow-end {
  background: url("../img/tree_icon-80-100.gif") no-repeat -999px -999px transparent;
}
.bui-tree-list .x-tree-icon-checkbox,
.bui-tree-grid .x-tree-icon-checkbox {
  background: url("../img/check_icon-100-100.gif") no-repeat 0px 0px transparent;
}
.bui-tree-list .x-tree-icon-radio,
.bui-tree-grid .x-tree-icon-radio {
  background: url("../img/radio_icon-64-40.gif") no-repeat 0px 0px transparent;
}
.bui-tree-list .x-tree-elbow-expander,
.bui-tree-grid .x-tree-elbow-expander {
  background-position: 0 0;
}
.bui-tree-list .x-tree-elbow-expander:hover,
.bui-tree-grid .x-tree-elbow-expander:hover {
  background-position: -32px 0;
}
.bui-tree-list .x-tree-elbow-dir,
.bui-tree-grid .x-tree-elbow-dir,
.bui-tree-list .x-tree-elbow-leaf,
.bui-tree-grid .x-tree-elbow-leaf {
  margin: 2px 3px 0 0;
}
.bui-tree-list .x-tree-elbow-dir,
.bui-tree-grid .x-tree-elbow-dir {
  background-position: 0 -80px;
}
.bui-tree-list .x-tree-elbow-leaf,
.bui-tree-grid .x-tree-elbow-leaf {
  background-position: -40px -80px;
}
.bui-tree-item-expanded .x-tree-elbow-expander,
.bui-grid-row-expanded .x-tree-elbow-expander {
  background-position: -16px 0;
}
.bui-tree-item-expanded .x-tree-elbow-expander:hover,
.bui-grid-row-expanded .x-tree-elbow-expander:hover {
  background-position: -48px 0;
}
.bui-tree-item-expanded .x-tree-elbow-dir,
.bui-grid-row-expanded .x-tree-elbow-dir {
  background-position: -20px -80px;
}
.bui-tree-item-checked {
  font-style: italic;
}
.x-tree-icon-checkbox:hover,
.x-tree-icon-radio:hover {
  background-position: -16px 0px;
}
.bui-tree-item-checked .x-tree-icon-checkbox,
.bui-grid-row-checked .x-tree-icon-checkbox,
.bui-tree-item-checked .x-tree-icon-radio,
.bui-grid-row-checked .x-tree-icon-radio {
  background-position: 0 -20px;
}
.bui-tree-item-checked .x-tree-icon-checkbox:hover,
.bui-grid-row-checked .x-tree-icon-checkbox:hover,
.bui-tree-item-checked .x-tree-icon-radio:hover,
.bui-grid-row-checked .x-tree-icon-radio:hover {
  background-position: -16px -20px;
}
.bui-tree-item-partial-checked .x-tree-icon-checkbox,
.bui-grid-row-partial-checked .x-tree-icon-checkbox,
.bui-tree-item-partial-checked .x-tree-icon-radio,
.bui-grid-row-partial-checked .x-tree-icon-radio {
  background-position: 0 -40px;
}
.bui-tree-item-partial-checked .x-tree-icon-checkbox:hover,
.bui-grid-row-partial-checked .x-tree-icon-checkbox:hover,
.bui-tree-item-partial-checked .x-tree-icon-radio:hover,
.bui-grid-row-partial-checked .x-tree-icon-radio:hover {
  background-position: -16px -40px;
}
.bui-tree-item-disabled .bui-grid-row-disabled .x-tree-icon-checkbox,
.bui-tree-item-disabled .bui-grid-row-disabled .x-tree-icon-radio {
  background-position: -48px 0;
}
.bui-tree-item-checked.bui-tree-item-disabled .x-tree-icon-checkbox,
.bui-grid-row-checked.bui-grid-row-disabled .x-tree-icon-checkbox,
.bui-tree-item-checked.bui-tree-item-disabled .x-tree-icon-radio,
.bui-grid-row-checked.bui-grid-row-disabled .x-tree-icon-radio {
  background-position: -48px -20px;
}
.x-tree-show-line .x-tree-elbow {
  background-position: 0 -20px;
}
.x-tree-show-line .x-tree-elbow-end {
  background-position: -20px -20px;
}
.x-tree-show-line .x-tree-elbow-line {
  background-position: -40px -20px;
}
.x-tree-show-line .x-tree-elbow-expander,
.x-tree-show-line .x-tree-elbow-expander:hover {
  background-position: -60px -40px;
}
.x-tree-show-line .x-tree-elbow-expander-end {
  background-position: -20px -40px;
}
.x-tree-show-line .bui-tree-item-expanded .x-tree-elbow-expander,
.x-tree-show-line .bui-grid-row-expanded .x-tree-elbow-expander {
  background-position: -40px -40px;
}
.x-tree-show-line .bui-tree-item-expanded .x-tree-elbow-expander-end,
.x-tree-show-line .bui-grid-row-expanded .x-tree-elbow-expander-end {
  background-position: 0px -40px;
}
.bui-tree-list .bui-tree-item-loading .x-tree-elbow-expander,
.bui-grid .bui-grid-row-loading .x-tree-elbow-expander {
  background: url("../img/load-16-16.gif") no-repeat 0 0 transparent;
}
.bui-tree-grid .bui-grid-table .bui-grid-cell-text {
  display: inline-block;
  *display: inline;
  /* IE7 inline-block hack */

  *zoom: 1;
}
.x-align-arrow,
.x-align-arrow-inner {
  border: 6px solid transparent;
  _border-color: tomato;
  _filter: chroma(color=#ff6347);
  position: absolute;
}
.x-align-top .x-align-arrow,
.x-align-top-left .x-align-arrow,
.x-align-top-right .x-align-arrow {
  border-top: 8px solid #c3c3d6;
  bottom: -15px;
}
.x-align-top .x-align-arrow-inner,
.x-align-top-left .x-align-arrow-inner,
.x-align-top-right .x-align-arrow-inner {
  border-top: 8px solid #ffffff;
  top: -9px;
  left: -6px;
}
.x-align-top .x-align-arrow,
.x-align-bottom .x-align-arrow {
  left: 50%;
}
.x-align-top-left .x-align-arrow,
.x-align-bottom-left .x-align-arrow {
  left: 10px;
}
.x-align-top-right .x-align-arrow,
.x-align-bottom-right .x-align-arrow {
  right: 10px;
}
.x-align-right .x-align-arrow {
  border-right: 8px solid #c3c3d6;
  top: 50%;
  left: -15px;
}
.x-align-right .x-align-arrow-inner {
  border-right: 8px solid #ffffff;
  top: -6px;
  left: -4px;
}
.x-align-left .x-align-arrow {
  border-left: 8px solid #c3c3d6;
  top: 50%;
  right: -15px;
}
.x-align-left .x-align-arrow-inner {
  border-left: 8px solid #ffffff;
  top: -6px;
  left: -9px;
}
.x-align-bottom .x-align-arrow,
.x-align-bottom-left .x-align-arrow,
.x-align-bottom-right .x-align-arrow {
  border-bottom: 8px solid #c3c3d6;
  top: -15px;
}
.x-align-bottom .x-align-arrow-inner,
.x-align-bottom-left .x-align-arrow-inner,
.x-align-bottom-right .x-align-arrow-inner {
  border-bottom: 8px solid #ffffff;
  top: -5px;
  left: -6px;
}
.tips[class^="x-align-top"] .x-align-arrow,
.tips[class*=" x-align-top"] .x-align-arrow {
  border-top-color: #e5e5e5;
}
.tips[class^="x-align-top"] .x-align-arrow-inner,
.tips[class*=" x-align-top"] .x-align-arrow-inner {
  border-top-color: #fafafa;
}
.tips.x-align-right .x-align-arrow {
  border-right-color: #e5e5e5;
}
.tips.x-align-right .x-align-arrow-inner {
  border-right-color: #fafafa;
}
.tips.x-align-left .x-align-arrow {
  border-left-color: #e5e5e5;
}
.tips.x-align-left .x-align-arrow-inner {
  border-left-color: #fafafa;
}
.tips[class^="x-align-bottom"] .x-align-arrow,
.tips[class*=" x-align-bottom"] .x-align-arrow {
  border-bottom-color: #e5e5e5;
}
.tips[class^="x-align-bottom"] .x-align-arrow-inner,
.tips[class*=" x-align-bottom"] .x-align-arrow-inner {
  border-bottom-color: #fafafa;
}
.tips-success[class^="x-align-top"] .x-align-arrow,
.tips-success[class*=" x-align-top"] .x-align-arrow {
  border-top-color: #aae77f;
}
.tips-success[class^="x-align-top"] .x-align-arrow-inner,
.tips-success[class*=" x-align-top"] .x-align-arrow-inner {
  border-top-color: #f0ffe5;
}
.tips-success.x-align-right .x-align-arrow {
  border-right-color: #aae77f;
}
.tips-success.x-align-right .x-align-arrow-inner {
  border-right-color: #f0ffe5;
}
.tips-success.x-align-left .x-align-arrow {
  border-left-color: #aae77f;
}
.tips-success.x-align-left .x-align-arrow-inner {
  border-left-color: #f0ffe5;
}
.tips-success[class^="x-align-bottom"] .x-align-arrow,
.tips-success[class*=" x-align-bottom"] .x-align-arrow {
  border-bottom-color: #aae77f;
}
.tips-success[class^="x-align-bottom"] .x-align-arrow-inner,
.tips-success[class*=" x-align-bottom"] .x-align-arrow-inner {
  border-bottom-color: #f0ffe5;
}
.tips-warning[class^="x-align-top"] .x-align-arrow,
.tips-warning[class*=" x-align-top"] .x-align-arrow {
  border-top-color: #ffb2b2;
}
.tips-warning[class^="x-align-top"] .x-align-arrow-inner,
.tips-warning[class*=" x-align-top"] .x-align-arrow-inner {
  border-top-color: #fff3f3;
}
.tips-warning.x-align-right .x-align-arrow {
  border-right-color: #ffb2b2;
}
.tips-warning.x-align-right .x-align-arrow-inner {
  border-right-color: #fff3f3;
}
.tips-warning.x-align-left .x-align-arrow {
  border-left-color: #ffb2b2;
}
.tips-warning.x-align-left .x-align-arrow-inner {
  border-left-color: #fff3f3;
}
.tips-warning[class^="x-align-bottom"] .x-align-arrow,
.tips-warning[class*=" x-align-bottom"] .x-align-arrow {
  border-bottom-color: #ffb2b2;
}
.tips-warning[class^="x-align-bottom"] .x-align-arrow-inner,
.tips-warning[class*=" x-align-bottom"] .x-align-arrow-inner {
  border-bottom-color: #fff3f3;
}
.tips-info[class^="x-align-top"] .x-align-arrow,
.tips-info[class*=" x-align-top"] .x-align-arrow {
  border-top-color: #acd5ff;
}
.tips-info[class^="x-align-top"] .x-align-arrow-inner,
.tips-info[class*=" x-align-top"] .x-align-arrow-inner {
  border-top-color: #e6f2ff;
}
.tips-info.x-align-right .x-align-arrow {
  border-right-color: #acd5ff;
}
.tips-info.x-align-right .x-align-arrow-inner {
  border-right-color: #e6f2ff;
}
.tips-info.x-align-left .x-align-arrow {
  border-left-color: #acd5ff;
}
.tips-info.x-align-left .x-align-arrow-inner {
  border-left-color: #e6f2ff;
}
.tips-info[class^="x-align-bottom"] .x-align-arrow,
.tips-info[class*=" x-align-bottom"] .x-align-arrow {
  border-bottom-color: #acd5ff;
}
.tips-info[class^="x-align-bottom"] .x-align-arrow-inner,
.tips-info[class*=" x-align-bottom"] .x-align-arrow-inner {
  border-bottom-color: #e6f2ff;
}
.tips-notice[class^="x-align-top"] .x-align-arrow,
.tips-notice[class*=" x-align-top"] .x-align-arrow {
  border-top-color: #ffcc7f;
}
.tips-notice[class^="x-align-top"] .x-align-arrow-inner,
.tips-notice[class*=" x-align-top"] .x-align-arrow-inner {
  border-top-color: #ffffe5;
}
.tips-notice.x-align-right .x-align-arrow {
  border-right-color: #ffcc7f;
}
.tips-notice.x-align-right .x-align-arrow-inner {
  border-right-color: #ffffe5;
}
.tips-notice.x-align-left .x-align-arrow {
  border-left-color: #ffcc7f;
}
.tips-notice.x-align-left .x-align-arrow-inner {
  border-left-color: #ffffe5;
}
.tips-notice[class^="x-align-bottom"] .x-align-arrow,
.tips-notice[class*=" x-align-bottom"] .x-align-arrow {
  border-bottom-color: #ffcc7f;
}
.tips-notice[class^="x-align-bottom"] .x-align-arrow-inner,
.tips-notice[class*=" x-align-bottom"] .x-align-arrow-inner {
  border-bottom-color: #ffffe5;
}
/** 公共的一些定义 **/
.bui-uploader .bui-uploader-button-wrap {
  display: inline-block;
  padding: 0 5px;
  height: 24px;
  overflow: hidden;
  line-height: 24px;
  position: relative;
  z-index: 500;
  margin-right: 10px;
  text-decoration: none;
  font-size: 12px;
  text-align: center;
  cursor: pointer;
}
.bui-uploader .bui-uploader-button-wrap .file-input-wrapper {
  display: block;
  width: 200px;
  height: 26px;
  position: absolute;
  left: 0;
  top: 0;
  overflow: hidden;
  z-index: 300;
}
.bui-uploader .bui-uploader-button-wrap .file-input-wrapper .file-input {
  background: none repeat scroll 0 0 transparent;
  border: medium none;
  cursor: pointer;
  height: 200px;
  width: 200px;
  top: -50px;
  opacity: 0;
  filter: alpha(opacity=0);
  position: absolute;
  font-size: 100px;
  left: 0;
}
.bui-uploader .bui-uploader-button-wrap .uploader-button-swf {
  display: block;
  width: 200px;
  height: 26px;
  position: absolute;
  left: 0;
  top: 0;
  overflow: hidden;
  z-index: 300;
}
.bui-uploader .bui-uploader-button-wrap .uploader-button-swf .file-input {
  background: none repeat scroll 0 0 transparent;
  border: medium none;
  cursor: pointer;
  height: 200px;
  width: 200px;
  top: -50px;
  opacity: 0;
  filter: alpha(opacity=0);
  position: absolute;
  font-size: 100px;
  left: 0;
}
/**默认定义的样式**/
.defaultTheme .bui-uploader-button-wrap {
  display: inline-block;
  padding: 0 5px;
  height: 24px;
  overflow: hidden;
  line-height: 24px;
  position: relative;
  z-index: 500;
  margin-right: 10px;
  text-decoration: none;
  font-size: 12px;
  text-align: center;
  border: 1px solid #C4DAED;
  /** 渐变 **/

  background: -webkit-gradient(linear, left top, left bottom, from(#fdfefe), to(#dfe7ef));
  background: -moz-linear-gradient(top, #fdfefe, #dfe7ef);
  filter: progid:dximagetransform.microsoft.gradient(startColorstr='#FDFEFE', endColorstr='#DFE7EF');
}
.defaultTheme .bui-uploader-button-wrap:link,
.defaultTheme .bui-uploader-button-wrap:visited {
  color: #404040;
}
.defaultTheme .bui-uploader-button-wrap:hover {
  text-decoration: none;
  color: black;
  background: -webkit-gradient(linear, left top, left bottom, from(#dfe7ef), to(#fdfefe));
  background: -moz-linear-gradient(top, #dfe7ef, #fdfefe);
  filter: progid:dximagetransform.microsoft.gradient(startColorstr='#DFE7EF', endColorstr='#FDFEFE');
}
.defaultTheme .bui-uploader-button-wrap:active {
  background: #DFE7EF;
  filter: none;
}
.defaultTheme .bui-uploader-htmlButton-disabled .bui-uploader-button-wrap {
  border: 1px solid #bfbfbf;
  color: #404040;
  cursor: default;
  background: -webkit-gradient(linear, left top, left bottom, from(#fafafa), to(#e5e5e5));
  background: -moz-linear-gradient(top, #fafafa, #e5e5e5);
  filter: progid:dximagetransform.microsoft.gradient(startColorstr='#FAFAFA', endColorstr='#E5E5E5');
}
.defaultTheme .bui-queue-item {
  position: relative;
}
.defaultTheme .bui-queue-item .default,
.defaultTheme .bui-queue-item .success,
.defaultTheme .bui-queue-item .progress,
.defaultTheme .bui-queue-item .error {
  margin-right: 40px;
}
.defaultTheme .bui-queue-item .error .uploader-error {
  color: #fd7316;
}
.defaultTheme .bui-queue-item .action {
  display: block;
  position: absolute;
  padding-left: 10px;
  width: 30px;
  top: 0px;
  right: 0px;
}
.defaultTheme .bui-queue-item .action .bui-queue-item-del {
  cursor: pointer;
}
.defaultTheme .bui-queue-item .action .bui-queue-item-del:hover {
  color: #fd7316;
}
/**带图片预览的主题样式**/
.imageViewTheme .bui-uploader-button-wrap {
  color: #333;
  border: 1px solid #ddd;
  background-color: #fdfcfc;
  background-color: #eeeeee;
  background-image: -moz-linear-gradient(left, #fdfcfc, #eeeeee);
  background-image: -ms-linear-gradient(left, #fdfcfc, #eeeeee);
  background-image: -webkit-gradient(linear, 0 0, 100% 0, from(#fdfcfc), to(#eeeeee));
  background-image: -webkit-linear-gradient(left, #fdfcfc, #eeeeee);
  background-image: -o-linear-gradient(left, #fdfcfc, #eeeeee);
  background-image: linear-gradient(left, #fdfcfc, #eeeeee);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#fdfcfc', endColorstr='#eeeeee', GradientType=1);
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
}
.imageViewTheme .bui-uploader-button-wrap:hover {
  border-color: #369bd7;
  background-color: #fdfcfc;
  background-color: #e2f1fc;
  background-image: -moz-linear-gradient(left, #fdfcfc, #e2f1fc);
  background-image: -ms-linear-gradient(left, #fdfcfc, #e2f1fc);
  background-image: -webkit-gradient(linear, 0 0, 100% 0, from(#fdfcfc), to(#e2f1fc));
  background-image: -webkit-linear-gradient(left, #fdfcfc, #e2f1fc);
  background-image: -o-linear-gradient(left, #fdfcfc, #e2f1fc);
  background-image: linear-gradient(left, #fdfcfc, #e2f1fc);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#fdfcfc', endColorstr='#e2f1fc', GradientType=1);
}
.imageViewTheme .bui-uploader-htmlButton-disabled .bui-uploader-button-wrap {
  border-color: #eee;
  color: #404040;
  cursor: default;
  background-color: #eee;
}
.imageViewTheme .bui-uploader-htmlButton-disabled .bui-uploader-button-wrap:hover {
  border-color: #eee;
  background-color: #eee;
  background-color: #eeeeee;
  background-image: -moz-linear-gradient(left, #fdfcfc, #eeeeee);
  background-image: -ms-linear-gradient(left, #fdfcfc, #eeeeee);
  background-image: -webkit-gradient(linear, 0 0, 100% 0, from(#fdfcfc), to(#eeeeee));
  background-image: -webkit-linear-gradient(left, #fdfcfc, #eeeeee);
  background-image: -o-linear-gradient(left, #fdfcfc, #eeeeee);
  background-image: linear-gradient(left, #fdfcfc, #eeeeee);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#fdfcfc', endColorstr='#eeeeee', GradientType=1);
}
.imageViewTheme .bui-queue ul {
  *zoom: 1;
}
.imageViewTheme .bui-queue ul:before,
.imageViewTheme .bui-queue ul:after {
  display: table;
  content: "";
}
.imageViewTheme .bui-queue ul:after {
  clear: both;
}
.imageViewTheme .bui-queue .bui-queue-item {
  display: inline;
  float: left;
  margin: 0 10px 10px 0;
  padding: 0;
  width: 120px;
  height: 120px;
  overflow: hidden;
  position: relative;
  border: solid 1px #e0e0e0;
  background-color: #fff;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
  -webkit-box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
  -moz-box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}
.imageViewTheme .bui-queue .bui-queue-item .error,
.imageViewTheme .bui-queue .bui-queue-item .progress {
  margin-top: -10px;
  width: 120px;
  position: absolute;
  top: 50%;
  left: 0;
  z-index: 30;
  color: #f00;
  text-align: center;
}
.imageViewTheme .bui-queue .bui-queue-item .action .bui-queue-item-del {
  display: none;
  color: #676767;
  width: 60px;
  height: 24px;
  line-height: 24px;
  position: absolute;
  top: 50px;
  left: 50px;
  margin-left: -20px;
  text-decoration: none;
  z-index: 3000;
  border: 1px solid #ddd;
  background-color: #fff;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
  text-align: center;
  cursor: pointer;
}
.imageViewTheme .bui-queue .bui-queue-item:hover .action .bui-queue-item-del {
  display: block;
}
.imageViewTheme .bui-queue .bui-queue-item-error {
  border-color: #f00;
}
.bui-slider {
  border: 1px solid #c3c3d6;
  position: relative;
  overflow: visible;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
}
.bui-slider .x-slider-back {
  position: absolute;
  background-color: #eeeeee;
}
.x-slider-handle {
  z-index: 1;
  cursor: pointer;
  display: inline-block;
  *display: inline;
  /* IE7 inline-block hack */

  *zoom: 1;
  position: absolute;
  background-color: #f4f4f4;
  background-image: -moz-linear-gradient(top, #ffffff, #e4e4e4);
  background-image: -ms-linear-gradient(top, #ffffff, #e4e4e4);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#ffffff), to(#e4e4e4));
  background-image: -webkit-linear-gradient(top, #ffffff, #e4e4e4);
  background-image: -o-linear-gradient(top, #ffffff, #e4e4e4);
  background-image: linear-gradient(top, #ffffff, #e4e4e4);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffffff', endColorstr='#e4e4e4', GradientType=0);
  border: 1px solid #d3d3d3;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
  height: 15px;
  width: 15px;
}
.x-slider-handle:hover {
  border: 1px solid #c3c3d6;
  background-color: #e6e6e6;
  background-position: 0 -5px;
  -webkit-transition: background-position 0.1s linear;
  -moz-transition: background-position 0.1s linear;
  -ms-transition: background-position 0.1s linear;
  -o-transition: background-position 0.1s linear;
  transition: background-position 0.1s linear;
}
.x-slider-horizontal {
  height: 10px;
}
.x-slider-horizontal .x-slider-handle {
  top: -3px;
  margin-left: -9px;
}
.x-slider-horizontal .x-slider-back {
  height: 100%;
}
.x-slider-vertical {
  width: 10px;
}
.x-slider-vertical .x-slider-handle {
  left: -3px;
  margin-left: 0;
  margin-bottom: -10px;
}
.x-slider-vertical .x-slider-back {
  width: 100%;
}
.pull-right {
  float: right;
}
.pull-left {
  float: left;
}
.pull-none {
  float: none;
}
.hide {
  display: none;
}
.show {
  display: block;
}
.invisible {
  visibility: hidden;
}
.bordered {
  border: 1px solid #c3c3d6;
}
.bordered-radius {
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
}
.centered {
  text-align: center;
}
.pull-right {
  float: right;
}
.pull-left {
  float: left;
}
.pull-none {
  float: none;
}
.hide {
  display: none;
}
.show {
  display: block;
}
.invisible {
  visibility: hidden;
}
/* inline block */
.bui-inline-block {
  display: inline-block;
  *display: inline;
  *zoom: 1;
}
.bui-ext-mask {
  background-color: #333333;
  opacity: 0.5;
  filter: alpha(opacity=50);
}
.bui-overlay {
  position: absolute;
  left: -9999px;
  top: -9999px;
}
.x-relative {
  position: relative;
}
.x-absolute {
  position: absolute;
}
.bui-clear {
  *zoom: 1;
}
.bui-clear:before,
.bui-clear:after {
  display: table;
  content: "";
}
.bui-clear:after {
  clear: both;
}
.bui-hidden {
  display: none;
}
