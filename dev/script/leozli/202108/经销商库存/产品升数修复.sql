select pack,*
from wx_t_product p 
join wx_t_value_transform_map vtp on p.sku like '%' + vtp.value_before_transform and vtp.transform_type='ProductUnitMapping'
where (p.capacity is null or p.capacity='') and p.status=1
and exists (select 1 from [PP_MID].[dbo].[syn_dw_to_pp_product] bp where bp.[product_code_SAP]=p.sku)

select pack,p.capacity,vtp.value_after_transform,*
--update p set p.capacity=vtp.value_after_transform
from wx_t_product p 
join wx_t_value_transform_map vtp on p.sku like '%' + vtp.value_before_transform and vtp.transform_type='ProductUnitMapping'
where p.status=1 and p.capacity != vtp.value_after_transform
and exists (select 1 from [PP_MID].[dbo].[syn_dw_to_pp_product] bp where bp.[product_code_SAP]=p.sku)

INSERT INTO [wx_t_value_transform_map]([transform_type],[value_before_transform],[value_after_transform],[status],[create_time]) VALUES ('ProductUnitMapping','DNK','200',1,getdate());
INSERT INTO [wx_t_value_transform_map]([transform_type],[value_before_transform],[value_after_transform],[status],[create_time]) VALUES ('ProductUnitMapping','LMB','4',1,getdate());
INSERT INTO [wx_t_value_transform_map]([transform_type],[value_before_transform],[value_after_transform],[status],[create_time]) VALUES ('ProductUnitMapping','HRB','18',1,getdate());
INSERT INTO [wx_t_value_transform_map]([transform_type],[value_before_transform],[value_after_transform],[status],[create_time]) VALUES ('ProductUnitMapping','HRK','18',1,getdate());
INSERT INTO [wx_t_value_transform_map]([transform_type],[value_before_transform],[value_after_transform],[status],[create_time]) VALUES ('ProductUnitMapping','LPK','4',1,getdate());
INSERT INTO [wx_t_value_transform_map]([transform_type],[value_before_transform],[value_after_transform],[status],[create_time]) VALUES ('ProductUnitMapping','DNB','200',1,getdate());

INSERT INTO [wx_t_value_transform_map]([transform_type],[value_before_transform],[value_after_transform],[status],[create_time]) VALUES ('ProductUnitMapping','LPL','4',1,getdate());
INSERT INTO [wx_t_value_transform_map]([transform_type],[value_before_transform],[value_after_transform],[status],[create_time]) VALUES ('ProductUnitMapping','NJL','1',1,getdate());
INSERT INTO [wx_t_value_transform_map]([transform_type],[value_before_transform],[value_after_transform],[status],[create_time]) VALUES ('ProductUnitMapping','LMD','4',1,getdate());
INSERT INTO [wx_t_value_transform_map]([transform_type],[value_before_transform],[value_after_transform],[status],[create_time]) VALUES ('ProductUnitMapping','NJB','1',1,getdate());
INSERT INTO [wx_t_value_transform_map]([transform_type],[value_before_transform],[value_after_transform],[status],[create_time]) VALUES ('ProductUnitMapping','DND','200',1,getdate());
INSERT INTO [wx_t_value_transform_map]([transform_type],[value_before_transform],[value_after_transform],[status],[create_time]) VALUES ('ProductUnitMapping','LPB','4',1,getdate());
INSERT INTO [wx_t_value_transform_map]([transform_type],[value_before_transform],[value_after_transform],[status],[create_time]) VALUES ('ProductUnitMapping','NJK','1',1,getdate());
INSERT INTO [wx_t_value_transform_map]([transform_type],[value_before_transform],[value_after_transform],[status],[create_time]) VALUES ('ProductUnitMapping','LPD','4',1,getdate());
INSERT INTO [wx_t_value_transform_map]([transform_type],[value_before_transform],[value_after_transform],[status],[create_time]) VALUES ('ProductUnitMapping','DNL','200',1,getdate());
INSERT INTO [wx_t_value_transform_map]([transform_type],[value_before_transform],[value_after_transform],[status],[create_time]) VALUES ('ProductUnitMapping','DEE','208',1,getdate());
INSERT INTO [wx_t_value_transform_map]([transform_type],[value_before_transform],[value_after_transform],[status],[create_time]) VALUES ('ProductUnitMapping','KID','0.355',1,getdate());
INSERT INTO [wx_t_value_transform_map]([transform_type],[value_before_transform],[value_after_transform],[status],[create_time]) VALUES ('ProductUnitMapping','KIL','0.355',1,getdate());
INSERT INTO [wx_t_value_transform_map]([transform_type],[value_before_transform],[value_after_transform],[status],[create_time]) VALUES ('ProductUnitMapping','KJB','0.355',1,getdate());
INSERT INTO [wx_t_value_transform_map]([transform_type],[value_before_transform],[value_after_transform],[status],[create_time]) VALUES ('ProductUnitMapping','KJL','0.355',1,getdate());
INSERT INTO [wx_t_value_transform_map]([transform_type],[value_before_transform],[value_after_transform],[status],[create_time]) VALUES ('ProductUnitMapping','UVB','0.355',1,getdate());
INSERT INTO [wx_t_value_transform_map]([transform_type],[value_before_transform],[value_after_transform],[status],[create_time]) VALUES ('ProductUnitMapping','UYE','0.355',1,getdate());
INSERT INTO [wx_t_value_transform_map]([transform_type],[value_before_transform],[value_after_transform],[status],[create_time]) VALUES ('ProductUnitMapping','NTL','0.295',1,getdate());
INSERT INTO [wx_t_value_transform_map]([transform_type],[value_before_transform],[value_after_transform],[status],[create_time]) VALUES ('ProductUnitMapping','PNB','0.47',1,getdate());
INSERT INTO [wx_t_value_transform_map]([transform_type],[value_before_transform],[value_after_transform],[status],[create_time]) VALUES ('ProductUnitMapping','PNL','0.47',1,getdate());
INSERT INTO [wx_t_value_transform_map]([transform_type],[value_before_transform],[value_after_transform],[status],[create_time]) VALUES ('ProductUnitMapping','MQB','0.1',1,getdate());

INSERT INTO [wx_t_value_transform_map]([transform_type],[value_before_transform],[value_after_transform],[status],[create_time]) VALUES ('ProductUnitMapping','MQL','0.1',1,getdate());
INSERT INTO [wx_t_value_transform_map]([transform_type],[value_before_transform],[value_after_transform],[status],[create_time]) VALUES ('ProductUnitMapping','MRB','0.1',1,getdate());
INSERT INTO [wx_t_value_transform_map]([transform_type],[value_before_transform],[value_after_transform],[status],[create_time]) VALUES ('ProductUnitMapping','MSB','0.1',1,getdate());
INSERT INTO [wx_t_value_transform_map]([transform_type],[value_before_transform],[value_after_transform],[status],[create_time]) VALUES ('ProductUnitMapping','MSK','0.1',1,getdate());
INSERT INTO [wx_t_value_transform_map]([transform_type],[value_before_transform],[value_after_transform],[status],[create_time]) VALUES ('ProductUnitMapping','MSL','0.1',1,getdate());
INSERT INTO [wx_t_value_transform_map]([transform_type],[value_before_transform],[value_after_transform],[status],[create_time]) VALUES ('ProductUnitMapping','O01','0.06',1,getdate());
INSERT INTO [wx_t_value_transform_map]([transform_type],[value_before_transform],[value_after_transform],[status],[create_time]) VALUES ('ProductUnitMapping','KIB','0.355',1,getdate());
INSERT INTO [wx_t_value_transform_map]([transform_type],[value_before_transform],[value_after_transform],[status],[create_time]) VALUES ('ProductUnitMapping','FOK','180.000',1,getdate());
INSERT INTO [wx_t_value_transform_map]([transform_type],[value_before_transform],[value_after_transform],[status],[create_time]) VALUES ('ProductUnitMapping','BYB','1000',1,getdate());

INSERT INTO [wx_t_value_transform_map]([transform_type],[value_before_transform],[value_after_transform],[status],[create_time]) VALUES ('ProductUnitMapping','955','1000',1,getdate());
INSERT INTO [wx_t_value_transform_map]([transform_type],[value_before_transform],[value_after_transform],[status],[create_time]) VALUES ('ProductUnitMapping','448','18.9',1,getdate());
INSERT INTO [wx_t_value_transform_map]([transform_type],[value_before_transform],[value_after_transform],[status],[create_time]) VALUES ('ProductUnitMapping','HOK','20',1,getdate());
INSERT INTO [wx_t_value_transform_map]([transform_type],[value_before_transform],[value_after_transform],[status],[create_time]) VALUES ('ProductUnitMapping','BSB','195.000',1,getdate());

INSERT INTO [wx_t_value_transform_map]([transform_type],[value_before_transform],[value_after_transform],[status],[create_time]) VALUES ('ProductUnitMapping','MEL','6',1,getdate());
INSERT INTO [wx_t_value_transform_map]([transform_type],[value_before_transform],[value_after_transform],[status],[create_time]) VALUES ('ProductUnitMapping','LGL','5',1,getdate());
INSERT INTO [wx_t_value_transform_map]([transform_type],[value_before_transform],[value_after_transform],[status],[create_time]) VALUES ('ProductUnitMapping','898','205',1,getdate());
INSERT INTO [wx_t_value_transform_map]([transform_type],[value_before_transform],[value_after_transform],[status],[create_time]) VALUES ('ProductUnitMapping','AHE','1.00',1,getdate());

INSERT INTO [wx_t_value_transform_map]([transform_type],[value_before_transform],[value_after_transform],[status],[create_time]) VALUES ('ProductUnitMapping','HOE','20',1,getdate());
INSERT INTO [wx_t_value_transform_map]([transform_type],[value_before_transform],[value_after_transform],[status],[create_time]) VALUES ('ProductUnitMapping','DEK','208',1,getdate());

INSERT INTO [wx_t_value_transform_map]([transform_type],[value_before_transform],[value_after_transform],[status],[create_time]) VALUES ('ProductUnitMapping','BUE','850.000',1,getdate());
INSERT INTO [wx_t_value_transform_map]([transform_type],[value_before_transform],[value_after_transform],[status],[create_time]) VALUES ('ProductUnitMapping','IEE','15.900',1,getdate());

INSERT INTO [wx_t_value_transform_map]([transform_type],[value_before_transform],[value_after_transform],[status],[create_time]) VALUES ('ProductUnitMapping','871','18.000',1,getdate());
INSERT INTO [wx_t_value_transform_map]([transform_type],[value_before_transform],[value_after_transform],[status],[create_time]) VALUES ('ProductUnitMapping','FWE','54.400',1,getdate());
INSERT INTO [wx_t_value_transform_map]([transform_type],[value_before_transform],[value_after_transform],[status],[create_time]) VALUES ('ProductUnitMapping','FJE','181.400',1,getdate());

INSERT INTO [wx_t_value_transform_map]([transform_type],[value_before_transform],[value_after_transform],[status],[create_time]) VALUES ('ProductUnitMapping','IEL','15.900',1,getdate());
INSERT INTO [wx_t_value_transform_map]([transform_type],[value_before_transform],[value_after_transform],[status],[create_time]) VALUES ('ProductUnitMapping','885','180.000',1,getdate());
INSERT INTO [wx_t_value_transform_map]([transform_type],[value_before_transform],[value_after_transform],[status],[create_time]) VALUES ('ProductUnitMapping','JFK','16.000',1,getdate());

INSERT INTO [wx_t_value_transform_map]([transform_type],[value_before_transform],[value_after_transform],[status],[create_time]) VALUES ('ProductUnitMapping','GHE','158.900',1,getdate());
INSERT INTO [wx_t_value_transform_map]([transform_type],[value_before_transform],[value_after_transform],[status],[create_time]) VALUES ('ProductUnitMapping','ICE','18.000',1,getdate());
INSERT INTO [wx_t_value_transform_map]([transform_type],[value_before_transform],[value_after_transform],[status],[create_time]) VALUES ('ProductUnitMapping','BDD','1000',1,getdate());
INSERT INTO [wx_t_value_transform_map]([transform_type],[value_before_transform],[value_after_transform],[status],[create_time]) VALUES ('ProductUnitMapping','HRD','18',1,getdate());

INSERT INTO [wx_t_value_transform_map]([transform_type],[value_before_transform],[value_after_transform],[status],[create_time]) VALUES ('ProductUnitMapping','BDB','1000',1,getdate());
INSERT INTO [wx_t_value_transform_map]([transform_type],[value_before_transform],[value_after_transform],[status],[create_time]) VALUES ('ProductUnitMapping','HVB','16',1,getdate());
INSERT INTO [wx_t_value_transform_map]([transform_type],[value_before_transform],[value_after_transform],[status],[create_time]) VALUES ('ProductUnitMapping','UBL','0.591',1,getdate());
INSERT INTO [wx_t_value_transform_map]([transform_type],[value_before_transform],[value_after_transform],[status],[create_time]) VALUES ('ProductUnitMapping','UFL','0.35',1,getdate());

INSERT INTO [wx_t_value_transform_map]([transform_type],[value_before_transform],[value_after_transform],[status],[create_time]) VALUES ('ProductUnitMapping','LMK','4',1,getdate());
INSERT INTO [wx_t_value_transform_map]([transform_type],[value_before_transform],[value_after_transform],[status],[create_time]) VALUES ('ProductUnitMapping','HRL','18',1,getdate());

INSERT INTO [wx_t_value_transform_map]([transform_type],[value_before_transform],[value_after_transform],[status],[create_time]) VALUES ('ProductUnitMapping','LML','4',1,getdate());
INSERT INTO [wx_t_value_transform_map]([transform_type],[value_before_transform],[value_after_transform],[status],[create_time]) VALUES ('ProductUnitMapping','OYL','0.1',1,getdate());
INSERT INTO [wx_t_value_transform_map]([transform_type],[value_before_transform],[value_after_transform],[status],[create_time]) VALUES ('ProductUnitMapping','IDK','16.000',1,getdate());
INSERT INTO [wx_t_value_transform_map]([transform_type],[value_before_transform],[value_after_transform],[status],[create_time]) VALUES ('ProductUnitMapping','HOB','20',1,getdate());

INSERT INTO [wx_t_value_transform_map]([transform_type],[value_before_transform],[value_after_transform],[status],[create_time]) VALUES ('ProductUnitMapping','MLK','3.5',1,getdate());
INSERT INTO [wx_t_value_transform_map]([transform_type],[value_before_transform],[value_after_transform],[status],[create_time]) VALUES ('ProductUnitMapping','NKE','1',1,getdate());
INSERT INTO [wx_t_value_transform_map]([transform_type],[value_before_transform],[value_after_transform],[status],[create_time]) VALUES ('ProductUnitMapping','AME','1.00',1,getdate());
INSERT INTO [wx_t_value_transform_map]([transform_type],[value_before_transform],[value_after_transform],[status],[create_time]) VALUES ('ProductUnitMapping','FNE','181.00',1,getdate());

INSERT INTO [wx_t_value_transform_map]([transform_type],[value_before_transform],[value_after_transform],[status],[create_time]) VALUES ('ProductUnitMapping','TAK','20',1,getdate());

INSERT INTO [wx_t_value_transform_map]([transform_type],[value_before_transform],[value_after_transform],[status],[create_time]) VALUES ('ProductUnitMapping','FZK','50',1,getdate());
INSERT INTO [wx_t_value_transform_map]([transform_type],[value_before_transform],[value_after_transform],[status],[create_time]) VALUES ('ProductUnitMapping','RGE','0.400',1,getdate());
