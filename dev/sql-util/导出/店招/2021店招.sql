SELECT 
	msa.req_no "申请编号",
	di2.dic_item_name "品牌",
	di.dic_item_name "申请类别", 
	case when msa.local_make='Y' then '14A申请' else '非14A申请' end "申请方式", 
	case when msa.local_make='N' then s.supplier_name
		else '' end "供应商", 
	msa.req_user_name "销售", 
	msa.region "区域",
	msa.distributor_name "经销商",
	msa.store_name "客户名称",
	CONVERT(varchar(100), wf_wi1.apply_time, 120) "申请时间",
	(case when wf_wsh2.approve_status=15 then '撤回'
		when wf_wsh2.approve_status=40 then '驳回'
		when wf_wsh2.approve_status=30 and wf_ws2.accept_alias<>'' then wf_ws2.accept_alias
		else '通过' end) + wf_ws2.step_name "批复状态",
	CONVERT(varchar(4), YEAR(msa.record_approval_time)) "批复年",
	CONVERT(varchar(2), MONTH(msa.record_approval_time)) "批复月",
	CONVERT(varchar(2), DAY(msa.record_approval_time)) "批复日",
	CONVERT(varchar(4), YEAR(msa.fin_close_process_time)) "FIN关闭流程年",
	CONVERT(varchar(2), MONTH(msa.fin_close_process_time)) "FIN关闭流程月",
	CONVERT(varchar(2), DAY(msa.fin_close_process_time)) "FIN关闭流程日",
	msa.budget_amount "申请金额",
	msa.settlement_amount "结算金额",
	msa.company_code AS "CCN",
	msa.cost_center AS "成本中心",
	(Select distinct d.dic_item_name from parseJSON(msa.store_sign_info) t left join wx_t_dic_item d on d.dic_type_code in ('storeSign14a.materialType','storeSign.materialType','outsideAd14a.materialType','carAd14a.materialType') and d.dic_item_code=t.StringValue where name='signboardMaterial') "门头材质",
	(Select convert(float, StringValue) from parseJSON(msa.store_sign_info) where name='signboardArea') "门头面积",
	case when msa.brand=1 then (Select convert(float, StringValue) from parseJSON(msa.store_sign_info) where name='signboardDoorQuote')
		else (Select convert(float, StringValue) from parseJSON(msa.store_sign_info) where name='signboardQuote') end "门头报价"
FROM wx_t_mkt_signage_apply msa 
left join wx_t_supplier s on s.id=msa.supplier_id
left join wx_t_dic_item di on di.dic_type_code='signage.applyType' and di.dic_item_code=msa.sign_type
left join wx_t_dic_item di2 on di2.dic_type_code='ChevronBrand' and di2.dic_item_code=msa.brand
left join view_customer_region_sales_channel crsc on msa.distributor_id=crsc.distributor_id and crsc.channel_weight&2>0
left join wx_t_workflow_instance wf_wi1 on msa.id=wf_wi1.form_key and wf_wi1.workflow_code='SIGNAGE_APPLY'
left join wx_t_user wf_wi1_u1 on wf_wi1_u1.user_id=wf_wi1.request_user
left join wx_t_workflow_step_history wf_wsh2 on wf_wsh2.history_id=(select max(wf_wsh2_1.history_id) from wx_t_workflow_step_history wf_wsh2_1
left join wx_t_workflow_step_instance wf_wsi2_1 on wf_wsh2_1.step_instance_id=wf_wsi2_1.step_instance_id
left join wx_t_workflow_step wf_ws2_1 on wf_ws2_1.step_id=wf_wsi2_1.step_id
	where wf_wi1.flow_instance_id=wf_wsi2_1.workflow_instance_id and wf_ws2_1.step_code=wf_wi1.current_step)
left join wx_t_user wf_u2 on wf_u2.user_id=wf_wsh2.executor
left join wx_t_workflow_step_instance wf_wsi2 on wf_wsh2.step_instance_id=wf_wsi2.step_instance_id
left join wx_t_workflow_step wf_ws2 on wf_ws2.step_id=wf_wsi2.step_id
WHERE msa.form_status>=10
order by wf_wsh2.execute_time desc



SELECT 
	msa.req_no "申请编号",
	di2.dic_item_name "品牌",
	di.dic_item_name "申请类别", 
	case when msa.local_make='Y' then '14A申请' else '非14A申请' end "申请方式", 
	case when msa.local_make='N' then s.supplier_name
		else '' end "供应商", 
	msa.req_user_name "销售", 
	msa.region "区域",
	msa.distributor_name "经销商",
	msa.store_name "客户名称",
	CONVERT(varchar(100), wf_wi1.apply_time, 120) "申请时间",
	(case when wf_wsh2.approve_status=15 then '撤回'
		when wf_wsh2.approve_status=40 then '驳回'
		when wf_wsh2.approve_status=30 and wf_ws2.accept_alias<>'' then wf_ws2.accept_alias
		else '通过' end) + wf_ws2.step_name "批复状态",
	CONVERT(varchar(4), YEAR(msa.record_approval_time)) "批复年",
	CONVERT(varchar(2), MONTH(msa.record_approval_time)) "批复月",
	CONVERT(varchar(2), DAY(msa.record_approval_time)) "批复日",
	CONVERT(varchar(4), YEAR(msa.fin_close_process_time)) "FIN关闭流程年",
	CONVERT(varchar(2), MONTH(msa.fin_close_process_time)) "FIN关闭流程月",
	CONVERT(varchar(2), DAY(msa.fin_close_process_time)) "FIN关闭流程日",
	msa.budget_amount "申请金额",
	msa.settlement_amount "结算金额",
	msa.company_code AS "CCN",
	msa.cost_center AS "成本中心",
	(Select distinct d.dic_item_name from parseJSON(msa.store_sign_info) t left join wx_t_dic_item d on d.dic_type_code in ('storeSign14a.materialType','storeSign.materialType','outsideAd14a.materialType','carAd14a.materialType') and d.dic_item_code=t.StringValue where name='signboardMaterial') "门头材质",
	(Select convert(float, StringValue) from parseJSON(msa.store_sign_info) where name='signboardArea') "门头面积",
	case when msa.brand=1 then (Select convert(float, StringValue) from parseJSON(msa.store_sign_info) where name='signboardDoorQuote')
		else (Select convert(float, StringValue) from parseJSON(msa.store_sign_info) where name='signboardQuote') end "门头报价",
	case when msa.brand=1 then (Select convert(float, StringValue) from parseJSON(msa.store_sign_info) where name='signboardDecorationQuote')
		else '' end "室内装修报价",
	(Select convert(float, StringValue) from parseJSON(msa.store_sign_info) where name='signboardQuote') "总报价"
FROM wx_t_mkt_signage_apply msa 
left join wx_t_supplier s on s.id=msa.supplier_id
left join wx_t_dic_item di on di.dic_type_code='signage.applyType' and di.dic_item_code=msa.sign_type
left join wx_t_dic_item di2 on di2.dic_type_code='ChevronBrand' and di2.dic_item_code=msa.brand
left join view_customer_region_sales_channel crsc on msa.distributor_id=crsc.distributor_id and crsc.channel_weight&2>0
left join wx_t_workflow_instance wf_wi1 on msa.id=wf_wi1.form_key and wf_wi1.workflow_code='SIGNAGE_APPLY'
left join wx_t_user wf_wi1_u1 on wf_wi1_u1.user_id=wf_wi1.request_user
left join wx_t_workflow_step_history wf_wsh2 on wf_wsh2.history_id=(select max(wf_wsh2_1.history_id) from wx_t_workflow_step_history wf_wsh2_1
left join wx_t_workflow_step_instance wf_wsi2_1 on wf_wsh2_1.step_instance_id=wf_wsi2_1.step_instance_id
left join wx_t_workflow_step wf_ws2_1 on wf_ws2_1.step_id=wf_wsi2_1.step_id
	where wf_wi1.flow_instance_id=wf_wsi2_1.workflow_instance_id and wf_ws2_1.step_code=wf_wi1.current_step)
left join wx_t_user wf_u2 on wf_u2.user_id=wf_wsh2.executor
left join wx_t_workflow_step_instance wf_wsi2 on wf_wsh2.step_instance_id=wf_wsi2.step_instance_id
left join wx_t_workflow_step wf_ws2 on wf_ws2.step_id=wf_wsi2.step_id
WHERE msa.form_status>=10
order by wf_wsh2.execute_time desc

SELECT 
	msa.req_no "申请编号",
	di2.dic_item_name "品牌",
	di.dic_item_name "申请类别", 
	case when msa.local_make='Y' then '14A申请' else '非14A申请' end "申请方式", 
	case when msa.local_make='N' then s.supplier_name
		else '' end "供应商", 
	msa.req_user_name "销售", 
	msa.region "区域",
	msa.distributor_name "经销商",
	msa.store_name "客户名称",
	CONVERT(varchar(100), wf_wi1.apply_time, 120) "申请时间",
	(case when wf_wsh2.approve_status=15 then '撤回'
		when wf_wsh2.approve_status=40 then '驳回'
		when wf_wsh2.approve_status=30 and wf_ws2.accept_alias<>'' then wf_ws2.accept_alias
		else '通过' end) + wf_ws2.step_name "批复状态",
	CONVERT(varchar(4), YEAR(msa.record_approval_time)) "批复年",
	CONVERT(varchar(2), MONTH(msa.record_approval_time)) "批复月",
	CONVERT(varchar(2), DAY(msa.record_approval_time)) "批复日",
	CONVERT(varchar(4), YEAR(msa.fin_close_process_time)) "FIN关闭流程年",
	CONVERT(varchar(2), MONTH(msa.fin_close_process_time)) "FIN关闭流程月",
	CONVERT(varchar(2), DAY(msa.fin_close_process_time)) "FIN关闭流程日",
	msa.budget_amount "申请金额",
	msa.settlement_amount "结算金额",
	msa.company_code AS "CCN",
	msa.cost_center AS "成本中心",
	(Select distinct d.dic_item_name from parseJSON(msa.store_sign_info) t left join wx_t_dic_item d on d.dic_type_code in ('storeSign14a.materialType','storeSign.materialType','outsideAd14a.materialType','carAd14a.materialType') and d.dic_item_code=t.StringValue where name='signboardMaterial') "门头材质",
	(Select convert(float, StringValue) from parseJSON(msa.store_sign_info) where name='signboardArea') "门头面积",
	case when msa.brand=1 then (Select convert(float, StringValue) from parseJSON(msa.store_sign_info) where name='signboardDoorQuote')
		else (Select convert(float, StringValue) from parseJSON(msa.store_sign_info) where name='signboardQuote') end "门头报价",
	case when msa.brand=1 then (Select convert(float, StringValue) from parseJSON(msa.store_sign_info) where name='signboardDecorationQuote')
		else '' end "室内装修报价",
	(Select convert(float, StringValue) from parseJSON(msa.store_sign_info) where name='signboardQuote') "总报价"
FROM wx_t_mkt_signage_apply msa 
left join wx_t_supplier s on s.id=msa.supplier_id
left join wx_t_dic_item di on di.dic_type_code='signage.applyType' and di.dic_item_code=msa.sign_type
left join wx_t_dic_item di2 on di2.dic_type_code='ChevronBrand' and di2.dic_item_code=msa.brand
left join view_customer_region_sales_channel crsc on msa.distributor_id=crsc.distributor_id and crsc.channel_weight&(case when msa.brand=4 then 2 else msa.brand end)>0
left join wx_t_workflow_instance wf_wi1 on msa.id=wf_wi1.form_key and wf_wi1.workflow_code='SIGNAGE_APPLY'
left join wx_t_user wf_wi1_u1 on wf_wi1_u1.user_id=wf_wi1.request_user
left join wx_t_workflow_step_history wf_wsh2 on wf_wsh2.history_id=(select max(wf_wsh2_1.history_id) from wx_t_workflow_step_history wf_wsh2_1
left join wx_t_workflow_step_instance wf_wsi2_1 on wf_wsh2_1.step_instance_id=wf_wsi2_1.step_instance_id
left join wx_t_workflow_step wf_ws2_1 on wf_ws2_1.step_id=wf_wsi2_1.step_id
	where wf_wi1.flow_instance_id=wf_wsi2_1.workflow_instance_id and wf_ws2_1.step_code=wf_wi1.current_step)
left join wx_t_user wf_u2 on wf_u2.user_id=wf_wsh2.executor
left join wx_t_workflow_step_instance wf_wsi2 on wf_wsh2.step_instance_id=wf_wsi2.step_instance_id
left join wx_t_workflow_step wf_ws2 on wf_ws2.step_id=wf_wsi2.step_id
WHERE msa.form_status>=50 and msa.settlement_amount<0.000001
order by wf_wsh2.execute_time desc