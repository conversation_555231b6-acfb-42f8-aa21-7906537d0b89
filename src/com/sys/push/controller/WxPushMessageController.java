package com.sys.push.controller;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import com.common.util.ContextUtil;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.common.constants.Constants;
import com.common.exception.WxPltException;
import com.common.util.MessageResourceUtil;
import com.sys.push.business.MessageBizService;
import com.sys.push.model.MessageParams;
import com.sys.push.model.PushMessageParams;
import com.sys.push.model.WxPushMessage;
import com.sys.push.service.MessagePushService;


@Controller
public class WxPushMessageController {
	
	@Autowired
	private MessagePushService  messagePushService;
	
	@Autowired
	private MessageBizService messageBizService;
	
	public final static String SESSION_KEY_MAIN_PARAMS = "com.sys.push.controller.WxPushMessageController.mainParams";
	
	private final static Logger log = Logger.getLogger(WxPushMessageController.class);

	@ResponseBody
	@RequestMapping(value="/messagepush/test.do", method = {RequestMethod.POST, RequestMethod.GET})
	public void testPushMessage() throws Exception{
		String message = "<div><h1>11111</h1></div><div>2222test</div>";
//		Long userId = Long.valueOf("85788");
//		Long userId2 = Long.valueOf("85748");
		List<Long> userIdList = new ArrayList<Long>();
		userIdList.add(120975l);
//		userIdList.add(userId2);
		messagePushService.pushMessage(WxPushMessage.MSG_TYPE_PUBLIC, "just a test", message, null, userIdList);
	}
	
	/**
	 * 主消息列表页面查询
	 * @param params 查询参数
	 * @return 查询结果
	 */
	@ResponseBody
	@RequestMapping(value="/messagepush/maindata.do")
	public Map<String,Object> queryMainForPage(MessageParams params, HttpServletRequest request){
		Map<String,Object> resultMap = new HashMap<String,Object>(5);
		try {
			messageBizService.queryForPage(params, resultMap);
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
			request.getSession().setAttribute(SESSION_KEY_MAIN_PARAMS, params);
		} catch (WxPltException e) {
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, e.getMessage());
		} catch (Exception e) {
			e.printStackTrace();
			resultMap.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			resultMap.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error(e);
		}
		return resultMap;
	}

	@ResponseBody
	@RequestMapping(value="/messagepush/data.do", method = {RequestMethod.POST, RequestMethod.GET})
	public Map<String, Object> queryForPage(PushMessageParams params){
		return messagePushService.querySumMessageForPage(params);
	}

	@ResponseBody
	@RequestMapping(value="/messagepush/personaldata.do", method = {RequestMethod.POST, RequestMethod.GET})
	public Map<String, Object> queryPersonalForPage(PushMessageParams params){
		Map<String, Object> map = new HashMap<String, Object>(5);
		try {
            if(params.getFrom() != null && params.getFrom().equals("app")){
                params.setAppAvailable(1);
            }
            messageBizService.queryPersonalMessageForPage(params, map);
			map.put(Constants.RESULT_CODE_KEY, Constants.SUCCESS_CODE);
		} catch (Exception e) {
			e.printStackTrace();
			map.put(Constants.RESULT_CODE_KEY, Constants.ERROR_CODE);
			map.put(Constants.RESULT_ERROR_MSG_KEY, MessageResourceUtil.getMessage("system.unexpected_exception"));
			log.error(e.getMessage(), e);
		}
		return map;
	}
	
	@RequestMapping(value="/messagepush/view.do", method = {RequestMethod.POST, RequestMethod.GET})
	public String viewMessage(@RequestParam(value="messageId",required=false)Long messageId, HttpServletRequest request) {
		try {
			if(messageId == null){
				throw new WxPltException("消息ID不能为空");
			}
			request.setAttribute("data", messageBizService.getMessage(messageId));
			return "forward:/sys/push/viewMessage.jsp";
		} catch (WxPltException e) {
			log.error(e.getExpMsg());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		return "forward:/common/jsp/error.jsp";
	}
	
	@RequestMapping(value="/messagepush/editMainMessage.do", method = {RequestMethod.POST, RequestMethod.GET})
	public String viewMainMessage(@RequestParam(value="id",required=false)Long id, HttpServletRequest request) {
		try {
			if(id == null){
				throw new WxPltException("消息ID不能为空");
			}
			request.setAttribute("data", messageBizService.getMainMessage(id));
			return "forward:/sys/push/createMessage.jsp";
		} catch (WxPltException e) {
			log.error(e.getExpMsg());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		return "forward:/common/jsp/error.jsp";
	}
}
