SELECT o.organization_name "经销商名称", max(u.ch_name) "DSR", ws.id, ws.work_shop_name "门店名称", 
(case when ws.status='0' then '潜在' when ws.status='3' then '合作'
when ws.status='-10' then '待抽查' when ws.status='-3' then '不合格'
 when ws.status='-5' then '待完善' when ws.status='1' then '已功店' else ws.status end) "门店状态", wto.create_time "下单时间",  
SUM(wol.amount * CONVERT(FLOAT, tp.capacity)) AS "下单升数"
FROM wx_t_order wto
	LEFT JOIN wx_t_order_line wol ON wto.id = wol.order_id
	LEFT JOIN wx_t_product tp ON wol.sku = tp.sku
	LEFT JOIN wx_t_work_shop ws ON ws.id = wto.work_shop_id
	LEFT JOIN wx_T_workshop_partner wp ON ws.id = wp.workshop_id
	left join wx_t_organization o on o.id=wp.partner_id
	left join wx_t_user u on u.user_id=wto.creator
WHERE wto.status IN (10, 11) and  o.id != 9
	AND ws.delete_flag = 0
	AND wol.id IS NOT NULL
	AND wto.create_time >= '2020-04-01'
	AND wto.create_time < '2020-05-01'
	--and tp.product_property='SN Above'
GROUP BY o.id, o.organization_name, ws.id, ws.work_shop_name, wto.create_time,wto.creator,ws.status
order by o.id,wto.creator